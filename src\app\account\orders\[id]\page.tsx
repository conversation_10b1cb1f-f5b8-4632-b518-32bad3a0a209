"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import { Button } from "@/components/ui/button";
import { getOrderClient } from "@/utils/supabase/orders-client";
import { Order } from "@/utils/supabase/orders";
import { createClient } from "@/utils/supabase/client";
import { Package, ArrowLeft, Clock, CreditCard, Truck, Check, AlertTriangle, Loader2 } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export default function OrderDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    async function fetchOrderData() {
      try {
        setLoading(true);
        
        // Get the current user
        const supabase = createClient();
        const { data: userData } = await supabase.auth.getUser();
        
        if (!userData?.user) {
          router.push('/sign-in?redirect=/account/orders');
          return;
        }
        
        setUser(userData.user);
        
        // Fetch order details
        if (!params.id) {
          throw new Error('Order ID is required');
        }
        
        const orderData = await getOrderClient(params.id as string);
        
        // Check if the order belongs to the current user
        if (orderData.user_id !== userData.user.id) {
          throw new Error('You do not have permission to view this order');
        }
        
        setOrder(orderData);
      } catch (err: any) {
        console.error('Error fetching order:', err);
        setError(err.message || 'Failed to load order details');
      } finally {
        setLoading(false);
      }
    }
    
    fetchOrderData();
  }, [params.id, router]);

  // Helper function to get status badge color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'shipped':
        return 'bg-blue-100 text-blue-800';
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Helper function to get payment status badge color
  const getPaymentStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'refunded':
        return 'bg-purple-100 text-purple-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  // Helper function to get status icon
  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'delivered':
        return <Check className="h-5 w-5 text-green-600" />;
      case 'shipped':
        return <Truck className="h-5 w-5 text-blue-600" />;
      case 'processing':
        return <Clock className="h-5 w-5 text-yellow-600" />;
      case 'cancelled':
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      default:
        return <Package className="h-5 w-5 text-gray-600" />;
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <main className="flex-grow container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-96">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <main className="flex-grow container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-destructive/10 mb-4">
              <AlertTriangle className="h-8 w-8 text-destructive" />
            </div>
            <h2 className="text-2xl font-bold text-foreground mb-2">
              {error || "Order not found"}
            </h2>
            <p className="text-muted-foreground mb-6">
              We couldn't find the order you're looking for.
            </p>
            <Button asChild>
              <Link href="/account/orders">Back to Orders</Link>
            </Button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Format order date
  const orderDate = new Date(order.created_at || Date.now()).toLocaleDateString("en-ZA", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />

      <main className="flex-grow container mx-auto px-4 py-8">
        {/* Back button */}
        <Button variant="ghost" asChild className="mb-6">
          <Link href="/account/orders" className="flex items-center">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Orders
          </Link>
        </Button>

        {/* Order Header */}
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Order #{order.id.substring(0, 8)}
            </h1>
            <p className="text-muted-foreground">
              Placed on {orderDate}
            </p>
          </div>
          <div className="flex items-center gap-3 mt-4 md:mt-0">
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(order.status)}`}>
              {order.status}
            </span>
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getPaymentStatusColor(order.payment_status)}`}>
              {order.payment_status}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Order Details */}
          <div className="lg:col-span-2 space-y-8">
            {/* Order Status */}
            <div className="bg-card border border-border rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Order Status</h2>
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                  {getStatusIcon(order.status)}
                </div>
                <div>
                  <p className="font-medium">{order.status}</p>
                  <p className="text-sm text-muted-foreground">
                    {order.status === 'delivered' ? 'Your order has been delivered' :
                     order.status === 'shipped' ? 'Your order is on its way' :
                     order.status === 'processing' ? 'Your order is being processed' :
                     order.status === 'cancelled' ? 'Your order has been cancelled' :
                     'Your order has been received'}
                  </p>
                </div>
              </div>
            </div>

            {/* Order Items */}
            <div className="bg-card border border-border rounded-lg overflow-hidden">
              <h2 className="text-xl font-semibold p-6 border-b border-border">Order Items</h2>
              <ul className="divide-y divide-border">
                {order.items.map((item, index) => (
                  <li key={index} className="p-4 sm:p-6 flex flex-col sm:flex-row gap-4">
                    <div className="relative h-20 w-20 rounded-md overflow-hidden bg-muted flex-shrink-0">
                      {item.image ? (
                        <Image
                          src={item.image}
                          alt={item.name}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full w-full">
                          <Package className="h-8 w-8 text-muted-foreground" />
                        </div>
                      )}
                    </div>
                    <div className="flex-grow">
                      <h3 className="font-medium">{item.name}</h3>
                      <p className="text-sm text-muted-foreground mb-1">
                        Quantity: {item.quantity}
                      </p>
                      <p className="text-sm font-medium">
                        R {item.price.toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        R {(item.price * item.quantity).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                      </p>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Order Summary */}
          <div className="space-y-8">
            {/* Payment Info */}
            <div className="bg-card border border-border rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Payment Information</h2>
              <div className="flex items-center gap-4 mb-4">
                <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                  <CreditCard className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="font-medium capitalize">{order.payment_status}</p>
                  <p className="text-sm text-muted-foreground">
                    {order.payment_status === 'paid' ? 'Payment received' : 
                     order.payment_status === 'pending' ? 'Awaiting payment' : 
                     order.payment_status === 'failed' ? 'Payment failed' : 
                     'Payment status unknown'}
                  </p>
                </div>
              </div>
              <div className="border-t border-border pt-4 space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Subtotal</span>
                  <span>
                    R {(order.total_amount - 99.99).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Shipping</span>
                  <span>
                    R 99.99
                  </span>
                </div>
                <div className="flex justify-between font-medium text-lg pt-2 border-t border-border mt-2">
                  <span>Total</span>
                  <span className="text-primary">
                    R {order.total_amount.toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </span>
                </div>
              </div>
            </div>

            {/* Shipping Info */}
            <div className="bg-card border border-border rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Shipping Information</h2>
              <div className="flex items-center gap-4 mb-4">
                <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                  <Truck className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="font-medium">{order.shipping_details.name}</p>
                  <p className="text-sm text-muted-foreground">{order.shipping_details.email}</p>
                </div>
              </div>
              <div className="border-t border-border pt-4">
                <p className="font-medium mb-1">Shipping Address</p>
                <address className="text-sm text-muted-foreground not-italic">
                  {order.shipping_details.address}<br />
                  {order.shipping_details.city}, {order.shipping_details.postal_code}<br />
                  {order.shipping_details.country}<br />
                  {order.shipping_details.phone && (
                    <div className="mt-2">
                      Phone: {order.shipping_details.phone}
                    </div>
                  )}
                </address>
              </div>
            </div>

            {/* Need Help */}
            <div className="bg-muted/30 rounded-lg p-6">
              <h3 className="font-medium mb-2">Need Help?</h3>
              <p className="text-sm text-muted-foreground mb-4">
                If you have any questions about your order, please contact our customer support team.
              </p>
              <Button variant="outline" asChild className="w-full">
                <Link href="/contact">Contact Support</Link>
              </Button>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
