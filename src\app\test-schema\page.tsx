"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import { Loader2 } from "lucide-react";

export default function TestSchemaPage() {
  const [schema, setSchema] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [addingColumns, setAddingColumns] = useState(false);

  const fetchSchema = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/test-schema');
      const data = await response.json();
      
      if (response.ok) {
        setSchema(data);
      } else {
        setError(data.error || 'Failed to fetch schema');
      }
    } catch (err: any) {
      console.error('Error fetching schema:', err);
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const addMissingColumns = async () => {
    setAddingColumns(true);
    setError(null);
    
    try {
      // Create a simple API endpoint to add the columns
      const response = await fetch('/api/add-columns', {
        method: 'POST'
      });
      const data = await response.json();
      
      if (response.ok) {
        alert('Columns added successfully!');
        // Refresh the schema
        fetchSchema();
      } else {
        setError(data.error || 'Failed to add columns');
      }
    } catch (err: any) {
      console.error('Error adding columns:', err);
      setError(err.message || 'An error occurred');
    } finally {
      setAddingColumns(false);
    }
  };

  useEffect(() => {
    fetchSchema();
  }, []);

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow container mx-auto px-4 py-12">
        <h1 className="text-2xl font-bold mb-6">Database Schema Test</h1>
        
        <div className="mb-8 flex space-x-4">
          <Button 
            onClick={fetchSchema}
            disabled={loading}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Fetching Schema...
              </>
            ) : (
              'Refresh Schema'
            )}
          </Button>
          
          <Button 
            onClick={addMissingColumns}
            disabled={addingColumns}
            variant="secondary"
          >
            {addingColumns ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Adding Columns...
              </>
            ) : (
              'Add Missing Columns'
            )}
          </Button>
        </div>
        
        {error && (
          <div className="mt-4 p-4 bg-red-50 text-red-700 rounded-md">
            {error}
          </div>
        )}
        
        {schema && (
          <div className="mt-8">
            <h2 className="text-xl font-semibold mb-4">Table Columns</h2>
            
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white border border-gray-200">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="py-2 px-4 border-b text-left">Column Name</th>
                  </tr>
                </thead>
                <tbody>
                  {schema.columns.map((column: string) => (
                    <tr key={column} className="hover:bg-gray-50">
                      <td className="py-2 px-4 border-b">{column}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            {schema.sample && schema.sample.length > 0 && (
              <div className="mt-8">
                <h2 className="text-xl font-semibold mb-4">Sample Data</h2>
                <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto">
                  {JSON.stringify(schema.sample, null, 2)}
                </pre>
              </div>
            )}
          </div>
        )}
      </main>
      
      <Footer />
    </div>
  )
} 