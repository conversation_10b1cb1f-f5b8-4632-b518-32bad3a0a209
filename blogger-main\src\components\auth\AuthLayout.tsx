import { ReactNode } from "react";
import { <PERSON> } from "react-router-dom";

export default function AuthLayout({ children }: { children: ReactNode }) {
  return (
    <div className="min-h-screen bg-[#f5f5f7] text-black">
      <div className="min-h-screen flex items-center justify-center">
        <div className="max-w-md w-full px-4">
          <div className="text-center mb-8">
            <Link to="/">
              <h2 className="text-4xl font-bold tracking-tight">
                Thabo Bester
              </h2>
            </Link>
            <p className="text-xl font-medium text-gray-600 mt-2">
              Your premium content platform
            </p>
          </div>
          {children}
        </div>
      </div>
    </div>
  );
}
