-- =====================================================
-- SIMPLE ADMIN USER CREATION (Most Reliable Method)
-- This script creates admin user using the safest approach
-- =====================================================

-- STEP 1: ENSURE PROFILES TABLE EXISTS
-- =====================================================

-- Create profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.profiles (
    id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    first_name text,
    last_name text,
    email text,
    role text DEFAULT 'user' CHECK (role IN ('user', 'admin', 'author')),
    avatar_url text,
    bio text,
    website text,
    location text,
    last_sign_in_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- Enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (drop existing first)
DROP POLICY IF EXISTS "Enable read access for all users" ON public.profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users based on user_id" ON public.profiles;
DROP POLICY IF EXISTS "Enable update for users based on user_id" ON public.profiles;

CREATE POLICY "Enable read access for all users" ON public.profiles 
    FOR SELECT USING (true);

CREATE POLICY "Enable insert for authenticated users based on user_id" ON public.profiles 
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Enable update for users based on user_id" ON public.profiles 
    FOR UPDATE USING (auth.uid() = id);

-- STEP 2: CREATE USER CREATION FUNCTION
-- =====================================================

-- Drop existing function and trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user() CASCADE;

-- Create user creation function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (
        id, 
        email, 
        first_name, 
        last_name, 
        role, 
        last_sign_in_at,
        created_at,
        updated_at
    )
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(
            NEW.raw_user_meta_data->>'first_name', 
            NEW.raw_user_meta_data->>'full_name', 
            split_part(NEW.email, '@', 1)
        ),
        COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
        CASE
            WHEN (SELECT COUNT(*) FROM auth.users WHERE id != NEW.id) = 0 THEN 'admin'
            ELSE 'user'
        END,
        NEW.last_sign_in_at,
        NEW.created_at,
        NEW.updated_at
    )
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name,
        last_sign_in_at = EXCLUDED.last_sign_in_at,
        updated_at = timezone('utc'::text, now());

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger
CREATE TRIGGER on_auth_user_created
    AFTER INSERT OR UPDATE ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- STEP 3: UPDATE EXISTING USER TO ADMIN (IF EXISTS)
-- =====================================================

-- Check if the user already exists and update their role
DO $$
DECLARE
    existing_user_id UUID;
BEGIN
    -- Try to find existing user by email
    SELECT id INTO existing_user_id 
    FROM auth.users 
    WHERE email = '<EMAIL>';
    
    IF existing_user_id IS NOT NULL THEN
        -- User exists, update their profile to admin
        INSERT INTO public.profiles (
            id, email, first_name, last_name, role, bio,
            last_sign_in_at, created_at, updated_at
        ) VALUES (
            existing_user_id, '<EMAIL>', 'reeper', '', 'admin',
            'Admin user - updated via SQL',
            timezone('utc'::text, now()), timezone('utc'::text, now()), timezone('utc'::text, now())
        )
        ON CONFLICT (id) DO UPDATE SET
            role = 'admin',
            first_name = 'reeper',
            updated_at = timezone('utc'::text, now());
            
        RAISE NOTICE '✅ Existing user updated to admin: %', existing_user_id;
    ELSE
        RAISE NOTICE '⚠️  User <EMAIL> does not exist yet';
        RAISE NOTICE '📋 MANUAL STEPS REQUIRED:';
        RAISE NOTICE '1. Go to your app and register with: <EMAIL>';
        RAISE NOTICE '2. Use password: 123456789';
        RAISE NOTICE '3. The trigger will automatically make them admin';
        RAISE NOTICE '4. Or use Supabase Auth dashboard to create user';
    END IF;
END $$;

-- STEP 4: CREATE STORAGE BUCKETS
-- =====================================================

-- Create avatars bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'avatars',
    'avatars',
    true,
    52428800, -- 50MB
    ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']
)
ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Create articles bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'articles',
    'articles',
    true,
    52428800, -- 50MB
    ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'video/webm']
)
ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Create products bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'products',
    'products',
    true,
    52428800, -- 50MB
    ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']
)
ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Create media bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'media',
    'media',
    true,
    104857600, -- 100MB
    ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'video/webm', 'audio/mpeg', 'audio/wav', 'application/pdf', 'application/zip']
)
ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- STEP 5: CREATE STORAGE POLICIES
-- =====================================================

-- Drop existing storage policies
DROP POLICY IF EXISTS "Avatar images are publicly accessible" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can upload an avatar" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can update their own avatar" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can delete their own avatar" ON storage.objects;

-- Avatars bucket policies
CREATE POLICY "Avatar images are publicly accessible" ON storage.objects
    FOR SELECT USING (bucket_id = 'avatars');

CREATE POLICY "Anyone can upload an avatar" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'avatars' AND auth.role() = 'authenticated');

CREATE POLICY "Anyone can update their own avatar" ON storage.objects
    FOR UPDATE USING (bucket_id = 'avatars' AND auth.role() = 'authenticated');

CREATE POLICY "Anyone can delete their own avatar" ON storage.objects
    FOR DELETE USING (bucket_id = 'avatars' AND auth.role() = 'authenticated');

-- STEP 6: VERIFICATION AND INSTRUCTIONS
-- =====================================================

-- Check current state
SELECT 
    'CURRENT USERS' as check_type,
    u.id, u.email, u.email_confirmed_at,
    p.role, p.first_name
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
ORDER BY u.created_at;

-- Count admin users
SELECT 
    'ADMIN COUNT' as check_type,
    COUNT(*) as total_admins,
    string_agg(email, ', ') as admin_emails
FROM public.profiles 
WHERE role = 'admin';

-- Final instructions
DO $$
DECLARE
    user_exists BOOLEAN;
    buckets_count INTEGER;
BEGIN
    SELECT EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>') INTO user_exists;
    SELECT COUNT(*) INTO buckets_count FROM storage.buckets WHERE id IN ('avatars', 'articles', 'products', 'media');
    
    RAISE NOTICE '🎉 SIMPLE ADMIN SETUP COMPLETE!';
    RAISE NOTICE '==========================================';
    RAISE NOTICE '📊 SYSTEM STATUS:';
    RAISE NOTICE 'User <EMAIL> exists: %', user_exists;
    RAISE NOTICE 'Storage buckets created: %/4', buckets_count;
    RAISE NOTICE 'Profiles table: ✅ Ready';
    RAISE NOTICE 'User creation trigger: ✅ Active';
    RAISE NOTICE '==========================================';
    
    IF user_exists THEN
        RAISE NOTICE '✅ SUCCESS: Admin user is ready!';
        RAISE NOTICE '🚀 LOGIN DETAILS:';
        RAISE NOTICE 'Email: <EMAIL>';
        RAISE NOTICE 'Password: 123456789';
        RAISE NOTICE 'Role: admin';
        RAISE NOTICE '';
        RAISE NOTICE '📋 Next steps:';
        RAISE NOTICE '1. Go to your app login page';
        RAISE NOTICE '2. Login with the credentials above';
        RAISE NOTICE '3. You should have full admin access';
    ELSE
        RAISE NOTICE '📋 TO CREATE ADMIN USER:';
        RAISE NOTICE '1. Go to your app registration page';
        RAISE NOTICE '2. Register with: <EMAIL>';
        RAISE NOTICE '3. Use password: 123456789';
        RAISE NOTICE '4. The trigger will automatically make you admin';
        RAISE NOTICE '';
        RAISE NOTICE 'OR use Supabase Auth dashboard:';
        RAISE NOTICE '1. Go to Authentication → Users';
        RAISE NOTICE '2. Click "Invite user"';
        RAISE NOTICE '3. Email: <EMAIL>';
        RAISE NOTICE '4. The trigger will make them admin automatically';
    END IF;
    
    RAISE NOTICE '==========================================';
END $$;
