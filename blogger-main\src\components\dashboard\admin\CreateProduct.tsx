import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogDescription } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Plus,
  Save,
  Upload,
  FileText,
  Video,
  Music,
  Download,
  Folder,
  ArrowLeft,
  Package
} from 'lucide-react';
import { supabase } from '../../../../supabase/supabase';
import { useAuth } from '../../../../supabase/auth';
import { useNavigate } from 'react-router-dom';

interface ProductCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  color: string;
}

interface ProductFormData {
  name: string;
  slug: string;
  description: string;
  price: number;
  stock_quantity: number;
  category_id: string;
  image_url?: string;
  status: 'active' | 'inactive' | 'draft';
  product_type: 'physical' | 'digital';
  file_type?: 'pdf' | 'ebook' | 'audiobook' | 'video' | 'audio' | 'course' | 'software' | 'template';
  download_limit?: number;
  access_duration_days?: number;
  digital_files: File[];
  image_files: File[];
}

export function CreateProduct() {
  const { toast } = useToast();
  const { user, isAdmin } = useAuth();
  const navigate = useNavigate();
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isCategoryDialogOpen, setIsCategoryDialogOpen] = useState(false);
  const [newCategory, setNewCategory] = useState({ name: '', description: '', color: '#3B82F6' });
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    slug: '',
    description: '',
    price: 0,
    stock_quantity: 0,
    category_id: '',
    image_url: '',
    status: 'active',
    product_type: 'physical',
    file_type: undefined,
    download_limit: undefined,
    access_duration_days: undefined,
    digital_files: [],
    image_files: []
  });

  const fileTypes = [
    { value: 'pdf', label: 'PDF Document', icon: FileText },
    { value: 'ebook', label: 'E-book (EPUB/PDF)', icon: FileText },
    { value: 'audiobook', label: 'Audio Book', icon: Music },
    { value: 'video', label: 'Video', icon: Video },
    { value: 'audio', label: 'Audio/Music', icon: Music },
    { value: 'course', label: 'Online Course', icon: Video },
    { value: 'software', label: 'Software', icon: Download },
    { value: 'template', label: 'Template', icon: Folder }
  ];

  useEffect(() => {
    // Check if user is admin
    if (!isAdmin) {
      toast({
        title: 'Access Denied',
        description: 'Only administrators can create products',
        variant: 'destructive',
      });
      navigate('/dashboard/products');
      return;
    }

    loadCategories();
  }, [isAdmin, navigate]);

  const loadCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('product_categories')
        .select('*')
        .order('name');

      if (error) throw error;
      setCategories(data || []);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const generateSlug = async (name: string) => {
    // Create base slug from name
    let slug = name.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
    
    // Check if slug already exists and make it unique
    const { data: existingProducts } = await supabase
      .from('products')
      .select('slug')
      .like('slug', `${slug}%`);

    if (existingProducts && existingProducts.length > 0) {
      const existingSlugs = existingProducts.map(product => product.slug);
      let counter = 1;
      let uniqueSlug = slug;
      
      while (existingSlugs.includes(uniqueSlug)) {
        uniqueSlug = `${slug}-${counter}`;
        counter++;
      }
      
      slug = uniqueSlug;
    }

    return slug;
  };

  const createCategory = async () => {
    if (!newCategory.name.trim()) return;

    try {
      let slug = newCategory.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
      
      // Check if slug already exists and make it unique
      const { data: existingCategories } = await supabase
        .from('product_categories')
        .select('slug')
        .like('slug', `${slug}%`);

      if (existingCategories && existingCategories.length > 0) {
        const existingSlugs = existingCategories.map(cat => cat.slug);
        let counter = 1;
        let uniqueSlug = slug;
        
        while (existingSlugs.includes(uniqueSlug)) {
          uniqueSlug = `${slug}-${counter}`;
          counter++;
        }
        
        slug = uniqueSlug;
      }

      const { data, error } = await supabase
        .from('product_categories')
        .insert({
          name: newCategory.name,
          slug,
          description: newCategory.description,
          color: newCategory.color,
        })
        .select()
        .single();

      if (error) throw error;

      setCategories(prev => [...prev, data]);
      setNewCategory({ name: '', description: '', color: '#3B82F6' });
      setIsCategoryDialogOpen(false);

      toast({
        title: 'Success',
        description: 'Category created successfully',
      });
    } catch (error) {
      console.error('Error creating category:', error);
      toast({
        title: 'Error',
        description: 'Failed to create category',
        variant: 'destructive',
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Generate slug for the product
      const slug = formData.slug || await generateSlug(formData.name);

      // Upload image files if provided
      let imageUrl = formData.image_url;
      if (formData.image_files && formData.image_files.length > 0) {
        const imageFile = formData.image_files[0]; // Use first image
        const fileExt = imageFile.name.split('.').pop();
        const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('product-images')
          .upload(fileName, imageFile);

        if (uploadError) {
          console.error('Image upload error:', uploadError);
          toast({
            title: 'Warning',
            description: 'Failed to upload image, but product will be created without image',
            variant: 'destructive',
          });
        } else {
          const { data: { publicUrl } } = supabase.storage
            .from('product-images')
            .getPublicUrl(fileName);
          imageUrl = publicUrl;
        }
      }

      let productData = {
        name: formData.name,
        slug: slug,
        description: formData.description,
        price: formData.price,
        stock_quantity: formData.product_type === 'physical' ? formData.stock_quantity : 0,
        category_id: formData.category_id || null,
        image_url: imageUrl,
        status: formData.status,
        product_type: formData.product_type,
        file_type: formData.product_type === 'digital' ? formData.file_type : null,
        download_limit: formData.product_type === 'digital' ? formData.download_limit : null,
        access_duration_days: formData.product_type === 'digital' ? formData.access_duration_days : null,
      };

      // Create new product
      const { data, error } = await supabase
        .from('products')
        .insert([{
          ...productData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'Product created successfully',
      });

      // Navigate back to products list
      navigate('/dashboard/products');
    } catch (error) {
      console.error('Error saving product:', error);
      toast({
        title: 'Error',
        description: 'Failed to save product',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      slug: '',
      description: '',
      price: 0,
      stock_quantity: 0,
      category_id: '',
      image_url: '',
      status: 'active',
      product_type: 'physical',
      file_type: undefined,
      download_limit: undefined,
      access_duration_days: undefined,
      digital_files: [],
      image_files: []
    });
  };

  // Auto-generate slug when name changes
  useEffect(() => {
    if (formData.name && !formData.slug) {
      const autoSlug = formData.name.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
      setFormData(prev => ({ ...prev, slug: autoSlug }));
    }
  }, [formData.name]);

  return (
    <div className="space-y-4 sm:space-y-6 p-3 sm:p-4 lg:p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/dashboard/products')}
            className="text-xs sm:text-sm"
          >
            <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
            Back to Products
          </Button>
          <div>
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">Create New Product</h1>
            <p className="text-sm sm:text-base text-gray-600">Add a new product to your inventory</p>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            Product Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
            {/* Product Type Toggle */}
            <div className="flex items-center space-x-2">
              <Switch
                id="product-type"
                checked={formData.product_type === 'digital'}
                onCheckedChange={(checked) => 
                  setFormData(prev => ({ 
                    ...prev, 
                    product_type: checked ? 'digital' : 'physical',
                    file_type: checked ? 'pdf' : undefined,
                    stock_quantity: checked ? 0 : prev.stock_quantity
                  }))
                }
              />
              <Label htmlFor="product-type" className="text-sm font-medium">
                Digital Product
              </Label>
              <Badge variant={formData.product_type === 'digital' ? 'default' : 'secondary'} className="text-xs">
                {formData.product_type === 'digital' ? 'Digital' : 'Physical'}
              </Badge>
            </div>

            {/* Basic Information */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">Product Name *</Label>
                <Input
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter product name"
                  className="text-sm"
                  required
                />
              </div>
              <div>
                <Label className="text-sm font-medium">Slug *</Label>
                <Input
                  value={formData.slug}
                  onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                  placeholder="product-slug"
                  className="text-sm"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  URL-friendly version of the name (auto-generated)
                </p>
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium">Description *</Label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Enter product description"
                rows={4}
                className="text-sm"
                required
              />
            </div>

            {/* Category Selection */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label className="text-sm font-medium">Category</Label>
                <Dialog open={isCategoryDialogOpen} onOpenChange={setIsCategoryDialogOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm" className="text-xs">
                      <Plus className="w-3 h-3 mr-1" />
                      Add Category
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="w-[95vw] max-w-md">
                    <DialogHeader>
                      <DialogTitle className="text-lg">Create Category</DialogTitle>
                      <DialogDescription className="text-sm">
                        Add a new product category.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="category-name" className="text-sm font-medium">Name</Label>
                        <Input
                          id="category-name"
                          value={newCategory.name}
                          onChange={(e) => setNewCategory(prev => ({ ...prev, name: e.target.value }))}
                          placeholder="Category name"
                          className="text-sm"
                        />
                      </div>
                      <div>
                        <Label htmlFor="category-description" className="text-sm font-medium">Description</Label>
                        <Textarea
                          id="category-description"
                          value={newCategory.description}
                          onChange={(e) => setNewCategory(prev => ({ ...prev, description: e.target.value }))}
                          placeholder="Category description"
                          rows={2}
                          className="text-sm"
                        />
                      </div>
                      <div>
                        <Label htmlFor="category-color" className="text-sm font-medium">Color</Label>
                        <Input
                          id="category-color"
                          type="color"
                          value={newCategory.color}
                          onChange={(e) => setNewCategory(prev => ({ ...prev, color: e.target.value }))}
                          className="h-10"
                        />
                      </div>
                      <div className="flex gap-2">
                        <Button onClick={createCategory} className="flex-1 text-sm">
                          Create Category
                        </Button>
                        <Button variant="outline" onClick={() => setIsCategoryDialogOpen(false)} className="text-sm">
                          Cancel
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
              <Select
                value={formData.category_id}
                onValueChange={(value) => setFormData(prev => ({ ...prev, category_id: value }))}
              >
                <SelectTrigger className="text-sm">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id} className="text-sm">
                      <div className="flex items-center gap-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: category.color }}
                        />
                        {category.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Pricing and Stock */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div>
                <Label className="text-sm font-medium">Price (ZAR) *</Label>
                <Input
                  type="number"
                  step="0.01"
                  value={formData.price}
                  onChange={(e) => setFormData(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                  placeholder="0.00"
                  className="text-sm"
                  required
                />
              </div>
              {formData.product_type === 'physical' && (
                <div>
                  <Label className="text-sm font-medium">Stock Quantity *</Label>
                  <Input
                    type="number"
                    value={formData.stock_quantity}
                    onChange={(e) => setFormData(prev => ({ ...prev, stock_quantity: parseInt(e.target.value) || 0 }))}
                    placeholder="0"
                    className="text-sm"
                    required
                  />
                </div>
              )}
              <div>
                <Label className="text-sm font-medium">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value: any) => setFormData(prev => ({ ...prev, status: value }))}
                >
                  <SelectTrigger className="text-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Digital Product Settings */}
            {formData.product_type === 'digital' && (
              <div className="space-y-4 p-4 border rounded-lg bg-purple-50">
                <h3 className="font-medium text-purple-900">Digital Product Settings</h3>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">File Type</Label>
                    <Select
                      value={formData.file_type || ''}
                      onValueChange={(value: any) => setFormData(prev => ({ ...prev, file_type: value }))}
                    >
                      <SelectTrigger className="text-sm">
                        <SelectValue placeholder="Select file type" />
                      </SelectTrigger>
                      <SelectContent>
                        {fileTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value} className="text-sm">
                            <div className="flex items-center gap-2">
                              <type.icon className="w-4 h-4" />
                              {type.label}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Download Limit</Label>
                    <Input
                      type="number"
                      value={formData.download_limit || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, download_limit: parseInt(e.target.value) || undefined }))}
                      placeholder="Unlimited"
                      className="text-sm"
                    />
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium">Access Duration (Days)</Label>
                  <Input
                    type="number"
                    value={formData.access_duration_days || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, access_duration_days: parseInt(e.target.value) || undefined }))}
                    placeholder="Lifetime access"
                    className="text-sm"
                  />
                </div>

                <div>
                  <Label className="text-sm font-medium">Digital Files</Label>
                  <Input
                    type="file"
                    multiple
                    accept=".pdf,.epub,.mobi,.mp3,.m4a,.wav,.aac,.mp4,.avi,.mov,.zip"
                    onChange={(e) => {
                      const files = Array.from(e.target.files || []);
                      setFormData(prev => ({ ...prev, digital_files: files }));
                    }}
                    className="text-sm"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Supported: PDF, EPUB, MOBI, MP3, M4A, WAV, AAC, MP4, AVI, MOV, ZIP
                  </p>
                </div>
              </div>
            )}

            {/* Product Images */}
            <div>
              <Label className="text-sm font-medium">Product Images</Label>
              <div className="space-y-2">
                <Input
                  type="url"
                  value={formData.image_url || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, image_url: e.target.value }))}
                  placeholder="Image URL (optional)"
                  className="text-sm"
                />
                <div className="text-xs text-gray-500">OR</div>
                <Input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={(e) => {
                    const files = Array.from(e.target.files || []);
                    setFormData(prev => ({ ...prev, image_files: files }));
                  }}
                  className="text-sm"
                />
                <p className="text-xs text-gray-500">
                  Upload product images (JPG, PNG, WebP)
                </p>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/dashboard/products')}
              >
                Cancel
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={resetForm}
              >
                Reset Form
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Upload className="w-4 h-4 mr-2 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Create Product
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
