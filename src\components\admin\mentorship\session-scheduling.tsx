"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

type Session = {
  id: string;
  studentName: string;
  date: string;
  time: string;
  duration: string;
  status: "scheduled" | "completed" | "cancelled";
};

const mockSessions: Session[] = [
  {
    id: "1",
    studentName: "John Doe",
    date: "2025-05-22",
    time: "10:00 AM",
    duration: "60 min",
    status: "scheduled",
  },
  // Add more mock data as needed
];

export function SessionScheduling() {
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [sessions] = useState<Session[]>(mockSessions);

  return (
    <div className="grid grid-cols-12 gap-6">
      <Card className="col-span-4 p-6">
        <h3 className="font-semibold mb-4">Calendar</h3>
        <Calendar
          mode="single"
          selected={date}
          onSelect={setDate}
          className="rounded-md border"
        />
        <Button className="w-full mt-4">Schedule New Session</Button>
      </Card>

      <Card className="col-span-8 p-6">
        <h3 className="font-semibold mb-4">Upcoming Sessions</h3>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Student</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Time</TableHead>
              <TableHead>Duration</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sessions.map((session) => (
              <TableRow key={session.id}>
                <TableCell>{session.studentName}</TableCell>
                <TableCell>{session.date}</TableCell>
                <TableCell>{session.time}</TableCell>
                <TableCell>{session.duration}</TableCell>
                <TableCell>
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    session.status === "scheduled" 
                      ? "bg-blue-100 text-blue-800"
                      : session.status === "completed"
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }`}>
                    {session.status}
                  </span>
                </TableCell>
                <TableCell className="text-right">
                  <Button variant="ghost" size="sm">Edit</Button>
                  <Button variant="ghost" size="sm">Cancel</Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>
    </div>
  );
}
