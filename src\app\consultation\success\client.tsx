'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { format } from 'date-fns';
import { CheckCircle, Calendar, Clock, MapPin, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { createClient } from '@/utils/supabase/client';

// Define the consultation data type for better type safety
type ConsultationData = {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  location?: string;
  scheduled_date: string;
  scheduled_time: string;
  duration: number;
  reason?: string;
  status: string;
  payment_reference?: string;
  payment_status?: string;
  payment_amount?: number;
  yoco_payment_id?: string;
  experience_level?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
};

/**
 * ConsultationSuccessClient component
 * Displays confirmation details after successful payment
 */
export default function ConsultationSuccessClient() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const reference = searchParams.get('reference');
  
  const [loading, setLoading] = useState(true);
  const [consultation, setConsultation] = useState<ConsultationData | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    // Function to fetch consultation details
    const fetchConsultationDetails = async () => {
      try {
        console.log('Starting fetchConsultationDetails with reference:', reference);
        
        // First try to get data from localStorage
        const storedData = localStorage.getItem('consultation_data');

        if (storedData) {
          try {
            const parsedData = JSON.parse(storedData);
            console.log('Retrieved consultation data from localStorage:', parsedData);

            // Transform the data to match the expected format
            const transformedData: ConsultationData = {
              id: parsedData.id || reference || 'unknown-id',
              first_name: parsedData.first_name || 'Customer',
              last_name: parsedData.last_name || '',
              email: parsedData.email || '<EMAIL>',
              phone_number: parsedData.phone_number || '',
              location: parsedData.location || 'Location TBD',
              scheduled_date: parsedData.scheduled_date || new Date().toISOString().split('T')[0],
              scheduled_time: parsedData.scheduled_time || '09:00',
              duration: parsedData.duration || 60,
              reason: parsedData.reason || 'Tennis Consultation',
              status: 'confirmed',
              payment_reference: parsedData.payment_reference || reference,
              payment_status: 'paid',
              payment_amount: parsedData.payment_amount || 3,
              yoco_payment_id: parsedData.yoco_payment_id || reference,
              experience_level: parsedData.experience_level,
              notes: parsedData.notes,
              created_at: parsedData.created_at || new Date().toISOString(),
              updated_at: new Date().toISOString()
            };

            console.log('Transformed consultation data:', transformedData);
            setConsultation(transformedData);

            // Clear localStorage after retrieving the data
            localStorage.removeItem('consultation_data');

            // Try to update the database record if it exists
            try {
              // Create Supabase client to update the status
              const supabase = createClient();

              // Update the consultation status to confirmed if we have an ID
              if (transformedData.id && !transformedData.id.startsWith('temp-')) {
                const { error: updateError } = await supabase
                  .from('consultations')
                  .update({
                    status: 'confirmed',
                    payment_status: 'paid'
                  })
                  .eq('id', transformedData.id);

                if (updateError) {
                  console.error('Error updating consultation status:', updateError);
                }
              }
            } catch (dbError) {
              console.error('Database error while updating status:', dbError);
              // Continue anyway since we have the data from localStorage
            }

            setLoading(false);
            return;
          } catch (parseError) {
            console.error('Error parsing stored consultation data:', parseError);
            // Continue to try other methods
          }
        }
        
        // If no localStorage data or parsing failed, try to fetch from database
        if (!reference) {
          console.error('No reference provided in URL parameters');
          setError('No reference provided');
          setLoading(false);
          return;
        }
        
        console.log('Attempting to fetch consultation with reference:', reference);
        
        try {
          const supabase = createClient();
          
          // First try to fetch by payment reference
          console.log('Trying to fetch by payment_reference');
          const firstAttempt = await supabase
            .from('consultations')
            .select('*')
            .eq('payment_reference', reference)
            .single();
          
          if (!firstAttempt.error && firstAttempt.data) {
            console.log('Found consultation by payment_reference:', firstAttempt.data);
            setConsultation(firstAttempt.data);
          } else {
            // If not found by payment_reference, try by yoco_payment_id
            console.log('Not found by payment_reference, trying yoco_payment_id');
            const secondAttempt = await supabase
              .from('consultations')
              .select('*')
              .eq('yoco_payment_id', reference)
              .single();
              
            if (!secondAttempt.error && secondAttempt.data) {
              console.log('Found consultation by yoco_payment_id:', secondAttempt.data);
              setConsultation(secondAttempt.data);
            } else {
              console.error('Could not find consultation in database:', 
                firstAttempt.error || secondAttempt.error);
              
              // Create fallback data
              createFallbackData();
            }
          }
        } catch (dbError: any) {
          console.error('Database error:', dbError);
          // Create fallback data
          createFallbackData();
        }
      } catch (error: any) {
        console.error('Error fetching consultation details:', error);
        setError('An unexpected error occurred: ' + error.message);
        setLoading(false);
      }
    };
    
    // Function to create fallback data when database lookup fails
    const createFallbackData = () => {
      console.log('Creating fallback consultation data');
      
      // Extract reference details to create fallback data
      const refMatch = reference?.match(/CONS-(\d+)-(\w+)/);
      const refTimestamp = refMatch ? new Date(parseInt(refMatch[1])) : new Date();
      
      // Get URL params for more information
      const urlParams = new URLSearchParams(window.location.search);
      const amount = urlParams.get('amount');
      const paymentId = urlParams.get('paymentId');
      
      const fallbackData: ConsultationData = {
        id: reference || 'unknown-id',
        first_name: 'Customer',
        last_name: '',
        email: '<EMAIL>',
        phone_number: '',
        location: 'Location TBD',
        scheduled_date: refTimestamp.toISOString().split('T')[0],
        scheduled_time: '09:00',
        duration: 60,
        reason: 'Tennis Consultation',
        status: 'confirmed',
        payment_reference: reference || undefined,
        payment_status: 'paid',
        payment_amount: amount ? parseInt(amount) : 3, // Fixed: R3.00
        yoco_payment_id: paymentId || reference || undefined,
        created_at: refTimestamp.toISOString(),
        updated_at: new Date().toISOString()
      };
      
      console.log('Created fallback data:', fallbackData);
      setConsultation(fallbackData);
      setLoading(false);
    };
    
    // Execute the fetch function
    fetchConsultationDetails();
  }, [reference]);
  
  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'EEEE, MMMM d, yyyy');
    } catch (e) {
      console.error('Error formatting date:', e);
      return dateString;
    }
  };
  
  // Format time for display
  const formatTime = (timeString: string) => {
    try {
      const [hours, minutes] = timeString.split(':');
      const date = new Date();
      date.setHours(parseInt(hours, 10));
      date.setMinutes(parseInt(minutes, 10));
      return format(date, 'h:mm a');
    } catch (e) {
      console.error('Error formatting time:', e);
      return timeString;
    }
  };
  
  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-lg font-medium">Loading your booking details...</p>
        </div>
      </div>
    );
  }
  
  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="max-w-md w-full border-red-200 shadow-lg">
          <CardHeader className="text-center border-b border-border pb-6">
            <div className="flex justify-center mb-4">
              <svg className="h-16 w-16 text-red-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
              </svg>
            </div>
            <CardTitle className="text-2xl font-bold">Something went wrong</CardTitle>
            <CardDescription className="mt-2 text-red-600">{error}</CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground mb-4">
              We couldn't retrieve your booking details. Please contact our support team for assistance.
            </p>
          </CardContent>
          <CardFooter className="flex justify-center pt-6">
            <Button 
              onClick={() => router.push('/')}
              className="w-full"
            >
              Return to Home
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }
  
  // No consultation data
  if (!consultation) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="max-w-md w-full border-yellow-200 shadow-lg">
          <CardHeader className="text-center border-b border-border pb-6">
            <div className="flex justify-center mb-4">
              <svg className="h-16 w-16 text-yellow-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
              </svg>
            </div>
            <CardTitle className="text-2xl font-bold">Booking Not Found</CardTitle>
            <CardDescription className="mt-2">We couldn't find your consultation booking details</CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground mb-4">
              If you've just completed a booking, please try refreshing the page or contact our support team.
            </p>
          </CardContent>
          <CardFooter className="flex justify-center pt-6">
            <Button 
              onClick={() => router.push('/consultation/booking')}
              className="w-full"
            >
              Book a Consultation
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Success state with consultation data
  return (
    <div className="min-h-screen bg-background py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        <Card className="border border-green-200 shadow-lg backdrop-blur-sm bg-black/5">
          <CardHeader className="text-center border-b border-border pb-6">
            <div className="flex justify-center mb-4">
              <CheckCircle className="h-16 w-16 text-green-500" />
            </div>
            <CardTitle className="text-2xl font-bold">Booking Confirmed!</CardTitle>
            <CardDescription className="mt-2">
              Your tennis consultation has been successfully booked and confirmed
            </CardDescription>
          </CardHeader>
          
          <CardContent className="pt-6">
            <div className="space-y-6">
              {/* Booking Summary Banner */}
              <div className="bg-gradient-to-r from-blue-50 to-green-50 border border-blue-100 p-4 rounded-lg shadow-sm">
                <h3 className="font-semibold text-blue-800 text-lg mb-2">Booking Summary</h3>
                <p className="text-blue-700 text-sm">
                  Your consultation with our tennis expert has been scheduled for:
                </p>
                
                <div className="mt-3 grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 mr-2 text-blue-600" />
                    <div>
                      <p className="text-sm font-medium text-blue-800">
                        {formatDate(consultation.scheduled_date)}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <Clock className="h-5 w-5 mr-2 text-blue-600" />
                    <div>
                      <p className="text-sm font-medium text-blue-800">
                        {formatTime(consultation.scheduled_time)}
                      </p>
                      <p className="text-xs text-blue-600">
                        {consultation.duration} minutes
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <MapPin className="h-5 w-5 mr-2 text-blue-600" />
                    <div>
                      <p className="text-sm font-medium text-blue-800">
                        {consultation.location || 'Location TBD'}
                      </p>
                      <p className="text-xs text-blue-600">
                        Consultation Location
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Personal Information */}
              <div className="space-y-4">
                <h3 className="font-semibold text-lg flex items-center">
                  <svg className="h-5 w-5 mr-2 text-primary" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>
                  Your Information
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-muted/50 p-4 rounded-lg border border-border/50 hover:border-primary/20 transition-colors">
                    <p className="text-sm text-muted-foreground">Name</p>
                    <p className="font-medium">{consultation.first_name} {consultation.last_name}</p>
                  </div>
                  
                  <div className="bg-muted/50 p-4 rounded-lg border border-border/50 hover:border-primary/20 transition-colors">
                    <p className="text-sm text-muted-foreground">Email</p>
                    <p className="font-medium">{consultation.email}</p>
                  </div>
                  
                  {consultation.phone_number && (
                    <div className="bg-muted/50 p-4 rounded-lg border border-border/50 hover:border-primary/20 transition-colors">
                      <p className="text-sm text-muted-foreground">Phone</p>
                      <p className="font-medium">{consultation.phone_number}</p>
                    </div>
                  )}
                  
                  <div className="bg-muted/50 p-4 rounded-lg border border-border/50 hover:border-primary/20 transition-colors">
                    <p className="text-sm text-muted-foreground">Consultation Reason</p>
                    <p className="font-medium">{consultation.reason || 'Tennis Consultation'}</p>
                  </div>
                  
                  <div className="bg-muted/50 p-4 rounded-lg border border-border/50 hover:border-primary/20 transition-colors">
                    <p className="text-sm text-muted-foreground">Experience Level</p>
                    <p className="font-medium">{consultation.experience_level || 'Not specified'}</p>
                  </div>
                </div>
              </div>
              
              {/* Payment details */}
              <div className="space-y-4">
                <h3 className="font-semibold text-lg flex items-center">
                  <svg className="h-5 w-5 mr-2 text-primary" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect width="20" height="14" x="2" y="5" rx="2"/>
                    <line x1="2" x2="22" y1="10" y2="10"/>
                  </svg>
                  Payment Information
                </h3>
                
                <div className="bg-gradient-to-r from-green-50 to-green-100 border border-green-200 p-4 rounded-lg shadow-sm">
                  <div className="flex items-center mb-3">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                    <h4 className="font-medium text-green-800">Payment Successful</h4>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                    <div className="bg-white/50 p-3 rounded border border-green-200">
                      <p className="text-sm text-green-700">Reference ID</p>
                      <p className="font-medium text-green-900">{reference}</p>
                    </div>
                    
                    <div className="bg-white/50 p-3 rounded border border-green-200">
                      <p className="text-sm text-green-700">Amount</p>
                      <p className="font-medium text-green-900">
                        R{consultation.payment_amount ? consultation.payment_amount.toFixed(2) : '3.00'} <span className="text-xs">ZAR</span>
                      </p>
                    </div>
                  </div>
                  
                  <p className="text-green-700 text-sm mt-3">
                    A receipt has been sent to your email address.
                  </p>
                </div>
              </div>
              
              {/* Notes */}
              {consultation.notes && (
                <div className="space-y-4">
                  <h3 className="font-semibold text-lg flex items-center">
                    <svg className="h-5 w-5 mr-2 text-primary" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M14 3v4a1 1 0 0 0 1 1h4"/>
                      <path d="M17 21H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7l5 5v11a2 2 0 0 1-2 2z"/>
                      <line x1="9" y1="9" x2="10" y2="9"/>
                      <line x1="9" y1="13" x2="15" y2="13"/>
                      <line x1="9" y1="17" x2="15" y2="17"/>
                    </svg>
                    Your Notes
                  </h3>
                  <div className="bg-muted/50 p-4 rounded-lg border border-border/50">
                    <p className="text-sm">{consultation.notes}</p>
                  </div>
                </div>
              )}
              
              {/* What's next */}
              <div className="space-y-4">
                <h3 className="font-semibold text-lg flex items-center">
                  <svg className="h-5 w-5 mr-2 text-primary" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"/>
                    <path d="m9 12 2 2 4-4"/>
                  </svg>
                  What's Next?
                </h3>
                
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 p-5 rounded-lg shadow-sm">
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <div className="bg-blue-100 rounded-full p-1 mr-3 mt-0.5">
                        <svg className="h-4 w-4 text-blue-700" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <rect width="18" height="18" x="3" y="4" rx="2" ry="2"/>
                          <line x1="16" x2="16" y1="2" y2="6"/>
                          <line x1="8" x2="8" y1="2" y2="6"/>
                          <line x1="3" x2="21" y1="10" y2="10"/>
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-medium text-blue-800">Confirmation Email</h4>
                        <p className="text-blue-700 text-sm">You'll receive a detailed confirmation email with all booking information.</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="bg-blue-100 rounded-full p-1 mr-3 mt-0.5">
                        <svg className="h-4 w-4 text-blue-700" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-medium text-blue-800">Coach Contact</h4>
                        <p className="text-blue-700 text-sm">Our coach will contact you 24-48 hours before your session to confirm details.</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="bg-blue-100 rounded-full p-1 mr-3 mt-0.5">
                        <svg className="h-4 w-4 text-blue-700" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="10"/>
                          <polyline points="12 6 12 12 16 14"/>
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-medium text-blue-800">Arrival Time</h4>
                        <p className="text-blue-700 text-sm">Please arrive 15 minutes before your scheduled time and bring appropriate tennis gear.</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
          
          <CardFooter className="flex flex-col sm:flex-row gap-4 pt-6">
            <Button 
              variant="outline" 
              className="w-full sm:w-auto"
              onClick={() => router.push('/')}
            >
              Return to Home
            </Button>
            
            <Button 
              className="w-full sm:w-auto"
              onClick={() => router.push('/shop')}
            >
              Browse Tennis Gear
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
