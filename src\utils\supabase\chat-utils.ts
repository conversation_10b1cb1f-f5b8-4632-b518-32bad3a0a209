import { createClient } from './client';
import { Tables, InsertTables, UpdateTables } from './database.types';
import { v4 as uuidv4 } from 'uuid';

/**
 * Chat utilities for Tennis-Gear application
 * These functions handle real-time messaging between mentors and students
 */

/**
 * Get all conversations for a user
 * @param userId User ID
 * @returns List of conversations or null if error
 */
export async function getConversations(userId: string) {
  const supabase = createClient();
  
  // First, get all unique conversation IDs for this user
  const { data: conversationData, error: convError } = await supabase
    .from('messages')
    .select('conversation_id')
    .or(`sender_id.eq.${userId},receiver_id.eq.${userId}`)
    .order('created_at', { ascending: false });
  
  if (convError || !conversationData) {
    console.error('Error fetching conversations:', convError);
    return null;
  }
  
  // Get unique conversation IDs
  const uniqueConversationIds = Array.from(new Set(conversationData.map(item => item.conversation_id)));
  
  // For each conversation, get the other user involved
  const conversations: Array<{
    id: string;
    user1_id: string;
    user2_id: string;
  }> = [];
  
  for (const conversationId of uniqueConversationIds) {
    // Get the first message to identify the users involved
    const { data: messageData, error: msgError } = await supabase
      .from('messages')
      .select('sender_id, receiver_id')
      .eq('conversation_id', conversationId)
      .limit(1)
      .single();
    
    if (msgError || !messageData) {
      console.error('Error fetching message data:', msgError);
      continue;
    }
    
    // Determine the other user ID
    const otherUserId = messageData.sender_id === userId ? messageData.receiver_id : messageData.sender_id;
    
    // Add to conversations array
    conversations.push({
      id: conversationId,
      user1_id: messageData.sender_id,
      user2_id: messageData.receiver_id
    });
  }
  
  return conversations;
}

/**
 * Get messages for a conversation
 * @param conversationId Conversation ID
 * @param limit Number of messages to fetch
 * @param offset Offset for pagination
 * @returns List of messages or null if error
 */
export async function getMessages(
  conversationId: string,
  limit: number = 50,
  offset: number = 0
) {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('messages')
    .select('*')
    .eq('conversation_id', conversationId)
    .order('created_at', { ascending: true })
    .range(offset, offset + limit - 1);
  
  if (error || !data) {
    console.error('Error fetching messages:', error);
    return null;
  }
  
  return data;
}

/**
 * Get or create a conversation between two users
 * @param userId1 First user ID
 * @param userId2 Second user ID
 * @returns Conversation ID or null if error
 */
export async function getOrCreateConversation(userId1: string, userId2: string) {
  const supabase = createClient();
  
  // Check if a conversation already exists
  const { data, error } = await supabase
    .from('messages')
    .select('conversation_id')
    .or(`and(sender_id.eq.${userId1},receiver_id.eq.${userId2}),and(sender_id.eq.${userId2},receiver_id.eq.${userId1})`)
    .limit(1);
  
  if (error) {
    console.error('Error checking for existing conversation:', error);
    return null;
  }
  
  // If a conversation exists, return its ID
  if (data && data.length > 0) {
    return data[0].conversation_id;
  }
  
  // Otherwise, generate a new conversation ID
  return uuidv4();
}

/**
 * Send a message
 * @param message Message data
 * @returns Created message or null if error
 */
export async function sendMessage(message: {
  conversationId: string;
  senderId: string;
  receiverId: string;
  content: string;
  attachmentUrl?: string;
}) {
  const supabase = createClient();
  
  const messageData: InsertTables<'messages'> = {
    id: uuidv4(),
    conversation_id: message.conversationId,
    sender_id: message.senderId,
    receiver_id: message.receiverId,
    content: message.content,
    attachment_url: message.attachmentUrl,
    read: false,
    created_at: new Date().toISOString()
  };
  
  const { data, error } = await supabase
    .from('messages')
    .insert(messageData)
    .select()
    .single();
  
  if (error) {
    console.error('Error sending message:', error);
    return null;
  }
  
  return data;
}

/**
 * Upload a message attachment
 * @param file File to upload
 * @param senderId Sender ID
 * @returns Public URL of the uploaded file or null if error
 */
export async function uploadMessageAttachment(file: File, senderId: string) {
  const supabase = createClient();
  
  try {
    // Generate a unique file path
    const fileExt = file.name.split('.').pop();
    const fileName = `${uuidv4()}.${fileExt}`;
    const filePath = `${senderId}/${fileName}`;
    
    // Upload file to storage
    const { data, error } = await supabase
      .storage
      .from('message-attachments')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });
    
    if (error) {
      throw error;
    }
    
    // Get the public URL
    const { data: { publicUrl } } = supabase
      .storage
      .from('message-attachments')
      .getPublicUrl(filePath);
    
    return publicUrl;
  } catch (error) {
    console.error('Error uploading attachment:', error);
    return null;
  }
}

/**
 * Mark messages as read
 * @param conversationId Conversation ID
 * @param userId User ID of the reader
 * @returns Success boolean
 */
export async function markMessagesAsRead(conversationId: string, userId: string) {
  const supabase = createClient();
  
  const updateData: UpdateTables<'messages'> = { read: true };
  
  const { error } = await supabase
    .from('messages')
    .update(updateData)
    .eq('conversation_id', conversationId)
    .eq('receiver_id', userId)
    .eq('read', false);
  
  if (error) {
    console.error('Error marking messages as read:', error);
    return false;
  }
  
  return true;
}

/**
 * Get unread message count for a user
 * @param userId User ID
 * @returns Count of unread messages or 0 if error
 */
export async function getUnreadMessageCount(userId: string) {
  const supabase = createClient();
  
  const { data, error, count } = await supabase
    .from('messages')
    .select('id', { count: 'exact' })
    .eq('receiver_id', userId)
    .eq('read', false);
  
  if (error) {
    console.error('Error getting unread message count:', error);
    return 0;
  }
  
  return count || 0;
}

/**
 * Delete a message
 * @param messageId Message ID
 * @param userId User ID (for verification)
 * @returns Success or error
 */
export async function deleteMessage(messageId: string, userId: string) {
  const supabase = createClient();
  
  // First, check if the user is the sender of the message
  const { data, error } = await supabase
    .from('messages')
    .select('*')
    .eq('id', messageId)
    .eq('sender_id', userId)
    .single();
  
  if (error || !data) {
    return { success: false, error: 'Message not found or you are not authorized to delete it' };
  }
  
  // Delete the message
  const { error: deleteError } = await supabase
    .from('messages')
    .delete()
    .eq('id', messageId);
  
  if (deleteError) {
    return { success: false, error: deleteError.message };
  }
  
  return { success: true, error: null };
}

/**
 * Subscribe to new messages in a conversation
 * @param conversationId Conversation ID
 * @param callback Function to call when a new message is received
 * @returns Subscription object
 */
export function subscribeToConversation(conversationId: string, callback: (payload: any) => void) {
  const supabase = createClient();
  
  return supabase
    .channel(`messages:conversation_id=eq.${conversationId}`)
    .on('postgres_changes', {
      event: 'INSERT',
      schema: 'public',
      table: 'messages',
      filter: `conversation_id=eq.${conversationId}`
    }, callback)
    .subscribe();
}

/**
 * Subscribe to all new messages for a user
 * @param userId User ID
 * @param callback Function to call when a new message is received
 * @returns Subscription object
 */
export function subscribeToUserMessages(userId: string, callback: (payload: any) => void) {
  const supabase = createClient();
  
  return supabase
    .channel(`messages:receiver_id=eq.${userId}`)
    .on('postgres_changes', {
      event: 'INSERT',
      schema: 'public',
      table: 'messages',
      filter: `receiver_id=eq.${userId}`
    }, callback)
    .subscribe();
}
