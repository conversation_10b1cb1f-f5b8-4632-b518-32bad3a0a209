import React, { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { Search, X, Clock, TrendingUp, Hash, ArrowRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { NeomorphismCard, NeomorphismInput, NeomorphismBadge } from './neomorphism-card';

interface SearchResult {
  id: string;
  title: string;
  type: 'article' | 'product' | 'prayer' | 'user';
  description: string;
  url: string;
  category?: string;
}

interface SearchModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSearch?: (query: string) => Promise<SearchResult[]>;
}

const mockRecentSearches = [
  'Digital products',
  'Prayer room',
  'User management',
  'Privacy settings',
];

const mockTrendingSearches = [
  'E-books',
  'Daily prayers',
  'Admin dashboard',
  'Payment methods',
];

const mockQuickActions = [
  { label: 'View Products', url: '/products', icon: '🛍️' },
  { label: 'Prayer Room', url: '/dashboard/prayers', icon: '🙏' },
  { label: 'My Orders', url: '/dashboard/orders', icon: '📦' },
  { label: 'Help Center', url: '/dashboard/help', icon: '❓' },
];

export function SearchModal({ isOpen, onClose, onSearch }: SearchModalProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
      setTimeout(() => inputRef.current?.focus(), 100);
    } else {
      document.body.style.overflow = 'unset';
      setQuery('');
      setResults([]);
      setSelectedIndex(-1);
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev < results.length - 1 ? prev + 1 : prev
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
          break;
        case 'Enter':
          e.preventDefault();
          if (selectedIndex >= 0 && results[selectedIndex]) {
            window.location.href = results[selectedIndex].url;
            onClose();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, results, selectedIndex, onClose]);

  useEffect(() => {
    if (query.trim() && onSearch) {
      setIsLoading(true);
      const searchTimeout = setTimeout(async () => {
        try {
          const searchResults = await onSearch(query);
          setResults(searchResults);
        } catch (error) {
          console.error('Search error:', error);
          setResults([]);
        } finally {
          setIsLoading(false);
        }
      }, 300);

      return () => clearTimeout(searchTimeout);
    } else {
      setResults([]);
      setIsLoading(false);
    }
  }, [query, onSearch]);

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'article': return '📄';
      case 'product': return '🛍️';
      case 'prayer': return '🙏';
      case 'user': return '👤';
      default: return '🔍';
    }
  };

  if (!isOpen) return null;

  return createPortal(
    <div
      className="fixed inset-0 z-50 flex items-start justify-center pt-[10vh] px-4"
      onClick={handleBackdropClick}
    >
      {/* Backdrop with blur effect */}
      <div className="absolute inset-0 bg-black/20 backdrop-blur-sm" />
      
      {/* Search Modal */}
      <NeomorphismCard
        ref={modalRef}
        className="relative w-full max-w-2xl max-h-[80vh] overflow-hidden bg-gray-50"
        variant="elevated"
        size="lg"
      >
        {/* Header */}
        <div className="flex items-center gap-3 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              ref={inputRef}
              type="text"
              placeholder="Search articles, products, prayers..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-gray-100 border-none outline-none rounded-xl text-gray-700 placeholder-gray-500 shadow-[inset_6px_6px_12px_#d1d9e6,inset_-6px_-6px_12px_#ffffff] focus:shadow-[inset_8px_8px_16px_#d1d9e6,inset_-8px_-8px_16px_#ffffff] transition-all duration-200"
            />
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg text-gray-500 hover:text-gray-700 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="max-h-[60vh] overflow-y-auto">
          {query.trim() ? (
            // Search Results
            <div>
              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : results.length > 0 ? (
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-500 mb-3">
                    Search Results ({results.length})
                  </h3>
                  {results.map((result, index) => (
                    <NeomorphismCard
                      key={result.id}
                      className={cn(
                        'p-3 cursor-pointer transition-all duration-200',
                        selectedIndex === index && 'ring-2 ring-blue-500'
                      )}
                      variant="flat"
                      interactive
                      onClick={() => {
                        window.location.href = result.url;
                        onClose();
                      }}
                    >
                      <div className="flex items-start gap-3">
                        <span className="text-lg">{getTypeIcon(result.type)}</span>
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-gray-900 truncate">
                            {result.title}
                          </h4>
                          <p className="text-sm text-gray-600 line-clamp-2 mt-1">
                            {result.description}
                          </p>
                          <div className="flex items-center gap-2 mt-2">
                            <NeomorphismBadge variant="info">
                              {result.type}
                            </NeomorphismBadge>
                            {result.category && (
                              <NeomorphismBadge variant="default">
                                {result.category}
                              </NeomorphismBadge>
                            )}
                          </div>
                        </div>
                        <ArrowRight className="h-4 w-4 text-gray-400" />
                      </div>
                    </NeomorphismCard>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No results found
                  </h3>
                  <p className="text-gray-500">
                    Try adjusting your search terms or browse our categories.
                  </p>
                </div>
              )}
            </div>
          ) : (
            // Default Content
            <div className="space-y-6">
              {/* Quick Actions */}
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-3 flex items-center gap-2">
                  <Hash className="h-4 w-4" />
                  Quick Actions
                </h3>
                <div className="grid grid-cols-2 gap-2">
                  {mockQuickActions.map((action) => (
                    <NeomorphismCard
                      key={action.label}
                      className="p-3 cursor-pointer text-center"
                      variant="flat"
                      interactive
                      onClick={() => {
                        window.location.href = action.url;
                        onClose();
                      }}
                    >
                      <div className="text-lg mb-1">{action.icon}</div>
                      <div className="text-sm font-medium text-gray-700">
                        {action.label}
                      </div>
                    </NeomorphismCard>
                  ))}
                </div>
              </div>

              {/* Recent Searches */}
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-3 flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Recent Searches
                </h3>
                <div className="flex flex-wrap gap-2">
                  {mockRecentSearches.map((search) => (
                    <NeomorphismBadge
                      key={search}
                      className="cursor-pointer hover:bg-gray-200 transition-colors"
                      onClick={() => setQuery(search)}
                    >
                      {search}
                    </NeomorphismBadge>
                  ))}
                </div>
              </div>

              {/* Trending Searches */}
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-3 flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Trending
                </h3>
                <div className="flex flex-wrap gap-2">
                  {mockTrendingSearches.map((search) => (
                    <NeomorphismBadge
                      key={search}
                      variant="info"
                      className="cursor-pointer hover:bg-blue-200 transition-colors"
                      onClick={() => setQuery(search)}
                    >
                      {search}
                    </NeomorphismBadge>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center gap-4">
              <span>↑↓ Navigate</span>
              <span>↵ Select</span>
              <span>Esc Close</span>
            </div>
            <span>Powered by Search</span>
          </div>
        </div>
      </NeomorphismCard>
    </div>,
    document.body
  );
}
