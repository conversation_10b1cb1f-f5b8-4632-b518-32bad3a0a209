# Complete Guide: Fixing "Database error saving new user" in Supabase Authentication

This comprehensive guide provides step-by-step instructions for software developers and AI agents to permanently resolve the "Database error saving new user" issue in Supabase. This error typically occurs when setting up automatic user profile creation triggers between `auth.users` and custom `public.users` tables.

## Prerequisites
- Supabase project with admin access
- SQL Editor access in Supabase Dashboard
- Understanding of PostgreSQL triggers and RLS policies

---

## Step 1: Complete Database Schema Reset (If Needed)

### 1.1 Clean Up Existing Data
First, remove any conflicting data that might cause unique constraint violations:

```sql
-- Check for duplicate entries that cause conflicts
SELECT email, COUNT(*) as count 
FROM public.users 
GROUP BY email 
HAVING COUNT(*) > 1;

-- Clean up duplicates (adjust WHERE clause as needed)
DELETE FROM public.users WHERE email = '<EMAIL>';

-- Clean up orphaned auth.users if necessary (use with caution)
-- DELETE FROM auth.users WHERE email = '<EMAIL>';
```

### 1.2 Drop and Recreate the Users Table
```sql
-- Drop existing table and dependencies
DROP TABLE IF EXISTS public.users CASCADE;

-- Create properly structured users table
CREATE TABLE public.users (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  name TEXT,
  role TEXT NOT NULL DEFAULT 'user' CHECK (role IN ('user', 'admin', 'mentor')),
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

---

## Step 2: Create Robust Trigger Function with Error Handling

### 2.1 Create the Trigger Function
```sql
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Only proceed if this is a real user signup (has email)
  IF NEW.email IS NULL OR NEW.email = '' THEN
    RETURN NEW;
  END IF;
  
  -- Insert new user profile with data from auth.users
  INSERT INTO public.users (id, email, full_name, name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'role', 'user')
  )
  ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = COALESCE(EXCLUDED.full_name, public.users.full_name),
    name = COALESCE(EXCLUDED.name, public.users.name),
    role = COALESCE(EXCLUDED.role, public.users.role),
    updated_at = NOW();
  
  RETURN NEW;
  
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the auth.users insertion
    RAISE WARNING 'Failed to create user profile for %: % (SQLSTATE: %)', NEW.id, SQLERRM, SQLSTATE;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 2.2 Create the Trigger
```sql
-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Create new trigger
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
```

---

## Step 3: Configure Row Level Security (RLS) Policies

### 3.1 Enable RLS and Create Policies
```sql
-- Enable RLS on users table
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Admins can view all users" ON public.users;
DROP POLICY IF EXISTS "Admins can manage all users" ON public.users;

-- Create comprehensive RLS policies
CREATE POLICY "Users can view own profile"
  ON public.users
  FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "Users can update own profile"
  ON public.users
  FOR UPDATE
  USING (auth.uid() = id);

CREATE POLICY "Admins can view all users"
  ON public.users
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Admins can manage all users"
  ON public.users
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND role = 'admin'
    )
  );
```

### 3.2 Grant Necessary Permissions
```sql
-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON public.users TO authenticated;
GRANT SELECT ON public.users TO anon;

-- Grant usage on sequences if any
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
```

---

## Step 4: Test the Complete Setup

### 4.1 Test User Registration
```javascript
// Test regular user signup
const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'SecurePassword123',
  options: {
    data: {
      full_name: 'Test User',
      role: 'user'
    }
  }
});

// Test admin user signup
const { data: adminData, error: adminError } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'SecurePassword123',
  options: {
    data: {
      full_name: 'Admin User',
      role: 'admin'
    }
  }
});
```

### 4.2 Verify Database State
```sql
-- Check that users were created in both tables
SELECT 
  au.id,
  au.email as auth_email,
  au.created_at as auth_created,
  pu.email as profile_email,
  pu.role,
  pu.created_at as profile_created
FROM auth.users au
LEFT JOIN public.users pu ON au.id = pu.id
WHERE au.email IN ('<EMAIL>', '<EMAIL>');
```

---

## Step 5: Troubleshooting Common Issues

### 5.1 Check for Trigger Execution
```sql
-- Verify trigger exists and is active
SELECT trigger_name, event_manipulation, action_timing
FROM information_schema.triggers
WHERE trigger_schema = 'auth' AND trigger_name = 'on_auth_user_created';
```

### 5.2 Check Function Permissions
```sql
-- Verify function exists with correct permissions
SELECT routine_name, routine_type, security_type
FROM information_schema.routines
WHERE routine_schema = 'public' AND routine_name = 'handle_new_user';
```

### 5.3 Monitor for Errors
```sql
-- Check PostgreSQL logs for any trigger-related errors
-- (This requires appropriate log access in your Supabase project)
```

---

## Step 6: Production Considerations

### 6.1 Backup Strategy
- Always backup your database before making schema changes
- Test the complete flow in a staging environment first

### 6.2 Performance Optimization
- Consider adding indexes on frequently queried columns
- Monitor trigger performance with large user volumes

### 6.3 Security Best Practices
- Regularly audit RLS policies
- Use least-privilege principle for role assignments
- Implement proper email verification flows

---

## Expected Outcomes

After completing this guide, you should have:
- ✅ A robust user profile creation system that never fails auth signup
- ✅ Proper RLS policies protecting user data
- ✅ Error handling that prevents authentication failures
- ✅ A scalable foundation for user management
- ✅ Complete elimination of "Database error saving new user" issues

## Additional Resources
- Reference video: https://www.youtube.com/watch?v=mcrqn77lUmM
- Supabase Auth Documentation: https://supabase.com/docs/guides/auth
- PostgreSQL Trigger Documentation: https://www.postgresql.org/docs/current/sql-createtrigger.html

> ✅ This comprehensive approach ensures permanent resolution of Supabase authentication issues while maintaining data integrity and security.
