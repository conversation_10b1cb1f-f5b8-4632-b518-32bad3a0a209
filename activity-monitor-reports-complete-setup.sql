-- ✅ Complete Activity Monitor & Reports System Setup
-- Run this script in your Supabase SQL Editor to ensure all functions and data are properly set up

-- 1. Ensure admin_activity_logs table exists with proper structure
CREATE TABLE IF NOT EXISTS public.admin_activity_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    admin_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    action_type TEXT NOT NULL, -- 'order_management', 'product_management', 'user_management', 'system_config'
    action_description TEXT NOT NULL,
    target_id UUID, -- ID of the affected resource (order_id, product_id, user_id, etc.)
    target_type TEXT, -- 'order', 'product', 'user', 'system'
    metadata JSONB, -- Additional action details
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN NOT NULL DEFAULT true,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Ensure admin_reports table exists with proper structure
CREATE TABLE IF NOT EXISTS public.admin_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    generated_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    report_type TEXT NOT NULL, -- 'activity_summary', 'performance_metrics', 'audit_trail'
    report_name TEXT NOT NULL,
    filters JSONB, -- Date ranges, admin roles, activity types
    file_path TEXT, -- Path to generated report file
    file_format TEXT NOT NULL, -- 'pdf', 'excel'
    download_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE
);

-- 3. Enable RLS on both tables
ALTER TABLE public.admin_activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.admin_reports ENABLE ROW LEVEL SECURITY;

-- 4. Create RLS policies for admin_activity_logs
DROP POLICY IF EXISTS "Admins can view all activity logs" ON public.admin_activity_logs;
CREATE POLICY "Admins can view all activity logs"
    ON public.admin_activity_logs
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid()
            AND role = 'admin'
        )
    );

DROP POLICY IF EXISTS "All admin types can insert their own activity logs" ON public.admin_activity_logs;
CREATE POLICY "All admin types can insert their own activity logs"
    ON public.admin_activity_logs
    FOR INSERT
    WITH CHECK (
        admin_id = auth.uid() AND
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid()
            AND role = 'admin'
        )
    );

-- 5. Create RLS policies for admin_reports
DROP POLICY IF EXISTS "Admins can manage reports" ON public.admin_reports;
CREATE POLICY "Admins can manage reports"
    ON public.admin_reports
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid()
            AND role = 'admin'
        )
    );

-- 6. Create function to get activity summary metrics
CREATE OR REPLACE FUNCTION get_activity_summary_metrics()
RETURNS TABLE (
    total_activities BIGINT,
    success_rate NUMERIC,
    activities_today BIGINT,
    activities_this_week BIGINT,
    most_active_admin TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH activity_stats AS (
        SELECT 
            COUNT(*) as total_count,
            COUNT(*) FILTER (WHERE success = true) as success_count,
            COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE) as today_count,
            COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '7 days') as week_count
        FROM admin_activity_logs
    ),
    most_active AS (
        SELECT 
            u.full_name
        FROM admin_activity_logs a
        JOIN users u ON a.admin_id = u.id
        GROUP BY u.id, u.full_name
        ORDER BY COUNT(*) DESC
        LIMIT 1
    )
    SELECT 
        s.total_count,
        CASE 
            WHEN s.total_count > 0 THEN ROUND((s.success_count::NUMERIC / s.total_count::NUMERIC) * 100, 2)
            ELSE 0
        END,
        s.today_count,
        s.week_count,
        COALESCE(m.full_name, 'No activity')
    FROM activity_stats s
    CROSS JOIN most_active m;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Create function to get admin performance metrics
CREATE OR REPLACE FUNCTION get_admin_performance_metrics()
RETURNS TABLE (
    admin_id UUID,
    admin_name TEXT,
    admin_email TEXT,
    admin_role TEXT,
    total_actions BIGINT,
    successful_actions BIGINT,
    failed_actions BIGINT,
    success_rate NUMERIC,
    last_activity TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id,
        u.full_name,
        u.email,
        u.admin_role::TEXT,
        COUNT(a.id) as total_actions,
        COUNT(a.id) FILTER (WHERE a.success = true) as successful_actions,
        COUNT(a.id) FILTER (WHERE a.success = false) as failed_actions,
        CASE 
            WHEN COUNT(a.id) > 0 THEN ROUND((COUNT(a.id) FILTER (WHERE a.success = true)::NUMERIC / COUNT(a.id)::NUMERIC) * 100, 2)
            ELSE 0
        END as success_rate,
        MAX(a.created_at) as last_activity
    FROM users u
    LEFT JOIN admin_activity_logs a ON u.id = a.admin_id
    WHERE u.role = 'admin'
    GROUP BY u.id, u.full_name, u.email, u.admin_role
    ORDER BY total_actions DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Create function to get audit trail metrics
CREATE OR REPLACE FUNCTION get_audit_trail_metrics()
RETURNS TABLE (
    total_failures BIGINT,
    failure_rate NUMERIC,
    most_common_error TEXT,
    failures_today BIGINT,
    failures_this_week BIGINT
) AS $$
BEGIN
    RETURN QUERY
    WITH failure_stats AS (
        SELECT 
            COUNT(*) FILTER (WHERE success = false) as total_fail_count,
            COUNT(*) as total_count,
            COUNT(*) FILTER (WHERE success = false AND created_at >= CURRENT_DATE) as today_fail_count,
            COUNT(*) FILTER (WHERE success = false AND created_at >= CURRENT_DATE - INTERVAL '7 days') as week_fail_count
        FROM admin_activity_logs
    ),
    common_error AS (
        SELECT 
            COALESCE(error_message, 'Unknown error') as error_msg
        FROM admin_activity_logs
        WHERE success = false AND error_message IS NOT NULL
        GROUP BY error_message
        ORDER BY COUNT(*) DESC
        LIMIT 1
    )
    SELECT 
        s.total_fail_count,
        CASE 
            WHEN s.total_count > 0 THEN ROUND((s.total_fail_count::NUMERIC / s.total_count::NUMERIC) * 100, 2)
            ELSE 0
        END,
        COALESCE(e.error_msg, 'No failures recorded'),
        s.today_fail_count,
        s.week_fail_count
    FROM failure_stats s
    CROSS JOIN common_error e;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. Create function to log admin activities
CREATE OR REPLACE FUNCTION log_admin_activity(
    p_admin_id UUID,
    p_action_type TEXT,
    p_action_description TEXT,
    p_target_id UUID DEFAULT NULL,
    p_target_type TEXT DEFAULT NULL,
    p_metadata JSONB DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_success BOOLEAN DEFAULT TRUE,
    p_error_message TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    activity_id UUID;
BEGIN
    INSERT INTO admin_activity_logs (
        admin_id,
        action_type,
        action_description,
        target_id,
        target_type,
        metadata,
        ip_address,
        user_agent,
        success,
        error_message,
        created_at
    ) VALUES (
        p_admin_id,
        p_action_type,
        p_action_description,
        p_target_id,
        p_target_type,
        p_metadata,
        p_ip_address,
        p_user_agent,
        p_success,
        p_error_message,
        NOW()
    ) RETURNING id INTO activity_id;
    
    RETURN activity_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. Grant necessary permissions
GRANT EXECUTE ON FUNCTION get_activity_summary_metrics() TO authenticated;
GRANT EXECUTE ON FUNCTION get_admin_performance_metrics() TO authenticated;
GRANT EXECUTE ON FUNCTION get_audit_trail_metrics() TO authenticated;
GRANT EXECUTE ON FUNCTION log_admin_activity(UUID, TEXT, TEXT, UUID, TEXT, JSONB, INET, TEXT, BOOLEAN, TEXT) TO authenticated;

-- 11. Add some sample activity data if tables are empty
DO $$
DECLARE
    admin_user_id UUID;
    activity_count INTEGER;
BEGIN
    -- Check if we have any admin users
    SELECT id INTO admin_user_id FROM users WHERE role = 'admin' LIMIT 1;
    
    -- Check if we have any activity logs
    SELECT COUNT(*) INTO activity_count FROM admin_activity_logs;
    
    -- Only add sample data if we have admin users and no existing activity logs
    IF admin_user_id IS NOT NULL AND activity_count = 0 THEN
        -- Add some sample activity logs
        INSERT INTO admin_activity_logs (admin_id, action_type, action_description, success, created_at) VALUES
        (admin_user_id, 'product_management', 'Updated product pricing', true, NOW() - INTERVAL '2 hours'),
        (admin_user_id, 'user_management', 'Updated user role to admin', true, NOW() - INTERVAL '1 day'),
        (admin_user_id, 'system_config', 'Generated activity summary report', true, NOW() - INTERVAL '30 minutes'),
        (admin_user_id, 'order_management', 'Changed order status to shipped', true, NOW() - INTERVAL '3 hours'),
        (admin_user_id, 'user_management', 'Changed user role from admin to admin (admin)', true, NOW() - INTERVAL '10 minutes');
        
        -- Add a sample report
        INSERT INTO admin_reports (generated_by, report_type, report_name, file_format, created_at) VALUES
        (admin_user_id, 'activity_summary', 'Sample Activity Report', 'pdf', NOW() - INTERVAL '1 hour');
        
        RAISE NOTICE 'Sample activity data and report added successfully!';
    ELSE
        RAISE NOTICE 'Activity data already exists or no admin users found. Skipping sample data insertion.';
    END IF;
END $$;

-- 12. Final verification
SELECT 
    'Setup Complete!' as status,
    COUNT(*) as activity_logs_count
FROM admin_activity_logs
UNION ALL
SELECT 
    'Reports Count' as status,
    COUNT(*) as reports_count
FROM admin_reports;

-- Success message
DO $$
BEGIN
    RAISE NOTICE '✅ Activity Monitor & Reports System Setup Complete!';
    RAISE NOTICE 'All database functions, tables, and policies have been created.';
    RAISE NOTICE 'The system is now ready for production use.';
    RAISE NOTICE 'Access the activity monitor at /admin/activity';
    RAISE NOTICE 'Access the reports system at /admin/reports';
END $$;
