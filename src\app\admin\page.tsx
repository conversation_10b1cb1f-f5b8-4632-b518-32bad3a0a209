"use client";

import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  BarChart,
  DollarSign,
  Package,
  ShoppingCart,
  Users,
  ArrowUpRight,
  ArrowDownRight,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Activity,
  AlertCircle
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useEffect, useState, useCallback } from "react";
import { useToast } from "@/hooks/use-toast";
import { RealTimeNotifications } from "@/components/admin/real-time-notifications";
import RealTimeAnalytics from "@/components/admin/real-time-analytics";
import { BusinessReports } from "@/components/admin/business-reports";

interface DashboardStats {
  total_orders: number;
  pending_orders: number;
  processing_orders: number;
  delivered_orders: number;
  total_revenue: number;
  monthly_growth: number;
  total_products: number;
  total_users: number;
  recent_activities: number;
}

interface ActivityMetrics {
  total_activities: number;
  success_rate: number;
  activities_today: number;
  activities_this_week: number;
  most_active_admin: string;
}

interface RecentOrder {
  id: string;
  customer_name: string;
  customer_email: string;
  status: string;
  payment_status: string;
  total_amount: number;
  created_at: string;
  formatted_date: string;
  formatted_amount: string;
}

interface TopProduct {
  id: string;
  name: string;
  category: string;
  sales_count: number;
  total_revenue: number;
  trend: 'up' | 'down' | 'stable';
  formatted_revenue: string;
  image_url?: string;
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [activityMetrics, setActivityMetrics] = useState<ActivityMetrics | null>(null);
  const [recentOrders, setRecentOrders] = useState<RecentOrder[]>([]);
  const [topProducts, setTopProducts] = useState<TopProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [ordersLoading, setOrdersLoading] = useState(true);
  const [productsLoading, setProductsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [autoRefresh, setAutoRefresh] = useState(true);
  const { toast } = useToast();

  // Fetch dashboard statistics
  const fetchStats = useCallback(async () => {
    try {
      // Fetch order stats
      const orderResponse = await fetch('/api/admin/orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
      });

      if (!orderResponse.ok) {
        throw new Error('Failed to fetch order stats');
      }

      const orderData = await orderResponse.json();

      // Fetch activity metrics
      const activityResponse = await fetch('/api/admin/activity/metrics', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
      });

      let activityData: ActivityMetrics | null = null;
      if (activityResponse.ok) {
        activityData = await activityResponse.json();
      }

      // Fetch additional stats (products, users)
      const [productsResponse, usersResponse] = await Promise.all([
        fetch('/api/admin/products/stats', { credentials: 'include' }),
        fetch('/api/admin/users/stats', { credentials: 'include' })
      ]);

      const productsData = productsResponse.ok ? await productsResponse.json() : { total: 0 };
      const usersData = usersResponse.ok ? await usersResponse.json() : { total: 0 };

      // Combine all stats
      const combinedStats: DashboardStats = {
        ...orderData.data,
        total_products: productsData.total || 0,
        total_users: usersData.total || 0,
        recent_activities: activityData?.activities_today || 0,
      };

      setStats(combinedStats);
      setActivityMetrics(activityData);
      setLastUpdated(new Date());
      setLoading(false);

    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      toast({
        title: "Error",
        description: "Failed to fetch dashboard statistics",
        variant: "destructive",
      });
      setLoading(false);
    }
  }, [toast]);

  // Fetch recent orders
  const fetchRecentOrders = useCallback(async () => {
    try {
      setOrdersLoading(true);
      const response = await fetch('/api/admin/dashboard/recent-orders?limit=10', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch recent orders');
      }

      const data = await response.json();
      setRecentOrders(data.data.orders || []);
      setOrdersLoading(false);

    } catch (error) {
      console.error('Error fetching recent orders:', error);
      setOrdersLoading(false);
    }
  }, []);

  // Fetch top products
  const fetchTopProducts = useCallback(async () => {
    try {
      setProductsLoading(true);
      const response = await fetch('/api/admin/dashboard/top-products?limit=5&period=month', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch top products');
      }

      const data = await response.json();
      setTopProducts(data.data.products || []);
      setProductsLoading(false);

    } catch (error) {
      console.error('Error fetching top products:', error);
      setProductsLoading(false);
    }
  }, []);

  // Fetch all data
  const fetchAllData = useCallback(async () => {
    await Promise.all([
      fetchStats(),
      fetchRecentOrders(),
      fetchTopProducts()
    ]);
  }, [fetchStats, fetchRecentOrders, fetchTopProducts]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    fetchAllData();

    if (autoRefresh) {
      const interval = setInterval(fetchAllData, 30000);
      return () => clearInterval(interval);
    }
  }, [fetchAllData, autoRefresh]);

  const handleRefresh = () => {
    setLoading(true);
    setOrdersLoading(true);
    setProductsLoading(true);
    fetchAllData();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <div className="flex items-center gap-4">
          <RealTimeNotifications />
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <div className="text-sm text-muted-foreground">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </div>
          </div>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        {loading ? (
          // Loading skeleton
          Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <div className="h-4 w-24 bg-muted animate-pulse rounded" />
                <div className="h-4 w-4 bg-muted animate-pulse rounded" />
              </CardHeader>
              <CardContent>
                <div className="h-8 w-32 bg-muted animate-pulse rounded mb-2" />
                <div className="h-4 w-20 bg-muted animate-pulse rounded" />
              </CardContent>
            </Card>
          ))
        ) : stats ? (
          <>
            <Card className="glass-effect-dark border border-white/10 rounded-3xl neo-shadow hover:neo-shadow-light transition-neo">
              <CardHeader className="flex flex-row items-center justify-between pb-3">
                <CardTitle className="text-sm font-semibold text-foreground">Total Revenue</CardTitle>
                <div className="glass-effect-subtle rounded-2xl p-2 neo-shadow-light">
                  <DollarSign className="h-5 w-5 text-green-500" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold gradient-text mb-2">
                  R {stats.total_revenue.toLocaleString('en-ZA', { minimumFractionDigits: 2 })}
                </div>
                <div className="flex items-center pt-1 text-xs">
                  <span className={`flex items-center font-medium ${stats.monthly_growth >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                    {stats.monthly_growth >= 0 ? (
                      <ArrowUpRight className="mr-1 h-3 w-3" />
                    ) : (
                      <ArrowDownRight className="mr-1 h-3 w-3" />
                    )}
                    {stats.monthly_growth >= 0 ? '+' : ''}{stats.monthly_growth.toFixed(1)}%
                  </span>
                  <span className="ml-1 text-muted-foreground">from last month</span>
                </div>
              </CardContent>
            </Card>

            <Card className="glass-effect-dark border border-white/10 rounded-3xl neo-shadow hover:neo-shadow-light transition-neo">
              <CardHeader className="flex flex-row items-center justify-between pb-3">
                <CardTitle className="text-sm font-semibold text-foreground">Total Orders</CardTitle>
                <div className="glass-effect-subtle rounded-2xl p-2 neo-shadow-light">
                  <ShoppingCart className="h-5 w-5 text-blue-500" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold gradient-text mb-2">{stats.total_orders.toLocaleString()}</div>
                <div className="flex items-center pt-1 text-xs">
                  <span className="text-blue-500 font-medium">
                    {stats.pending_orders} pending, {stats.processing_orders} processing
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card className="glass-effect-dark border border-white/10 rounded-3xl neo-shadow hover:neo-shadow-light transition-neo">
              <CardHeader className="flex flex-row items-center justify-between pb-3">
                <CardTitle className="text-sm font-semibold text-foreground">Products</CardTitle>
                <div className="glass-effect-subtle rounded-2xl p-2 neo-shadow-light">
                  <Package className="h-5 w-5 text-orange-500" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold gradient-text mb-2">{stats.total_products.toLocaleString()}</div>
                <div className="flex items-center pt-1 text-xs">
                  <span className="text-green-500 font-medium">Active products</span>
                </div>
              </CardContent>
            </Card>

            <Card className="glass-effect-dark border border-white/10 rounded-3xl neo-shadow hover:neo-shadow-light transition-neo">
              <CardHeader className="flex flex-row items-center justify-between pb-3">
                <CardTitle className="text-sm font-semibold text-foreground">Total Users</CardTitle>
                <div className="glass-effect-subtle rounded-2xl p-2 neo-shadow-light">
                  <Users className="h-5 w-5 text-purple-500" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold gradient-text mb-2">{stats.total_users.toLocaleString()}</div>
                <div className="flex items-center pt-1 text-xs">
                  <span className="text-purple-500 font-medium">
                    {activityMetrics?.activities_today || 0} activities today
                  </span>
                </div>
              </CardContent>
            </Card>
          </>
        ) : (
          <Card className="col-span-full glass-effect-dark border border-white/10 rounded-3xl neo-shadow">
            <CardContent className="flex items-center justify-center py-8">
              <div className="flex items-center gap-3 text-destructive">
                <AlertCircle className="h-5 w-5" />
                <span className="font-medium">Failed to load dashboard statistics</span>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Tabs for different views */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="glass-effect-dark border border-white/10 rounded-2xl neo-shadow p-1">
          <TabsTrigger
            value="overview"
            className="rounded-xl px-6 py-3 font-medium transition-all duration-300 data-[state=active]:glass-effect data-[state=active]:neo-shadow-inset data-[state=active]:text-primary min-h-[44px]"
          >
            Overview
          </TabsTrigger>
          <TabsTrigger
            value="analytics"
            className="rounded-xl px-6 py-3 font-medium transition-all duration-300 data-[state=active]:glass-effect data-[state=active]:neo-shadow-inset data-[state=active]:text-primary min-h-[44px]"
          >
            Analytics
          </TabsTrigger>
          <TabsTrigger
            value="reports"
            className="rounded-xl px-6 py-3 font-medium transition-all duration-300 data-[state=active]:glass-effect data-[state=active]:neo-shadow-inset data-[state=active]:text-primary min-h-[44px]"
          >
            Reports
          </TabsTrigger>
          <TabsTrigger
            value="notifications"
            className="rounded-xl px-6 py-3 font-medium transition-all duration-300 data-[state=active]:glass-effect data-[state=active]:neo-shadow-inset data-[state=active]:text-primary min-h-[44px]"
          >
            Notifications
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Recent Orders */}
          <Card className="glass-effect-dark border border-white/10 rounded-3xl neo-shadow">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-semibold gradient-text">Recent Orders</CardTitle>
              <CardDescription className="text-muted-foreground">
                {ordersLoading ? 'Loading recent orders...' : `You have received ${recentOrders.length} recent orders`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {ordersLoading ? (
                <div className="space-y-3">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="flex items-center space-x-4">
                      <div className="h-4 w-24 bg-muted animate-pulse rounded" />
                      <div className="h-4 w-32 bg-muted animate-pulse rounded" />
                      <div className="h-4 w-20 bg-muted animate-pulse rounded" />
                      <div className="h-4 w-24 bg-muted animate-pulse rounded" />
                      <div className="h-4 w-20 bg-muted animate-pulse rounded ml-auto" />
                    </div>
                  ))}
                </div>
              ) : recentOrders.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-border">
                        <th className="text-left font-medium py-3 px-2">Order</th>
                        <th className="text-left font-medium py-3 px-2">Customer</th>
                        <th className="text-left font-medium py-3 px-2">Status</th>
                        <th className="text-left font-medium py-3 px-2">Date</th>
                        <th className="text-right font-medium py-3 px-2">Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      {recentOrders.map((order) => (
                        <tr key={order.id} className="border-b border-border hover:bg-muted/30 transition-colors">
                          <td className="py-3 px-2 font-medium">
                            {order.id.substring(0, 12)}...
                          </td>
                          <td className="py-3 px-2">{order.customer_name}</td>
                          <td className="py-3 px-2">
                            <span
                              className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                                order.status === "delivered"
                                  ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                                  : order.status === "processing"
                                  ? "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400"
                                  : order.status === "shipped"
                                  ? "bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400"
                                  : order.status === "pending"
                                  ? "bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400"
                                  : "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-400"
                              }`}
                            >
                              {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                            </span>
                          </td>
                          <td className="py-3 px-2 text-muted-foreground">{order.formatted_date}</td>
                          <td className="py-3 px-2 text-right font-medium">{order.formatted_amount}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <ShoppingCart className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No recent orders found</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Top Products */}
          <Card className="glass-effect-dark border border-white/10 rounded-3xl neo-shadow">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-semibold gradient-text">Top Products</CardTitle>
              <CardDescription className="text-muted-foreground">
                {productsLoading ? 'Loading top products...' : `Your best-selling products this month`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {productsLoading ? (
                <div className="space-y-4">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="flex items-center justify-between border-b border-border pb-4">
                      <div className="flex items-center gap-4">
                        <div className="w-10 h-10 bg-muted animate-pulse rounded-md" />
                        <div className="space-y-2">
                          <div className="h-4 w-32 bg-muted animate-pulse rounded" />
                          <div className="h-3 w-20 bg-muted animate-pulse rounded" />
                        </div>
                      </div>
                      <div className="text-right space-y-2">
                        <div className="h-4 w-24 bg-muted animate-pulse rounded ml-auto" />
                        <div className="h-3 w-16 bg-muted animate-pulse rounded ml-auto" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : topProducts.length > 0 ? (
                <div className="space-y-4">
                  {topProducts.map((product) => (
                    <div
                      key={product.id}
                      className="flex items-center justify-between border-b border-border pb-4 last:border-0 last:pb-0"
                    >
                      <div className="flex items-center gap-4">
                        <div className="w-10 h-10 rounded-md bg-primary/10 flex items-center justify-center">
                          {product.image_url ? (
                            <img
                              src={product.image_url}
                              alt={product.name}
                              className="w-10 h-10 rounded-md object-cover"
                            />
                          ) : (
                            <Package className="h-5 w-5 text-primary" />
                          )}
                        </div>
                        <div>
                          <p className="font-medium">{product.name}</p>
                          <p className="text-sm text-muted-foreground capitalize">
                            {product.category}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{product.formatted_revenue}</p>
                        <div className="flex items-center justify-end text-sm">
                          <span className="mr-1">{product.sales_count} sold</span>
                          {product.trend === "up" ? (
                            <TrendingUp className="h-3 w-3 text-green-500" />
                          ) : product.trend === "down" ? (
                            <TrendingDown className="h-3 w-3 text-red-500" />
                          ) : (
                            <div className="h-3 w-3 rounded-full bg-gray-400" />
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No product sales data available</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card className="glass-effect-dark border border-white/10 rounded-3xl neo-shadow">
            <CardContent className="text-center py-12">
              <div className="glass-effect-subtle rounded-2xl p-6 neo-shadow-light max-w-md mx-auto">
                <BarChart className="h-12 w-12 mx-auto mb-4 text-primary" />
                <p className="text-foreground font-medium mb-2">Analytics tab content is now available in the dedicated Analytics page.</p>
                <p className="text-sm text-muted-foreground">
                  Access comprehensive analytics via the sidebar: Analytics → Enterprise Analytics Dashboard
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports">
          <div className="glass-effect-dark border border-white/10 rounded-3xl neo-shadow p-6">
            <BusinessReports />
          </div>
        </TabsContent>

        <TabsContent value="notifications">
          <div className="glass-effect-dark border border-white/10 rounded-3xl neo-shadow p-6">
            <RealTimeNotifications />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
