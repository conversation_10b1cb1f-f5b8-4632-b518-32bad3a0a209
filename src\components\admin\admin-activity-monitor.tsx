'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Activity, 
  Search, 
  Filter, 
  Download,
  CheckCircle,
  XCircle,
  Package,
  ShoppingCart,
  Users,
  Settings,
  Clock,
  User
} from "lucide-react";
import { createClient } from '@/utils/supabase/client';
import { useToast } from "@/hooks/use-toast";

interface AdminActivityLog {
  id: string;
  admin_id: string;
  action_type: 'order_management' | 'product_management' | 'user_management' | 'system_config';
  action_description: string;
  target_id: string | null;
  target_type: 'order' | 'product' | 'user' | 'system' | null;
  metadata: any;
  success: boolean;
  error_message: string | null;
  created_at: string;
  admin: {
    email: string;
    full_name: string | null;
    admin_role: string;
  };
}

interface ActivityMetrics {
  total_activities: number;
  success_rate: number;
  most_active_admin: string;
  activities_today: number;
  activities_this_week: number;
}

export default function AdminActivityMonitor() {
  const [activities, setActivities] = useState<AdminActivityLog[]>([]);
  const [metrics, setMetrics] = useState<ActivityMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [actionTypeFilter, setActionTypeFilter] = useState<string>('all');
  const [adminFilter, setAdminFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('7d');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const { toast } = useToast();

  const supabase = createClient();
  const itemsPerPage = 20;

  useEffect(() => {
    fetchActivities();
    fetchMetrics();
  }, [currentPage, actionTypeFilter, adminFilter, dateFilter, searchTerm]);

  const fetchActivities = async () => {
    try {
      setLoading(true);

      // Build query parameters
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: itemsPerPage.toString(),
      });

      if (actionTypeFilter !== 'all') {
        params.append('action_type', actionTypeFilter);
      }

      if (adminFilter !== 'all') {
        params.append('admin_id', adminFilter);
      }

      if (dateFilter !== 'all') {
        params.append('date_filter', dateFilter);
      }

      if (searchTerm) {
        params.append('search', searchTerm);
      }

      // Fetch from API route
      const response = await fetch(`/api/admin/activity?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch activities`);
      }

      const data = await response.json();

      setActivities(data.activities || []);
      setTotalPages(data.totalPages || 1);
    } catch (error) {
      console.error('Error fetching activities:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      toast({
        title: "Error",
        description: `Failed to fetch admin activities: ${errorMessage}`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchMetrics = async () => {
    try {
      const response = await fetch('/api/admin/activity/metrics', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (response.ok) {
        const metricsData = await response.json();
        setMetrics(metricsData);
      } else {
        // Calculate metrics from current activities if API fails
        const totalActivities = activities.length;
        const successfulActivities = activities.filter(a => a.success).length;
        const successRate = totalActivities > 0 ? (successfulActivities / totalActivities) * 100 : 100;

        const today = new Date().toDateString();
        const activitiesToday = activities.filter(a =>
          new Date(a.created_at).toDateString() === today
        ).length;

        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        const activitiesThisWeek = activities.filter(a =>
          new Date(a.created_at) >= weekAgo
        ).length;

        setMetrics({
          total_activities: totalActivities,
          success_rate: successRate,
          most_active_admin: activities[0]?.admin?.full_name || 'No activity yet',
          activities_today: activitiesToday,
          activities_this_week: activitiesThisWeek,
        });
      }
    } catch (error) {
      console.error('Error fetching metrics:', error);
      // Set default metrics on error
      setMetrics({
        total_activities: activities.length,
        success_rate: 100,
        most_active_admin: 'No activity yet',
        activities_today: 0,
        activities_this_week: 0,
      });
    }
  };

  const getActionIcon = (actionType: string) => {
    switch (actionType) {
      case 'order_management':
        return <ShoppingCart className="h-4 w-4 text-blue-500" />;
      case 'product_management':
        return <Package className="h-4 w-4 text-green-500" />;
      case 'user_management':
        return <Users className="h-4 w-4 text-purple-500" />;
      case 'system_config':
        return <Settings className="h-4 w-4 text-orange-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const getActionBadgeColor = (actionType: string) => {
    switch (actionType) {
      case 'order_management':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'product_management':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'user_management':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'system_config':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const exportActivities = async () => {
    try {
      // This would typically call an API endpoint to generate and download a report
      toast({
        title: "Export Started",
        description: "Your activity report is being generated...",
      });
      
      // Placeholder for export functionality
      setTimeout(() => {
        toast({
          title: "Export Complete",
          description: "Activity report has been downloaded",
        });
      }, 2000);
    } catch (error) {
      console.error('Error exporting activities:', error);
      toast({
        title: "Export Failed",
        description: "Failed to export activity report",
        variant: "destructive",
      });
    }
  };

  if (loading && activities.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold tracking-tight">Admin Activity Monitor</h2>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Admin Activity Monitor</h2>
          <p className="text-muted-foreground">
            Track and monitor all administrative activities
          </p>
        </div>
        <Button onClick={exportActivities}>
          <Download className="h-4 w-4 mr-2" />
          Export Report
        </Button>
      </div>

      {/* Metrics Cards */}
      {metrics && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Total Activities</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.total_activities.toLocaleString()}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.success_rate.toFixed(1)}%</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Today</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.activities_today}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">This Week</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.activities_this_week}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search activities..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={actionTypeFilter} onValueChange={setActionTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Action Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Actions</SelectItem>
                <SelectItem value="order_management">Order Management</SelectItem>
                <SelectItem value="product_management">Product Management</SelectItem>
                <SelectItem value="user_management">User Management</SelectItem>
                <SelectItem value="system_config">System Config</SelectItem>
              </SelectContent>
            </Select>

            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Time Period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1d">Last 24 Hours</SelectItem>
                <SelectItem value="7d">Last 7 Days</SelectItem>
                <SelectItem value="30d">Last 30 Days</SelectItem>
                <SelectItem value="all">All Time</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" onClick={() => {
              setSearchTerm('');
              setActionTypeFilter('all');
              setAdminFilter('all');
              setDateFilter('7d');
            }}>
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Activities Table */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activities ({activities.length})</CardTitle>
          <CardDescription>
            Detailed log of all administrative actions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Admin</TableHead>
                <TableHead>Action</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Time</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {activities.map((activity) => (
                <TableRow key={activity.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="font-medium">{activity.admin?.full_name || 'Unknown'}</p>
                        <p className="text-xs text-muted-foreground">{activity.admin?.email}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getActionIcon(activity.action_type)}
                      <Badge className={getActionBadgeColor(activity.action_type)}>
                        {activity.action_type.replace('_', ' ')}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <p className="max-w-md truncate">{activity.action_description}</p>
                  </TableCell>
                  <TableCell>
                    {activity.success ? (
                      <Badge variant="outline" className="text-green-600 border-green-200">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Success
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="text-red-600 border-red-200">
                        <XCircle className="h-3 w-3 mr-1" />
                        Failed
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <p>{new Date(activity.created_at).toLocaleDateString()}</p>
                      <p className="text-muted-foreground">
                        {new Date(activity.created_at).toLocaleTimeString()}
                      </p>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <p className="text-sm text-muted-foreground">
                Page {currentPage} of {totalPages}
              </p>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
