"use client";

import { useEffect, useState } from "react";
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Heart, Share2, Minus, Plus, ShoppingCart, Star, Truck } from "lucide-react";
import Image from "next/image";
import { notFound, useParams, useRouter } from "next/navigation";
import { getProductClient, getProductsByCategoryClient } from "@/utils/supabase/products-client";
import { Product } from "@/utils/supabase/products";
import { useCart } from "@/context/cart-context";
import ReviewFormModal from "@/components/review-form-modal";
import { toast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";
import { useParallaxScroll, useMobileParallax } from "@/hooks/use-parallax-scroll";

export default function ProductPage() {
  const params = useParams();
  const router = useRouter();
  const { addToCart } = useCart();
  const [product, setProduct] = useState<Product | null>(null);
  const [relatedProducts, setRelatedProducts] = useState<Product[]>([]);
  const [quantity, setQuantity] = useState(1);
  const [activeImage, setActiveImage] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [reviewModalOpen, setReviewModalOpen] = useState(false);
  const [selectedSize, setSelectedSize] = useState('M');
  const [selectedColor, setSelectedColor] = useState('blue');

  // Parallax scroll effects for mobile
  const { imageStyles, contentStyles } = useParallaxScroll({
    imageHeight: 60,
    contentOffset: 55,
    enableParallax: true
  });
  const { isScrolling, scrollProgress } = useMobileParallax();

  useEffect(() => {
    async function fetchProductData() {
      setLoading(true);
      try {
        if (!params.id) {
          throw new Error('Product ID is required');
        }
        
        // Fetch product details
        const productData = await getProductClient(params.id as string);
        
        if (!productData) {
          notFound();
          return;
        }
        
        setProduct(productData);
        setActiveImage(0); // Reset active image when product changes
        
        // Fetch related products from the same category
        if (productData.category) {
          const categoryProducts = await getProductsByCategoryClient(productData.category);
          // Filter out the current product and limit to 4 related products
          const related = categoryProducts
            .filter(p => p.id !== productData.id)
            .slice(0, 4);
          setRelatedProducts(related);
        }
      } catch (err) {
        console.error('Error fetching product:', err);
        setError('Failed to load product. Please try again later.');
      } finally {
        setLoading(false);
      }
    }
    
    fetchProductData();
  }, [params.id]);

  // Handle quantity changes
  const incrementQuantity = () => setQuantity(prev => prev + 1);
  const decrementQuantity = () => setQuantity(prev => prev > 1 ? prev - 1 : 1);
  
  // Handle add to cart
  const handleAddToCart = () => {
    if (product) {
      addToCart({
        id: product.id as unknown as number, // Convert string ID to number for cart
        name: product.name,
        price: product.price,
        image: product.image || (product.images && product.images.length > 0 ? product.images[0] : ''),
      });
    }
  };

  // Handle review submission
  const handleReviewSubmit = (review: {
    rating: number;
    title: string;
    content: string;
    name: string;
    email: string;
  }) => {
    // In a real application, this would send the review to your backend
    console.log('Review submitted:', review);
    
    // Show success message
    toast({
      title: "Review Submitted",
      description: "Thank you for your feedback! Your review will be published after moderation.",
      variant: "default",
    });
  };

  if (loading) {
    return (
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <main className="flex-grow container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-96">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <main className="flex-grow container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-foreground mb-2">
              {error || "Product not found"}
            </h2>
            <Button asChild className="mt-4">
              <a href="/shop">Return to Shop</a>
            </Button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Prepare image gallery
  const productImages = product.images && product.images.length > 0 
    ? product.images 
    : product.image 
      ? [product.image] 
      : [];

  return (
    <div className="flex flex-col min-h-screen bg-background">
      {/* Mobile Header */}
      <div className="md:hidden fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-lg border-b border-border/50">
        <div className="flex items-center justify-between p-4">
          <button
            onClick={() => router.back()}
            className="w-10 h-10 rounded-full bg-muted/50 flex items-center justify-center neo-shadow-light"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div className="flex items-center gap-2">
            <button className="w-10 h-10 rounded-full bg-muted/50 flex items-center justify-center neo-shadow-light">
              <Share2 className="h-5 w-5" />
            </button>
            <button className="w-10 h-10 rounded-full bg-muted/50 flex items-center justify-center neo-shadow-light">
              <Heart className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Desktop Navbar */}
      <div className="hidden md:block">
        <Navbar />
      </div>

      <main className="flex-grow md:pt-8 pb-24 md:pb-8">
        {/* Mobile Parallax Layout */}
        <div className="md:hidden relative min-h-screen mobile-product-scroll">
          {/* Fixed Product Image Container */}
          <div
            className="fixed top-0 left-0 right-0 z-10 touch-manipulation"
            style={{
              height: '60vh',
              ...imageStyles
            }}
          >
            {productImages.length > 0 ? (
              <div className="relative w-full h-full overflow-hidden">
                <Image
                  src={productImages[activeImage]}
                  alt={product.name}
                  fill
                  className="object-cover"
                  sizes="100vw"
                  priority
                />

                {/* Gradient overlay for better text readability */}
                <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/20" />

                {/* Back button */}
                <button
                  onClick={() => router.back()}
                  className="absolute top-6 left-4 w-10 h-10 rounded-full glass-effect-dark flex items-center justify-center neo-shadow-light z-20"
                >
                  <ArrowLeft className="h-5 w-5 text-white" />
                </button>

                {/* Share button */}
                <button className="absolute top-6 right-4 w-10 h-10 rounded-full glass-effect-dark flex items-center justify-center neo-shadow-light z-20">
                  <Share2 className="h-5 w-5 text-white" />
                </button>

                {/* Image indicators */}
                {productImages.length > 1 && (
                  <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex gap-2 z-20">
                    {productImages.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setActiveImage(index)}
                        className={cn(
                          "w-2 h-2 rounded-full transition-all duration-300",
                          index === activeImage ? "bg-white scale-125" : "bg-white/50"
                        )}
                      />
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <div className="w-full h-full bg-muted flex items-center justify-center">
                <ShoppingCart className="h-16 w-16 text-muted-foreground" />
              </div>
            )}
          </div>

          {/* Scrollable Content Container */}
          <div
            className="relative z-20 bg-background rounded-t-3xl neo-shadow mobile-product-content"
            style={{
              marginTop: '55vh',
              minHeight: '100vh',
              ...contentStyles
            }}
          >
            {/* Pull indicator */}
            <div className="flex justify-center pt-4 pb-6">
              <div className="w-12 h-1 bg-muted-foreground/30 rounded-full"></div>
            </div>

            {/* Scroll progress indicator */}
            {isScrolling && (
              <div className="fixed top-4 right-4 z-30 md:hidden">
                <div className="w-12 h-12 rounded-full glass-effect-dark flex items-center justify-center">
                  <div
                    className="w-8 h-8 rounded-full border-2 border-primary/30"
                    style={{
                      background: `conic-gradient(from 0deg, #4facfe ${scrollProgress * 360}deg, transparent ${scrollProgress * 360}deg)`
                    }}
                  />
                </div>
              </div>
            )}

            {/* Mobile Product Info */}
            <div className="px-6 pb-8">
              {/* Product Name and Price */}
              <div className="mb-6">
                <h1 className="text-2xl font-bold text-foreground mb-2">{product.name}</h1>
                <div className="flex items-center justify-between mb-2">
                  <div className="text-3xl font-bold text-gradient">
                    R{product.price.toFixed(0)}
                  </div>
                  <div className="flex items-center gap-1 px-3 py-1 rounded-full bg-yellow-400/10">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-sm font-semibold text-yellow-600 dark:text-yellow-400">{product.rating || 4.5}</span>
                    <span className="text-xs text-muted-foreground">({product.reviews || 0})</span>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">Price Inclusive Of All Taxes</p>
              </div>

              {/* Choose Size */}
              <div className="mb-6">
                <h3 className="font-semibold text-foreground mb-4">Choose Size</h3>
                <div className="flex gap-3">
                  {['S', 'M', 'L', 'XL'].map((size) => (
                    <button
                      key={size}
                      onClick={() => setSelectedSize(size)}
                      className={cn(
                        "w-14 h-14 rounded-2xl border-2 transition-all duration-300 font-semibold text-lg",
                        selectedSize === size
                          ? "border-primary bg-primary text-white neo-shadow scale-105"
                          : "border-border bg-muted/20 text-muted-foreground hover:border-primary/50 neo-shadow-light hover:scale-105"
                      )}
                    >
                      {size}
                    </button>
                  ))}
                </div>
              </div>

              {/* Choose Color */}
              <div className="mb-8">
                <h3 className="font-semibold text-foreground mb-4">Choose Color</h3>
                <div className="flex gap-4">
                  {[
                    { name: 'blue', color: 'bg-blue-500', ring: 'ring-blue-500' },
                    { name: 'purple', color: 'bg-purple-500', ring: 'ring-purple-500' },
                    { name: 'pink', color: 'bg-pink-500', ring: 'ring-pink-500' },
                  ].map((color) => (
                    <button
                      key={color.name}
                      onClick={() => setSelectedColor(color.name)}
                      className={cn(
                        "w-14 h-14 rounded-full transition-all duration-300 ring-4 ring-offset-4 ring-offset-background",
                        color.color,
                        selectedColor === color.name
                          ? `${color.ring} ring-opacity-100 scale-110`
                          : "ring-transparent hover:scale-105"
                      )}
                    />
                  ))}
                </div>
              </div>

              {/* Tabs */}
              <div className="mb-8">
                <div className="flex border-b border-border/50">
                  <button className="px-4 py-3 text-sm font-semibold text-primary border-b-2 border-primary">
                    Description
                  </button>
                  <button className="px-4 py-3 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">
                    Delivery
                  </button>
                  <button className="px-4 py-3 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">
                    Reviews
                  </button>
                </div>

                {/* Tab Content */}
                <div className="pt-6">
                  <p className="text-muted-foreground leading-relaxed">
                    {product.description || 'Berrylush is a contemporary Western style that is relaxed, occasional and suited for everyday use. Perfect for tennis enthusiasts who value both style and performance.'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Desktop Layout - Two Column Design */}
        <div className="hidden lg:block">
          <div className="container mx-auto px-6 py-12">
            <div className="grid grid-cols-12 gap-12">

              {/* Left Column - Image Gallery (60% width) */}
              <div className="col-span-7">
                <div className="flex gap-6">

                  {/* Thumbnail Gallery - Vertical */}
                  {productImages.length > 1 && (
                    <div className="flex flex-col gap-3 w-20">
                      {productImages.map((image, index) => (
                        <button
                          key={index}
                          onClick={() => setActiveImage(index)}
                          className={cn(
                            "relative aspect-square rounded-xl overflow-hidden transition-all duration-300 neo-shadow-light hover:neo-shadow",
                            index === activeImage
                              ? "ring-2 ring-green-500 ring-offset-2 ring-offset-background scale-105"
                              : "hover:scale-105"
                          )}
                        >
                          <Image
                            src={image}
                            alt={`${product.name} view ${index + 1}`}
                            fill
                            className="object-cover"
                            sizes="80px"
                          />
                        </button>
                      ))}
                    </div>
                  )}

                  {/* Main Product Image */}
                  <div className="flex-1">
                    {productImages.length > 0 ? (
                      <div className="relative aspect-square rounded-2xl overflow-hidden neo-shadow-hover">
                        <Image
                          src={productImages[activeImage]}
                          alt={product.name}
                          fill
                          className="object-cover transition-transform duration-500 hover:scale-105"
                          sizes="(min-width: 1024px) 50vw, 100vw"
                          priority
                        />

                        {/* Zoom indicator */}
                        <div className="absolute top-4 right-4 bg-black/20 backdrop-blur-sm rounded-full p-2 opacity-0 hover:opacity-100 transition-opacity">
                          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                          </svg>
                        </div>
                      </div>
                    ) : (
                      <div className="aspect-square bg-muted rounded-2xl flex items-center justify-center neo-shadow-light">
                        <ShoppingCart className="h-16 w-16 text-muted-foreground" />
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Right Column - Product Details (40% width) */}
              <div className="col-span-5">
                <div className="sticky top-8">

                  {/* Breadcrumb */}
                  <div className="flex items-center gap-2 text-sm text-muted-foreground mb-6">
                    <button onClick={() => router.push('/shop')} className="hover:text-foreground transition-colors">
                      Shop
                    </button>
                    <span>/</span>
                    <span className="text-foreground">{product.category || 'Product'}</span>
                  </div>

                  {/* Product Title */}
                  <h1 className="text-4xl font-bold text-foreground mb-4 leading-tight">
                    {product.name}
                  </h1>

                  {/* Rating and Reviews */}
                  <div className="flex items-center gap-4 mb-6">
                    <div className="flex items-center gap-1">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={cn(
                            "h-4 w-4",
                            i < Math.floor(product.rating || 4.5)
                              ? "fill-yellow-400 text-yellow-400"
                              : "text-gray-300"
                          )}
                        />
                      ))}
                    </div>
                    <span className="text-sm font-medium text-foreground">
                      {product.rating || 4.5}
                    </span>
                    <button
                      onClick={() => setReviewModalOpen(true)}
                      className="text-sm text-muted-foreground hover:text-foreground transition-colors underline"
                    >
                      ({product.reviews || 0} reviews)
                    </button>
                  </div>

                  {/* Price */}
                  <div className="mb-8">
                    <div className="text-5xl font-bold text-green-600 mb-2">
                      R{product.price.toFixed(0)}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Price inclusive of all taxes • Free shipping on orders over R1000
                    </p>
                  </div>

                  {/* Product Options */}
                  <div className="space-y-8 mb-8">

                    {/* Size Selection */}
                    <div>
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-foreground">Size</h3>
                        <button className="text-sm text-green-600 hover:text-green-700 transition-colors underline">
                          Size Guide
                        </button>
                      </div>
                      <div className="grid grid-cols-4 gap-3">
                        {['S', 'M', 'L', 'XL'].map((size) => (
                          <button
                            key={size}
                            onClick={() => setSelectedSize(size)}
                            className={cn(
                              "h-14 rounded-xl border-2 transition-all duration-300 font-semibold text-lg neo-shadow-light hover:neo-shadow",
                              selectedSize === size
                                ? "border-green-500 bg-green-50 text-green-700 scale-105"
                                : "border-gray-200 bg-white text-gray-700 hover:border-green-300"
                            )}
                          >
                            {size}
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Color Selection */}
                    <div>
                      <h3 className="text-lg font-semibold text-foreground mb-4">Color</h3>
                      <div className="flex gap-4">
                        {[
                          { name: 'blue', color: 'bg-blue-500', label: 'Ocean Blue' },
                          { name: 'purple', color: 'bg-purple-500', label: 'Royal Purple' },
                          { name: 'pink', color: 'bg-pink-500', label: 'Rose Pink' },
                        ].map((color) => (
                          <button
                            key={color.name}
                            onClick={() => setSelectedColor(color.name)}
                            className={cn(
                              "relative w-16 h-16 rounded-full transition-all duration-300 neo-shadow-light hover:neo-shadow",
                              color.color,
                              selectedColor === color.name
                                ? "ring-4 ring-green-500 ring-offset-4 ring-offset-background scale-110"
                                : "hover:scale-105"
                            )}
                            title={color.label}
                          >
                            {selectedColor === color.name && (
                              <div className="absolute inset-0 rounded-full border-2 border-white flex items-center justify-center">
                                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                              </div>
                            )}
                          </button>
                        ))}
                      </div>
                      {selectedColor && (
                        <p className="text-sm text-muted-foreground mt-2 capitalize">
                          Selected: {selectedColor === 'blue' ? 'Ocean Blue' : selectedColor === 'purple' ? 'Royal Purple' : 'Rose Pink'}
                        </p>
                      )}
                    </div>
                  </div>
                  {/* Quantity and Add to Cart */}
                  <div className="space-y-6 mb-8">

                    {/* Quantity Selector */}
                    <div>
                      <h3 className="text-lg font-semibold text-foreground mb-4">Quantity</h3>
                      <div className="flex items-center gap-4">
                        <div className="flex items-center border-2 border-gray-200 rounded-xl neo-shadow-light">
                          <button
                            onClick={decrementQuantity}
                            className="w-12 h-12 flex items-center justify-center text-gray-600 hover:text-green-600 transition-colors rounded-l-xl hover:bg-green-50"
                            aria-label="Decrease quantity"
                          >
                            <Minus className="h-5 w-5" />
                          </button>
                          <div className="w-16 h-12 flex items-center justify-center border-x-2 border-gray-200 font-semibold text-lg">
                            {quantity}
                          </div>
                          <button
                            onClick={incrementQuantity}
                            className="w-12 h-12 flex items-center justify-center text-gray-600 hover:text-green-600 transition-colors rounded-r-xl hover:bg-green-50"
                            aria-label="Increase quantity"
                          >
                            <Plus className="h-5 w-5" />
                          </button>
                        </div>

                        {/* Stock indicator */}
                        <div className="flex items-center gap-2 text-sm">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <span className="text-muted-foreground">
                            {product.stock && product.stock > 0 ? `${product.stock} in stock` : 'In stock'}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Add to Cart Button */}
                    <Button
                      className="w-full h-16 text-lg font-semibold rounded-xl bg-green-600 hover:bg-green-700 text-white neo-shadow hover:neo-shadow-hover transition-all duration-300 flex items-center justify-center gap-3"
                      onClick={handleAddToCart}
                      disabled={product.stock === 0}
                    >
                      {product.stock === 0 ? (
                        'Out of Stock'
                      ) : (
                        <>
                          <ShoppingCart className="h-5 w-5" />
                          Add to Cart • R{(product.price * quantity).toFixed(0)}
                        </>
                      )}
                    </Button>

                    {/* Action Buttons */}
                    <div className="grid grid-cols-2 gap-4">
                      <Button
                        variant="outline"
                        className="h-12 rounded-xl border-2 border-gray-200 hover:border-green-300 neo-shadow-light hover:neo-shadow transition-all duration-300"
                      >
                        <Heart className="h-4 w-4 mr-2" />
                        Wishlist
                      </Button>
                      <Button
                        variant="outline"
                        className="h-12 rounded-xl border-2 border-gray-200 hover:border-green-300 neo-shadow-light hover:neo-shadow transition-all duration-300"
                      >
                        <Share2 className="h-4 w-4 mr-2" />
                        Share
                      </Button>
                    </div>
                  </div>

                  {/* Product Features */}
                  <div className="space-y-4 mb-8">
                    <div className="flex items-center gap-3 p-4 bg-green-50 rounded-xl neo-shadow-light">
                      <Truck className="h-5 w-5 text-green-600" />
                      <div>
                        <p className="font-medium text-green-800">Free Shipping</p>
                        <p className="text-sm text-green-600">On orders over R1000</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-4 bg-blue-50 rounded-xl neo-shadow-light">
                      <svg className="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <div>
                        <p className="font-medium text-blue-800">Quality Guarantee</p>
                        <p className="text-sm text-blue-600">30-day return policy</p>
                      </div>
                    </div>
                  </div>

                  {/* Product Description */}
                  <div className="border-t border-gray-200 pt-8">
                    <h3 className="text-xl font-semibold text-foreground mb-4">Product Details</h3>
                    <div className="prose prose-sm max-w-none">
                      <p className="text-muted-foreground leading-relaxed mb-4">
                        {product.description || 'Experience premium tennis equipment designed for performance and style. This contemporary piece combines functionality with modern aesthetics, perfect for players who demand excellence on and off the court.'}
                      </p>

                      {/* Product Specifications */}
                      <div className="grid grid-cols-1 gap-3 mt-6">
                        <div className="flex justify-between py-2 border-b border-gray-100">
                          <span className="font-medium text-gray-700">Material</span>
                          <span className="text-gray-600">Premium Synthetic</span>
                        </div>
                        <div className="flex justify-between py-2 border-b border-gray-100">
                          <span className="font-medium text-gray-700">Category</span>
                          <span className="text-gray-600 capitalize">{product.category || 'Tennis Equipment'}</span>
                        </div>
                        <div className="flex justify-between py-2 border-b border-gray-100">
                          <span className="font-medium text-gray-700">Brand</span>
                          <span className="text-gray-600">Tennis Whisperer</span>
                        </div>
                        <div className="flex justify-between py-2">
                          <span className="font-medium text-gray-700">SKU</span>
                          <span className="text-gray-600">TW-{product.id}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tablet Layout (md to lg) - Keep existing layout */}
        <div className="hidden md:block lg:hidden">
          {/* Product Image - Tablet */}
          <div className="relative">
            {productImages.length > 0 ? (
              <div className="relative aspect-square md:aspect-[4/3] overflow-hidden">
                <Image
                  src={productImages[activeImage]}
                  alt={product.name}
                  fill
                  className="object-cover"
                  sizes="100vw"
                  priority
                />
                {/* Image indicators */}
                {productImages.length > 1 && (
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
                    {productImages.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setActiveImage(index)}
                        className={cn(
                          "w-2 h-2 rounded-full transition-colors",
                          index === activeImage ? "bg-white" : "bg-white/50"
                        )}
                      />
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <div className="aspect-square bg-muted flex items-center justify-center">
                <ShoppingCart className="h-16 w-16 text-muted-foreground" />
              </div>
            )}
          </div>

          <div className="container mx-auto px-4">
            {/* Existing tablet layout content */}
            <div className="py-6">
              <h1 className="text-2xl md:text-3xl font-bold text-foreground mb-2">{product.name}</h1>

              {/* Price and Rating */}
              <div className="flex items-center justify-between mb-4">
                <div className="text-3xl font-bold text-primary">
                  R{product.price.toFixed(0)}
                </div>
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="text-sm font-medium">{product.rating || 4.5}</span>
                  <span className="text-sm text-muted-foreground">({product.reviews || 0})</span>
                </div>
              </div>

              {/* Price note */}
              <p className="text-sm text-muted-foreground mb-6">Price Inclusive Of All Taxes</p>

              {/* Choose Size */}
              <div className="mb-6">
                <h3 className="font-medium text-foreground mb-3">Choose Size</h3>
                <div className="flex gap-3">
                  {['S', 'M', 'L', 'XL'].map((size) => (
                    <button
                      key={size}
                      onClick={() => setSelectedSize(size)}
                      className={cn(
                        "w-12 h-12 rounded-xl border-2 transition-neo font-medium",
                        selectedSize === size
                          ? "border-primary bg-primary text-primary-foreground neo-shadow"
                          : "border-border bg-muted/30 text-muted-foreground hover:border-primary/50 neo-shadow-light"
                      )}
                    >
                      {size}
                    </button>
                  ))}
                </div>
              </div>

              {/* Choose Color */}
              <div className="mb-6">
                <h3 className="font-medium text-foreground mb-3">Choose Color</h3>
                <div className="flex gap-3">
                  {[
                    { name: 'blue', color: 'bg-blue-500' },
                    { name: 'purple', color: 'bg-purple-500' },
                    { name: 'pink', color: 'bg-pink-500' },
                  ].map((color) => (
                    <button
                      key={color.name}
                      onClick={() => setSelectedColor(color.name)}
                      className={cn(
                        "w-12 h-12 rounded-full border-4 transition-neo",
                        color.color,
                        selectedColor === color.name
                          ? "border-foreground neo-shadow"
                          : "border-transparent neo-shadow-light"
                      )}
                    />
                  ))}
                </div>
              </div>

              {/* Description */}
              <div className="mb-6">
                <h3 className="font-medium text-foreground mb-3">Description</h3>
                <p className="text-muted-foreground text-sm leading-relaxed">
                  {product.description || 'Berrylush is a contemporary Western style that is relaxed, occasional and suited for everyday use.'}
                </p>
              </div>
            </div>
          </div>
        </div>

      </main>

      {/* Sticky Bottom Add to Cart - Mobile */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 z-50 glass-effect-dark border-t border-border/30 pb-safe">
        <div className="p-6">
          <Button
            className="w-full h-16 rounded-2xl gradient-purple text-white font-bold text-lg neo-shadow hover:neo-shadow-hover transition-neo flex items-center justify-center gap-3"
            onClick={handleAddToCart}
            disabled={product.stock === 0}
          >
            {product.stock === 0 ? (
              'Out of Stock'
            ) : (
              <>
                <ShoppingCart className="h-5 w-5" />
                Add To Cart R{product.price.toFixed(0)}
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Tablet Add to Cart (md to lg) */}
      <div className="hidden md:block lg:hidden container mx-auto px-4 pb-8">
        <div className="flex items-center gap-4">
          <div className="flex border border-border rounded-md">
            <button
              onClick={decrementQuantity}
              className="px-4 py-2 text-muted-foreground hover:text-primary transition-colors"
              aria-label="Decrease quantity"
            >
              <Minus className="h-4 w-4" />
            </button>
            <div className="px-4 py-2 border-x border-border min-w-[3rem] text-center">
              {quantity}
            </div>
            <button
              onClick={incrementQuantity}
              className="px-4 py-2 text-muted-foreground hover:text-primary transition-colors"
              aria-label="Increase quantity"
            >
              <Plus className="h-4 w-4" />
            </button>
          </div>

          <Button
            className="flex-1 gap-2"
            onClick={handleAddToCart}
            disabled={product.stock === 0}
          >
            <ShoppingCart className="h-4 w-4" />
            {product.stock === 0 ? 'Out of Stock' : 'Add to Cart'}
          </Button>
        </div>
      </div>

      <Footer />

      {/* Review Form Modal */}
      {product && (
        <ReviewFormModal
          productId={product.id as string}
          open={reviewModalOpen}
          onOpenChange={setReviewModalOpen}
          onSubmit={handleReviewSubmit}
        />
      )}
    </div>
  );
}
