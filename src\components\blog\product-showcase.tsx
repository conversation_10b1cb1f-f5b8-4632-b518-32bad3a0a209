"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ShoppingCart, 
  ExternalLink, 
  Star,
  DollarSign,
  Package,
  Eye
} from 'lucide-react';
import Link from 'next/link';

interface Product {
  id: string;
  name: string;
  price: number;
  image_url: string;
  slug: string;
  category: string;
  description: string;
}

interface ProductReference {
  product: Product;
  reference_type: 'featured' | 'mentioned' | 'related';
  position: number;
}

interface ProductShowcaseProps {
  references: ProductReference[];
  className?: string;
  showType?: 'all' | 'featured' | 'mentioned' | 'related';
  layout?: 'grid' | 'list' | 'carousel';
}

export function ProductShowcase({
  references,
  className = '',
  showType = 'all',
  layout = 'grid'
}: ProductShowcaseProps) {
  // Filter references by type
  const filteredReferences = showType === 'all' 
    ? references 
    : references.filter(ref => ref.reference_type === showType);

  // Sort by position
  const sortedReferences = filteredReferences.sort((a, b) => a.position - b.position);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const getReferenceTypeColor = (type: string) => {
    switch (type) {
      case 'featured': return 'bg-primary/20 text-primary border-primary/30';
      case 'mentioned': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'related': return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getReferenceTypeIcon = (type: string) => {
    switch (type) {
      case 'featured': return <Star className="h-4 w-4" />;
      case 'mentioned': return <Package className="h-4 w-4" />;
      case 'related': return <Eye className="h-4 w-4" />;
      default: return <Package className="h-4 w-4" />;
    }
  };

  if (sortedReferences.length === 0) {
    return null;
  }

  // Featured products get special treatment
  const featuredProducts = sortedReferences.filter(ref => ref.reference_type === 'featured');
  const otherProducts = sortedReferences.filter(ref => ref.reference_type !== 'featured');

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Featured Products Section */}
      {featuredProducts.length > 0 && (showType === 'all' || showType === 'featured') && (
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Star className="h-5 w-5 text-primary" />
            <h3 className="text-xl font-semibold text-foreground">Featured Tennis Gear</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {featuredProducts.map((reference) => (
              <Card key={reference.product.id} className="glass-effect border border-white/10 neo-shadow hover:neo-shadow-light transition-all duration-300 group">
                <div className="relative">
                  <img
                    src={reference.product.image_url}
                    alt={reference.product.name}
                    className="w-full h-48 object-cover rounded-t-2xl"
                  />
                  <Badge className="absolute top-3 left-3 bg-primary/20 text-primary border-primary/30">
                    <Star className="h-3 w-3 mr-1" />
                    Featured
                  </Badge>
                </div>
                
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-lg font-semibold text-foreground group-hover:text-primary transition-colors">
                        {reference.product.name}
                      </h4>
                      <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                        {reference.product.description}
                      </p>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1 text-lg font-bold text-primary">
                        <DollarSign className="h-4 w-4" />
                        {formatPrice(reference.product.price)}
                      </div>
                      <Badge variant="outline" className="border-white/20 text-muted-foreground">
                        {reference.product.category}
                      </Badge>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button asChild className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground min-h-[44px]">
                        <Link href={`/products/${reference.product.slug}`}>
                          <Eye className="h-4 w-4 mr-2" />
                          View Product
                        </Link>
                      </Button>
                      <Button 
                        variant="outline" 
                        size="icon"
                        className="glass-effect border-white/20 hover:glass-effect-subtle min-h-[44px] min-w-[44px]"
                      >
                        <ShoppingCart className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Other Products Section */}
      {otherProducts.length > 0 && (showType === 'all' || showType !== 'featured') && (
        <div className="space-y-4">
          {showType === 'all' && (
            <div className="flex items-center gap-2">
              <Package className="h-5 w-5 text-muted-foreground" />
              <h3 className="text-lg font-semibold text-foreground">Related Products</h3>
            </div>
          )}
          
          <div className={layout === 'grid' 
            ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4" 
            : "space-y-3"
          }>
            {otherProducts.map((reference) => (
              <Card key={reference.product.id} className="glass-effect border border-white/10 neo-shadow hover:neo-shadow-light transition-all duration-300 group">
                <CardContent className="p-4">
                  <div className={layout === 'grid' ? "space-y-3" : "flex items-center gap-4"}>
                    <div className={layout === 'grid' ? "relative" : "flex-shrink-0"}>
                      <img
                        src={reference.product.image_url}
                        alt={reference.product.name}
                        className={layout === 'grid' 
                          ? "w-full h-32 object-cover rounded-xl" 
                          : "w-16 h-16 object-cover rounded-lg"
                        }
                      />
                      {layout === 'grid' && (
                        <Badge className={`absolute top-2 left-2 ${getReferenceTypeColor(reference.reference_type)}`}>
                          {getReferenceTypeIcon(reference.reference_type)}
                          <span className="ml-1 capitalize">{reference.reference_type}</span>
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex-1 space-y-2">
                      <div>
                        <h4 className="font-medium text-foreground group-hover:text-primary transition-colors line-clamp-1">
                          {reference.product.name}
                        </h4>
                        {layout === 'list' && (
                          <p className="text-sm text-muted-foreground line-clamp-1">
                            {reference.product.description}
                          </p>
                        )}
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-1 font-semibold text-primary">
                          <DollarSign className="h-3 w-3" />
                          <span className="text-sm">{formatPrice(reference.product.price)}</span>
                        </div>
                        {layout === 'list' && (
                          <Badge className={getReferenceTypeColor(reference.reference_type)}>
                            {reference.reference_type}
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex gap-2">
                        <Button 
                          asChild 
                          variant="outline" 
                          size="sm"
                          className="flex-1 glass-effect border-white/20 hover:glass-effect-subtle min-h-[36px]"
                        >
                          <Link href={`/products/${reference.product.slug}`}>
                            <ExternalLink className="h-3 w-3 mr-1" />
                            View
                          </Link>
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          className="glass-effect border-white/20 hover:glass-effect-subtle min-h-[36px] min-w-[36px] p-0"
                        >
                          <ShoppingCart className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Call to Action */}
      <Card className="glass-effect border border-primary/20 neo-shadow bg-primary/5">
        <CardContent className="p-6 text-center">
          <div className="space-y-3">
            <div className="flex items-center justify-center gap-2">
              <Package className="h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold text-foreground">Explore Our Full Collection</h3>
            </div>
            <p className="text-muted-foreground">
              Discover more premium tennis equipment and gear at Tennis Whisperer
            </p>
            <Button asChild className="bg-primary hover:bg-primary/90 text-primary-foreground min-h-[44px]">
              <Link href="/products">
                <ShoppingCart className="h-4 w-4 mr-2" />
                Shop All Products
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
