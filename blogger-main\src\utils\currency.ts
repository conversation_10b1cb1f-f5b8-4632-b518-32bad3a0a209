/**
 * Currency formatting utilities for South African Rand (ZAR)
 */

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

export const formatCurrencyCompact = (amount: number): string => {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
    notation: 'compact',
    minimumFractionDigits: 0,
    maximumFractionDigits: 1,
  }).format(amount);
};

export const parseCurrency = (currencyString: string): number => {
  // Remove currency symbol and spaces, then parse
  const cleanString = currencyString.replace(/[R\s,]/g, '');
  return parseFloat(cleanString) || 0;
};

// South African tax and shipping constants
export const TAX_RATE = 0.15; // 15% VAT
export const FREE_SHIPPING_THRESHOLD = 750; // R750
export const SHIPPING_COST = 149.99; // R149.99

export const calculateTax = (amount: number): number => {
  return amount * TAX_RATE;
};

export const calculateShipping = (subtotal: number): number => {
  return subtotal >= FREE_SHIPPING_THRESHOLD ? 0 : SHIPPING_COST;
};

export const calculateTotal = (subtotal: number, discount = 0): number => {
  const tax = calculateTax(subtotal);
  const shipping = calculateShipping(subtotal);
  return subtotal - discount + tax + shipping;
};
