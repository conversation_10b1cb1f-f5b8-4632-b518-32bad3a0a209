"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { verifyDatabaseTables, testStudentDashboardQueries } from '@/utils/database-verification';
import { createBrowserClient } from '@supabase/ssr';

export default function TestDatabasePage() {
  const [verification, setVerification] = useState<any>(null);
  const [queryTest, setQueryTest] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    loadUser();
  }, []);

  const loadUser = async () => {
    const supabase = createBrowserClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );
    
    const { data: { user } } = await supabase.auth.getUser();
    setUser(user);
  };

  const runVerification = async () => {
    setLoading(true);
    try {
      const results = await verifyDatabaseTables();
      setVerification(results);
    } catch (error) {
      console.error('Verification failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const runQueryTest = async () => {
    setLoading(true);
    try {
      const results = await testStudentDashboardQueries(user?.id);
      setQueryTest(results);
    } catch (error) {
      console.error('Query test failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const createSampleData = async () => {
    setLoading(true);
    try {
      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );

      // Insert sample programs
      const { error: programError } = await supabase
        .from('mentorship_programs')
        .upsert([
          {
            id: '550e8400-e29b-41d4-a716-446655440001',
            name: '6-Month Tennis Mastery',
            description: 'Comprehensive tennis training program for beginners to intermediate players',
            duration_months: 6,
            price_monthly: 299.99,
            price_upfront: 1599.99,
            features: ["Weekly 1-on-1 sessions", "Video analysis", "Training plans", "Progress tracking"]
          },
          {
            id: '550e8400-e29b-41d4-a716-************',
            name: '12-Month Pro Development',
            description: 'Advanced tennis coaching for competitive players',
            duration_months: 12,
            price_monthly: 399.99,
            price_upfront: 4199.99,
            features: ["Bi-weekly sessions", "Tournament preparation", "Mental coaching", "Nutrition guidance"]
          }
        ]);

      if (programError) {
        console.error('Program insert error:', programError);
      }

      // Insert sample resources
      const { error: resourceError } = await supabase
        .from('resources')
        .upsert([
          {
            id: '550e8400-e29b-41d4-a716-************',
            title: 'Tennis Fundamentals Video Series',
            description: 'Complete video series covering basic tennis techniques and fundamentals',
            type: 'video',
            category: 'Fundamentals',
            format: 'mp4',
            file_path: 'resources/tennis-fundamentals.mp4',
            size_bytes: 524288000,
            download_count: 45,
            created_by: user?.id
          },
          {
            id: '550e8400-e29b-41d4-a716-************',
            title: 'Serve Technique Guide',
            description: 'Comprehensive PDF guide to improving your tennis serve',
            type: 'document',
            category: 'Technique',
            format: 'pdf',
            file_path: 'resources/serve-guide.pdf',
            size_bytes: 2048000,
            download_count: 32,
            created_by: user?.id
          },
          {
            id: '550e8400-e29b-41d4-a716-************',
            title: 'Weekly Training Plan',
            description: 'Structured training program for intermediate players',
            type: 'training',
            category: 'Training Plans',
            format: 'pdf',
            file_path: 'resources/weekly-plan.pdf',
            size_bytes: 1024000,
            download_count: 28,
            created_by: user?.id
          }
        ]);

      if (resourceError) {
        console.error('Resource insert error:', resourceError);
      }

      // Create a sample mentor if user exists
      if (user) {
        const { error: mentorError } = await supabase
          .from('mentors')
          .upsert([
            {
              id: '550e8400-e29b-41d4-a716-************',
              user_id: user.id,
              bio: 'Professional tennis coach with 15 years of experience. Former college player and certified instructor.',
              specialties: ["Serve technique", "Backhand improvement", "Mental game", "Tournament preparation"],
              experience_years: 15,
              availability: {
                "monday": ["09:00", "17:00"],
                "tuesday": ["09:00", "17:00"],
                "wednesday": ["09:00", "17:00"],
                "thursday": ["09:00", "17:00"],
                "friday": ["09:00", "17:00"]
              }
            }
          ]);

        if (mentorError) {
          console.error('Mentor insert error:', mentorError);
        }

        // Create a sample enrollment
        const { error: enrollmentError } = await supabase
          .from('student_enrollments')
          .upsert([
            {
              id: '550e8400-e29b-41d4-a716-446655440008',
              student_id: user.id,
              program_id: '550e8400-e29b-41d4-a716-446655440001',
              mentor_id: '550e8400-e29b-41d4-a716-************',
              start_date: '2025-01-01T00:00:00Z',
              end_date: '2025-07-01T00:00:00Z',
              payment_type: 'monthly',
              status: 'active'
            }
          ]);

        if (enrollmentError) {
          console.error('Enrollment insert error:', enrollmentError);
        }

        // Create sample sessions
        const { error: sessionError } = await supabase
          .from('mentorship_sessions')
          .upsert([
            {
              id: '550e8400-e29b-41d4-a716-446655440009',
              enrollment_id: '550e8400-e29b-41d4-a716-446655440008',
              scheduled_at: '2025-01-25T10:00:00Z',
              duration_minutes: 60,
              status: 'scheduled',
              notes: 'Focus on serve technique and footwork'
            },
            {
              id: '550e8400-e29b-41d4-a716-44665544000a',
              enrollment_id: '550e8400-e29b-41d4-a716-446655440008',
              scheduled_at: '2025-01-18T10:00:00Z',
              duration_minutes: 60,
              status: 'completed',
              notes: 'Worked on backhand technique - great improvement shown'
            },
            {
              id: '550e8400-e29b-41d4-a716-44665544000b',
              enrollment_id: '550e8400-e29b-41d4-a716-446655440008',
              scheduled_at: '2025-02-01T10:00:00Z',
              duration_minutes: 60,
              status: 'scheduled',
              notes: 'Progress review and goal setting for next month'
            }
          ]);

        if (sessionError) {
          console.error('Session insert error:', sessionError);
        }
      }

      alert('Sample data created successfully!');
      
      // Re-run verification
      await runVerification();
      await runQueryTest();
    } catch (error) {
      console.error('Sample data creation failed:', error);
      alert('Failed to create sample data. Check console for details.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-3xl font-bold">Database Verification</h1>
      
      <div className="flex gap-4">
        <Button onClick={runVerification} disabled={loading}>
          Verify Database Tables
        </Button>
        <Button onClick={runQueryTest} disabled={loading}>
          Test Student Queries
        </Button>
        <Button onClick={createSampleData} disabled={loading || !user}>
          Create Sample Data
        </Button>
      </div>

      {user && (
        <Card className="p-4">
          <h3 className="font-semibold mb-2">Current User</h3>
          <p>ID: {user.id}</p>
          <p>Email: {user.email}</p>
        </Card>
      )}

      {verification && (
        <Card className="p-4">
          <h3 className="font-semibold mb-4">Database Verification Results</h3>
          
          <div className="space-y-2 mb-4">
            <div className="flex items-center gap-2">
              <span>Authentication:</span>
              <Badge variant={verification.authentication ? "default" : "destructive"}>
                {verification.authentication ? "✓ Connected" : "✗ Failed"}
              </Badge>
            </div>
          </div>

          <div className="space-y-2 mb-4">
            <h4 className="font-medium">Tables:</h4>
            {Object.entries(verification.tables).map(([table, exists]) => (
              <div key={table} className="flex items-center gap-2">
                <span>{table}:</span>
                <Badge variant={exists ? "default" : "destructive"}>
                  {exists ? "✓ Exists" : "✗ Missing"}
                </Badge>
              </div>
            ))}
          </div>

          <div className="space-y-2 mb-4">
            <h4 className="font-medium">Sample Data Count:</h4>
            {Object.entries(verification.sampleData).map(([type, count]) => (
              <div key={type} className="flex items-center gap-2">
                <span>{type}:</span>
                <Badge variant={(count as number) > 0 ? "default" : "secondary"}>
                  {count as number} records
                </Badge>
              </div>
            ))}
          </div>

          {verification.errors.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium text-red-600">Errors:</h4>
              {verification.errors.map((error: string, index: number) => (
                <p key={index} className="text-sm text-red-600 bg-red-50 p-2 rounded">
                  {error}
                </p>
              ))}
            </div>
          )}
        </Card>
      )}

      {queryTest && (
        <Card className="p-4">
          <h3 className="font-semibold mb-4">Query Test Results</h3>
          <p className="mb-4">User ID: {queryTest.userId}</p>
          
          {Object.entries(queryTest.queries).map(([query, result]: [string, any]) => (
            <div key={query} className="mb-4 p-3 border rounded">
              <div className="flex items-center gap-2 mb-2">
                <span className="font-medium">{query}:</span>
                <Badge variant={result.success ? "default" : "destructive"}>
                  {result.success ? "✓ Success" : "✗ Failed"}
                </Badge>
                <Badge variant="secondary">{result.count} records</Badge>
              </div>
              {result.error && (
                <p className="text-sm text-red-600 bg-red-50 p-2 rounded">
                  {result.error}
                </p>
              )}
            </div>
          ))}
        </Card>
      )}
    </div>
  );
}
