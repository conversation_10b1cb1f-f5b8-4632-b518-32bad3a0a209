import { NextRequest, NextResponse } from 'next/server';
import { createClient, createServiceRoleClient } from '@/utils/supabase/server';

/**
 * Debug endpoint for admin authentication and database connectivity
 * GET /api/admin/debug
 */
export async function GET(request: NextRequest) {
  const debugInfo: any = {
    timestamp: new Date().toISOString(),
    environment: {
      hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
    },
    authentication: {},
    database: {},
    orders: {}
  };

  try {
    console.log('🔍 Admin Debug - Starting comprehensive check');

    // 1. Test basic Supabase client creation
    try {
      const supabase = await createClient();
      debugInfo.supabaseClient = { created: true };
      
      // 2. Test authentication
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      debugInfo.authentication = {
        hasUser: !!user,
        userId: user?.id,
        userEmail: user?.email,
        authError: authError?.message
      };

      if (user) {
        // 3. Test user data fetch
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('id, email, full_name, role, created_at')
          .eq('id', user.id)
          .single();

        debugInfo.authentication.userData = {
          found: !!userData,
          role: userData?.role,
          email: userData?.email,
          fullName: userData?.full_name,
          error: userError?.message
        };

        // 4. Test service role client
        try {
          const serviceSupabase = createServiceRoleClient();
          debugInfo.serviceClient = { created: true };

          // 5. Test database connectivity with service client
          const { data: ordersCount, error: countError } = await serviceSupabase
            .from('orders')
            .select('id', { count: 'exact', head: true });

          debugInfo.database = {
            connected: !countError,
            ordersTableExists: !countError,
            totalOrders: ordersCount?.length || 0,
            error: countError?.message
          };

          // 6. Test fetching actual orders
          const { data: sampleOrders, error: ordersError } = await serviceSupabase
            .from('orders')
            .select(`
              id,
              status,
              payment_status,
              total_amount,
              created_at,
              shipping_details,
              users!user_id (
                email,
                full_name
              )
            `)
            .limit(3);

          debugInfo.orders = {
            fetchSuccessful: !ordersError,
            sampleCount: sampleOrders?.length || 0,
            sampleOrders: sampleOrders?.map((order: any) => ({
              id: order.id,
              status: order.status,
              paymentStatus: order.payment_status,
              totalAmount: order.total_amount,
              customerEmail: order.users?.email,
              customerPhone: order.shipping_details?.phone || null
            })) || [],
            error: ordersError?.message
          };

          // 7. Test table schema
          const { data: tableInfo, error: schemaError } = await serviceSupabase
            .rpc('get_table_columns', { table_name: 'orders' })
            .limit(20);

          debugInfo.schema = {
            fetchSuccessful: !schemaError,
            columns: tableInfo || [],
            error: schemaError?.message
          };

        } catch (serviceError: any) {
          debugInfo.serviceClient = {
            created: false,
            error: serviceError.message
          };
        }

      }

    } catch (clientError: any) {
      debugInfo.supabaseClient = {
        created: false,
        error: clientError.message
      };
    }

    // 8. Overall health check
    debugInfo.healthCheck = {
      authenticationWorking: !!debugInfo.authentication.hasUser && debugInfo.authentication.userData?.role === 'admin',
      databaseConnected: debugInfo.database.connected,
      ordersAccessible: debugInfo.orders.fetchSuccessful,
      overallStatus: 'unknown'
    };

    if (debugInfo.healthCheck.authenticationWorking && 
        debugInfo.healthCheck.databaseConnected && 
        debugInfo.healthCheck.ordersAccessible) {
      debugInfo.healthCheck.overallStatus = 'healthy';
    } else if (!debugInfo.authentication.hasUser) {
      debugInfo.healthCheck.overallStatus = 'authentication_required';
    } else if (debugInfo.authentication.userData?.role !== 'admin') {
      debugInfo.healthCheck.overallStatus = 'insufficient_permissions';
    } else if (!debugInfo.healthCheck.databaseConnected) {
      debugInfo.healthCheck.overallStatus = 'database_error';
    } else {
      debugInfo.healthCheck.overallStatus = 'partial_failure';
    }

    console.log('✅ Admin Debug - Check completed:', debugInfo.healthCheck.overallStatus);

    return NextResponse.json({
      success: true,
      debug: debugInfo
    });

  } catch (error: any) {
    console.error('💥 Admin Debug - Unexpected error:', error);
    
    debugInfo.unexpectedError = {
      message: error.message,
      stack: error.stack,
      name: error.name
    };

    return NextResponse.json({
      success: false,
      error: 'Debug check failed',
      debug: debugInfo
    }, { status: 500 });
  }
}

/**
 * Test order creation for debugging
 * POST /api/admin/debug
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    if (action === 'create_test_order') {
      const supabase = await createClient();
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      const serviceSupabase = createServiceRoleClient();

      // Create a test order
      const testOrder = {
        user_id: user.id,
        items: [
          {
            name: 'Test Tennis Racket',
            price: 1500.00,
            quantity: 1,
            product_id: 'test-001'
          }
        ],
        shipping_details: {
          name: 'Test Customer',
          email: user.email,
          phone: '+27123456789',
          address: '123 Test Street',
          city: 'Test City',
          postal_code: '1234',
          country: 'South Africa'
        },
        status: 'pending',
        payment_status: 'pending',
        total_amount: 1500.00,
        notes: 'Test order created for debugging',
        shipping_method: 'standard'
      };

      const { data: newOrder, error } = await serviceSupabase
        .from('orders')
        .insert([testOrder])
        .select()
        .single();

      if (error) {
        return NextResponse.json(
          { error: 'Failed to create test order', details: error.message },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: 'Test order created successfully',
        orderId: newOrder.id
      });
    }

    return NextResponse.json(
      { error: 'Unknown action' },
      { status: 400 }
    );

  } catch (error: any) {
    return NextResponse.json(
      { error: 'Failed to process request', details: error.message },
      { status: 500 }
    );
  }
}
