import { NextRequest, NextResponse } from 'next/server';
import { createClient, createServiceRoleClient } from '@/utils/supabase/server';

/**
 * GET /api/admin/dashboard/recent-orders
 * Get recent orders for dashboard overview
 */
export async function GET(request: NextRequest) {
  try {
    console.log('GET /api/admin/dashboard/recent-orders - Request received');

    // Check authentication
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin role
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || userData.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 });
    }

    console.log('GET /api/admin/dashboard/recent-orders - Admin access confirmed');

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');

    // Use service role client for database operations
    const serviceSupabase = createServiceRoleClient();

    // Fetch recent orders with customer information
    const { data: orders, error } = await serviceSupabase
      .from('orders')
      .select(`
        id,
        status,
        payment_status,
        total_amount,
        created_at,
        updated_at,
        users!user_id (
          email,
          full_name
        )
      `)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('GET /api/admin/dashboard/recent-orders - Database error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch recent orders', details: error.message },
        { status: 500 }
      );
    }

    // Transform data for frontend
    const transformedOrders = orders?.map(order => {
      // Handle the users relationship - it could be an object or null
      const user = Array.isArray(order.users) ? order.users[0] : order.users;

      return {
        id: order.id,
        customer_name: user?.full_name || 'Unknown Customer',
        customer_email: user?.email || '',
        status: order.status,
        payment_status: order.payment_status,
        total_amount: order.total_amount,
        created_at: order.created_at,
        updated_at: order.updated_at,
        formatted_date: new Date(order.created_at).toLocaleDateString('en-ZA', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        formatted_amount: `R ${order.total_amount.toLocaleString('en-ZA', { minimumFractionDigits: 2 })}`
      };
    }) || [];

    // Calculate weekly stats
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    
    const weeklyOrders = transformedOrders.filter(order => 
      new Date(order.created_at) >= weekAgo
    );

    console.log('GET /api/admin/dashboard/recent-orders - Success:', {
      totalOrders: transformedOrders.length,
      weeklyOrders: weeklyOrders.length
    });

    return NextResponse.json({
      success: true,
      data: {
        orders: transformedOrders,
        weekly_count: weeklyOrders.length,
        total_count: transformedOrders.length
      }
    });

  } catch (error: any) {
    console.error('GET /api/admin/dashboard/recent-orders - General error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
