{"mcpServers": {"puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}, "firecrawl": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-firecrawl"], "env": {"FIRECRAWL_API_KEY": "fc-385d1d0ffde44b4fa330ab879a223d34"}}, "sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "magic": {"command": "npx", "args": ["-y", "@21st-dev/magic@latest"], "env": {"API_KEY": "905097d7988fd740cf8870f47a3e60f72718c6490700b2282ad0f121beb35bd6"}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "C:\\Users\\<USER>\\Downloads\\Projects\\Tennis-Gear"]}}}