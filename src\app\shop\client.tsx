"use client";

import { useState } from "react";
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import { Filter, Grid3X3, List, Search, SlidersHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ProductCard } from "@/components/product-card";
import { SortSelect } from "@/components/sort-select";
import { useProducts, useCategories } from "@/hooks";
import { cn } from "@/lib/utils";

// Default categories for filtering (will be replaced with data from Supabase)
const defaultCategories = [
  { name: "All", value: "all" },
  { name: "Rackets", value: "rackets" },
  { name: "Balls", value: "balls" },
  { name: "Footwear", value: "footwear" },
  { name: "Apparel", value: "apparel" },
  { name: "Accessories", value: "accessories" }
];

// Category data with icons for mobile design
const categoryData = [
  { name: "All", value: "all", icon: "🏪", gradient: "gradient-blue" },
  { name: "Footwear", value: "footwear", icon: "👟", gradient: "gradient-blue" },
  { name: "Rackets", value: "rackets", icon: "🎾", gradient: "gradient-blue" },
  { name: "Apparel", value: "apparel", icon: "👕", gradient: "gradient-blue" },
  { name: "Accessories", value: "accessories", icon: "🎒", gradient: "gradient-blue" },
];

export default function ShopPageClient() {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [priceMin, setPriceMin] = useState('');
  const [priceMax, setPriceMax] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Use React Query hooks for data fetching
  const {
    data: products = [],
    isLoading: productsLoading,
    error: productsError
  } = useProducts({
    category: selectedCategory !== 'all' ? selectedCategory : undefined,
    search: searchQuery || undefined,
  });

  const {
    data: categoriesData = [],
    isLoading: categoriesLoading
  } = useCategories();

  // Transform categories for UI
  const categories = [
    { name: "All", value: "all" },
    ...categoriesData.map(cat => ({
      name: cat.name,
      value: cat.name.toLowerCase()
    }))
  ];

  const loading = productsLoading || categoriesLoading;

  // Handle category selection
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    // React Query will automatically refetch with new category
  };

  // Handle search
  const handleSearch = () => {
    // React Query will automatically refetch with new search query
    // The search query is already set in state
  };

  // Apply price filtering to the fetched products
  const filteredProducts = products.filter(product => {
    let passesFilter = true;

    // Apply price filter if values are provided
    if (priceMin && product.price < parseFloat(priceMin)) {
      passesFilter = false;
    }

    if (priceMax && product.price > parseFloat(priceMax)) {
      passesFilter = false;
    }

    return passesFilter;
  });

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />

      <main className="flex-grow container mx-auto px-4 py-8 pb-20 md:pb-8">
        {/* Enhanced Mobile Hero Section */}
        <div className="mb-2 mt-10">
          <div className="text-center mb-2">
            <h1 className="text-3xl md:text-4xl font-bold mb-3">
              <span className="text-foreground">Discover</span>
              <br />
              <span className="text-gradient">Premium Tennis Gear</span>
            </h1>
            <p className="text-muted-foreground text-base max-w-md mx-auto leading-relaxed">
              Explore our curated collection of professional tennis equipment
            </p>
          </div>

          {/* Enhanced Search Bar */}
          <div className="relative mb-6">
            <div className="relative group">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground group-focus-within:text-primary transition-colors" />
              <Input
                placeholder="Search for tennis gear..."
                className="pl-12 pr-14 h-14 rounded-2xl form-input neo-shadow-inset focus:neo-shadow-light transition-neo text-base"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
              <Button
                size="sm"
                onClick={handleSearch}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-10 w-10 p-0 rounded-xl btn-primary neo-shadow-light hover:neo-shadow transition-neo"
                aria-label="Search"
              >
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Enhanced Category Pills */}
          <div className="mb-4">
            <div className="flex gap-4 overflow-x-auto  pb-3 scrollbar-hide">
              {categoryData.map((category) => (
                <button
                  key={category.value}
                  onClick={() => setSelectedCategory(category.value)}
                  className={cn(
                    "flex flex-col items-center justify-center min-w-[80px] h-[80px] rounded-2xl transition-all duration-300 flex-shrink-0 group focus-ring",
                    selectedCategory === category.value
                      ? `${category.gradient} text-white neo-shadow scale-105`
                      : "modern-card text-muted-foreground hover:text-foreground"
                  )}
                >
                  <span className="text-2xl mb-1.5 group-hover:scale-110 transition-transform duration-200">{category.icon}</span>
                  <span className="text-xs font-semibold">{category.name}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Sort and Filter Row */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-2">
              <h2 className="text-lg font-semibold">
                {loading ? 'Loading...' : `${filteredProducts.length} Products`}
              </h2>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="rounded-xl neo-shadow-light"
              >
                <SlidersHorizontal className="h-4 w-4 mr-2" />
                Sort By
              </Button>
            </div>
          </div>
        </div>

        {/* Shop content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Enhanced Filters sidebar */}
          <div className="lg:col-span-1">
            <div className="modern-card p-6 sticky top-6 neo-shadow-light">
              <div className="flex items-center justify-between mb-6">
                <h2 className="font-bold text-xl flex items-center gap-3">
                  <div className="p-2 rounded-full bg-primary/15">
                    <Filter className="w-5 h-5 text-primary" />
                  </div>
                  Filters
                </h2>
              </div>

              {/* Enhanced Categories */}
              <div className="mb-8">
                <h3 className="font-semibold mb-4 text-foreground">Categories</h3>
                <div className="space-y-3">
                  {categories.map((category) => (
                    <div key={category.value} className="flex items-center group">
                      <input
                        type="radio"
                        id={`category-${category.value}`}
                        name="category"
                        checked={selectedCategory === category.value}
                        onChange={() => handleCategoryChange(category.value)}
                        className="mr-3 h-4 w-4 rounded border-border text-primary focus:ring-primary focus:ring-2 transition-all"
                      />
                      <label
                        htmlFor={`category-${category.value}`}
                        className="text-sm text-muted-foreground group-hover:text-foreground transition-colors cursor-pointer flex-1"
                      >
                        {category.name}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Enhanced Price Range */}
              <div className="mb-6">
                <h3 className="font-semibold mb-4 text-foreground">Price Range</h3>
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Input
                      type="number"
                      placeholder="Min"
                      value={priceMin}
                      onChange={(e) => setPriceMin(e.target.value)}
                      className="form-input h-11 rounded-xl"
                    />
                  </div>
                  <div>
                    <Input
                      type="number"
                      placeholder="Max"
                      value={priceMax}
                      onChange={(e) => setPriceMax(e.target.value)}
                      className="form-input h-11 rounded-xl"
                    />
                  </div>
                </div>
                <div className="text-xs text-muted-foreground mt-3 p-3 rounded-lg bg-muted/20">
                  💡 Price filters are applied automatically
                </div>
              </div>
            </div>
          </div>

          {/* Products grid */}
          <div className="lg:col-span-3">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
              <div className="mb-4 sm:mb-0">
                <h2 className="text-xl font-semibold">
                  {loading ? 'Loading products...' : `${filteredProducts.length} Products`}
                </h2>
              </div>
              <div className="flex items-center gap-2 w-full sm:w-auto">
                <SortSelect />
                <div className="flex border border-border rounded-md">
                  <Button variant="ghost" size="icon" className="rounded-r-none">
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" className="rounded-l-none">
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {loading ? (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {[...Array(8)].map((_, i) => (
                  <div key={i} className="modern-card h-80 skeleton rounded-2xl neo-shadow-light">
                    <div className="p-4 space-y-3">
                      <div className="aspect-square bg-muted/20 rounded-xl"></div>
                      <div className="space-y-2">
                        <div className="h-4 bg-muted/20 rounded w-3/4"></div>
                        <div className="h-3 bg-muted/20 rounded w-1/2"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : filteredProducts.length === 0 ? (
              <div className="text-center py-16">
                <div className="modern-card p-8 max-w-md mx-auto">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted/20 flex items-center justify-center">
                    <Search className="w-8 h-8 text-muted-foreground" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3">No products found</h3>
                  <p className="text-muted-foreground mb-6 leading-relaxed">
                    We couldn't find any products matching your criteria. Try adjusting your search or filters.
                  </p>
                  <Button
                    onClick={() => {
                      setSelectedCategory('all');
                      setSearchQuery('');
                      setPriceMin('');
                      setPriceMax('');
                    }}
                    className="btn-primary rounded-xl h-12 px-6 neo-shadow-light hover:neo-shadow transition-neo"
                  >
                    Reset All Filters
                  </Button>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {filteredProducts.map((product, index) => (
                  <div
                    key={product.id}
                    className="animate-virtra-fade-in"
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <ProductCard product={product} />
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
