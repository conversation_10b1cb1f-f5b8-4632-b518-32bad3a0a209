import { createClient } from "@/utils/supabase/server";
import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    // Prevent automatic calls during development/compilation
    const userAgent = request.headers.get('user-agent') || '';
    if (userAgent.includes('Next.js') || userAgent.includes('webpack')) {
      return NextResponse.json({
        success: false,
        error: "Test endpoint not available during compilation"
      }, { status: 503 });
    }

    // Validate request has proper content type
    const contentType = request.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      return NextResponse.json({
        success: false,
        error: "Content-Type must be application/json"
      }, { status: 400 });
    }

    const body = await request.json();
    const { email, password, fullName } = body;

    // Validate required parameters
    if (!email || !password || !fullName) {
      return NextResponse.json({
        success: false,
        error: "Missing required parameters: email, password, fullName"
      }, { status: 400 });
    }

    const supabase = await createClient();

    console.log("Testing auth.signUp with:", { email, fullName });

    // Clean up any existing profile first
    const { data: existingProfile } = await supabase
      .from('users')
      .select('id, email')
      .eq('email', email)
      .single();

    if (existingProfile) {
      console.log("Cleaning up existing profile:", existingProfile.id);
      await supabase
        .from('users')
        .delete()
        .eq('email', email);
    }

    // Test auth.signUp
    const { data: { user }, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
          email: email,
          role: 'admin'
        }
      }
    });

    if (error) {
      console.error("Auth sign-up error:", error);
      return NextResponse.json({
        success: false,
        error: {
          code: error.code,
          message: error.message,
          status: error.status,
          name: error.name
        }
      }, { status: 400 });
    }

    if (user) {
      console.log("Auth sign-up successful:", user.id);

      // Wait for trigger to create profile
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Check if profile was created
      const { data: profile, error: profileError } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();

      if (profileError) {
        console.error("Profile check error:", profileError);
        return NextResponse.json({
          success: false,
          authSuccess: true,
          profileError: {
            code: profileError.code,
            message: profileError.message
          }
        }, { status: 400 });
      }

      return NextResponse.json({
        success: true,
        message: "Auth sign-up and profile creation successful",
        user: {
          id: user.id,
          email: user.email
        },
        profile: {
          id: profile.id,
          email: profile.email,
          role: profile.role
        }
      });
    }

    return NextResponse.json({
      success: false,
      error: "No user returned from auth.signUp"
    }, { status: 400 });

  } catch (error: any) {
    console.error("API error:", error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    const supabase = await createClient();
    
    // Check auth system status
    const { data: { session }, error } = await supabase.auth.getSession();

    return NextResponse.json({
      success: true,
      message: "Auth system accessible",
      hasSession: !!session,
      sessionError: error?.message || null
    });

  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
