"use client";

import { useState, useEffect } from "react";
import { createBrowserClient } from '@supabase/ssr';
import { redirect } from "next/navigation";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Loader2,
  AlertCircle,
  Calendar,
  Clock,
  Users,
  TrendingUp,
  Award,
  Target
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { getStudentProgress, getStudentAllSessions, getStudentEnrollments } from "@/utils/supabase/mentorship-utils";
import Link from "next/link";

export default function ProgressPage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [progressData, setProgressData] = useState<any>(null);
  const [sessions, setSessions] = useState<any[]>([]);
  const [enrollments, setEnrollments] = useState<any[]>([]);

  useEffect(() => {
    async function loadProgressData() {
      try {
        setLoading(true);
        setError(null);

        const supabase = createBrowserClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
        );
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          redirect("/auth/sign-in");
          return;
        }

        // Load all progress-related data
        const [progressResult, sessionsResult, enrollmentsResult] = await Promise.all([
          getStudentProgress(user.id),
          getStudentAllSessions(user.id),
          getStudentEnrollments(user.id)
        ]);

        if (progressResult.error) {
          throw new Error('Failed to load progress data');
        }

        if (sessionsResult.error) {
          throw new Error('Failed to load sessions data');
        }

        if (enrollmentsResult.error) {
          throw new Error('Failed to load enrollments data');
        }

        setProgressData(progressResult.data);
        setSessions(sessionsResult.data || []);
        setEnrollments(enrollmentsResult.data || []);
      } catch (error: any) {
        console.error('Error loading progress data:', error);
        setError(error.message || 'Failed to load progress data');
      } finally {
        setLoading(false);
      }
    }

    loadProgressData();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading your progress...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="p-4 md:p-6 space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <h1 className="text-2xl md:text-3xl font-bold">Your Progress</h1>
        <div className="flex gap-2">
          <Link href="/student-dashboard/schedule">
            <Button>
              <Calendar className="h-4 w-4 mr-2" />
              Schedule Session
            </Button>
          </Link>
        </div>
      </div>

      {/* Progress Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-6">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <Target className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Overall Progress</p>
              <p className="text-2xl font-bold">{progressData?.completionPercentage || 0}%</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-green-100 dark:bg-green-900 rounded-lg">
              <Award className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Sessions Completed</p>
              <p className="text-2xl font-bold">{progressData?.completedSessions || 0}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-lg">
              <Clock className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Upcoming Sessions</p>
              <p className="text-2xl font-bold">{progressData?.upcomingSessions || 0}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-orange-100 dark:bg-orange-900 rounded-lg">
              <TrendingUp className="h-6 w-6 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Active Programs</p>
              <p className="text-2xl font-bold">{progressData?.activeEnrollments || 0}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Detailed Progress */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Progress Breakdown */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-6">Progress Breakdown</h2>
          <div className="space-y-6">
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>Overall Completion</span>
                <span>{progressData?.completionPercentage || 0}%</span>
              </div>
              <Progress value={progressData?.completionPercentage || 0} className="h-2" />
            </div>

            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>Sessions Progress</span>
                <span>
                  {progressData?.completedSessions || 0} / {progressData?.totalProgramSessions || 0}
                </span>
              </div>
              <Progress
                value={
                  progressData?.totalProgramSessions > 0
                    ? (progressData.completedSessions / progressData.totalProgramSessions) * 100
                    : 0
                }
                className="h-2"
              />
            </div>

            {enrollments.length > 0 && (
              <div>
                <h3 className="font-medium mb-3">Program Progress</h3>
                <div className="space-y-3">
                  {enrollments.map((enrollment: any) => (
                    <div key={enrollment.id} className="p-3 bg-muted/50 rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-medium text-sm">
                          {enrollment.program?.name || 'Mentorship Program'}
                        </span>
                        <Badge variant={enrollment.status === 'active' ? 'default' : 'secondary'}>
                          {enrollment.status}
                        </Badge>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Duration: {enrollment.program?.duration_months || 6} months
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </Card>

        {/* Recent Sessions */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-6">Recent Sessions</h2>
          <ScrollArea className="h-[400px]">
            <div className="space-y-4">
              {sessions.length > 0 ? (
                sessions.slice(0, 10).map((session: any) => (
                  <div key={session.id} className="p-4 border rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-medium">
                        {session.notes || 'Mentorship Session'}
                      </h4>
                      <Badge
                        variant={
                          session.status === 'completed' ? 'default' :
                          session.status === 'scheduled' ? 'secondary' :
                          'destructive'
                        }
                      >
                        {session.status}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        {new Date(session.scheduled_at).toLocaleDateString()}
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        {new Date(session.scheduled_at).toLocaleTimeString([], {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        {session.enrollment?.mentor?.user?.full_name || 'Mentor'}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No sessions found</p>
                  <p className="text-sm">Schedule your first session to get started</p>
                </div>
              )}
            </div>
          </ScrollArea>
        </Card>
      </div>
    </div>
  );
}
