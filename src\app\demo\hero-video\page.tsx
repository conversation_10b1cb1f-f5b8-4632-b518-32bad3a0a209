"use client";

import { Suspense } from "react";
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import HeroVideo from "@/components/demo/hero-video";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft, Play, Target, Sparkles } from "lucide-react";

export default function HeroVideoDemo() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-green-50/30 px-6 rounded-lg mx-a">
      <Navbar />
      
      {/* Demo Header */}
      <div className="pt-24 pb-8">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <Button asChild variant="outline" size="sm">
                <Link href="/">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Home
                </Link>
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-slate-900">Video Background Hero Demo</h1>
                <p className="text-slate-600">Interactive hotspots over tennis action video</p>
              </div>
            </div>
            
            {/* Demo Navigation */}
            <div className="flex gap-2">
              <Button asChild variant="outline" size="sm">
                <Link href="/demo/hero-3d">3D Demo</Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href="/demo/hero-ballpit">Ballpit Demo</Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href="/demo/hero-ballpit-cards">Ballpit + Cards</Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href="/demo/hero-gamified">Game Demo</Link>
              </Button>
            </div>
          </div>

          {/* Demo Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-green-100">
              <div className="flex items-center gap-2 mb-2">
                <Play className="w-4 h-4 text-green-600" />
                <span className="text-sm font-semibold">Video Background</span>
              </div>
              <p className="text-xs text-slate-600">Auto-playing tennis action with mobile optimization</p>
            </div>
            
            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-green-100">
              <div className="flex items-center gap-2 mb-2">
                <Target className="w-4 h-4 text-green-600" />
                <span className="text-sm font-semibold">Interactive Hotspots</span>
              </div>
              <p className="text-xs text-slate-600">Click equipment in video to see product details</p>
            </div>
            
            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-green-100">
              <div className="flex items-center gap-2 mb-2">
                <Sparkles className="w-4 h-4 text-green-600" />
                <span className="text-sm font-semibold">Particle Effects</span>
              </div>
              <p className="text-xs text-slate-600">Subtle animations around interactive elements</p>
            </div>
          </div>
        </div>
      </div>

      {/* Video Hero Section */}
      <Suspense fallback={
        <div className="h-[600px] flex items-center justify-center bg-slate-100 px-4">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-4  bg-green-600 rounded-full flex items-center justify-center animate-pulse">
              <Play className="w-8 h-8 text-white" />
            </div>
            <p className="text-slate-600">Loading Video Experience...</p>
          </div>
        </div>
      }>
        <HeroVideo />
      </Suspense>

      {/* Demo Info Section */}
      <section className="py-16 bg-white/50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-8">Video Hero Features</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-slate-900">Technical Implementation</h3>
                <ul className="space-y-2 text-slate-600">
                  <li>• HTML5 video with multiple format support</li>
                  <li>• Interactive hotspot positioning system</li>
                  <li>• Framer Motion for smooth animations</li>
                  <li>• Intersection Observer for scroll triggers</li>
                  <li>• Mobile-optimized video loading</li>
                </ul>
              </div>
              
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-slate-900">User Experience</h3>
                <ul className="space-y-2 text-slate-600">
                  <li>• Auto-play with muted audio (mobile-friendly)</li>
                  <li>• Touch-optimized hotspot interactions</li>
                  <li>• Product cards with quick-add functionality</li>
                  <li>• Scroll-triggered text animations</li>
                  <li>• Fallback poster image for slow connections</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
