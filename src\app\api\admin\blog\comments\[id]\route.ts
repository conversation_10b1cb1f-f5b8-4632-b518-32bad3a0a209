import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();
    
    // Check authentication
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin role
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (userError || userData?.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { data: comment, error } = await supabase
      .from('blog_comments')
      .select(`
        *,
        blog_articles (title, slug),
        users (full_name, email)
      `)
      .eq('id', params.id)
      .single();

    if (error) {
      console.error('Error fetching comment:', error);
      return NextResponse.json({ error: 'Comment not found' }, { status: 404 });
    }

    return NextResponse.json({ comment });

  } catch (error) {
    console.error('Error in blog comment GET API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();
    
    // Check authentication
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin role
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (userError || userData?.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { is_approved, content } = body;

    // Prepare update data
    const updateData: any = {
      updated_at: new Date().toISOString(),
    };

    if (is_approved !== undefined) {
      updateData.is_approved = is_approved;
    }

    if (content !== undefined) {
      updateData.content = content.trim();
    }

    const { data: comment, error } = await supabase
      .from('blog_comments')
      .update(updateData)
      .eq('id', params.id)
      .select(`
        *,
        blog_articles (title, slug),
        users (full_name, email)
      `)
      .single();

    if (error) {
      console.error('Error updating comment:', error);
      return NextResponse.json({ error: 'Failed to update comment' }, { status: 500 });
    }

    return NextResponse.json({ comment });

  } catch (error) {
    console.error('Error in blog comment PUT API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();
    
    // Check authentication
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin role
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (userError || userData?.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Check if comment has replies
    const { data: replies, error: repliesError } = await supabase
      .from('blog_comments')
      .select('id')
      .eq('parent_id', params.id)
      .limit(1);

    if (repliesError) {
      console.error('Error checking comment replies:', repliesError);
      return NextResponse.json({ error: 'Failed to check comment replies' }, { status: 500 });
    }

    if (replies && replies.length > 0) {
      return NextResponse.json({ 
        error: 'Cannot delete comment with replies. Delete replies first.' 
      }, { status: 400 });
    }

    const { error } = await supabase
      .from('blog_comments')
      .delete()
      .eq('id', params.id);

    if (error) {
      console.error('Error deleting comment:', error);
      return NextResponse.json({ error: 'Failed to delete comment' }, { status: 500 });
    }

    return NextResponse.json({ message: 'Comment deleted successfully' });

  } catch (error) {
    console.error('Error in blog comment DELETE API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
