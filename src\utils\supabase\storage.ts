import { createClient } from "./server";
import { v4 as uuidv4 } from "uuid";

/**
 * Uploads a file to Supabase storage and returns the public URL
 * @param file - The file to upload
 * @param bucket - The storage bucket name
 * @param folder - The folder path within the bucket
 * @returns The public URL of the uploaded file
 */
export async function uploadFile(file: File, bucket = 'product-images', folder = 'images') {
  const supabase = await createClient();
  
  // Create a unique file name to prevent collisions
  const fileExt = file.name.split('.').pop();
  const fileName = `${uuidv4()}.${fileExt}`;
  const filePath = `${folder}/${fileName}`;
  
  // Upload the file
  const { data, error } = await supabase.storage
    .from(bucket)
    .upload(filePath, file, {
      cacheControl: '3600',
      upsert: false
    });
  
  if (error) {
    console.error('Error uploading file:', error);
    throw error;
  }
  
  // Get the public URL
  const { data: { publicUrl } } = supabase.storage
    .from(bucket)
    .getPublicUrl(filePath);
  
  return publicUrl;
}

/**
 * Uploads multiple files to Supabase storage and returns their public URLs
 * @param files - The files to upload
 * @param bucket - The storage bucket name
 * @param folder - The folder path within the bucket
 * @returns Array of public URLs for the uploaded files
 */
export async function uploadMultipleFiles(files: File[], bucket = 'product-images', folder = 'images') {
  const uploadPromises = files.map(file => uploadFile(file, bucket, folder));
  return Promise.all(uploadPromises);
}

/**
 * Deletes a file from Supabase storage
 * @param url - The public URL of the file to delete
 * @param bucket - The storage bucket name
 * @returns Boolean indicating success
 */
export async function deleteFileByUrl(url: string, bucket = 'product-images') {
  const supabase = await createClient();
  
  // Extract the path from the URL
  const urlObj = new URL(url);
  const pathMatch = urlObj.pathname.match(new RegExp(`/${bucket}/object/public/(.+)$`));
  
  if (!pathMatch || !pathMatch[1]) {
    console.error('Invalid file URL format');
    return false;
  }
  
  const filePath = decodeURIComponent(pathMatch[1]);
  
  // Delete the file
  const { error } = await supabase.storage
    .from(bucket)
    .remove([filePath]);
  
  if (error) {
    console.error('Error deleting file:', error);
    return false;
  }
  
  return true;
}
