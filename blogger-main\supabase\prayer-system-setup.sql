-- Prayer System Database Setup
-- Run this in Supabase SQL Editor

-- Create prayers table
CREATE TABLE IF NOT EXISTS prayers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('text', 'image', 'video', 'mixed')),
  image_url TEXT,
  video_url TEXT,
  author_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create prayer_likes table
CREATE TABLE IF NOT EXISTS prayer_likes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  prayer_id UUID NOT NULL REFERENCES prayers(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(prayer_id, user_id)
);

-- Create prayer_comments table
CREATE TABLE IF NOT EXISTS prayer_comments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  content TEXT NOT NULL,
  prayer_id UUID NOT NULL REFERENCES prayers(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create prayer_notifications table for daily prayer notifications
CREATE TABLE IF NOT EXISTS prayer_notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  prayer_id UUID NOT NULL REFERENCES prayers(id) ON DELETE CASCADE,
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on all tables
ALTER TABLE prayers ENABLE ROW LEVEL SECURITY;
ALTER TABLE prayer_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE prayer_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE prayer_notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies for prayers table
CREATE POLICY "Anyone can view prayers" ON prayers FOR SELECT USING (true);
CREATE POLICY "Only admins can create prayers" ON prayers FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role = 'admin'
  )
);
CREATE POLICY "Only admins can update prayers" ON prayers FOR UPDATE USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role = 'admin'
  )
);
CREATE POLICY "Only admins can delete prayers" ON prayers FOR DELETE USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role = 'admin'
  )
);

-- RLS Policies for prayer_likes table
CREATE POLICY "Anyone can view prayer likes" ON prayer_likes FOR SELECT USING (true);
CREATE POLICY "Users can like prayers" ON prayer_likes FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can unlike their own likes" ON prayer_likes FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for prayer_comments table
CREATE POLICY "Anyone can view prayer comments" ON prayer_comments FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create comments" ON prayer_comments FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own comments" ON prayer_comments FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own comments" ON prayer_comments FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for prayer_notifications table
CREATE POLICY "Users can view their own notifications" ON prayer_notifications FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "System can create notifications" ON prayer_notifications FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can update their own notifications" ON prayer_notifications FOR UPDATE USING (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_prayers_author_id ON prayers(author_id);
CREATE INDEX IF NOT EXISTS idx_prayers_created_at ON prayers(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_prayer_likes_prayer_id ON prayer_likes(prayer_id);
CREATE INDEX IF NOT EXISTS idx_prayer_likes_user_id ON prayer_likes(user_id);
CREATE INDEX IF NOT EXISTS idx_prayer_comments_prayer_id ON prayer_comments(prayer_id);
CREATE INDEX IF NOT EXISTS idx_prayer_comments_user_id ON prayer_comments(user_id);
CREATE INDEX IF NOT EXISTS idx_prayer_notifications_user_id ON prayer_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_prayer_notifications_is_read ON prayer_notifications(is_read);

-- Create function to automatically create notifications when a new prayer is posted
CREATE OR REPLACE FUNCTION create_prayer_notifications()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert notification for all users except the author
  INSERT INTO prayer_notifications (user_id, prayer_id)
  SELECT id, NEW.id
  FROM auth.users
  WHERE id != NEW.author_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically create notifications
DROP TRIGGER IF EXISTS trigger_create_prayer_notifications ON prayers;
CREATE TRIGGER trigger_create_prayer_notifications
  AFTER INSERT ON prayers
  FOR EACH ROW
  EXECUTE FUNCTION create_prayer_notifications();

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
DROP TRIGGER IF EXISTS trigger_update_prayers_updated_at ON prayers;
CREATE TRIGGER trigger_update_prayers_updated_at
  BEFORE UPDATE ON prayers
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_update_prayer_comments_updated_at ON prayer_comments;
CREATE TRIGGER trigger_update_prayer_comments_updated_at
  BEFORE UPDATE ON prayer_comments
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT ALL ON prayers TO authenticated;
GRANT ALL ON prayer_likes TO authenticated;
GRANT ALL ON prayer_comments TO authenticated;
GRANT ALL ON prayer_notifications TO authenticated;

-- Insert sample prayer (optional)
INSERT INTO prayers (title, content, type, author_id) 
SELECT 
  'Welcome to the Prayer Room',
  'May this space bring you peace, comfort, and spiritual guidance. Let us come together in prayer and reflection.',
  'text',
  id
FROM profiles 
WHERE role = 'admin' 
LIMIT 1
ON CONFLICT DO NOTHING;
