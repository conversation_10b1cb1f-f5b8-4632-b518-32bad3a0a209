"use client";

import { useState, useRef, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Play, 
  Pause, 
  RotateCcw, 
  Trophy, 
  Star, 
  Volume2, 
  VolumeX,
  ShoppingCart,
  Gift,
  Target
} from "lucide-react";
import { useCart } from "@/context/cart-context";
import { toast } from "@/components/ui/use-toast";

// Game configuration
const GAME_CONFIG = {
  ballSpeed: 2,
  ballSize: 20,
  paddleWidth: 100,
  paddleHeight: 20,
  canvasWidth: 800,
  canvasHeight: 400,
  targetFPS: 60
};

// Ball interface
interface Ball {
  x: number;
  y: number;
  dx: number;
  dy: number;
  size: number;
  color: string;
  id: number;
}

// Reward milestones
const REWARDS = [
  { score: 10, discount: 5, code: "TENNIS5" },
  { score: 25, discount: 10, code: "TENNIS10" },
  { score: 50, discount: 15, code: "TENNIS15" },
  { score: 100, discount: 20, code: "TENNIS20" }
];

// Featured products for between rounds
const FEATURED_PRODUCTS = [
  {
    id: 1,
    name: "Pro Tour Racket",
    price: 3599.99,
    image: "/images/tennis-racket.png",
    rating: 4.9
  },
  {
    id: 2,
    name: "Competition Tennis Balls",
    price: 269.99,
    image: "https://images.unsplash.com/photo-1592709823125-a191f07a2a5e?w=400&q=80",
    rating: 4.7
  }
];

export default function HeroGameified() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const [gameState, setGameState] = useState<'menu' | 'playing' | 'paused' | 'gameOver' | 'reward'>('menu');
  const [score, setScore] = useState(0);
  const [highScore, setHighScore] = useState(0);
  const [balls, setBalls] = useState<Ball[]>([]);
  const [paddleX, setPaddleX] = useState(GAME_CONFIG.canvasWidth / 2 - GAME_CONFIG.paddleWidth / 2);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [unlockedRewards, setUnlockedRewards] = useState<typeof REWARDS>([]);
  const [currentReward, setCurrentReward] = useState<typeof REWARDS[0] | null>(null);
  const [leaderboard, setLeaderboard] = useState<{name: string, score: number}[]>([]);
  const { addToCart } = useCart();

  // Load saved data on mount
  useEffect(() => {
    const savedHighScore = localStorage.getItem('tennis-game-high-score');
    const savedRewards = localStorage.getItem('tennis-game-rewards');
    const savedLeaderboard = localStorage.getItem('tennis-game-leaderboard');
    
    if (savedHighScore) setHighScore(parseInt(savedHighScore));
    if (savedRewards) setUnlockedRewards(JSON.parse(savedRewards));
    if (savedLeaderboard) setLeaderboard(JSON.parse(savedLeaderboard));
  }, []);

  // Save high score
  useEffect(() => {
    if (score > highScore) {
      setHighScore(score);
      localStorage.setItem('tennis-game-high-score', score.toString());
    }
  }, [score, highScore]);

  // Initialize game
  const initGame = useCallback(() => {
    setBalls([]);
    setScore(0);
    setPaddleX(GAME_CONFIG.canvasWidth / 2 - GAME_CONFIG.paddleWidth / 2);
    setCurrentReward(null);
  }, []);

  // Spawn new ball
  const spawnBall = useCallback(() => {
    const newBall: Ball = {
      x: Math.random() * (GAME_CONFIG.canvasWidth - GAME_CONFIG.ballSize),
      y: -GAME_CONFIG.ballSize,
      dx: (Math.random() - 0.5) * 2,
      dy: GAME_CONFIG.ballSpeed,
      size: GAME_CONFIG.ballSize,
      color: '#9ACD32',
      id: Date.now() + Math.random()
    };
    
    setBalls(prev => [...prev, newBall]);
  }, []);

  // Game loop
  const gameLoop = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas || gameState !== 'playing') return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, GAME_CONFIG.canvasWidth, GAME_CONFIG.canvasHeight);

    // Draw background
    ctx.fillStyle = 'linear-gradient(to bottom, #f0f9ff, #ecfdf5)';
    ctx.fillRect(0, 0, GAME_CONFIG.canvasWidth, GAME_CONFIG.canvasHeight);

    // Update and draw balls
    setBalls(prevBalls => {
      const updatedBalls = prevBalls.map(ball => ({
        ...ball,
        x: ball.x + ball.dx,
        y: ball.y + ball.dy
      })).filter(ball => {
        // Check collision with paddle
        if (
          ball.y + ball.size >= GAME_CONFIG.canvasHeight - GAME_CONFIG.paddleHeight &&
          ball.x + ball.size >= paddleX &&
          ball.x <= paddleX + GAME_CONFIG.paddleWidth
        ) {
          // Hit!
          setScore(prev => prev + 1);
          if (soundEnabled) {
            // Play hit sound (placeholder)
            console.log('Hit sound!');
          }
          return false; // Remove ball
        }
        
        // Remove balls that fall off screen
        return ball.y < GAME_CONFIG.canvasHeight + ball.size;
      });

      // Draw balls
      updatedBalls.forEach(ball => {
        ctx.beginPath();
        ctx.arc(ball.x + ball.size/2, ball.y + ball.size/2, ball.size/2, 0, Math.PI * 2);
        ctx.fillStyle = ball.color;
        ctx.fill();
        
        // Tennis ball seam
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(ball.x + ball.size/2, ball.y + ball.size/2, ball.size/3, -Math.PI/4, Math.PI/4);
        ctx.arc(ball.x + ball.size/2, ball.y + ball.size/2, ball.size/3, 3*Math.PI/4, 5*Math.PI/4);
        ctx.stroke();
      });

      return updatedBalls;
    });

    // Draw paddle
    ctx.fillStyle = '#22c55e';
    ctx.fillRect(paddleX, GAME_CONFIG.canvasHeight - GAME_CONFIG.paddleHeight, GAME_CONFIG.paddleWidth, GAME_CONFIG.paddleHeight);
    ctx.strokeStyle = '#16a34a';
    ctx.lineWidth = 2;
    ctx.strokeRect(paddleX, GAME_CONFIG.canvasHeight - GAME_CONFIG.paddleHeight, GAME_CONFIG.paddleWidth, GAME_CONFIG.paddleHeight);

    // Spawn new balls occasionally
    if (Math.random() < 0.02) {
      spawnBall();
    }

    animationRef.current = requestAnimationFrame(gameLoop);
  }, [gameState, paddleX, soundEnabled, spawnBall]);

  // Start game
  const startGame = () => {
    initGame();
    setGameState('playing');
  };

  // Pause/Resume game
  const togglePause = () => {
    setGameState(gameState === 'playing' ? 'paused' : 'playing');
  };

  // Reset game
  const resetGame = () => {
    initGame();
    setGameState('menu');
  };

  // Handle mouse/touch movement
  const handleCanvasMove = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (gameState !== 'playing') return;
    
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    let clientX: number;
    
    if ('touches' in e) {
      clientX = e.touches[0].clientX;
    } else {
      clientX = e.clientX;
    }
    
    const x = ((clientX - rect.left) / rect.width) * GAME_CONFIG.canvasWidth;
    setPaddleX(Math.max(0, Math.min(x - GAME_CONFIG.paddleWidth / 2, GAME_CONFIG.canvasWidth - GAME_CONFIG.paddleWidth)));
  };

  // Check for rewards
  useEffect(() => {
    const newReward = REWARDS.find(reward => 
      score >= reward.score && !unlockedRewards.some(ur => ur.score === reward.score)
    );
    
    if (newReward) {
      setCurrentReward(newReward);
      setUnlockedRewards(prev => [...prev, newReward]);
      setGameState('reward');
      localStorage.setItem('tennis-game-rewards', JSON.stringify([...unlockedRewards, newReward]));
    }
  }, [score, unlockedRewards]);

  // Game loop effect
  useEffect(() => {
    if (gameState === 'playing') {
      animationRef.current = requestAnimationFrame(gameLoop);
    } else {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    }
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [gameState, gameLoop]);

  const handleAddToCart = (product: typeof FEATURED_PRODUCTS[0]) => {
    addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image
    });
  };

  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 to-green-50/30">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          
          {/* Game Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl md:text-5xl font-bold mb-4 text-slate-900">
              Tennis Ball Challenge
            </h1>
            <p className="text-lg text-slate-600 mb-6">
              Hit the tennis balls to score points and unlock exclusive discounts!
            </p>
            
            {/* Score Display */}
            <div className="flex justify-center gap-6 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{score}</div>
                <div className="text-sm text-slate-600">Score</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-slate-900">{highScore}</div>
                <div className="text-sm text-slate-600">Best</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{unlockedRewards.length}</div>
                <div className="text-sm text-slate-600">Rewards</div>
              </div>
            </div>
          </div>

          {/* Game Canvas */}
          <div className="relative mx-auto mb-8" style={{ maxWidth: '800px' }}>
            <canvas
              ref={canvasRef}
              width={GAME_CONFIG.canvasWidth}
              height={GAME_CONFIG.canvasHeight}
              className="w-full h-auto border-2 border-green-200 rounded-lg shadow-lg bg-gradient-to-b from-blue-50 to-green-50"
              onMouseMove={handleCanvasMove}
              onTouchMove={handleCanvasMove}
              style={{ touchAction: 'none' }}
            />
            
            {/* Game Overlay */}
            <AnimatePresence>
              {gameState === 'menu' && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-lg"
                >
                  <div className="text-center text-white">
                    <h2 className="text-3xl font-bold mb-4">Ready to Play?</h2>
                    <p className="mb-6">Move your mouse/finger to control the paddle</p>
                    <Button onClick={startGame} size="lg" className="bg-green-600 hover:bg-green-700">
                      <Play className="w-5 h-5 mr-2" />
                      Start Game
                    </Button>
                  </div>
                </motion.div>
              )}
              
              {gameState === 'paused' && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-lg"
                >
                  <div className="text-center text-white">
                    <h2 className="text-3xl font-bold mb-4">Game Paused</h2>
                    <Button onClick={togglePause} size="lg" className="bg-green-600 hover:bg-green-700">
                      <Play className="w-5 h-5 mr-2" />
                      Resume
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Game Controls */}
          <div className="flex justify-center gap-4 mb-8">
            {gameState === 'playing' && (
              <Button onClick={togglePause} variant="outline">
                <Pause className="w-4 h-4 mr-2" />
                Pause
              </Button>
            )}
            
            <Button onClick={resetGame} variant="outline">
              <RotateCcw className="w-4 h-4 mr-2" />
              Reset
            </Button>
            
            <Button 
              onClick={() => setSoundEnabled(!soundEnabled)} 
              variant="outline"
            >
              {soundEnabled ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
            </Button>
          </div>

          {/* Rewards Section */}
          {unlockedRewards.length > 0 && (
            <div className="mb-8">
              <h3 className="text-2xl font-bold text-center mb-4">Your Rewards</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {unlockedRewards.map((reward) => (
                  <Card key={reward.score} className="text-center">
                    <CardContent className="p-4">
                      <Trophy className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
                      <div className="font-bold text-green-600">{reward.discount}% OFF</div>
                      <div className="text-sm text-slate-600">Code: {reward.code}</div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Featured Products */}
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-6">Featured Tennis Gear</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {FEATURED_PRODUCTS.map((product) => (
                <Card key={product.id} className="overflow-hidden">
                  <CardContent className="p-6">
                    <img 
                      src={product.image} 
                      alt={product.name}
                      className="w-24 h-24 object-cover rounded-lg mx-auto mb-4"
                    />
                    <h4 className="font-semibold text-lg mb-2">{product.name}</h4>
                    <div className="flex items-center justify-center gap-2 mb-3">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm">{product.rating}</span>
                    </div>
                    <p className="text-xl font-bold text-green-600 mb-4">
                      R {product.price.toFixed(2)}
                    </p>
                    <Button 
                      onClick={() => handleAddToCart(product)}
                      className="w-full bg-green-600 hover:bg-green-700"
                    >
                      <ShoppingCart className="w-4 h-4 mr-2" />
                      Add to Cart
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Reward Modal */}
      <AnimatePresence>
        {gameState === 'reward' && currentReward && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="bg-white rounded-lg p-8 max-w-md mx-4 text-center"
            >
              <Trophy className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-4">Reward Unlocked!</h2>
              <p className="text-lg mb-4">
                You've earned <span className="font-bold text-green-600">{currentReward.discount}% OFF</span>
              </p>
              <Badge variant="outline" className="text-lg px-4 py-2 mb-6">
                {currentReward.code}
              </Badge>
              <div className="flex gap-4">
                <Button 
                  onClick={() => setGameState('playing')}
                  className="flex-1"
                >
                  Continue Playing
                </Button>
                <Button 
                  onClick={() => setGameState('menu')}
                  variant="outline"
                  className="flex-1"
                >
                  Main Menu
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
}
