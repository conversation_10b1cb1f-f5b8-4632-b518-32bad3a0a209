import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function POST() {
  try {
    const supabase = await createClient();
    
    // First, let's modify the yoco-checkout route to not use the notes column
    // This is a temporary fix until we can add the column to the database
    const { data: updateResult, error: updateError } = await supabase
      .from('orders')
      .update({ id: 'test-update' })
      .eq('id', 'non-existent-id')
      .select();
    
    // The above query won't actually update anything, but it will help us see if we can access the table
    
    if (updateError) {
      console.error('Error testing orders table access:', updateError);
      
      // If the error is about the notes column, we need to tell the user how to fix it
      if (updateError.message.includes('notes')) {
        return NextResponse.json({
          message: 'Database schema issue detected',
          error: updateError.message,
          solution: `
            You need to add the 'notes' and 'shipping_method' columns to your orders table.
            
            Please run the following SQL in your Supabase dashboard SQL editor:
            
            ALTER TABLE public.orders ADD COLUMN IF NOT EXISTS notes TEXT;
            ALTER TABLE public.orders ADD COLUMN IF NOT EXISTS shipping_method TEXT;
          `
        });
      }
      
      return NextResponse.json(
        { error: `Database error: ${updateError.message}` },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      message: 'Database connection successful',
      note: 'To add the missing columns, please run the SQL commands in your Supabase dashboard',
      sql_commands: [
        'ALTER TABLE public.orders ADD COLUMN IF NOT EXISTS notes TEXT;',
        'ALTER TABLE public.orders ADD COLUMN IF NOT EXISTS shipping_method TEXT;'
      ]
    });
  } catch (error: any) {
    console.error('Error:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
} 