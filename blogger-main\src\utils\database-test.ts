import { supabase } from '../../supabase/supabase';

export async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...');
  
  try {
    // Test 1: Basic connection
    const { data: connectionTest, error: connectionError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (connectionError) {
      console.error('❌ Connection test failed:', connectionError);
      return false;
    }
    
    console.log('✅ Database connection successful');
    
    // Test 2: Check if profiles table exists
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);
    
    if (profilesError) {
      console.error('❌ Profiles table test failed:', profilesError);
      
      // Try to create profiles table if it doesn't exist
      console.log('🔧 Attempting to create profiles table...');
      await createProfilesTable();
    } else {
      console.log('✅ Profiles table exists');
    }
    
    // Test 3: Check if articles table exists
    const { data: articles, error: articlesError } = await supabase
      .from('articles')
      .select('*')
      .limit(1);
    
    if (articlesError) {
      console.error('❌ Articles table test failed:', articlesError);
    } else {
      console.log('✅ Articles table exists');
    }
    
    // Test 4: Check if products table exists
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('*')
      .limit(1);
    
    if (productsError) {
      console.error('❌ Products table test failed:', productsError);
    } else {
      console.log('✅ Products table exists');
    }
    
    // Test 5: Check if categories table exists
    const { data: categories, error: categoriesError } = await supabase
      .from('categories')
      .select('*')
      .limit(1);
    
    if (categoriesError) {
      console.error('❌ Categories table test failed:', categoriesError);
    } else {
      console.log('✅ Categories table exists');
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
    return false;
  }
}

async function createProfilesTable() {
  try {
    // This would require admin privileges, so we'll just log the SQL
    console.log('📝 SQL to create profiles table:');
    console.log(`
      CREATE TABLE IF NOT EXISTS public.profiles (
        id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
        first_name text,
        last_name text,
        email text,
        role text DEFAULT 'user' CHECK (role IN ('user', 'admin')),
        avatar_url text,
        bio text,
        website text,
        location text,
        created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
        updated_at timestamp with time zone DEFAULT timezone('utc'::text, now())
      );
      
      ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
      
      CREATE POLICY "Users can view their own profile" ON public.profiles
        FOR SELECT USING (auth.uid() = id);
      
      CREATE POLICY "Users can update their own profile" ON public.profiles
        FOR UPDATE USING (auth.uid() = id);
      
      CREATE POLICY "Users can insert their own profile" ON public.profiles
        FOR INSERT WITH CHECK (auth.uid() = id);
    `);
    
    return true;
  } catch (error) {
    console.error('❌ Failed to create profiles table:', error);
    return false;
  }
}

export async function seedMockData() {
  console.log('🌱 Seeding mock data...');
  
  try {
    // Seed categories
    const { error: categoriesError } = await supabase
      .from('categories')
      .upsert([
        { id: '1', name: 'Technology', slug: 'technology', color: '#3B82F6' },
        { id: '2', name: 'Business', slug: 'business', color: '#10B981' },
        { id: '3', name: 'Design', slug: 'design', color: '#F59E0B' },
        { id: '4', name: 'Development', slug: 'development', color: '#8B5CF6' },
        { id: '5', name: 'Marketing', slug: 'marketing', color: '#EF4444' },
      ], { onConflict: 'id' });
    
    if (categoriesError) {
      console.error('❌ Failed to seed categories:', categoriesError);
    } else {
      console.log('✅ Categories seeded successfully');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Failed to seed mock data:', error);
    return false;
  }
}
