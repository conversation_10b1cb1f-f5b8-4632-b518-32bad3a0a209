-- =====================================================
-- CHECK AND FIX USER ROLES LIVE
-- This script checks current user roles and fixes them
-- =====================================================

-- 1. CHECK CURRENT USER ROLES
SELECT 
    'CURRENT USER ROLES' as check_type,
    id,
    email,
    role,
    created_at,
    updated_at
FROM public.profiles 
ORDER BY created_at;

-- 2. CHECK SPECIFIC USERS FROM ERROR LOG
SELECT 
    'SPECIFIC USERS CHECK' as check_type,
    id,
    email,
    role,
    CASE 
        WHEN id = 'cbdb1cae-699d-4abf-b9bb-ecdb34e050df' THEN '<EMAIL> user'
        WHEN id = 'a148b104-9d17-4254-bc8c-f8b3c652d273' THEN '<EMAIL> user'
        ELSE 'other user'
    END as user_description
FROM public.profiles 
WHERE id IN ('cbdb1cae-699d-4abf-b9bb-ecdb34e050df', 'a148b104-9d17-4254-bc8c-f8b3c652d273');

-- 3. FIX ROLES - SET FIRST USER AS ADMIN
WITH user_order AS (
    SELECT 
        id,
        email,
        role,
        created_at,
        ROW_NUMBER() OVER (ORDER BY created_at) as user_number
    FROM public.profiles
    ORDER BY created_at
)
UPDATE public.profiles 
SET 
    role = CASE 
        WHEN id = (SELECT id FROM user_order WHERE user_number = 1) THEN 'admin'
        ELSE 'user'
    END,
    updated_at = timezone('utc'::text, now())
WHERE id IN (
    SELECT id FROM user_order
);

-- 4. VERIFY THE FIX
SELECT 
    'AFTER FIX - USER ROLES' as check_type,
    id,
    email,
    role,
    created_at,
    updated_at,
    CASE 
        WHEN ROW_NUMBER() OVER (ORDER BY created_at) = 1 THEN '✅ SHOULD BE ADMIN'
        ELSE '✅ SHOULD BE USER'
    END as expected_role
FROM public.profiles 
ORDER BY created_at;

-- 5. CHECK AUTH USERS TABLE
SELECT 
    'AUTH USERS CHECK' as check_type,
    id,
    email,
    created_at,
    email_confirmed_at,
    last_sign_in_at
FROM auth.users
ORDER BY created_at;

-- 6. SUCCESS MESSAGE
DO $$
DECLARE
    admin_count INTEGER;
    total_count INTEGER;
    first_user_email TEXT;
BEGIN
    SELECT COUNT(*) INTO admin_count FROM public.profiles WHERE role = 'admin';
    SELECT COUNT(*) INTO total_count FROM public.profiles;
    SELECT email INTO first_user_email FROM public.profiles ORDER BY created_at LIMIT 1;
    
    RAISE NOTICE '🎉 USER ROLES FIXED!';
    RAISE NOTICE '==========================================';
    RAISE NOTICE 'Total users: %', total_count;
    RAISE NOTICE 'Admin users: %', admin_count;
    RAISE NOTICE 'First user (admin): %', first_user_email;
    RAISE NOTICE '==========================================';
    RAISE NOTICE '📋 Next steps:';
    RAISE NOTICE '1. Clear browser cache and cookies';
    RAISE NOTICE '2. Refresh the application';
    RAISE NOTICE '3. Check that admin role shows correctly';
    RAISE NOTICE '==========================================';
END $$;
