const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Create certificates directory
const certDir = path.join(__dirname, 'certificates');
if (!fs.existsSync(certDir)) {
  fs.mkdirSync(certDir);
}

console.log('Generating self-signed certificates for HTTPS development...');

try {
  // Generate private key
  execSync(`openssl genrsa -out ${path.join(certDir, 'localhost-key.pem')} 2048`, { stdio: 'inherit' });
  
  // Generate certificate
  execSync(`openssl req -new -x509 -key ${path.join(certDir, 'localhost-key.pem')} -out ${path.join(certDir, 'localhost.pem')} -days 365 -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"`, { stdio: 'inherit' });
  
  console.log('✅ Certificates generated successfully!');
  console.log('📁 Certificates saved in:', certDir);
  console.log('🚀 You can now run: npm run dev:https');
  
} catch (error) {
  console.error('❌ Error generating certificates:', error.message);
  console.log('\n📝 Alternative: Use mkcert for easier certificate generation:');
  console.log('1. Install mkcert: https://github.com/FiloSottile/mkcert');
  console.log('2. Run: mkcert -install');
  console.log('3. Run: mkcert localhost');
  console.log('4. Move the generated files to certificates/ folder');
}
