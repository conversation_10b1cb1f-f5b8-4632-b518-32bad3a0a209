import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Youtube,
  Mail,
  Phone,
  MapPin,
  Heart,
  Send,
  Loader2
} from 'lucide-react';
import { blogService } from '@/lib/supabase-services';

interface Category {
  id: string;
  name: string;
  slug: string;
}

export function Footer() {
  const currentYear = new Date().getFullYear();
  const [categories, setCategories] = useState<Category[]>([]);
  const [email, setEmail] = useState('');
  const [isSubscribing, setIsSubscribing] = useState(false);

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      const data = await blogService.getCategories();
      setCategories(data.slice(0, 6)); // Show only first 6 categories
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const handleNewsletterSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) {
      alert('Please enter your email address');
      return;
    }

    setIsSubscribing(true);
    try {
      await blogService.subscribeToNewsletter(email);
      alert('Successfully subscribed to our newsletter!');
      setEmail('');
    } catch (error) {
      console.error('Newsletter signup error:', error);
      alert('Failed to subscribe. Please try again.');
    } finally {
      setIsSubscribing(false);
    }
  };

  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">

          {/* Brand Section */}
          <div className="space-y-3 sm:space-y-4 col-span-1 sm:col-span-2 lg:col-span-1">
            <div className="flex items-center space-x-2">
              <div className="w-7 h-7 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xs sm:text-sm">TB</span>
              </div>
              <span className="text-lg sm:text-xl font-bold">Thabo Bester</span>
            </div>
            <p className="text-gray-400 text-xs sm:text-sm leading-relaxed hidden sm:block">
              Professional insights, thought leadership, and expert analysis on technology,
              business, and innovation.
            </p>
            <p className="text-gray-400 text-xs leading-relaxed sm:hidden">
              Professional insights and expert analysis.
            </p>
            <div className="flex space-x-3 sm:space-x-4">
              <a
                href="#"
                aria-label="Facebook"
                className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors duration-200"
              >
                <Facebook className="w-4 h-4 sm:w-5 sm:h-5" />
              </a>
              <a
                href="#"
                aria-label="Twitter"
                className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-blue-400 transition-colors duration-200"
              >
                <Twitter className="w-4 h-4 sm:w-5 sm:h-5" />
              </a>
              <a
                href="#"
                aria-label="LinkedIn"
                className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors duration-200"
              >
                <Linkedin className="w-4 h-4 sm:w-5 sm:h-5" />
              </a>
              {/* Show additional social icons only on larger screens */}
              <a
                href="#"
                aria-label="Instagram"
                className="hidden sm:flex w-10 h-10 bg-gray-800 rounded-full items-center justify-center hover:bg-pink-600 transition-colors duration-200"
              >
                <Instagram className="w-5 h-5" />
              </a>
              <a
                href="#"
                aria-label="YouTube"
                className="hidden lg:flex w-10 h-10 bg-gray-800 rounded-full items-center justify-center hover:bg-red-600 transition-colors duration-200"
              >
                <Youtube className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-3 sm:space-y-4">
            <h3 className="text-base sm:text-lg font-semibold">Quick Links</h3>
            <ul className="space-y-1.5 sm:space-y-2">
              <li>
                <Link
                  to="/about"
                  className="text-gray-400 hover:text-white transition-colors duration-200 text-xs sm:text-sm"
                >
                  About
                </Link>
              </li>
              <li>
                <Link
                  to="/articles"
                  className="text-gray-400 hover:text-white transition-colors duration-200 text-xs sm:text-sm"
                >
                  Articles
                </Link>
              </li>
              <li>
                <Link
                  to="/products"
                  className="text-gray-400 hover:text-white transition-colors duration-200 text-xs sm:text-sm"
                >
                  Products
                </Link>
              </li>
              <li>
                <Link
                  to="/contact"
                  className="text-gray-400 hover:text-white transition-colors duration-200 text-xs sm:text-sm"
                >
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          {/* Categories - Hidden on mobile, shown on tablet+ */}
          <div className="space-y-3 sm:space-y-4 hidden sm:block">
            <h3 className="text-base sm:text-lg font-semibold">Categories</h3>
            <ul className="space-y-1.5 sm:space-y-2">
              {categories.length > 0 ? (
                categories.map((category) => (
                  <li key={category.id}>
                    <Link
                      to={`/articles?category=${category.slug}`}
                      className="text-gray-400 hover:text-white transition-colors duration-200 text-xs sm:text-sm"
                    >
                      {category.name}
                    </Link>
                  </li>
                ))
              ) : (
                // Fallback static categories while loading
                <>
                  <li>
                    <Link
                      to="/articles?category=technology"
                      className="text-gray-400 hover:text-white transition-colors duration-200 text-sm"
                    >
                      Technology
                    </Link>
                  </li>
                  <li>
                    <Link
                      to="/articles?category=business"
                      className="text-gray-400 hover:text-white transition-colors duration-200 text-sm"
                    >
                      Business
                    </Link>
                  </li>
                  <li>
                    <Link
                      to="/articles?category=health"
                      className="text-gray-400 hover:text-white transition-colors duration-200 text-sm"
                    >
                      Health
                    </Link>
                  </li>
                </>
              )}
            </ul>
          </div>

          {/* Contact Info & Newsletter */}
          <div className="space-y-3 sm:space-y-4 col-span-1 sm:col-span-2 lg:col-span-1">
            <h3 className="text-base sm:text-lg font-semibold">Stay Connected</h3>

            {/* Contact Info - Compact on mobile */}
            <div className="space-y-2 sm:space-y-3">
              <div className="flex items-center space-x-2 sm:space-x-3">
                <Mail className="w-3 h-3 sm:w-4 sm:h-4 text-gray-400 flex-shrink-0" />
                <span className="text-gray-400 text-xs sm:text-sm truncate"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-2 sm:space-x-3 sm:hidden">
                <Phone className="w-3 h-3 text-gray-400 flex-shrink-0" />
                <span className="text-gray-400 text-xs">+27 11 234 5678</span>
              </div>
              {/* Show more contact info on larger screens */}
              <div className="hidden sm:flex items-center space-x-3">
                <Phone className="w-4 h-4 text-gray-400" />
                <span className="text-gray-400 text-sm">+27 11 234 5678</span>
              </div>
              <div className="hidden sm:flex items-center space-x-3">
                <MapPin className="w-4 h-4 text-gray-400" />
                <span className="text-gray-400 text-sm">Morningside, Sandton</span>
              </div>
            </div>

            {/* Newsletter Signup - Compact */}
            <div className="mt-4 sm:mt-6">
              <h4 className="text-xs sm:text-sm font-semibold mb-2">Newsletter</h4>
              <p className="text-xs text-gray-400 mb-2 sm:mb-3 hidden sm:block">
                Get the latest articles and insights delivered to your inbox.
              </p>
              <form onSubmit={handleNewsletterSignup} className="flex">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Your email"
                  disabled={isSubscribing}
                  className="flex-1 px-2 sm:px-3 py-1.5 sm:py-2 bg-gray-800 border border-gray-700 rounded-l-md text-xs sm:text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
                />
                <button
                  type="submit"
                  disabled={isSubscribing}
                  title="Subscribe to newsletter"
                  aria-label="Subscribe to newsletter"
                  className="px-2 sm:px-4 py-1.5 sm:py-2 bg-blue-600 hover:bg-blue-700 rounded-r-md transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubscribing ? (
                    <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 animate-spin" />
                  ) : (
                    <Send className="w-3 h-3 sm:w-4 sm:h-4" />
                  )}
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3 sm:py-4 lg:py-6">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">

            {/* Copyright */}
            <div className="flex flex-col sm:flex-row items-center space-y-1 sm:space-y-0 sm:space-x-2 text-xs sm:text-sm text-gray-400">
              <span>© {currentYear} Thabo Bester. All rights reserved.</span>
              <span className="hidden sm:inline">•</span>
              <span className="flex items-center space-x-1">
                <span>Made with</span>
                <Heart className="w-3 h-3 sm:w-4 sm:h-4 text-red-500" />
                <span>for readers worldwide</span>
              </span>
            </div>

            {/* Legal Links */}
            <div className="flex items-center space-x-3 sm:space-x-6">
              <Link
                to="/privacy"
                className="text-xs sm:text-sm text-gray-400 hover:text-white transition-colors duration-200"
              >
                Privacy
              </Link>
              <Link
                to="/terms"
                className="text-xs sm:text-sm text-gray-400 hover:text-white transition-colors duration-200"
              >
                Terms
              </Link>
              <Link
                to="/cookies"
                className="text-xs sm:text-sm text-gray-400 hover:text-white transition-colors duration-200"
              >
                Cookies
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
