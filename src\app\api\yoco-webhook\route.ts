import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

/**
 * This route handles webhooks from Yoco payment gateway
 * It processes payment confirmations and updates order statuses
 */
export async function POST(request: Request) {
  try {
    // Get the webhook payload
    const payload = await request.json();
    console.log('Received Yoco webhook:', payload);

    // Verify the webhook signature (in a production environment)
    // This would use the YOCO_WEBHOOK_SECRET to verify the signature

    // Extract the relevant information from the webhook
    const { 
      event_type, 
      data: { 
        id: paymentId,
        metadata,
        status
      } 
    } = payload;

    // Check if this is a payment event
    if (!event_type.startsWith('payment.')) {
      return NextResponse.json({ received: true });
    }

    // Get the order ID or consultation ID from metadata
    const orderId = metadata?.order_id;
    const consultationId = metadata?.consultation_id;

    if (!orderId && !consultationId) {
      console.error('No order ID or consultation ID found in metadata');
      return NextResponse.json(
        { error: 'No order ID or consultation ID found in metadata' },
        { status: 400 }
      );
    }

    // Update the order or consultation in the database
    const supabase = await createClient();

    const updates: any = {
      yoco_payment_id: paymentId
    };

    // Update the payment status based on the event type
    if (event_type === 'payment.succeeded') {
      updates.payment_status = 'paid';
      if (orderId) {
        updates.status = 'processing';
      } else if (consultationId) {
        updates.status = 'confirmed';
      }
    } else if (event_type === 'payment.failed') {
      updates.payment_status = 'failed';
    } else if (event_type === 'payment.refunded') {
      updates.payment_status = 'refunded';
    }

    let data, error;

    if (orderId) {
      // Update order
      const result = await supabase
        .from('orders')
        .update(updates)
        .eq('id', orderId)
        .select()
        .single();
      data = result.data;
      error = result.error;

      if (error) {
        console.error(`Error updating order ${orderId}:`, error);
        return NextResponse.json(
          { error: `Failed to update order: ${error.message}` },
          { status: 500 }
        );
      }

      return NextResponse.json({ success: true, orderId, paymentId });
    } else if (consultationId) {
      // Update consultation
      const result = await supabase
        .from('consultations')
        .update(updates)
        .eq('id', consultationId)
        .select()
        .single();
      data = result.data;
      error = result.error;

      if (error) {
        console.error(`Error updating consultation ${consultationId}:`, error);
        return NextResponse.json(
          { error: `Failed to update consultation: ${error.message}` },
          { status: 500 }
        );
      }

      return NextResponse.json({ success: true, consultationId, paymentId });
    }
  } catch (error: any) {
    console.error('Error processing webhook:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
