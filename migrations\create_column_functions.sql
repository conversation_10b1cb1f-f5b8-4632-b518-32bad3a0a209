-- Function to add notes column
CREATE OR R<PERSON>LACE FUNCTION public.add_notes_column()
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the column already exists
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'public'
      AND table_name = 'orders'
      AND column_name = 'notes'
  ) THEN
    -- Add the column
    EXECUTE 'ALTER TABLE public.orders ADD COLUMN notes TEXT';
  END IF;
  
  RETURN true;add .
  
END;
$$;

-- Function to add shipping_method column
CREATE OR REPLACE FUNCTION public.add_shipping_method_column()
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the column already exists
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'public'
      AND table_name = 'orders'
      AND column_name = 'shipping_method'
  ) THEN
    -- Add the column
    EXECUTE 'ALTER TABLE public.orders ADD COLUMN shipping_method TEXT';
  END IF;
  
  <PERSON><PERSON><PERSON><PERSON> true;
END;
$$; 