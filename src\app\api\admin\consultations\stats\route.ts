import { NextRequest, NextResponse } from 'next/server';
import { createClient, createServiceRoleClient } from '@/utils/supabase/server';
import { ConsultationStats } from '@/types/consultations';

/**
 * GET /api/admin/consultations/stats
 * Fetch consultation statistics for admin dashboard
 */
export async function GET(request: NextRequest) {
  try {
    console.log('GET /api/admin/consultations/stats - Starting request');

    // Verify admin authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      console.log('GET /api/admin/consultations/stats - No session found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const { data: userData } = await supabase
      .from('users')
      .select('role, admin_role')
      .eq('id', session.user.id)
      .single();

    if (!userData || userData.role !== 'admin') {
      console.log('GET /api/admin/consultations/stats - User is not admin');
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    console.log('GET /api/admin/consultations/stats - Admin access confirmed');

    // Use service role client for database operations
    const serviceSupabase = createServiceRoleClient();

    // Get total consultations count
    const { count: totalConsultations } = await serviceSupabase
      .from('consultations')
      .select('*', { count: 'exact', head: true });

    // Get consultations by status
    const { data: statusCounts } = await serviceSupabase
      .from('consultations')
      .select('status')
      .then(async (result) => {
        if (result.error) throw result.error;
        
        const counts = {
          pending: 0,
          confirmed: 0,
          completed: 0,
          cancelled: 0,
          rescheduled: 0,
        };

        result.data?.forEach((consultation) => {
          if (consultation.status in counts) {
            counts[consultation.status as keyof typeof counts]++;
          }
        });

        return { data: counts };
      });

    // Get revenue statistics
    const { data: revenueData } = await serviceSupabase
      .from('consultations')
      .select('payment_amount, payment_status, created_at')
      .eq('payment_status', 'paid');

    const totalRevenue = revenueData?.reduce((sum, consultation) => {
      return sum + (consultation.payment_amount || 0);
    }, 0) || 0;

    // Calculate monthly growth (compare current month to previous month)
    const now = new Date();
    const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const previousMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);

    const { count: currentMonthCount } = await serviceSupabase
      .from('consultations')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', currentMonth.toISOString());

    const { count: previousMonthCount } = await serviceSupabase
      .from('consultations')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', previousMonth.toISOString())
      .lt('created_at', currentMonth.toISOString());

    const monthlyGrowth = previousMonthCount && previousMonthCount > 0
      ? ((currentMonthCount || 0) - previousMonthCount) / previousMonthCount * 100
      : 0;

    // Get average duration
    const { data: durationData } = await serviceSupabase
      .from('consultations')
      .select('duration')
      .not('duration', 'is', null);

    const averageDuration = durationData && durationData.length > 0
      ? durationData.reduce((sum, consultation) => sum + (consultation.duration || 60), 0) / durationData.length
      : 60;

    // Calculate completion rate
    const completedCount = statusCounts?.completed || 0;
    const totalNonCancelled = (totalConsultations || 0) - (statusCounts?.cancelled || 0);
    const completionRate = totalNonCancelled > 0 ? (completedCount / totalNonCancelled) * 100 : 0;

    const stats: ConsultationStats = {
      total_consultations: totalConsultations || 0,
      pending_consultations: statusCounts?.pending || 0,
      confirmed_consultations: statusCounts?.confirmed || 0,
      completed_consultations: statusCounts?.completed || 0,
      cancelled_consultations: statusCounts?.cancelled || 0,
      total_revenue: totalRevenue,
      monthly_growth: Math.round(monthlyGrowth * 100) / 100,
      average_duration: Math.round(averageDuration),
      completion_rate: Math.round(completionRate * 100) / 100,
    };

    console.log('GET /api/admin/consultations/stats - Stats calculated:', stats);

    return NextResponse.json(stats);

  } catch (error: any) {
    console.error('GET /api/admin/consultations/stats - Error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
