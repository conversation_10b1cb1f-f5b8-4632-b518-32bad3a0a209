import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { createServiceClient } from '@/utils/supabase/service';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions
    const { data: adminData } = await supabase
      .from('users')
      .select('role, admin_role')
      .eq('id', session.user.id)
      .single();

    if (!adminData || adminData.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Use service role client for database operations
    const serviceSupabase = createServiceClient();

    // Get activity summary metrics using the database function
    const { data: metricsData, error: metricsError } = await serviceSupabase
      .rpc('get_activity_summary_metrics');

    if (metricsError) {
      console.error('Metrics error:', metricsError);
      // Fallback to manual calculation if function fails
      return await calculateMetricsManually(serviceSupabase);
    }

    const metrics = metricsData?.[0] || {
      total_activities: 0,
      success_rate: 0,
      activities_today: 0,
      activities_this_week: 0,
      most_active_admin: 'No activity'
    };

    return NextResponse.json(metrics);

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

async function calculateMetricsManually(serviceSupabase: any) {
  try {
    // Get all activity logs
    const { data: activities } = await serviceSupabase
      .from('admin_activity_logs')
      .select(`
        id,
        success,
        created_at,
        admin:users!admin_id(full_name)
      `);

    if (!activities || activities.length === 0) {
      return NextResponse.json({
        total_activities: 0,
        success_rate: 0,
        activities_today: 0,
        activities_this_week: 0,
        most_active_admin: 'No activity'
      });
    }

    // Calculate metrics
    const totalActivities = activities.length;
    const successfulActivities = activities.filter(a => a.success).length;
    const successRate = (successfulActivities / totalActivities) * 100;

    // Activities today
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const activitiesToday = activities.filter(a => 
      new Date(a.created_at) >= today
    ).length;

    // Activities this week
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    const activitiesThisWeek = activities.filter(a => 
      new Date(a.created_at) >= weekAgo
    ).length;

    // Most active admin
    const adminCounts = activities.reduce((acc, activity) => {
      const adminName = activity.admin?.full_name || 'Unknown';
      acc[adminName] = (acc[adminName] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const mostActiveAdmin = Object.entries(adminCounts)
      .sort(([,a], [,b]) => (b as number) - (a as number))[0]?.[0] || 'No activity';

    return NextResponse.json({
      total_activities: totalActivities,
      success_rate: Math.round(successRate * 100) / 100,
      activities_today: activitiesToday,
      activities_this_week: activitiesThisWeek,
      most_active_admin: mostActiveAdmin
    });

  } catch (error) {
    console.error('Manual calculation error:', error);
    return NextResponse.json({
      total_activities: 0,
      success_rate: 0,
      activities_today: 0,
      activities_this_week: 0,
      most_active_admin: 'Error calculating'
    });
  }
}
