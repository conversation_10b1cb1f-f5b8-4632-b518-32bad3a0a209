"use client";

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Home, Search, User, Store } from 'lucide-react';
import { cn } from '@/lib/utils';
import { PiShoppingCartLight } from "react-icons/pi";

type NavItem = {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
};

const navItems: NavItem[] = [
  { name: 'Home', href: '/', icon: Home },
  { name: 'Shop', href: '/shop', icon: Store },
  { name: 'Search', href: '/search', icon: Search },
  { name: 'Cart', href: '/cart', icon: PiShoppingCartLight },
  { name: 'Profile', href: '/account', icon: User },
];

export default function BottomNavigation() {
  const pathname = usePathname();

  return (
    <nav 
      aria-label="Main navigation" 
      className="fixed bottom-2 left-4 right-4 z-50 md:hidden bg-background/80 backdrop-blur-lg border-t border-border/50 pb-safe scrollbar-hide rounded-2xl hidden"
    >
      <div className="flex justify-around items-center py-2 px-4">
        {navItems.map((item) => {
          const isActive = pathname === item.href || 
            (item.href === '/shop' && pathname.startsWith('/shop')) ||
            (item.href === '/account' && pathname.startsWith('/account'));
          
          const IconComponent = item.icon;
          
          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex flex-col items-center justify-center min-w-[44px] min-h-[44px] py-1 px-2 rounded-lg transition-neo",
                isActive 
                  ? "text-primary bg-primary/10 neo-shadow-light" 
                  : "text-muted-foreground hover:text-foreground hover:bg-muted/50"
              )}
            >
              <IconComponent 
                className={cn(
                  "w-5 h-5 mb-1 transition-transform duration-150",
                  isActive && "scale-110"
                )} 
              />
              <span className={cn(
                "text-xs font-medium transition-colors duration-150",
                isActive ? "text-primary" : "text-muted-foreground"
              )}>
                {item.name}
              </span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
}
