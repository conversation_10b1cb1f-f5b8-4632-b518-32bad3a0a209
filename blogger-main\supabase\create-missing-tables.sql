-- =====================================================
-- CREATE ALL MISSING TABLES - Fix 404 and communication errors
-- This script creates all tables that are missing from the database
-- =====================================================

-- STEP 1: CREATE CONTACT_MESSAGES TABLE (FIXING 404 ERROR)
-- =====================================================

CREATE TABLE IF NOT EXISTS public.contact_messages (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text NOT NULL,
    email text NOT NULL,
    subject text,
    message text NOT NULL,
    status text DEFAULT 'unread' CHECK (status IN ('unread', 'read', 'replied', 'archived')),
    ip_address text,
    user_agent text,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- STEP 2: CREATE USER_SUBSCRIPTIONS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.user_subscriptions (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid UNIQUE REFERENCES public.profiles(id) ON DELETE CASCADE,
    plan text NOT NULL CHECK (plan IN ('free', 'premium', 'pro')),
    status text NOT NULL CHECK (status IN ('active', 'cancelled', 'expired', 'trialing')),
    start_date timestamp with time zone DEFAULT timezone('utc'::text, now()),
    end_date timestamp with time zone,
    stripe_subscription_id text,
    stripe_customer_id text,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- Add unique constraint if table already exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'user_subscriptions_user_id_key'
        AND table_name = 'user_subscriptions'
    ) THEN
        ALTER TABLE public.user_subscriptions ADD CONSTRAINT user_subscriptions_user_id_key UNIQUE (user_id);
        RAISE NOTICE '✅ Added unique constraint on user_subscriptions.user_id';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '⚠️  Could not add unique constraint: %', SQLERRM;
END $$;

-- STEP 3: CREATE LIKES TABLE (SEPARATE FROM ARTICLE_LIKES)
-- =====================================================

CREATE TABLE IF NOT EXISTS public.likes (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE,
    article_id uuid REFERENCES public.articles(id) ON DELETE CASCADE,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    UNIQUE(user_id, article_id)
);

-- STEP 4: CREATE NEWSLETTER_SUBSCRIBERS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.newsletter_subscribers (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    email text UNIQUE NOT NULL,
    name text,
    status text DEFAULT 'active' CHECK (status IN ('active', 'unsubscribed', 'bounced')),
    subscribed_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    unsubscribed_at timestamp with time zone,
    metadata jsonb DEFAULT '{}'::jsonb
);

-- STEP 5: CREATE ANALYTICS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.analytics (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    article_id uuid REFERENCES public.articles(id) ON DELETE CASCADE,
    user_id uuid REFERENCES public.profiles(id) ON DELETE SET NULL,
    event_type text NOT NULL, -- 'view', 'like', 'share', 'comment'
    ip_address text,
    user_agent text,
    referrer text,
    session_id text,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- STEP 6: CREATE NOTIFICATIONS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.notifications (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE,
    title text NOT NULL,
    message text NOT NULL,
    type text DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
    is_read boolean DEFAULT false,
    action_url text,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- STEP 7: CREATE MEDIA_UPLOADS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.media_uploads (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE,
    filename text NOT NULL,
    original_filename text NOT NULL,
    file_size bigint,
    mime_type text,
    file_path text NOT NULL,
    bucket_name text NOT NULL,
    is_public boolean DEFAULT true,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- STEP 8: ENABLE ROW LEVEL SECURITY
-- =====================================================

ALTER TABLE public.contact_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.newsletter_subscribers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.media_uploads ENABLE ROW LEVEL SECURITY;

-- STEP 9: CREATE RLS POLICIES
-- =====================================================

-- Contact messages policies (admin only)
DROP POLICY IF EXISTS "Admins can manage contact messages" ON public.contact_messages;
CREATE POLICY "Admins can manage contact messages" ON public.contact_messages 
    FOR ALL USING (
        EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
    );

-- User subscriptions policies
DROP POLICY IF EXISTS "Users can view their subscriptions" ON public.user_subscriptions;
DROP POLICY IF EXISTS "Users can update their subscriptions" ON public.user_subscriptions;
CREATE POLICY "Users can view their subscriptions" ON public.user_subscriptions 
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update their subscriptions" ON public.user_subscriptions 
    FOR UPDATE USING (auth.uid() = user_id);

-- Likes policies
DROP POLICY IF EXISTS "Users can manage their likes" ON public.likes;
CREATE POLICY "Users can manage their likes" ON public.likes 
    FOR ALL USING (auth.uid() = user_id);

-- Newsletter subscribers policies (admin only)
DROP POLICY IF EXISTS "Admins can manage newsletter subscribers" ON public.newsletter_subscribers;
CREATE POLICY "Admins can manage newsletter subscribers" ON public.newsletter_subscribers 
    FOR ALL USING (
        EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
    );

-- Analytics policies (admin only for viewing, automatic for creation)
DROP POLICY IF EXISTS "Admins can view analytics" ON public.analytics;
DROP POLICY IF EXISTS "Anyone can create analytics" ON public.analytics;
CREATE POLICY "Admins can view analytics" ON public.analytics 
    FOR SELECT USING (
        EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
    );
CREATE POLICY "Anyone can create analytics" ON public.analytics 
    FOR INSERT WITH CHECK (true);

-- Notifications policies
DROP POLICY IF EXISTS "Users can view their notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can update their notifications" ON public.notifications;
CREATE POLICY "Users can view their notifications" ON public.notifications 
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update their notifications" ON public.notifications 
    FOR UPDATE USING (auth.uid() = user_id);

-- Media uploads policies
DROP POLICY IF EXISTS "Users can view public media" ON public.media_uploads;
DROP POLICY IF EXISTS "Users can manage their media" ON public.media_uploads;
CREATE POLICY "Users can view public media" ON public.media_uploads 
    FOR SELECT USING (is_public = true OR auth.uid() = user_id);
CREATE POLICY "Users can manage their media" ON public.media_uploads 
    FOR ALL USING (auth.uid() = user_id);

-- STEP 10: CREATE STORAGE BUCKETS (ZAR PRICING SUPPORT)
-- =====================================================

-- Create avatars bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'avatars',
    'avatars',
    true,
    52428800, -- 50MB
    ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']
)
ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Create articles bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'articles',
    'articles',
    true,
    52428800, -- 50MB
    ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'video/webm']
)
ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Create products bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'products',
    'products',
    true,
    52428800, -- 50MB
    ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']
)
ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Create media bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'media',
    'media',
    true,
    104857600, -- 100MB
    ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'video/webm', 'audio/mpeg', 'audio/wav', 'application/pdf', 'application/zip']
)
ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- STEP 11: CREATE STORAGE POLICIES
-- =====================================================

-- Drop existing storage policies
DROP POLICY IF EXISTS "Avatar images are publicly accessible" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can upload an avatar" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can update their own avatar" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can delete their own avatar" ON storage.objects;

-- Avatars bucket policies
CREATE POLICY "Avatar images are publicly accessible" ON storage.objects
    FOR SELECT USING (bucket_id = 'avatars');

CREATE POLICY "Anyone can upload an avatar" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'avatars' AND auth.role() = 'authenticated');

CREATE POLICY "Anyone can update their own avatar" ON storage.objects
    FOR UPDATE USING (bucket_id = 'avatars' AND auth.role() = 'authenticated');

CREATE POLICY "Anyone can delete their own avatar" ON storage.objects
    FOR DELETE USING (bucket_id = 'avatars' AND auth.role() = 'authenticated');

-- STEP 12: CREATE INDEXES FOR PERFORMANCE
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_contact_messages_status ON public.contact_messages(status);
CREATE INDEX IF NOT EXISTS idx_contact_messages_created_at ON public.contact_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON public.user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON public.user_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_likes_article_id ON public.likes(article_id);
CREATE INDEX IF NOT EXISTS idx_likes_user_id ON public.likes(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_article_id ON public.analytics(article_id);
CREATE INDEX IF NOT EXISTS idx_analytics_created_at ON public.analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON public.notifications(is_read);

-- STEP 13: REFRESH SCHEMA AND SUCCESS MESSAGE
-- =====================================================

NOTIFY pgrst, 'reload schema';

DO $$
DECLARE
    tables_created INTEGER;
    buckets_created INTEGER;
BEGIN
    -- Count newly created tables
    SELECT COUNT(*) INTO tables_created
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name IN ('contact_messages', 'user_subscriptions', 'likes', 'newsletter_subscribers', 'analytics', 'notifications', 'media_uploads');
    
    -- Count storage buckets
    SELECT COUNT(*) INTO buckets_created
    FROM storage.buckets 
    WHERE id IN ('avatars', 'articles', 'products', 'media');
    
    RAISE NOTICE '🎉 MISSING TABLES CREATION COMPLETE!';
    RAISE NOTICE '==========================================';
    RAISE NOTICE '📊 RESULTS:';
    RAISE NOTICE 'Tables created/verified: %/7', tables_created;
    RAISE NOTICE 'Storage buckets: %/4', buckets_created;
    RAISE NOTICE '==========================================';
    RAISE NOTICE '✅ FIXED ISSUES:';
    RAISE NOTICE '1. contact_messages table created (fixes 404 error)';
    RAISE NOTICE '2. user_subscriptions table created';
    RAISE NOTICE '3. All missing tables added';
    RAISE NOTICE '4. Storage buckets configured';
    RAISE NOTICE '5. RLS policies applied';
    RAISE NOTICE '6. Performance indexes created';
    RAISE NOTICE '==========================================';
    RAISE NOTICE '🚀 NEXT STEPS:';
    RAISE NOTICE '1. Refresh your application';
    RAISE NOTICE '2. Test admin dashboard features';
    RAISE NOTICE '3. Verify contact form works';
    RAISE NOTICE '4. Check user management';
    RAISE NOTICE '==========================================';
END $$;
