'use client';

import * as React from "react";
import { Plus, X } from "lucide-react";
import { Input } from "./input";
import { Button } from "./button";
import { cn } from "@/lib/utils";

interface FeatureInputProps {
  features: string[];
  onChange: (features: string[]) => void;
  className?: string;
}

export function FeatureInput({
  features = [],
  onChange,
  className,
}: FeatureInputProps) {
  const [newFeature, setNewFeature] = React.useState("");

  // Add a new feature
  const handleAddFeature = () => {
    if (newFeature.trim() !== "") {
      const updatedFeatures = [...features, newFeature.trim()];
      onChange(updatedFeatures);
      setNewFeature("");
    }
  };

  // Remove a feature
  const handleRemoveFeature = (index: number) => {
    const updatedFeatures = [...features];
    updatedFeatures.splice(index, 1);
    onChange(updatedFeatures);
  };

  // Handle key press (Enter to add)
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddFeature();
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex gap-2">
        <Input
          value={newFeature}
          onChange={(e) => setNewFeature(e.target.value)}
          onKeyDown={handleKeyPress}
          placeholder="Add a product feature..."
          className="flex-1"
        />
        <Button 
          type="button" 
          onClick={handleAddFeature} 
          disabled={newFeature.trim() === ""}
          variant="outline"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add
        </Button>
      </div>

      {features.length > 0 && (
        <div className="space-y-2">
          {features.map((feature, index) => (
            <div 
              key={index} 
              className="flex items-center justify-between p-2 rounded-md bg-muted/50 border"
            >
              <span className="text-sm">{feature}</span>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => handleRemoveFeature(index)}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
