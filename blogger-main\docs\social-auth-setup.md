# 🔐 Social Authentication Setup Guide

This guide will help you set up social authentication for the Thabo Bester platform using Google, GitHub, Facebook, and Twitter.

## 📋 Prerequisites

1. Access to your Supabase project dashboard
2. Accounts with the social providers you want to enable
3. Your Supabase project URL: `https://nugwxsejlxsyppzckcrw.supabase.co`

## 🚀 Quick Setup Overview

All social authentication is handled through Supabase. You need to:
1. Create OAuth apps with each provider
2. Configure the providers in your Supabase dashboard
3. Add the credentials to your environment variables

## 🔧 Provider Setup Instructions

### 1. Google OAuth Setup

#### Step 1: Create Google OAuth Credentials
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Set Application type to "Web application"
6. Add Authorized redirect URIs:
   - `https://nugwxsejlxsyppzckcrw.supabase.co/auth/v1/callback`
   - `http://localhost:5173/auth/callback` (for development)

#### Step 2: Configure in Supabase
1. Go to your Supabase dashboard
2. Navigate to Authentication → Providers
3. Enable Google provider
4. Add your Google Client ID and Client Secret
5. Save the configuration

### 2. GitHub OAuth Setup

#### Step 1: Create GitHub OAuth App
1. Go to [GitHub Settings](https://github.com/settings/developers)
2. Click "Developer settings" → "OAuth Apps" → "New OAuth App"
3. Fill in the details:
   - Application name: "Thabo Bester Platform"
   - Homepage URL: `https://your-domain.com`
   - Authorization callback URL: `https://nugwxsejlxsyppzckcrw.supabase.co/auth/v1/callback`

#### Step 2: Configure in Supabase
1. Go to your Supabase dashboard
2. Navigate to Authentication → Providers
3. Enable GitHub provider
4. Add your GitHub Client ID and Client Secret
5. Save the configuration

### 3. Facebook OAuth Setup

#### Step 1: Create Facebook App
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Click "Create App" → "Consumer" → "Next"
3. Add "Facebook Login" product to your app
4. In Facebook Login settings, add Valid OAuth Redirect URIs:
   - `https://nugwxsejlxsyppzckcrw.supabase.co/auth/v1/callback`

#### Step 2: Configure in Supabase
1. Go to your Supabase dashboard
2. Navigate to Authentication → Providers
3. Enable Facebook provider
4. Add your Facebook App ID and App Secret
5. Save the configuration

### 4. Twitter OAuth Setup

#### Step 1: Create Twitter App
1. Go to [Twitter Developer Portal](https://developer.twitter.com/)
2. Create a new app
3. In app settings, set:
   - Callback URL: `https://nugwxsejlxsyppzckcrw.supabase.co/auth/v1/callback`
   - Website URL: `https://your-domain.com`

#### Step 2: Configure in Supabase
1. Go to your Supabase dashboard
2. Navigate to Authentication → Providers
3. Enable Twitter provider
4. Add your Twitter API Key and API Secret Key
5. Save the configuration

## 🔑 Environment Variables

After setting up the providers, add the following to your `.env` file:

```env
# Social Authentication
VITE_GOOGLE_CLIENT_ID=your_google_client_id
VITE_GITHUB_CLIENT_ID=your_github_client_id
VITE_FACEBOOK_APP_ID=your_facebook_app_id
VITE_TWITTER_API_KEY=your_twitter_api_key
```

## 🧪 Testing Social Authentication

1. Start your development server: `npm run dev`
2. Navigate to the login page: `http://localhost:5173/login`
3. Click on any social provider button
4. Complete the OAuth flow
5. You should be redirected back to the dashboard

## 🔒 Security Considerations

### Redirect URLs
Always use HTTPS in production and ensure your redirect URLs are correctly configured:
- Production: `https://nugwxsejlxsyppzckcrw.supabase.co/auth/v1/callback`
- Development: `http://localhost:5173/auth/callback`

### Scopes
The default scopes requested are:
- **Google**: `openid email profile`
- **GitHub**: `user:email`
- **Facebook**: `email`
- **Twitter**: `users.read tweet.read`

### User Data Handling
After successful authentication, user data is automatically:
1. Stored in the `auth.users` table
2. Profile created in the `profiles` table
3. Role assigned (default: 'user')

## 🐛 Troubleshooting

### Common Issues

#### 1. "Invalid redirect URI"
- Ensure the redirect URI in your OAuth app matches exactly: `https://nugwxsejlxsyppzckcrw.supabase.co/auth/v1/callback`
- Check for trailing slashes or typos

#### 2. "Client ID not found"
- Verify the Client ID is correctly added to Supabase dashboard
- Check environment variables are loaded correctly

#### 3. "Access denied"
- User may have denied permissions
- Check OAuth app permissions and scopes

#### 4. "Authentication failed"
- Check Supabase logs in the dashboard
- Verify provider configuration is complete

### Debug Mode
Enable debug mode by adding to your `.env`:
```env
VITE_DEBUG_AUTH=true
```

## 📱 Mobile Considerations

For mobile apps, you'll need to configure additional redirect URLs:
- iOS: `com.thabobester.app://auth/callback`
- Android: `com.thabobester.app://auth/callback`

## 🔄 User Flow

1. User clicks social login button
2. Redirected to provider's OAuth page
3. User grants permissions
4. Provider redirects back to Supabase
5. Supabase creates/updates user record
6. User redirected to dashboard based on role

## 📊 Analytics

Track social login usage in your analytics:
- Google Analytics events
- Supabase dashboard metrics
- Custom tracking in your application

## 🆘 Support

If you encounter issues:
1. Check Supabase dashboard logs
2. Verify provider app settings
3. Test with different browsers
4. Check network connectivity

## 🔗 Useful Links

- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [Google OAuth Documentation](https://developers.google.com/identity/protocols/oauth2)
- [GitHub OAuth Documentation](https://docs.github.com/en/developers/apps/building-oauth-apps)
- [Facebook Login Documentation](https://developers.facebook.com/docs/facebook-login/)
- [Twitter OAuth Documentation](https://developer.twitter.com/en/docs/authentication/oauth-2-0)

---

**Note**: Remember to update your production URLs when deploying to a live domain!
