"use client";

import { ArrowRight } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Facebook, Instagram, Twitter } from 'lucide-react';
import { motion } from 'framer-motion';

export default function CTASection() {
  const currentYear = new Date().getFullYear();

  return (
    <section className="relative py-24 overflow-hidden">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/images/tennis-cta-bg.jpg"
          alt="Tennis player with equipment"
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-black/60" />
      </div>

      {/* Content */}
      <div className="container relative z-10 mx-auto px-4">
        <div className="text-center mb-12 mt-12">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-4xl md:text-4xl font-bold text-white mb-4"
          >
            Explore our Latest Tennis Fashion Gear to complement your hobby
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="text-xl text-white/80 max-w-3xl mx-auto mb-8"
          >
            Elevate your game with premium equipment and expert mentorship. Join our community of tennis enthusiasts today.
          </motion.p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Button asChild size="lg" className="w-full sm:w-auto bg-white/40 text-primary hover:bg-white/90">
                <Link href="/shop">
                  Shop Now
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <Button asChild size="lg" variant="outline" className="w-full sm:w-auto border-white text-white hover:bg-white/10">
                <Link href="#mentorship">
                  Explore Mentorship
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </motion.div>
          </div>
        </div>

        {/* Footer Layout - White Rounded Section */}
        <div className="bg-background/65 rounded-3xl p-10 mt-48 shadow-2xl backdrop-blur-sm px-4">
          <div className="container mx-auto px-4 py-2">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-10 mb-12">
              {/* Brand Column */}
              <div className="md:col-span-1">
                <Link href="/" className="text-xl font-bold text-primary flex items-center gap-1 mb-4">
                  <span className="text-2xl">🎾</span>
                  <span>Tennis Whisperer</span>
                </Link>
                <p className="text-muted-foreground mb-6 max-w-xs">
                  Premium tennis equipment for players of all levels. Quality products, expert advice, and exceptional service.
                </p>
                <div className="flex space-x-4">
                  <a href="#" className="text-foreground/60 hover:text-primary transition-colors">
                    <span className="sr-only">Twitter</span>
                    <Twitter className="h-5 w-5" />
                  </a>
                  <a href="#" className="text-foreground/60 hover:text-primary transition-colors">
                    <span className="sr-only">Instagram</span>
                    <Instagram className="h-5 w-5" />
                  </a>
                  <a href="#" className="text-foreground/60 hover:text-primary transition-colors">
                    <span className="sr-only">Facebook</span>
                    <Facebook className="h-5 w-5" />
                  </a>
                </div>
              </div>

              {/* Shop Column */}
              <div>
                <h3 className="font-semibold text-foreground mb-4 text-sm uppercase tracking-wider">Shop</h3>
                <ul className="space-y-3">
                  <li><Link href="/shop?category=rackets" className="text-muted-foreground hover:text-primary transition-colors">Rackets</Link></li>
                  <li><Link href="/shop?category=balls" className="text-muted-foreground hover:text-primary transition-colors">Balls</Link></li>
                  <li><Link href="/shop?category=apparel" className="text-muted-foreground hover:text-primary transition-colors">Apparel</Link></li>
                  <li><Link href="/shop?category=accessories" className="text-muted-foreground hover:text-primary transition-colors">Accessories</Link></li>
                  <li><Link href="/shop?category=new" className="text-muted-foreground hover:text-primary transition-colors">New Arrivals</Link></li>
                  <li><Link href="/shop?category=sale" className="text-muted-foreground hover:text-primary transition-colors">Sale</Link></li>
                </ul>
              </div>

              {/* Company Column */}
              <div>
                <h3 className="font-semibold text-foreground mb-4 text-sm uppercase tracking-wider">Company</h3>
                <ul className="space-y-3">
                  <li><Link href="/about" className="text-muted-foreground hover:text-primary transition-colors">About Us</Link></li>
                  <li><Link href="/contact" className="text-muted-foreground hover:text-primary transition-colors">Contact</Link></li>
                  <li><Link href="/blog" className="text-muted-foreground hover:text-primary transition-colors">Blog</Link></li>
                  <li><Link href="/careers" className="text-muted-foreground hover:text-primary transition-colors">Careers</Link></li>
                  <li><Link href="/privacy" className="text-muted-foreground hover:text-primary transition-colors">Privacy Policy</Link></li>
                  <li><Link href="/terms" className="text-muted-foreground hover:text-primary transition-colors">Terms of Service</Link></li>
                </ul>
              </div>

              {/* Newsletter Column */}
              <div>
                <h3 className="font-semibold text-foreground mb-4 text-sm uppercase tracking-wider">Stay Updated</h3>
                <p className="text-muted-foreground mb-4">Subscribe to our newsletter for the latest products and exclusive offers.</p>
                <div className="flex gap-2">
                  <Input
                    type="email"
                    placeholder="Your email"
                    className="max-w-[220px]"
                  />
                  <Button type="submit" size="sm">
                    Subscribe
                  </Button>
                </div>
              </div>
            </div>

            <div className="mt-10 text-center text-xs text-muted-foreground border-t border-gray-300 pt-4">
              © {currentYear} Tennis Whisperer. All rights reserved.
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
