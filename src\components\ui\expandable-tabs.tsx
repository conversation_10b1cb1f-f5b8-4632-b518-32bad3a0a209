"use client";

import * as React from "react";
import { AnimatePresence, motion } from "framer-motion";
import { useOnClickOutside } from "usehooks-ts";
import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";
import { usePathname } from "next/navigation";

interface Tab {
  title: string;
  icon: LucideIcon;
  href?: string;
  type?: never;
}

interface Separator {
  type: "separator";
  title?: never;
  icon?: never;
  href?: never;
}

type TabItem = Tab | Separator;

interface ExpandableTabsProps {
  tabs: TabItem[];
  className?: string;
  activeColor?: string;
  expanded?: boolean;
  onChange?: (index: number | null) => void;
  onTabClick?: (href: string) => void;
}

const buttonVariants = {
  initial: {
    gap: 0,
    paddingLeft: ".5rem",
    paddingRight: ".5rem",
  },
  animate: (isSelected: boolean) => ({
    gap: isSelected ? ".5rem" : 0,
    paddingLeft: isSelected ? "1rem" : ".5rem",
    paddingRight: isSelected ? "1rem" : ".5rem",
  }),
};

const spanVariants = {
  initial: { width: 0, opacity: 0 },
  animate: { width: "auto", opacity: 1 },
  exit: { width: 0, opacity: 0 },
};

const transition = { delay: 0.1, type: "spring", bounce: 0, duration: 0.6 };

export function ExpandableTabs({
  tabs,
  className,
  activeColor = "text-primary",
  expanded = false,
  onChange,
  onTabClick,
}: ExpandableTabsProps) {
  const [hoveredIndex, setHoveredIndex] = React.useState<number | null>(null);
  const [selected, setSelected] = React.useState<number | null>(null);
  const outsideClickRef = React.useRef(null);
  const pathname = usePathname();

  // Find active tab based on current path
  const activeTabIndex = React.useMemo(() => {
    if (!pathname) return null;
    
    // Check for exact matches first
    const exactMatch = tabs.findIndex(
      tab => tab.type !== "separator" && tab.href === pathname
    );
    
    if (exactMatch !== -1) return exactMatch;
    
    // Then check for partial matches (for nested routes)
    return tabs.findIndex(
      tab => tab.type !== "separator" && 
             tab.href && 
             pathname.startsWith(tab.href.split('?')[0]) // Handle paths with query params
    );
  }, [pathname, tabs]);

  useOnClickOutside(outsideClickRef, () => {
    if (!expanded) {
      setSelected(null);
      setHoveredIndex(null);
      onChange?.(null);
    }
  });

  const handleSelect = (index: number) => {
    const tab = tabs[index];
    if (tab.type !== "separator" && tab.href) {
      onTabClick?.(tab.href);
    }
    
    if (!expanded) {
      setSelected(index === selected ? null : index);
      onChange?.(index === selected ? null : index);
    }
  };

  const Separator = () => (
    <div className="mx-1 h-[24px] w-[1.2px] bg-border" aria-hidden="true" />
  );

  return (
    <div
      ref={outsideClickRef}
      className={cn(
        "flex flex-wrap items-center gap-2 rounded-full border bg-background/80 backdrop-blur-sm p-1 shadow-sm",
        className
      )}
    >
      {tabs.map((tab, index) => {
        if (tab.type === "separator") {
          return <Separator key={`separator-${index}`} />;
        }

        const Icon = tab.icon;
        const isActive = activeTabIndex === index;
        const isHovered = hoveredIndex === index;
        const isExpanded = expanded || isActive || isHovered || selected === index;
        
        return (
          <motion.button
            key={tab.title}
            variants={buttonVariants}
            initial={false}
            animate="animate"
            custom={isExpanded}
            onClick={() => handleSelect(index)}
            onMouseEnter={() => setHoveredIndex(index)}
            onMouseLeave={() => setHoveredIndex(null)}
            transition={transition}
            className={cn(
              "relative flex items-center rounded-full px-2 py-1.5 text-sm font-medium transition-colors duration-300",
              isActive
                ? cn("bg-muted", activeColor)
                : isExpanded
                ? "bg-muted/50 text-foreground"
                : "text-muted-foreground hover:bg-muted hover:text-foreground"
            )}
          >
            <Icon size={20} />
            <AnimatePresence initial={false}>
              {isExpanded && (
                <motion.span
                  variants={spanVariants}
                  initial="initial"
                  animate="animate"
                  exit="exit"
                  transition={transition}
                  className="overflow-hidden"
                >
                  {tab.title}
                </motion.span>
              )}
            </AnimatePresence>
          </motion.button>
        );
      })}
    </div>
  );
}
