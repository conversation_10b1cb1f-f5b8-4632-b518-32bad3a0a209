"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search, 
  Plus, 
  X, 
  Package,
  ExternalLink,
  Star,
  DollarSign
} from 'lucide-react';
import { createClient } from '@/utils/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface Product {
  id: string;
  name: string;
  price: number;
  image_url: string;
  slug: string;
  category: string;
  description: string;
}

interface ProductReference {
  product: Product;
  reference_type: 'featured' | 'mentioned' | 'related';
  position: number;
}

interface ProductSelectorProps {
  articleId?: string;
  initialReferences?: ProductReference[];
  onReferencesChange?: (references: ProductReference[]) => void;
  className?: string;
}

export function ProductSelector({
  articleId,
  initialReferences = [],
  onReferencesChange,
  className = ''
}: ProductSelectorProps) {
  const [products, setProducts] = useState<Product[]>([]);
  const [references, setReferences] = useState<ProductReference[]>(initialReferences);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);
  const [showProductSearch, setShowProductSearch] = useState(false);
  const { toast } = useToast();
  const supabase = createClient();

  useEffect(() => {
    fetchProducts();
  }, [searchTerm, selectedCategory]);

  useEffect(() => {
    onReferencesChange?.(references);
  }, [references, onReferencesChange]);

  const fetchProducts = async () => {
    try {
      setIsLoading(true);
      
      let query = supabase
        .from('products')
        .select('id, name, price, image_url, slug, category, description')
        .eq('is_active', true);

      if (searchTerm) {
        query = query.or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
      }

      if (selectedCategory !== 'all') {
        query = query.eq('category', selectedCategory);
      }

      const { data, error } = await query
        .order('name')
        .limit(20);

      if (error) throw error;
      setProducts(data || []);
    } catch (error) {
      console.error('Error fetching products:', error);
      toast({
        title: "Error",
        description: "Failed to fetch products",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const addProductReference = (product: Product, referenceType: 'featured' | 'mentioned' | 'related') => {
    // Check if product is already referenced
    const existingRef = references.find(ref => ref.product.id === product.id);
    if (existingRef) {
      toast({
        title: "Product already referenced",
        description: "This product is already referenced in this article",
        variant: "destructive",
      });
      return;
    }

    const newReference: ProductReference = {
      product,
      reference_type: referenceType,
      position: references.length
    };

    setReferences([...references, newReference]);
    setShowProductSearch(false);
    
    toast({
      title: "Product added",
      description: `${product.name} added as ${referenceType} product`,
    });
  };

  const removeProductReference = (productId: string) => {
    setReferences(references.filter(ref => ref.product.id !== productId));
    toast({
      title: "Product removed",
      description: "Product reference removed from article",
    });
  };

  const updateReferenceType = (productId: string, newType: 'featured' | 'mentioned' | 'related') => {
    setReferences(references.map(ref => 
      ref.product.id === productId 
        ? { ...ref, reference_type: newType }
        : ref
    ));
  };

  const moveReference = (productId: string, direction: 'up' | 'down') => {
    const currentIndex = references.findIndex(ref => ref.product.id === productId);
    if (currentIndex === -1) return;

    const newReferences = [...references];
    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;

    if (targetIndex >= 0 && targetIndex < newReferences.length) {
      [newReferences[currentIndex], newReferences[targetIndex]] = 
      [newReferences[targetIndex], newReferences[currentIndex]];
      
      // Update positions
      newReferences.forEach((ref, index) => {
        ref.position = index;
      });
      
      setReferences(newReferences);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const getReferenceTypeColor = (type: string) => {
    switch (type) {
      case 'featured': return 'bg-primary/20 text-primary border-primary/30';
      case 'mentioned': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'related': return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Package className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold text-foreground">Product References</h3>
        </div>
        <Button
          onClick={() => setShowProductSearch(!showProductSearch)}
          variant="outline"
          size="sm"
          className="glass-effect border-white/20 hover:glass-effect-subtle min-h-[36px]"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Product
        </Button>
      </div>

      {/* Product Search */}
      {showProductSearch && (
        <Card className="glass-effect border border-white/10 neo-shadow">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold text-foreground">Search Products</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Search Controls */}
            <div className="flex gap-2">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 glass-effect border-white/20 text-foreground"
                />
              </div>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-48 glass-effect border-white/20 text-foreground">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent className="glass-effect border-white/20">
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="rackets">Rackets</SelectItem>
                  <SelectItem value="strings">Strings</SelectItem>
                  <SelectItem value="shoes">Shoes</SelectItem>
                  <SelectItem value="apparel">Apparel</SelectItem>
                  <SelectItem value="accessories">Accessories</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Product Results */}
            <div className="max-h-64 overflow-y-auto space-y-2">
              {isLoading ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
                </div>
              ) : products.length > 0 ? (
                products.map((product) => (
                  <div key={product.id} className="flex items-center gap-3 p-3 glass-effect border border-white/10 rounded-xl">
                    <img
                      src={product.image_url}
                      alt={product.name}
                      className="w-12 h-12 object-cover rounded-lg"
                    />
                    <div className="flex-1">
                      <h4 className="font-medium text-foreground">{product.name}</h4>
                      <p className="text-sm text-muted-foreground">{formatPrice(product.price)}</p>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        onClick={() => addProductReference(product, 'featured')}
                        variant="outline"
                        size="sm"
                        className="glass-effect border-primary/20 hover:bg-primary/20 text-primary min-h-[32px] px-2"
                      >
                        <Star className="h-3 w-3" />
                      </Button>
                      <Button
                        onClick={() => addProductReference(product, 'mentioned')}
                        variant="outline"
                        size="sm"
                        className="glass-effect border-blue-500/20 hover:bg-blue-500/20 text-blue-400 min-h-[32px] px-2"
                      >
                        M
                      </Button>
                      <Button
                        onClick={() => addProductReference(product, 'related')}
                        variant="outline"
                        size="sm"
                        className="glass-effect border-purple-500/20 hover:bg-purple-500/20 text-purple-400 min-h-[32px] px-2"
                      >
                        R
                      </Button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  No products found
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Referenced Products */}
      {references.length > 0 && (
        <Card className="glass-effect border border-white/10 neo-shadow">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold text-foreground">Referenced Products</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {references.map((reference, index) => (
              <div key={reference.product.id} className="flex items-center gap-3 p-3 glass-effect border border-white/10 rounded-xl">
                <img
                  src={reference.product.image_url}
                  alt={reference.product.name}
                  className="w-12 h-12 object-cover rounded-lg"
                />
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium text-foreground">{reference.product.name}</h4>
                    <Badge className={getReferenceTypeColor(reference.reference_type)}>
                      {reference.reference_type}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <DollarSign className="h-3 w-3" />
                    {formatPrice(reference.product.price)}
                  </div>
                </div>
                
                <div className="flex items-center gap-1">
                  <Select
                    value={reference.reference_type}
                    onValueChange={(value: 'featured' | 'mentioned' | 'related') => 
                      updateReferenceType(reference.product.id, value)
                    }
                  >
                    <SelectTrigger className="w-24 h-8 glass-effect border-white/20 text-foreground text-xs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="glass-effect border-white/20">
                      <SelectItem value="featured">Featured</SelectItem>
                      <SelectItem value="mentioned">Mentioned</SelectItem>
                      <SelectItem value="related">Related</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Button
                    onClick={() => moveReference(reference.product.id, 'up')}
                    variant="outline"
                    size="sm"
                    disabled={index === 0}
                    className="glass-effect border-white/20 hover:glass-effect-subtle min-h-[32px] min-w-[32px] p-0"
                  >
                    ↑
                  </Button>
                  
                  <Button
                    onClick={() => moveReference(reference.product.id, 'down')}
                    variant="outline"
                    size="sm"
                    disabled={index === references.length - 1}
                    className="glass-effect border-white/20 hover:glass-effect-subtle min-h-[32px] min-w-[32px] p-0"
                  >
                    ↓
                  </Button>
                  
                  <Button
                    onClick={() => removeProductReference(reference.product.id)}
                    variant="outline"
                    size="sm"
                    className="glass-effect border-destructive/20 hover:bg-destructive/20 text-destructive min-h-[32px] min-w-[32px] p-0"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Help Text */}
      {references.length === 0 && !showProductSearch && (
        <div className="text-center py-6 text-muted-foreground">
          <Package className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">No products referenced yet</p>
          <p className="text-xs">Add products to showcase Tennis Whisperer gear in your article</p>
        </div>
      )}
    </div>
  );
}
