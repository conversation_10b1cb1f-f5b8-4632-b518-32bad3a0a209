"use client";

import { useEffect, useState } from "react";
import { createClient } from "@/utils/supabase/client";
import { redirect, useRouter } from "next/navigation";
import { ChatInterface } from "@/components/chat/chat-interface";
import { sendMessage, getConversations, getMessages } from "@/utils/supabase/chat-utils";
import { uploadFile } from "@/utils/supabase/resource-utils";
import { Loader2 } from "lucide-react";
import { Tables } from "@/utils/supabase/database.types";

export default function ChatPage() {
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<Tables<'users'> | null>(null);
  const [contacts, setContacts] = useState<Array<{
    id: string;
    name: string;
    avatar?: string | null;
    lastMessage?: string | null;
    lastMessageTime?: Date | null;
    unreadCount?: number | null;
    status?: 'online' | 'offline' | 'away' | null;
    conversationId?: string;
  }>>([]);
  const [messages, setMessages] = useState<Record<string, any[]>>({});
  const router = useRouter();
  const supabase = createClient();

  // Check if user is authenticated
  useEffect(() => {
    const checkUser = async () => {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error || !session) {
        router.push('/auth/sign-in');
        return;
      }
      
      // Get user details
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', session.user.id)
        .single();
      
      if (userError || !userData) {
        console.error('Error fetching user data:', userError);
        return;
      }
      
      setUser(userData);
      
      // Load mentors as contacts
      await loadMentorContacts(userData.id);
      
      setLoading(false);
    };
    
    checkUser();
  }, []);

  // Load mentors assigned to this student
  const loadMentorContacts = async (studentId: string) => {
    try {
      // Get student enrollments to find assigned mentors
      const { data: enrollments, error: enrollmentError } = await supabase
        .from('student_enrollments')
        .select('mentor_id, program_id')
        .eq('student_id', studentId)
        .eq('status', 'active');
      
      if (enrollmentError || !enrollments || enrollments.length === 0) {
        console.error('No active enrollments found');
        return;
      }
      
      // Get mentor details
      const mentorIds = Array.from(new Set(enrollments.map(e => e.mentor_id)));
      
      const mentorContacts: Array<{
        id: string;
        name: string;
        avatar?: string | null;
        lastMessage?: string | null;
        lastMessageTime?: Date | null;
        unreadCount?: number | null;
        status?: 'online' | 'offline' | 'away' | null;
        conversationId?: string;
      }> = [];
      
      for (const mentorId of mentorIds) {
        // Get mentor user details
        const { data: mentorData, error: mentorError } = await supabase
          .from('users')
          .select('*')
          .eq('id', mentorId)
          .single();
        
        if (mentorError || !mentorData) {
          console.error('Error fetching mentor data:', mentorError);
          continue;
        }
        
        // Get or create conversation
        const { data: conversationData, error: convError } = await supabase
          .from('messages')
          .select('conversation_id')
          .or(`and(sender_id.eq.${studentId},receiver_id.eq.${mentorId}),and(sender_id.eq.${mentorId},receiver_id.eq.${studentId})`)
          .limit(1);
        
        let conversationId;
        
        if (convError || !conversationData || conversationData.length === 0) {
          // Create a new conversation ID
          conversationId = `${studentId}_${mentorId}_${Date.now()}`;
        } else {
          conversationId = conversationData[0].conversation_id;
        }
        
        // Get latest message
        const { data: latestMessage, error: msgError } = await supabase
          .from('messages')
          .select('*')
          .eq('conversation_id', conversationId)
          .order('created_at', { ascending: false })
          .limit(1);
        
        // Get unread count
        const { count: unreadCount, error: countError } = await supabase
          .from('messages')
          .select('id', { count: 'exact' })
          .eq('conversation_id', conversationId)
          .eq('receiver_id', studentId)
          .eq('read', false);

        // Add to contacts
        mentorContacts.push({
          id: mentorData.id,
          name: mentorData.full_name || 'Mentor',
          avatar: mentorData.avatar_url,
          lastMessage: latestMessage && latestMessage.length > 0 ? latestMessage[0].content : 'Start a conversation',
          lastMessageTime: latestMessage && latestMessage.length > 0 ? new Date(latestMessage[0].created_at) : null,
          unreadCount: unreadCount || 0,
          status: 'offline' as const, // We would need a presence system to determine online status
          conversationId: conversationId,
        });
        
        // Load messages for this conversation
        if (conversationId) {
          await loadMessages(conversationId, mentorData.id);
        }
      }
      
      setContacts(mentorContacts);
    } catch (error) {
      console.error('Error loading mentor contacts:', error);
    }
  };

  // Load messages for a specific conversation
  const loadMessages = async (conversationId: string, contactId: string) => {
    try {
      const { data: messagesData, error } = await supabase
        .from('messages')
        .select('*')
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: true });
      
      if (error || !messagesData) {
        console.error('Error fetching messages:', error);
        return;
      }
      
      // Format messages
      const formattedMessages = messagesData.map((msg) => ({
        id: msg.id,
        content: msg.content,
        sender: msg.sender_id === user?.id ? 'user' : 'mentor',
        timestamp: new Date(msg.created_at),
        read: msg.read,
        attachmentUrl: msg.attachment_url || undefined,
      }));
      
      // Update messages state
      setMessages((prev) => ({
        ...prev,
        [contactId]: formattedMessages,
      }));
    } catch (error) {
      console.error('Error loading messages:', error);
    }
  };

  // Handle sending a message
  const handleSendMessage = async (contactId: string, content: string, attachment?: File) => {
    if (!user) return;
    
    try {
      // Find the conversation with this contact
      const contact = contacts.find((c) => c.id === contactId);
      
      if (!contact || !contact.conversationId) {
        console.error('Conversation not found');
        return;
      }
      
      let attachmentUrl: string | undefined = undefined;
      
      // Upload attachment if provided
      if (attachment) {
        const fileData = await uploadFile(
          attachment, 
          'chat-attachments', 
          `${user.id}/${Date.now()}_${attachment.name}`
        );
        
        if (fileData) {
          attachmentUrl = fileData.url;
        }
      }
      
      // Send message to Supabase
      await sendMessage({
        conversationId: contact.conversationId,
        senderId: user.id,
        receiverId: contactId,
        content,
        attachmentUrl,
      });
      
      // Reload messages
      await loadMessages(contact.conversationId, contactId);
      
      // Update last message in contacts list
      setContacts((prev) => 
        prev.map((c) => 
          c.id === contactId
            ? {
                ...c,
                lastMessage: content,
                lastMessageTime: new Date(),
              }
            : c
        )
      );
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-lg">Loading chat...</span>
      </div>
    );
  }

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-6">Chat with Your Mentor</h1>
      <ChatInterface
        currentUserId={user?.id || ''}
        initialContacts={contacts}
        initialMessages={messages}
        onSendMessage={handleSendMessage}
      />
    </div>
  );
}
