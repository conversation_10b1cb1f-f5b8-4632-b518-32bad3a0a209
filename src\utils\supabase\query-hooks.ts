/**
 * Supabase integration with TanStack Query
 * 
 * This file provides hooks for data fetching with Supabase and TanStack Query.
 * It uses @supabase-cache-helpers/postgrest-react-query to simplify the integration.
 */

import { createClient } from './client';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  useQuery as supabaseQueryHook,
  useInsertMutation,
  useUpdateMutation,
  useDeleteMutation,
  useUpsertMutation
} from '@supabase-cache-helpers/postgrest-react-query';
import { PostgrestError } from '@supabase/supabase-js';
import { z } from 'zod';

// Create a Supabase client
const supabase = createClient();

// Export the query hooks for Supabase
export const useSelect = supabaseQueryHook;
export const useInsert = useInsertMutation;
export const useUpdate = useUpdateMutation;
export const useDelete = useDeleteMutation;
export const useUpsert = useUpsertMutation;

/**
 * Generic hook for fetching data from Supabase with TanStack Query
 * 
 * @param table The table name to query
 * @param schema The Zod schema to validate the data
 * @param options Additional options for the query
 * @returns The query result
 */
export function useSupabaseQuery<T extends z.ZodType>(
  table: string,
  schema: T,
  options: {
    queryKey?: string[];
    select?: string;
    filter?: (query: any) => any;
    enabled?: boolean;
  } = {}
) {
  const { queryKey = [table], select = '*', filter, enabled = true } = options;

  return useQuery({
    queryKey,
    queryFn: async () => {
      let query = supabase.from(table).select(select);
      
      if (filter) {
        query = filter(query);
      }
      
      const { data, error } = await query;
      
      if (error) {
        throw error;
      }
      
      // Validate data with Zod schema
      try {
        if (Array.isArray(data)) {
          return data.map(item => schema.parse(item)) as z.infer<T>[];
        } else {
          return schema.parse(data) as z.infer<T>;
        }
      } catch (validationError) {
        console.error('Validation error:', validationError);
        throw validationError;
      }
    },
    enabled,
  });
}

/**
 * Hook for inserting data into Supabase with TanStack Query
 * 
 * @param table The table name to insert into
 * @param schema The Zod schema to validate the data
 * @returns The mutation function and result
 */
export function useSupabaseInsert<T extends z.ZodType>(
  table: string,
  schema: T
) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: z.infer<T>) => {
      // Validate data with Zod schema
      const validatedData = schema.parse(data);
      
      const { data: result, error } = await supabase
        .from(table)
        .insert(validatedData)
        .select();
      
      if (error) {
        throw error;
      }
      
      return result;
    },
    onSuccess: () => {
      // Invalidate queries related to this table
      queryClient.invalidateQueries({ queryKey: [table] });
    },
  });
}

/**
 * Hook for updating data in Supabase with TanStack Query
 * 
 * @param table The table name to update
 * @param schema The Zod schema to validate the data
 * @returns The mutation function and result
 */
export function useSupabaseUpdate<T extends z.ZodObject<any>>(
  table: string,
  schema: T
) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<z.infer<T>> }) => {
      // Validate data with Zod schema (partial validation)
      const validatedData = schema.partial().parse(data);
      
      const { data: result, error } = await supabase
        .from(table)
        .update(validatedData)
        .eq('id', id)
        .select();
      
      if (error) {
        throw error;
      }
      
      return result;
    },
    onSuccess: () => {
      // Invalidate queries related to this table
      queryClient.invalidateQueries({ queryKey: [table] });
    },
  });
}

/**
 * Hook for deleting data from Supabase with TanStack Query
 * 
 * @param table The table name to delete from
 * @returns The mutation function and result
 */
export function useSupabaseDelete(table: string) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from(table)
        .delete()
        .eq('id', id);
      
      if (error) {
        throw error;
      }
      
      return true;
    },
    onSuccess: () => {
      // Invalidate queries related to this table
      queryClient.invalidateQueries({ queryKey: [table] });
    },
  });
}
