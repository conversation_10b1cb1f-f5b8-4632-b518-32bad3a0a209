import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Search } from '@/components/ui/search';
import {
  Plus,
  Trash2,
  Package,
  TrendingUp,
  FileText,
  Monitor
} from 'lucide-react';
import { supabase } from '../../../../supabase/supabase';
import { useAuth } from '../../../../supabase/auth';

interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  price: number;
  stock_quantity: number;
  category_id?: string;
  category?: { name: string; color: string };
  image_url?: string;
  status: 'active' | 'inactive' | 'draft';
  product_type: 'physical' | 'digital';
  file_type?: 'pdf' | 'ebook' | 'audiobook' | 'video' | 'audio' | 'course' | 'software' | 'template';
  file_size?: number;
  download_limit?: number;
  access_duration_days?: number;
  created_at: string;
  updated_at: string;
}

interface ProductCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  color: string;
}



export function ProductManagement() {
  const { toast } = useToast();
  const { user, userRole } = useAuth();
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');




  useEffect(() => {
    loadProducts();
    loadCategories();
  }, []);

  const loadProducts = async () => {
    try {
      setIsLoading(true);
      
      // Load products with a simple query first
      const { data: productsData, error: productsError } = await supabase
        .from('products')
        .select('*')
        .order('created_at', { ascending: false });

      if (productsError) {
        console.error('Products error:', productsError);
        throw productsError;
      }

      // Load categories separately
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('product_categories')
        .select('*');

      if (categoriesError) {
        console.error('Categories error:', categoriesError);
        // Don't throw error for categories, just continue without them
      }

      // Merge category data with products
      const productsWithCategories = productsData?.map(product => ({
        ...product,
        category: product.category_id && categoriesData
          ? categoriesData.find(cat => cat.id === product.category_id)
          : null
      })) || [];

      setProducts(productsWithCategories);
    } catch (error) {
      console.error('Error loading products:', error);
      toast({
        title: 'Error',
        description: 'Failed to load products',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('product_categories')
        .select('*')
        .order('name');

      if (error) throw error;
      setCategories(data || []);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };





  const deleteProduct = async (productId: string) => {
    if (userRole !== 'admin') {
      toast({
        title: 'Access Denied',
        description: 'Only administrators can delete products',
        variant: 'destructive',
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('products')
        .delete()
        .eq('id', productId);

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'Product deleted successfully',
      });

      loadProducts();
    } catch (error) {
      console.error('Error deleting product:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete product',
        variant: 'destructive',
      });
    }
  };



  const formatCurrency = (amount: number) => {
    if (isNaN(amount) || amount === null || amount === undefined) {
      return new Intl.NumberFormat('en-ZA', {
        style: 'currency',
        currency: 'ZAR',
        minimumFractionDigits: 2
      }).format(0);
    }
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const filteredProducts = products.filter(product => {
    const categoryName = product.category?.name || '';
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         categoryName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || product.status === filterStatus;
    const matchesType = filterType === 'all' || product.product_type === filterType;
    return matchesSearch && matchesStatus && matchesType;
  });

  const stats = {
    total: products.length,
    active: products.filter(p => p.status === 'active').length,
    draft: products.filter(p => p.status === 'draft').length,
    digital: products.filter(p => p.product_type === 'digital').length,
    physical: products.filter(p => p.product_type === 'physical').length,
    totalValue: products.reduce((sum, p) => {
      const price = parseFloat(p.price) || 0;
      const stock = parseInt(p.stock_quantity) || 0;
      return sum + (price * stock);
    }, 0)
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 p-3 sm:p-4 lg:p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">Product Management</h1>
          <p className="text-sm sm:text-base text-gray-600">Manage your product inventory</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
          {userRole === 'admin' && (
            <Button
              size="sm"
              className="text-xs sm:text-sm"
              onClick={() => window.location.href = '/dashboard/products/create'}
            >
              <Plus className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
              Create Product
            </Button>
          )}


        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4 lg:gap-6">
        <Card>
          <CardContent className="p-3 sm:p-4 lg:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Total</p>
                <p className="text-lg sm:text-2xl lg:text-3xl font-bold text-gray-900">{stats.total}</p>
              </div>
              <Package className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 sm:p-4 lg:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Active</p>
                <p className="text-lg sm:text-2xl lg:text-3xl font-bold text-gray-900">{stats.active}</p>
              </div>
              <TrendingUp className="h-6 w-6 sm:h-8 sm:w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 sm:p-4 lg:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Digital</p>
                <p className="text-lg sm:text-2xl lg:text-3xl font-bold text-gray-900">{stats.digital}</p>
              </div>
              <Monitor className="h-6 w-6 sm:h-8 sm:w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 sm:p-4 lg:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Physical</p>
                <p className="text-lg sm:text-2xl lg:text-3xl font-bold text-gray-900">{stats.physical}</p>
              </div>
              <Package className="h-6 w-6 sm:h-8 sm:w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-2 sm:col-span-3 lg:col-span-1">
          <CardContent className="p-3 sm:p-4 lg:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Total Value</p>
                <p className="text-lg sm:text-2xl lg:text-3xl font-bold text-gray-900">
                  {formatCurrency(stats.totalValue)}
                </p>
              </div>
              <TrendingUp className="h-6 w-6 sm:h-8 sm:w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg sm:text-xl">Search & Filter</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Search
              placeholder="Search products by name or category..."
              onSearch={(query) => setSearchTerm(query)}
              showFilters={true}
              filters={[
                {
                  key: 'status',
                  label: 'Status',
                  options: [
                    { value: 'active', label: 'Active' },
                    { value: 'inactive', label: 'Inactive' },
                    { value: 'draft', label: 'Draft' }
                  ]
                },
                {
                  key: 'type',
                  label: 'Type',
                  options: [
                    { value: 'physical', label: 'Physical' },
                    { value: 'digital', label: 'Digital' }
                  ]
                }
              ]}
              onFilter={(filters) => {
                setFilterStatus(filters.status || 'all');
                setFilterType(filters.type || 'all');
              }}
              variant="default"
              className="w-full"
            />
          </div>
        </CardContent>
      </Card>

      {/* Products List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg sm:text-xl">
              Products ({filteredProducts.length})
            </CardTitle>
            <div className="text-sm text-gray-500">
              Showing {filteredProducts.length} of {products.length} products
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredProducts.length === 0 ? (
              <div className="text-center py-8">
                <Package className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No products</h3>
                <p className="mt-1 text-sm text-gray-500">Get started by creating a new product.</p>
              </div>
            ) : (
              filteredProducts.map((product) => (
                <div key={product.id} className="flex flex-col sm:flex-row sm:items-center justify-between p-4 border rounded-lg gap-4">
                  <div className="flex items-center space-x-4">
                    {product.image_url && (
                      <img
                        src={product.image_url}
                        alt={product.name}
                        className="w-16 h-16 object-cover rounded"
                      />
                    )}
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-sm sm:text-base truncate">{product.name}</h3>
                      <p className="text-xs sm:text-sm text-gray-600 line-clamp-2">{product.description}</p>
                      <div className="flex flex-wrap items-center gap-2 sm:gap-4 mt-2">
                        {product.category && (
                          <span className="text-xs text-gray-500 flex items-center gap-1">
                            <div
                              className="w-2 h-2 rounded-full"
                              style={{ backgroundColor: product.category.color }}
                            />
                            {product.category.name}
                          </span>
                        )}
                        <span className="text-xs text-gray-500">
                          Type: {product.product_type}
                        </span>
                        {product.product_type === 'physical' && (
                          <span className="text-xs text-gray-500">
                            Stock: {product.stock_quantity}
                          </span>
                        )}
                        {product.product_type === 'digital' && product.file_type && (
                          <span className="text-xs text-gray-500 flex items-center gap-1">
                            <FileText className="w-3 h-3" />
                            {product.file_type.toUpperCase()}
                          </span>
                        )}
                        <Badge variant="secondary" className="text-xs">
                          {product.status}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between sm:justify-end space-x-3">
                    <span className="font-bold text-lg">{formatCurrency(product.price)}</span>
                    {userRole === 'admin' && (
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            if (window.confirm('Are you sure you want to delete this product?')) {
                              deleteProduct(product.id);
                            }
                          }}
                          className="text-xs text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-3 h-3 mr-1" />
                          Delete
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
