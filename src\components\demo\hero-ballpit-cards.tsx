"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { CardSwap, CardData } from "@/components/ui/card-swap";
import Ballpit from "@/components/ballpit";
import { ShoppingCart, Users, BookOpen, Star, Award, Zap } from "lucide-react";

// Card data for the CardSwap component
const heroCards: CardData[] = [
  {
    id: "tennis-shop",
    title: "Premium Tennis Equipment",
    subtitle: "Professional gear for every player",
    description: "Discover our curated collection of professional tennis rackets, shoes, and accessories. From beginner-friendly options to pro-level equipment used by champions.",
    image: "https://images.unsplash.com/photo-1738330194751-e096b51af7bf?q=80&w=358&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    badge: "Shop Now",
    badgeVariant: "default",
    ctaText: "Browse Equipment",
    ctaLink: "/shop",
    ctaVariant: "default",
    backgroundColor: "bg-gradient-to-br from-green-50 to-emerald-50",
  },
  {
    id: "mentorship-programs",
    title: "Expert Mentorship Programs",
    subtitle: "6-month & 12-month coaching tiers",
    description: "Transform your game with personalized coaching from professional tennis instructors. Choose from our comprehensive 6-month or intensive 12-month mentorship programs.",
    image: "https://images.unsplash.com/photo-1622279457486-62dcc4a431d6?w=400&q=80",
    badge: "Popular",
    badgeVariant: "default",
    ctaText: "Start Your Journey",
    ctaLink: "/mentorship",
    ctaVariant: "default",
    backgroundColor: "bg-gradient-to-br from-green-50 to-emerald-50",
  },
  {
    id: "blog-preview",
    title: "Tennis Insights & Tips",
    subtitle: "Expert advice and latest trends",
    description: "Stay ahead of the game with our comprehensive blog featuring technique tutorials, equipment reviews, and insights from professional players and coaches.",
    image: "https://images.unsplash.com/photo-1554068865-24cecd4e34b8?w=400&q=80",
    badge: "Coming Soon",
    badgeVariant: "default",
    ctaText: "Read Articles",
    ctaLink: "/blog",
    ctaVariant: "default",
    backgroundColor: "bg-gradient-to-br from-green-50 to-emerald-50",
  },
];

export default function HeroBallpitCards() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [ballCount, setBallCount] = useState(120);

  useEffect(() => {
    // Simulate loading delay for smooth transition
    const timer = setTimeout(() => setIsLoaded(true), 800);
    return () => clearTimeout(timer);
  }, []);

  // Adjust ball count based on screen size for performance
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setBallCount(38); // Mobile: fewer balls for performance
      } else if (width < 1024) {
        setBallCount(90); // Tablet: moderate ball count
      } else {
        setBallCount(120); // Desktop: optimal ball count
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <section className="relative min-h-screen overflow-hidden bg-gradient-to-br from-slate-50 via-green-50/30 to-blue-50/20">
      {/* Ballpit Background - Full Width */}
      <div className="absolute inset-0 w-full h-full">
        {/* Background gradient overlay for better contrast */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900/20 via-transparent to-slate-900/10 z-10" />
        
        {/* Ballpit Container */}
        <div className="absolute inset-0 w-full h-full">
          {isLoaded && (
            <Ballpit
              count={ballCount}
              gravity={0.3}
              friction={0.985}
              wallBounce={0.9}
              followCursor={false}
              colors={[0x60a5fa, 0x3b82f6, 0x2563eb, 0x1d4ed8, 0x2563eb]} // Lighter blue gradient colors
              className="w-full h-full"
            />
          )}
        </div>

        {/* Loading overlay */}
        {!isLoaded && (
          <div className="absolute inset-0 bg-gradient-to-br from-slate-900/80 via-slate-800/60 to-green-900/40 flex items-center justify-center z-20">
            <div className="text-center text-white">
              <div className="w-20 h-20 mx-auto mb-6 bg-green-600 rounded-full flex items-center justify-center animate-pulse">
                <Zap className="w-10 h-10" />
              </div>
              <p className="text-xl font-medium">Loading Interactive Experience...</p>
            </div>
          </div>
        )}
      </div>

      {/* Content Overlay */}
      <div className="relative z-20 min-h-screen flex items-center">
        <div className="container mx-auto px-4 py-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            
            {/* Left Column - Hero Text */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-center lg:text-left px-4"
            >
              {/* Main Headline */}
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 tracking-tight leading-tight">
                <span className="text-slate-900 dark:text-white">Elevate Your</span>
                <span className="block text-primary">Game</span>
              </h1>

              {/* Subheadline */}
              <p className="text-lg md:text-xl dark:text-white/80 mb-8 max-w-2xl mx-auto lg:mx-0 leading-relaxed">
                Discover premium equipment, expert mentorship, and valuable insights. 
                Interactive tennis balls respond to your cursor movement.
              </p>

              {/* Feature highlights */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8">
                <div className="flex items-center gap-3 bg-white/90 backdrop-blur-sm rounded-xl p-4 neo-shadow-light">
                  <ShoppingCart className="w-5 h-5 text-green-600 flex-shrink-0" />
                  <span className="text-sm font-medium text-slate-700">Premium Gear</span>
                </div>
                <div className="flex items-center gap-3 bg-white/90 backdrop-blur-sm rounded-xl p-4 neo-shadow-light">
                  <Users className="w-5 h-5 text-blue-600 flex-shrink-0" />
                  <span className="text-sm font-medium text-slate-700">Expert Coaching</span>
                </div>
                <div className="flex items-center gap-3 bg-white/90 backdrop-blur-sm rounded-xl p-4 neo-shadow-light">
                  <BookOpen className="w-5 h-5 text-purple-600 flex-shrink-0" />
                  <span className="text-sm font-medium text-slate-700">Tennis Insights</span>
                </div>
              </div>

              {/* Trust indicators */}
              {/* <div className="flex flex-wrap items-center justify-center lg:justify-start gap-6 text-sm text-slate-600">
                <div className="flex items-center gap-2">
                  <Star className="w-4 h-4 text-yellow-500" />
                  <span>4.9/5 Rating</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4 text-green-600" />
                  <span>2,000+ Players</span>
                </div>
                <div className="flex items-center gap-2">
                  <Award className="w-4 h-4 text-green-600" />
                  <span>Pro Approved</span>
                </div>
              </div> */}
            </motion.div>

            {/* Right Column - CardSwap Component */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="flex justify-center lg:justify-end"
            >
              <div className="w-full max-w-md">
                <CardSwap
                  cards={heroCards}
                  cardDistance={60}
                  verticalDistance={70}
                  delay={5000}
                  pauseOnHover={false}
                  autoPlay={true}
                  className="relative z-30"
                />
              </div>
            </motion.div>

          </div>
        </div>
      </div>

      {/* Bottom gradient fade */}
      <div className="absolute bottom-0 left-0 right-0 h-22 bg-gradient-to-t from-slate-50 to-transparent z-10" />
    </section>
  );
}
