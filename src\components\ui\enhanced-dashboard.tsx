"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { Button } from "./button";
import { Card, CardContent, CardHeader, CardTitle } from "./card";

// Enhanced Dashboard Container
interface EnhancedDashboardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  header?: React.ReactNode;
}

export const EnhancedDashboard = React.forwardRef<HTMLDivElement, EnhancedDashboardProps>(
  ({ className, children, sidebar, header, ...props }, ref) => {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-muted/10 to-background">
        {/* Background decoration */}
        <div className="absolute inset-0 overflow-hidden -z-10">
          <div className="absolute h-[500px] w-[500px] rounded-full gradient-blue top-[-250px] left-[-250px] blur-[120px] opacity-15 animate-pulse"></div>
          <div className="absolute h-[400px] w-[400px] rounded-full gradient-purple bottom-[-200px] right-[-200px] blur-[100px] opacity-20 animate-pulse"></div>
        </div>

        <div className={cn("flex min-h-screen", className)} ref={ref} {...props}>
          {/* Sidebar */}
          {sidebar && (
            <aside className="hidden md:flex w-64 flex-col">
              <div className="modern-card m-4 p-6 neo-shadow flex-1">
                {sidebar}
              </div>
            </aside>
          )}

          {/* Main Content */}
          <main className="flex-1 flex flex-col">
            {/* Header */}
            {header && (
              <header className="modern-card m-4 mb-0 p-6 neo-shadow-light">
                {header}
              </header>
            )}

            {/* Content */}
            <div className="flex-1 p-4">
              {children}
            </div>
          </main>
        </div>
      </div>
    );
  }
);
EnhancedDashboard.displayName = "EnhancedDashboard";

// Enhanced Dashboard Card
interface DashboardCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  subtitle?: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  action?: React.ReactNode;
  loading?: boolean;
}

export const DashboardCard = React.forwardRef<HTMLDivElement, DashboardCardProps>(
  ({ className, title, subtitle, icon, children, action, loading, ...props }, ref) => {
    return (
      <Card className={cn("modern-card neo-shadow-light border-0", className)} ref={ref} {...props}>
        {(title || subtitle || icon || action) && (
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {icon && (
                  <div className="p-2 rounded-full bg-primary/15">
                    {icon}
                  </div>
                )}
                <div>
                  {title && (
                    <CardTitle className="text-xl font-bold text-foreground">
                      {title}
                    </CardTitle>
                  )}
                  {subtitle && (
                    <p className="text-sm text-muted-foreground mt-1">
                      {subtitle}
                    </p>
                  )}
                </div>
              </div>
              {action && <div>{action}</div>}
            </div>
          </CardHeader>
        )}
        <CardContent className={cn(loading && "opacity-50")}>
          {loading ? (
            <div className="space-y-3">
              <div className="skeleton h-4 rounded w-3/4"></div>
              <div className="skeleton h-4 rounded w-1/2"></div>
              <div className="skeleton h-20 rounded"></div>
            </div>
          ) : (
            children
          )}
        </CardContent>
      </Card>
    );
  }
);
DashboardCard.displayName = "DashboardCard";

// Enhanced Stats Card
interface StatsCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: "increase" | "decrease";
  };
  icon?: React.ReactNode;
  loading?: boolean;
}

export const StatsCard = React.forwardRef<HTMLDivElement, StatsCardProps>(
  ({ className, title, value, change, icon, loading, ...props }, ref) => {
    return (
      <Card className={cn("modern-card neo-shadow-light border-0 interactive-card", className)} ref={ref} {...props}>
        <CardContent className="p-6">
          {loading ? (
            <div className="space-y-3">
              <div className="skeleton h-4 rounded w-1/2"></div>
              <div className="skeleton h-8 rounded w-3/4"></div>
              <div className="skeleton h-3 rounded w-1/3"></div>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">{title}</p>
                <p className="text-3xl font-bold text-gradient">{value}</p>
                {change && (
                  <div className={cn(
                    "flex items-center gap-1 text-xs font-medium",
                    change.type === "increase" ? "text-green-500" : "text-red-500"
                  )}>
                    <span>{change.type === "increase" ? "↗" : "↘"}</span>
                    <span>{Math.abs(change.value)}%</span>
                  </div>
                )}
              </div>
              {icon && (
                <div className="p-3 rounded-full bg-primary/15 text-primary">
                  {icon}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    );
  }
);
StatsCard.displayName = "StatsCard";

// Enhanced Progress Bar
interface ProgressBarProps extends React.HTMLAttributes<HTMLDivElement> {
  value: number;
  max?: number;
  label?: string;
  showPercentage?: boolean;
  color?: "primary" | "success" | "warning" | "danger";
}

export const ProgressBar = React.forwardRef<HTMLDivElement, ProgressBarProps>(
  ({ className, value, max = 100, label, showPercentage = true, color = "primary", ...props }, ref) => {
    const percentage = Math.min((value / max) * 100, 100);
    
    const colorClasses = {
      primary: "bg-primary",
      success: "bg-green-500",
      warning: "bg-yellow-500",
      danger: "bg-red-500"
    };

    return (
      <div className={cn("space-y-2", className)} ref={ref} {...props}>
        {(label || showPercentage) && (
          <div className="flex justify-between text-sm">
            {label && <span className="font-medium text-foreground">{label}</span>}
            {showPercentage && <span className="text-muted-foreground">{percentage.toFixed(0)}%</span>}
          </div>
        )}
        <div className="h-3 bg-muted/30 rounded-full overflow-hidden neo-shadow-inset">
          <div
            className={cn(
              "h-full rounded-full transition-all duration-500 ease-out",
              colorClasses[color]
            )}
            style={{ width: `${percentage}%` }}
          />
        </div>
      </div>
    );
  }
);
ProgressBar.displayName = "ProgressBar";

// Enhanced Action Button
interface ActionButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  icon?: React.ReactNode;
  variant?: "primary" | "secondary" | "outline" | "ghost";
  size?: "sm" | "md" | "lg";
  loading?: boolean;
  children: React.ReactNode;
}

export const ActionButton = React.forwardRef<HTMLButtonElement, ActionButtonProps>(
  ({ className, icon, variant = "primary", size = "md", loading, children, ...props }, ref) => {
    const baseClasses = "font-semibold rounded-xl transition-neo focus-ring";
    
    const variantClasses = {
      primary: "btn-primary text-white neo-shadow-light hover:neo-shadow",
      secondary: "bg-muted/30 text-foreground neo-shadow-light hover:bg-muted/50",
      outline: "border border-primary/20 glass-effect-subtle hover:border-primary/40 text-foreground",
      ghost: "text-foreground hover:bg-muted/20"
    };

    const sizeClasses = {
      sm: "h-9 px-3 text-sm",
      md: "h-11 px-4 text-base",
      lg: "h-13 px-6 text-lg"
    };

    return (
      <Button
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[size],
          className
        )}
        disabled={loading}
        ref={ref}
        {...props}
      >
        <div className="flex items-center gap-2">
          {loading ? (
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          ) : (
            icon && <span>{icon}</span>
          )}
          {children}
        </div>
      </Button>
    );
  }
);
ActionButton.displayName = "ActionButton";
