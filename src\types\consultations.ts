import { z } from 'zod';

/**
 * Consultation status enum for database and UI
 */
export const CONSULTATION_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed', 
  CANCELLED: 'cancelled',
  COMPLETED: 'completed',
  RESCHEDULED: 'rescheduled'
} as const;

export type ConsultationStatus = typeof CONSULTATION_STATUS[keyof typeof CONSULTATION_STATUS];

/**
 * Payment status enum for consultations
 */
export const PAYMENT_STATUS = {
  PENDING: 'pending',
  PAID: 'paid',
  FAILED: 'failed',
  REFUNDED: 'refunded'
} as const;

export type PaymentStatus = typeof PAYMENT_STATUS[keyof typeof PAYMENT_STATUS];

/**
 * Consultation database record interface
 */
export interface Consultation {
  id: string;
  user_id?: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  location: string;
  scheduled_date: string;
  scheduled_time: string;
  duration?: number;
  reason: string;
  status: ConsultationStatus;
  payment_reference?: string;
  payment_status?: PaymentStatus;
  payment_amount?: number;
  yoco_payment_id?: string;
  admin_notes?: string;
  cancellation_reason?: string;
  rescheduled_from?: string;
  created_at: string;
  updated_at: string;
}

/**
 * Admin consultation view with additional computed fields
 */
export interface AdminConsultationView extends Consultation {
  customer_name: string;
  formatted_date: string;
  formatted_time: string;
  formatted_amount: string;
  days_until_appointment: number;
  is_overdue: boolean;
}

/**
 * Consultation filters for admin interface
 */
export interface ConsultationFilters {
  status?: ConsultationStatus;
  payment_status?: PaymentStatus;
  date_from?: string;
  date_to?: string;
  search?: string;
  admin_id?: string;
}

/**
 * Consultation statistics for dashboard
 */
export interface ConsultationStats {
  total_consultations: number;
  pending_consultations: number;
  confirmed_consultations: number;
  completed_consultations: number;
  cancelled_consultations: number;
  total_revenue: number;
  monthly_growth: number;
  average_duration: number;
  completion_rate: number;
}

/**
 * Status update request schema
 */
export const consultationStatusUpdateSchema = z.object({
  status: z.enum(['pending', 'confirmed', 'cancelled', 'completed', 'rescheduled']),
  admin_notes: z.string().optional(),
  cancellation_reason: z.string().optional(),
  new_date: z.string().optional(),
  new_time: z.string().optional(),
});

export type ConsultationStatusUpdate = z.infer<typeof consultationStatusUpdateSchema>;

/**
 * Bulk action request schema
 */
export const consultationBulkActionSchema = z.object({
  consultation_ids: z.array(z.string().uuid()),
  action: z.enum(['confirm', 'cancel', 'mark_completed']),
  reason: z.string().optional(),
});

export type ConsultationBulkAction = z.infer<typeof consultationBulkActionSchema>;

/**
 * Status options for UI dropdowns
 */
export const CONSULTATION_STATUS_OPTIONS = [
  { value: 'pending', label: 'Pending', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'confirmed', label: 'Confirmed', color: 'bg-blue-100 text-blue-800' },
  { value: 'completed', label: 'Completed', color: 'bg-green-100 text-green-800' },
  { value: 'cancelled', label: 'Cancelled', color: 'bg-red-100 text-red-800' },
  { value: 'rescheduled', label: 'Rescheduled', color: 'bg-purple-100 text-purple-800' },
];

/**
 * Payment status options for UI dropdowns
 */
export const PAYMENT_STATUS_OPTIONS = [
  { value: 'pending', label: 'Pending', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'paid', label: 'Paid', color: 'bg-green-100 text-green-800' },
  { value: 'failed', label: 'Failed', color: 'bg-red-100 text-red-800' },
  { value: 'refunded', label: 'Refunded', color: 'bg-gray-100 text-gray-800' },
];

/**
 * Utility functions for consultation management
 */
export const getStatusColor = (status: ConsultationStatus): string => {
  const option = CONSULTATION_STATUS_OPTIONS.find(opt => opt.value === status);
  return option?.color || 'bg-gray-100 text-gray-800';
};

export const getPaymentStatusColor = (status: PaymentStatus): string => {
  const option = PAYMENT_STATUS_OPTIONS.find(opt => opt.value === status);
  return option?.color || 'bg-gray-100 text-gray-800';
};

/**
 * Format consultation date and time for display
 */
export const formatConsultationDateTime = (date: string, time: string): string => {
  try {
    const consultationDate = new Date(`${date}T${time}`);
    return consultationDate.toLocaleString('en-ZA', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  } catch {
    return `${date} at ${time}`;
  }
};

/**
 * Calculate days until appointment
 */
export const getDaysUntilAppointment = (date: string): number => {
  try {
    const appointmentDate = new Date(date);
    const today = new Date();
    const diffTime = appointmentDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  } catch {
    return 0;
  }
};

/**
 * Check if consultation is overdue
 */
export const isConsultationOverdue = (date: string, status: ConsultationStatus): boolean => {
  if (status === 'completed' || status === 'cancelled') return false;
  return getDaysUntilAppointment(date) < 0;
};
