import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { getProduct, deleteProduct } from "@/utils/supabase/products";
import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import Link from "next/link";
import { ArrowLeft, AlertTriangle } from "lucide-react";

export default async function DeleteProductPage({
  params,
}: {
  params: { id: string };
}) {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  
  // Check if user is admin
  if (!user) {
    redirect("/sign-in?redirect=/admin/products");
  }
  
  // Fetch product
  const product = await getProduct(params.id);
  
  if (!product) {
    redirect("/admin/products");
  }
  
  async function handleDeleteProduct() {
    "use server";
    
    await deleteProduct(params.id);
    redirect("/admin/products");
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Link href="/admin/products">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-3xl font-bold tracking-tight">Delete Product</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            Confirm Deletion
          </CardTitle>
          <CardDescription>
            Are you sure you want to delete this product? This action cannot be undone.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 rounded-md overflow-hidden bg-muted">
                {product.image ? (
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-muted" />
                )}
              </div>
              <div>
                <h3 className="font-medium">{product.name}</h3>
                <p className="text-sm text-muted-foreground">
                  Category: {product.category} | Stock: {product.stock} | Price: R{product.price.toFixed(2)}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end gap-4">
          <Link href="/admin/products">
            <Button variant="outline">Cancel</Button>
          </Link>
          <form action={handleDeleteProduct}>
            <Button type="submit" variant="destructive">Delete Product</Button>
          </form>
        </CardFooter>
      </Card>
    </div>
  );
}