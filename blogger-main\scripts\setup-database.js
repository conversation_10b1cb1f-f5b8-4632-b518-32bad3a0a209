#!/usr/bin/env node

/**
 * Database Setup Helper Script
 *
 * This script helps users set up the database by providing clear instructions
 * and checking for required environment variables.
 */

import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { config } from 'dotenv';

// Load environment variables
config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = dirname(__dirname);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function printHeader() {
  console.log(colorize('\n🗄️  Thabo Bester Database Setup Helper', 'cyan'));
  console.log(colorize('=====================================\n', 'cyan'));
}

function checkEnvironment() {
  console.log(colorize('📋 Checking Environment Variables...', 'blue'));
  
  const requiredVars = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY'
  ];
  
  const missing = [];
  
  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      missing.push(varName);
    }
  }
  
  if (missing.length > 0) {
    console.log(colorize('❌ Missing environment variables:', 'red'));
    missing.forEach(varName => {
      console.log(colorize(`   - ${varName}`, 'red'));
    });
    console.log(colorize('\n💡 Please copy .env.example to .env and configure your Supabase credentials.\n', 'yellow'));
    return false;
  }
  
  console.log(colorize('✅ Environment variables are configured.\n', 'green'));
  return true;
}

function printInstructions() {
  console.log(colorize('📝 Database Setup Instructions', 'magenta'));
  console.log(colorize('==============================\n', 'magenta'));
  
  console.log(colorize('1. Go to your Supabase project dashboard', 'bright'));
  console.log('   URL: ' + colorize(process.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co', 'blue'));
  
  console.log(colorize('\n2. Navigate to the SQL Editor', 'bright'));
  console.log('   Dashboard → SQL Editor → New Query');
  
  console.log(colorize('\n3. Choose your setup option:', 'bright'));

  console.log(colorize('\n   Option A: Fresh Setup (Recommended)', 'green'));
  console.log('   ├── Copy and run: ' + colorize('supabase/complete-setup.sql', 'cyan'));
  console.log('   └── Copy and run: ' + colorize('supabase/seed.sql', 'cyan'));

  console.log(colorize('\n   Option B: Comprehensive Sync (Latest Schema)', 'blue'));
  console.log('   ├── Copy and run: ' + colorize('supabase/master-database-sync.sql', 'cyan'));
  console.log('   ├── Copy and run: ' + colorize('supabase/update-policies-functions.sql', 'cyan'));
  console.log('   └── Copy and run: ' + colorize('supabase/seed.sql', 'cyan'));

  console.log(colorize('\n   Option C: Force Sync (⚠️ Recreates ALL tables)', 'red'));
  console.log('   ├── First check: ' + colorize('npm run db:check', 'yellow'));
  console.log('   ├── Copy and run: ' + colorize('supabase/force-sync-all-tables.sql', 'cyan'));
  console.log('   ├── Copy and run: ' + colorize('supabase/update-policies-functions.sql', 'cyan'));
  console.log('   └── Copy and run: ' + colorize('supabase/seed.sql', 'cyan'));

  console.log(colorize('\n   Option C: Fix "User Already Exists" Error (Quick Fix)', 'yellow'));
  console.log('   └── Copy and run: ' + colorize('supabase/fix-user-conflicts.sql', 'cyan'));

  console.log(colorize('\n   Option D: Complete Reset (Nuclear Option)', 'red'));
  console.log('   ├── Copy and run: ' + colorize('supabase/reset-database.sql', 'red') + colorize(' (⚠️  DELETES ALL DATA!)', 'red'));
  console.log('   ├── Copy and run: ' + colorize('supabase/complete-setup.sql', 'cyan'));
  console.log('   └── Copy and run: ' + colorize('supabase/seed.sql', 'cyan'));
  
  console.log(colorize('\n4. Verify Setup', 'bright'));
  console.log('   ├── Check that all tables are created');
  console.log('   ├── Verify storage buckets exist (Storage → Buckets)');
  console.log('   └── Test user registration in your app');
  
  console.log(colorize('\n📚 For detailed instructions, see: supabase/README.md', 'blue'));
}

function printFileContents() {
  const command = process.argv[2];
  
  if (!command) {
    return;
  }
  
  const fileMap = {
    'setup': 'supabase/complete-setup.sql',
    'seed': 'supabase/seed.sql',
    'reset': 'supabase/reset-database.sql',
    'fix': 'supabase/fix-user-conflicts.sql',
    'sync': 'supabase/comprehensive-update.sql',
    'master': 'supabase/master-database-sync.sql',
    'check': 'supabase/check-table-differences.sql',
    'force-sync': 'supabase/force-sync-all-tables.sql',
    'diagnose': 'supabase/diagnose-user-issues.sql',
    'fix-users': 'supabase/fix-user-already-exists.sql',
    'fix-all': 'supabase/complete-fix-all-issues.sql',
    'check-roles': 'supabase/check-and-fix-user-roles.sql',
    'create-admin': 'supabase/create-admin-user.sql',
    'create-admin-safe': 'supabase/create-admin-user-safe.sql',
    'complete-sync': 'supabase/complete-database-sync-with-admin.sql',
    'admin-simple': 'supabase/create-admin-simple.sql',
    'check-structure': 'supabase/check-database-structure.sql',
    'create-missing': 'supabase/create-missing-tables.sql',
    'ping-compare': 'supabase/ping-database-comparison.sql',
    'complete-admin-sync': 'supabase/complete-admin-sync.sql'
  };
  
  const filePath = fileMap[command];
  
  if (!filePath) {
    console.log(colorize(`❌ Unknown command: ${command}`, 'red'));
    console.log(colorize('Available commands: setup, seed, reset, fix, sync, master, check, force-sync, diagnose, fix-users, fix-all, check-roles, create-admin, create-admin-safe', 'yellow'));
    return;
  }
  
  try {
    const fullPath = join(projectRoot, filePath);
    const content = readFileSync(fullPath, 'utf8');
    
    console.log(colorize(`\n📄 Contents of ${filePath}:`, 'magenta'));
    console.log(colorize('='.repeat(50), 'magenta'));
    console.log(content);
    console.log(colorize('='.repeat(50), 'magenta'));
    console.log(colorize('\n💡 Copy the above SQL and paste it into your Supabase SQL Editor.', 'blue'));
    
  } catch (error) {
    console.log(colorize(`❌ Error reading file ${filePath}: ${error.message}`, 'red'));
  }
}

function main() {
  printHeader();
  
  const envOk = checkEnvironment();
  
  if (!envOk) {
    process.exit(1);
  }
  
  printInstructions();
  printFileContents();
  
  console.log(colorize('\n🎉 Happy coding!', 'green'));
}

main();
