import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '../../../supabase/auth';
import { useAvatar } from '../../contexts/AvatarContext';

interface UserAvatarProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  userId?: string;
  userEmail?: string;
  userName?: string;
  showFallback?: boolean;
}

const sizeClasses = {
  sm: 'h-8 w-8',
  md: 'h-10 w-10',
  lg: 'h-12 w-12',
  xl: 'h-20 w-20',
};

export function UserAvatar({ 
  className = '', 
  size = 'md', 
  userId, 
  userEmail, 
  userName,
  showFallback = true 
}: UserAvatarProps) {
  const { user } = useAuth();
  const { avatarUrl } = useAvatar();

  // Use provided props or fall back to current user
  const finalUserId = userId || user?.id;
  const finalUserEmail = userEmail || user?.email;
  const finalUserName = userName || user?.user_metadata?.full_name || user?.email;

  // Generate fallback avatar URL
  const fallbackAvatarUrl = `https://api.dicebear.com/7.x/avataaars/svg?seed=${finalUserEmail || finalUserId}`;

  // Use context avatar for current user, fallback for others
  const displayAvatarUrl = (userId === user?.id || !userId) ? avatarUrl : null;

  // Get initials for fallback
  const getInitials = () => {
    if (finalUserName) {
      return finalUserName
        .split(' ')
        .map(name => name[0])
        .join('')
        .toUpperCase()
        .slice(0, 2);
    }
    if (finalUserEmail) {
      return finalUserEmail[0].toUpperCase();
    }
    return 'U';
  };

  return (
    <Avatar className={`${sizeClasses[size]} ${className}`}>
      <AvatarImage
        src={displayAvatarUrl || fallbackAvatarUrl}
        alt={finalUserName || finalUserEmail || 'User avatar'}
      />
      {showFallback && (
        <AvatarFallback>
          {getInitials()}
        </AvatarFallback>
      )}
    </Avatar>
  );
}
