/**
 * Currency formatting utilities for South African Rand (ZAR)
 * Ensures consistent currency formatting throughout the application
 */

export const formatCurrency = (amount: number | string): string => {
  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  if (isNaN(numericAmount)) {
    return 'R 0.00';
  }

  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(numericAmount);
};

export const formatCurrencyCompact = (amount: number | string): string => {
  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  if (isNaN(numericAmount)) {
    return 'R 0';
  }

  if (numericAmount >= 1000000) {
    return `R ${(numericAmount / 1000000).toFixed(1)}M`;
  } else if (numericAmount >= 1000) {
    return `R ${(numericAmount / 1000).toFixed(1)}K`;
  }

  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(numericAmount);
};

export const parseCurrency = (currencyString: string): number => {
  // Remove currency symbols and spaces, then parse
  const cleanString = currencyString.replace(/[R\s,]/g, '');
  const parsed = parseFloat(cleanString);
  return isNaN(parsed) ? 0 : parsed;
};

export const formatPrice = (price: number | string): string => {
  return formatCurrency(price);
};

export const formatTotal = (total: number | string): string => {
  return formatCurrency(total);
};

// Validation function to ensure price is valid
export const isValidPrice = (price: number | string): boolean => {
  const numericPrice = typeof price === 'string' ? parseFloat(price) : price;
  return !isNaN(numericPrice) && numericPrice >= 0;
};

// Convert cents to rand (if needed for payment processing)
export const centsToRand = (cents: number): number => {
  return cents / 100;
};

// Convert rand to cents (if needed for payment processing)
export const randToCents = (rand: number): number => {
  return Math.round(rand * 100);
};

export default {
  formatCurrency,
  formatCurrencyCompact,
  parseCurrency,
  formatPrice,
  formatTotal,
  isValidPrice,
  centsToRand,
  randToCents,
};
