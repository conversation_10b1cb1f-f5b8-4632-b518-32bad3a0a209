@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Google Fonts for Glassmorphism Card */
@import url('https://fonts.googleapis.com/css2?family=Space+Mono:ital,wght@0,400;0,700;1,400;1,700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400&display=swap');

@layer base {
  :root {
    --background: 0 0% 100%; /* #ffffff - White */
    --foreground: 240 10% 3.9%; /* #0a0a0f - Very Dark Blue/Black */
    --card: 0 0% 100%; /* #ffffff - White */
    --card-foreground: 240 10% 3.9%; /* #0a0a0f - Very Dark Blue/Black */
    --popover: 0 0% 100%; /* #ffffff - White */
    --popover-foreground: 240 10% 3.9%; /* #0a0a0f - Very Dark Blue/Black */
    --primary: 190 85% 63%; /* #4ed7f1 - Sky Blue / <PERSON>an */
    --primary-foreground: 355 100% 100%; /* #ffffff - White */
    --secondary: 240 4.8% 95.9%; /* #f4f4f7 - Light Grayish Blue */
    --secondary-foreground: 240 5.9% 10%; /* #19191c - Very Dark Grayish Blue */
    --muted: 240 4.8% 95.9%; /* #f4f4f7 - Light Grayish Blue */
    --muted-foreground: 240 3.8% 46.1%; /* #72727a - Gray */
    --accent: 240 4.8% 95.9%; /* #f4f4f7 - Light Grayish Blue */
    --accent-foreground: 240 5.9% 10%; /* #19191c - Very Dark Grayish Blue */
    --destructive: 0 84.2% 60.2%; /* #f05454 - Soft Red / Coral Red */
    --destructive-foreground: 0 0% 98%; /* #fafafa - Off White */
    --border: 240 5.9% 90%; /* #e5e5ec - Light Gray */
    --input: 240 5.9% 90%; /* #e5e5ec - Light Gray */
    --ring: 142 76% 36%; /* #298e46 - Forest Green */
    --radius: 0.5rem;
    --chart-1: 142 76% 36%; /* #298e46 - Forest Green */
    --chart-2: 173 58% 39%; /* #36b49f - Teal Green */
    --chart-3: 197 37% 24%; /* #2f4d5c - Deep Blue Gray */
    --chart-4: 43 74% 66%; /* #ecc94b - Goldenrod / Yellow Gold */
    --chart-5: 27 87% 67%; /* #f5a623 - Bright Orange */
    --brand-gold: #D4AF37; /* Gold */
  }

  .dark {
    --background: 240 10% 3.9%; /* #0a0a0f - Very Dark Blue/Black */
    --foreground: 0 0% 98%; /* #fafafa - Off White */
    --card: 240 10% 3.9%; /* #0a0a0f - Very Dark Blue/Black */
    --card-foreground: 0 0% 98%; /* #fafafa - Off White */
    --popover: 240 10% 3.9%; /* #0a0a0f - Very Dark Blue/Black */
    --popover-foreground: 0 0% 98%; /* #fafafa - Off White */
    --primary: 190 85% 63%; /* #4ed7f1 - Sky Blue / Bright Cyan */
    --primary-foreground: 144 10% 10%; /* #121f1a - Very Dark Greenish Gray */
    --secondary: 240 3.7% 15.9%; /* #2a2a2d - Dark Gray Blue */
    --secondary-foreground: 0 0% 98%; /* #fafafa - Off White */
    --muted: 240 3.7% 15.9%; /* #2a2a2d - Dark Gray Blue */
    --muted-foreground: 240 5% 64.9%; /* #a4a4b8 - Cool Gray */
    --accent: 240 3.7% 15.9%; /* #2a2a2d - Dark Gray Blue */
    --accent-foreground: 0 0% 98%; /* #fafafa - Off White */
    --destructive: 0 62.8% 30.6%; /* #732626 - Dark Red */
    --destructive-foreground: 0 0% 98%; /* #fafafa - Off White */
    --border: 240 3.7% 15.9%; /* #2a2a2d - Dark Gray Blue */
    --input: 240 3.7% 15.9%; /* #2a2a2d - Dark Gray Blue */
    --ring: 142 70% 50%; /* #33cc66 - Emerald Green */
    --chart-1: 142 70% 50%; /* #33cc66 - Emerald Green */
    --chart-2: 160 60% 45%; /* #2ebc9b - Aquamarine / Teal */
    --chart-3: 30 80% 55%; /* #f5a623 - Bright Orange */
    --chart-4: 280 65% 60%; /* #b366cc - Lavender Purple */
    --chart-5: 340 75% 55%; /* #e34981 - Raspberry Pink */
    --brand-gold: #D4AF37; /* Gold */
  }
}


@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Enhanced Neomorphism and Glassmorphism Effects */
@layer utilities {
  /* Enhanced Neomorphism shadows */
  .neo-shadow {
    box-shadow:
      8px 8px 16px rgba(0, 0, 0, 0.25),
      -8px -8px 16px rgba(255, 255, 255, 0.08),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .neo-shadow-inset {
    box-shadow:
      inset 4px 4px 8px rgba(0, 0, 0, 0.25),
      inset -4px -4px 8px rgba(255, 255, 255, 0.08),
      inset 0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .neo-shadow-light {
    box-shadow:
      4px 4px 8px rgba(0, 0, 0, 0.15),
      -4px -4px 8px rgba(255, 255, 255, 0.05),
      0 0 0 1px rgba(255, 255, 255, 0.03);
  }

  .neo-shadow-hover {
    box-shadow:
      12px 12px 24px rgba(0, 0, 0, 0.3),
      -12px -12px 24px rgba(255, 255, 255, 0.1),
      0 0 0 1px rgba(255, 255, 255, 0.08);
  }

  .neo-shadow-pressed {
    box-shadow:
      inset 2px 2px 4px rgba(0, 0, 0, 0.3),
      inset -2px -2px 4px rgba(255, 255, 255, 0.1);
  }

  /* Enhanced Glassmorphism effects */
  .glass-effect {
    background: rgba(255, 255, 255, 0.12);
    backdrop-filter: blur(24px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.25);
    -webkit-backdrop-filter: blur(24px) saturate(180%);
  }

  .glass-effect-dark {
    background: rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(24px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    -webkit-backdrop-filter: blur(24px) saturate(180%);
  }

  .glass-effect-subtle {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(16px) saturate(150%);
    border: 1px solid rgba(255, 255, 255, 0.12);
    -webkit-backdrop-filter: blur(16px) saturate(150%);
  }

  /* Gradient Text Effects */
  .gradient-text {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)) 50%, hsl(var(--foreground)) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }

  /* Enhanced Gradient backgrounds */
  .gradient-purple {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .gradient-blue {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .gradient-pink {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  .gradient-orange {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  }

  .gradient-green {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  }

  .gradient-cyber {
    background: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%);
  }

  /* Enhanced Interactive States */
  .interactive-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
  }

  .interactive-card:hover {
    transform: translateY(-2px);
  }

  .interactive-card:active {
    transform: translateY(0);
  }

  /* Enhanced Button States */
  .btn-primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border: none;
    color: white;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, #3a8bfe 0%, #00d4fe 100%);
    transform: translateY(-1px);
  }

  .btn-primary:active {
    transform: translateY(0);
  }

  /* Enhanced Form Elements */
  .form-input {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .form-input:focus {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(79, 172, 254, 0.5);
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
  }

  /* Mobile bottom navigation safe area */
  .pb-safe {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .mb-safe {
    margin-bottom: env(safe-area-inset-bottom);
  }

  /* Enhanced transitions */
  .transition-neo {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .transition-smooth {
    transition: all 0.2s ease-in-out;
  }

  .transition-bounce {
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  /* Hide scrollbar */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Enhanced Typography */
  .text-gradient {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* Modern Card Styles - Inspired by 21st.dev */
  .modern-card {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(16px) saturate(150%);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .modern-card:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.18);
    transform: translateY(-2px);
  }

  /* Enhanced Loading States */
  .skeleton {
    background: linear-gradient(90deg,
      rgba(255, 255, 255, 0.05) 25%,
      rgba(255, 255, 255, 0.1) 50%,
      rgba(255, 255, 255, 0.05) 75%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  /* Enhanced Focus States */
  .focus-ring {
    transition: all 0.2s ease-in-out;
  }

  .focus-ring:focus-visible {
    outline: 2px solid rgba(79, 172, 254, 0.6);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(79, 172, 254, 0.1);
  }

  /* Modern Navigation Styles */
  .nav-pill {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .nav-pill:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
  }

  /* Enhanced Product Card Styles */
  .product-card {
    background: rgba(255, 255, 255, 0.06);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .product-card:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-4px);
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  /* VirTra-style preloader animations */
  @keyframes virtra-logo {
    0%, 100% {
      transform: scale(1) rotate(0deg);
      filter: drop-shadow(0 0 20px rgba(78, 215, 241, 0.3));
    }
    50% {
      transform: scale(1.02) rotate(0.5deg);
      filter: drop-shadow(0 0 25px rgba(78, 215, 241, 0.4));
    }
  }

  @keyframes virtra-dot {
    0%, 80%, 100% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    40% {
      transform: scale(1.2);
      opacity: 1;
    }
  }

  @keyframes virtra-fade-in {
    0% {
      opacity: 0;
      transform: translateY(20px) scale(0.95);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .animate-virtra-logo {
    animation: virtra-logo 4s ease-in-out infinite;
  }

  .animate-virtra-dot {
    animation: virtra-dot 1.4s ease-in-out infinite;
  }

  .animate-virtra-fade-in {
    animation: virtra-fade-in 0.8s ease-out forwards;
  }

  /* VirTra-style preloader specific styles */
  .virtra-preloader-bg {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
  }

  .virtra-logo-glow {
    filter: drop-shadow(0 0 30px rgba(78, 215, 241, 0.4))
            drop-shadow(0 0 60px rgba(78, 215, 241, 0.2));
  }

  /* Mobile Product Page Parallax */
  .mobile-product-scroll {
    scroll-behavior: smooth;
    overscroll-behavior: contain;
  }

  .mobile-product-content {
    transform: translateZ(0);
    will-change: transform;
  }

  /* Enhanced scroll snap for mobile product page */
  .scroll-snap-y {
    scroll-snap-type: y mandatory;
  }

  .scroll-snap-start {
    scroll-snap-align: start;
  }

  /* Smooth backdrop blur transitions */
  .backdrop-blur-transition {
    transition: backdrop-filter 0.3s ease-in-out;
  }

  /* Enhanced mobile touch interactions */
  .touch-manipulation {
    touch-action: manipulation;
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .animate-virtra-logo,
    .animate-virtra-dot,
    .animate-virtra-fade-in,
    .animate-pulse,
    .animate-bounce,
    .animate-ping {
      animation: none;
    }

    .animate-virtra-logo {
      transform: scale(1) rotate(0deg);
      filter: drop-shadow(0 0 20px rgba(78, 215, 241, 0.3));
    }

    .mobile-product-scroll {
      scroll-behavior: auto;
    }
  }
}