"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  TrendingUp, 
  Eye, 
  Heart, 
  MessageSquare,
  Users,
  Calendar,
  FileText,
  BarChart3,
  PieChart,
  Activity
} from "lucide-react";
import { createClient } from '@/utils/supabase/client';

interface BlogAnalytics {
  totalPosts: number;
  publishedPosts: number;
  draftPosts: number;
  totalViews: number;
  totalLikes: number;
  totalComments: number;
  avgViewsPerPost: number;
  avgLikesPerPost: number;
  avgCommentsPerPost: number;
  topPosts: Array<{
    id: string;
    title: string;
    views: number;
    likes: number;
    comments_count: number;
    published_at: string;
  }>;
  categoryStats: Array<{
    name: string;
    color: string;
    post_count: number;
    total_views: number;
  }>;
  recentActivity: Array<{
    type: string;
    title: string;
    date: string;
    value: number;
  }>;
}

export default function BlogAnalyticsPage() {
  const [analytics, setAnalytics] = useState<BlogAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [dateRange, setDateRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d');
  const supabase = createClient();

  useEffect(() => {
    fetchAnalytics();
  }, [dateRange]);

  const fetchAnalytics = async () => {
    try {
      setIsLoading(true);

      // Calculate date filter
      let dateFilter = '';
      if (dateRange !== 'all') {
        const days = parseInt(dateRange.replace('d', ''));
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        dateFilter = startDate.toISOString();
      }

      // Fetch blog posts with stats
      const postsQuery = supabase
        .from('blog_articles')
        .select(`
          id,
          title,
          views,
          likes,
          comments_count,
          is_published,
          published_at,
          created_at,
          category_id,
          blog_categories (name, color)
        `);

      if (dateFilter) {
        postsQuery.gte('created_at', dateFilter);
      }

      const { data: posts, error: postsError } = await postsQuery;
      if (postsError) throw postsError;

      // Fetch categories with post counts
      const { data: categories, error: categoriesError } = await supabase
        .from('blog_categories')
        .select('name, color, post_count');

      if (categoriesError) throw categoriesError;

      // Calculate analytics
      const totalPosts = posts?.length || 0;
      const publishedPosts = posts?.filter(p => p.is_published).length || 0;
      const draftPosts = totalPosts - publishedPosts;
      const totalViews = posts?.reduce((sum, p) => sum + (p.views || 0), 0) || 0;
      const totalLikes = posts?.reduce((sum, p) => sum + (p.likes || 0), 0) || 0;
      const totalComments = posts?.reduce((sum, p) => sum + (p.comments_count || 0), 0) || 0;

      // Top performing posts
      const topPosts = posts
        ?.filter(p => p.is_published)
        .sort((a, b) => (b.views || 0) - (a.views || 0))
        .slice(0, 5)
        .map(p => ({
          id: p.id,
          title: p.title,
          views: p.views || 0,
          likes: p.likes || 0,
          comments_count: p.comments_count || 0,
          published_at: p.published_at || p.created_at
        })) || [];

      // Category stats
      const categoryStats = categories?.map(cat => {
        const categoryPosts = posts?.filter(p => p.category_id === cat.name) || [];
        const categoryViews = categoryPosts.reduce((sum, p) => sum + (p.views || 0), 0);
        return {
          name: cat.name,
          color: cat.color,
          post_count: cat.post_count || 0,
          total_views: categoryViews
        };
      }) || [];

      // Recent activity (simplified)
      const recentActivity = posts
        ?.filter(p => p.is_published)
        .sort((a, b) => new Date(b.published_at || b.created_at).getTime() - new Date(a.published_at || a.created_at).getTime())
        .slice(0, 10)
        .map(p => ({
          type: 'post_published',
          title: p.title,
          date: p.published_at || p.created_at,
          value: p.views || 0
        })) || [];

      setAnalytics({
        totalPosts,
        publishedPosts,
        draftPosts,
        totalViews,
        totalLikes,
        totalComments,
        avgViewsPerPost: publishedPosts > 0 ? Math.round(totalViews / publishedPosts) : 0,
        avgLikesPerPost: publishedPosts > 0 ? Math.round(totalLikes / publishedPosts) : 0,
        avgCommentsPerPost: publishedPosts > 0 ? Math.round(totalComments / publishedPosts) : 0,
        topPosts,
        categoryStats,
        recentActivity
      });

    } catch (error) {
      console.error('Error fetching analytics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Blog Analytics</h1>
            <p className="text-muted-foreground">Track your blog performance</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="glass-effect border border-white/10 neo-shadow animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-white/20 rounded mb-2"></div>
                <div className="h-8 bg-white/10 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-foreground mb-2">No analytics data available</h3>
          <p className="text-muted-foreground">Analytics will appear once you have blog posts</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Blog Analytics</h1>
          <p className="text-muted-foreground">Track your blog performance and engagement</p>
        </div>
        <div className="flex gap-2">
          {(['7d', '30d', '90d', 'all'] as const).map((range) => (
            <Button
              key={range}
              variant={dateRange === range ? 'default' : 'outline'}
              onClick={() => setDateRange(range)}
              className="min-h-[44px]"
            >
              {range === 'all' ? 'All Time' : range.toUpperCase()}
            </Button>
          ))}
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="glass-effect border border-white/10 neo-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Posts</p>
                <p className="text-2xl font-bold text-foreground">{analytics.totalPosts}</p>
              </div>
              <FileText className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-effect border border-white/10 neo-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Published</p>
                <p className="text-2xl font-bold text-foreground">{analytics.publishedPosts}</p>
              </div>
              <Eye className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-effect border border-white/10 neo-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Views</p>
                <p className="text-2xl font-bold text-foreground">{formatNumber(analytics.totalViews)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-effect border border-white/10 neo-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Engagement</p>
                <p className="text-2xl font-bold text-foreground">
                  {formatNumber(analytics.totalLikes + analytics.totalComments)}
                </p>
              </div>
              <Activity className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Engagement Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="glass-effect border border-white/10 neo-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Views/Post</p>
                <p className="text-2xl font-bold text-foreground">{analytics.avgViewsPerPost}</p>
              </div>
              <Eye className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-effect border border-white/10 neo-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Likes/Post</p>
                <p className="text-2xl font-bold text-foreground">{analytics.avgLikesPerPost}</p>
              </div>
              <Heart className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-effect border border-white/10 neo-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Comments/Post</p>
                <p className="text-2xl font-bold text-foreground">{analytics.avgCommentsPerPost}</p>
              </div>
              <MessageSquare className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Performing Posts */}
      <Card className="glass-effect border border-white/10 neo-shadow">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-foreground">Top Performing Posts</CardTitle>
          <CardDescription>Your most viewed blog posts</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analytics.topPosts.map((post, index) => (
              <div key={post.id} className="flex items-center justify-between p-4 glass-effect border border-white/10 rounded-xl">
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <Badge variant="outline" className="border-white/20 text-muted-foreground">
                      #{index + 1}
                    </Badge>
                    <h4 className="font-medium text-foreground">{post.title}</h4>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    Published {formatDate(post.published_at)}
                  </p>
                </div>
                <div className="flex items-center gap-6 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Eye className="h-4 w-4" />
                    {formatNumber(post.views)}
                  </div>
                  <div className="flex items-center gap-1">
                    <Heart className="h-4 w-4" />
                    {post.likes}
                  </div>
                  <div className="flex items-center gap-1">
                    <MessageSquare className="h-4 w-4" />
                    {post.comments_count}
                  </div>
                </div>
              </div>
            ))}
            {analytics.topPosts.length === 0 && (
              <div className="text-center py-8">
                <FileText className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">No published posts yet</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Category Performance */}
      <Card className="glass-effect border border-white/10 neo-shadow">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-foreground">Category Performance</CardTitle>
          <CardDescription>Posts and views by category</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analytics.categoryStats.map((category) => (
              <div key={category.name} className="flex items-center justify-between p-4 glass-effect border border-white/10 rounded-xl">
                <div className="flex items-center gap-3">
                  <div 
                    className="w-4 h-4 rounded-full border border-white/20"
                    style={{ backgroundColor: category.color }}
                  ></div>
                  <h4 className="font-medium text-foreground">{category.name}</h4>
                </div>
                <div className="flex items-center gap-6 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <FileText className="h-4 w-4" />
                    {category.post_count} posts
                  </div>
                  <div className="flex items-center gap-1">
                    <Eye className="h-4 w-4" />
                    {formatNumber(category.total_views)} views
                  </div>
                </div>
              </div>
            ))}
            {analytics.categoryStats.length === 0 && (
              <div className="text-center py-8">
                <PieChart className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">No categories with posts yet</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card className="glass-effect border border-white/10 neo-shadow">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-foreground">Recent Activity</CardTitle>
          <CardDescription>Latest blog post publications</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.recentActivity.map((activity, index) => (
              <div key={index} className="flex items-center justify-between p-3 glass-effect border border-white/10 rounded-xl">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 rounded-full bg-primary"></div>
                  <div>
                    <p className="font-medium text-foreground">{activity.title}</p>
                    <p className="text-sm text-muted-foreground">Published</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-foreground">{activity.value} views</p>
                  <p className="text-xs text-muted-foreground">{formatDate(activity.date)}</p>
                </div>
              </div>
            ))}
            {analytics.recentActivity.length === 0 && (
              <div className="text-center py-8">
                <Activity className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">No recent activity</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
