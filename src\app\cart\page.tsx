import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import { createClient } from "../../utils/supabase/server";
import { CartItems } from "@/components/cart-items";

export default async function CartPage() {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      <main className="py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl font-bold text-foreground mb-8">Your Cart</h1>
          <CartItems />
        </div>
      </main>

      <Footer />
    </div>
  );
}
