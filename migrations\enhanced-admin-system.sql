-- Enhanced Admin System Migration
-- This migration adds role hierarchy, activity logging, and reporting capabilities

-- Step 1: Create admin_role enum if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'admin_role') THEN
        CREATE TYPE admin_role AS ENUM ('admin', 'senior_admin', 'junior_admin');
        RAISE NOTICE 'Created admin_role enum';
    END IF;
END $$;

-- Step 2: Add admin_role column to users table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'admin_role'
    ) THEN
        ALTER TABLE public.users ADD COLUMN admin_role admin_role;
        RAISE NOTICE 'Added admin_role column to users table';
    END IF;
END $$;

-- Step 3: Update existing admin users to have 'admin' admin_role
UPDATE public.users 
SET admin_role = 'admin' 
WHERE role = 'admin' AND admin_role IS NULL;

-- Step 4: Create admin_activity_logs table
CREATE TABLE IF NOT EXISTS public.admin_activity_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    admin_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    action_type TEXT NOT NULL, -- 'order_management', 'product_management', 'user_management', 'system_config'
    action_description TEXT NOT NULL,
    target_id UUID, -- ID of the affected resource (order_id, product_id, user_id, etc.)
    target_type TEXT, -- 'order', 'product', 'user', 'system'
    metadata JSONB, -- Additional action details
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN NOT NULL DEFAULT true,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 5: Enable RLS on admin_activity_logs
ALTER TABLE public.admin_activity_logs ENABLE ROW LEVEL SECURITY;

-- Step 6: Create admin activity logs policies
CREATE POLICY "Admins can view all activity logs"
    ON public.admin_activity_logs
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() 
            AND role = 'admin'
            AND admin_role = 'admin'
        )
    );

CREATE POLICY "All admin types can insert their own activity logs"
    ON public.admin_activity_logs
    FOR INSERT
    WITH CHECK (
        admin_id = auth.uid() AND
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() 
            AND role = 'admin'
        )
    );

-- Step 7: Create admin_reports table
CREATE TABLE IF NOT EXISTS public.admin_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    generated_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    report_type TEXT NOT NULL, -- 'activity_summary', 'performance_metrics', 'audit_trail'
    report_name TEXT NOT NULL,
    filters JSONB, -- Date ranges, admin roles, activity types
    file_path TEXT, -- Path to generated report file
    file_format TEXT NOT NULL, -- 'pdf', 'excel'
    download_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE
);

-- Step 8: Enable RLS on admin_reports
ALTER TABLE public.admin_reports ENABLE ROW LEVEL SECURITY;

-- Step 9: Create admin reports policies (only main admins can access)
CREATE POLICY "Main admins can manage reports"
    ON public.admin_reports
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() 
            AND role = 'admin'
            AND admin_role = 'admin'
        )
    );

-- Step 10: Create function to log admin activities
CREATE OR REPLACE FUNCTION log_admin_activity(
    p_action_type TEXT,
    p_action_description TEXT,
    p_target_id UUID DEFAULT NULL,
    p_target_type TEXT DEFAULT NULL,
    p_metadata JSONB DEFAULT NULL,
    p_success BOOLEAN DEFAULT true,
    p_error_message TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    activity_id UUID;
BEGIN
    INSERT INTO public.admin_activity_logs (
        admin_id,
        action_type,
        action_description,
        target_id,
        target_type,
        metadata,
        success,
        error_message
    ) VALUES (
        auth.uid(),
        p_action_type,
        p_action_description,
        p_target_id,
        p_target_type,
        p_metadata,
        p_success,
        p_error_message
    ) RETURNING id INTO activity_id;
    
    RETURN activity_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 11: Create function to check admin permissions
CREATE OR REPLACE FUNCTION check_admin_permission(
    p_required_role admin_role DEFAULT 'junior_admin'
) RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid() 
        AND role = 'admin'
        AND (
            admin_role = 'admin' OR
            (p_required_role = 'senior_admin' AND admin_role IN ('admin', 'senior_admin')) OR
            (p_required_role = 'junior_admin' AND admin_role IN ('admin', 'senior_admin', 'junior_admin'))
        )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 12: Update existing RLS policies to use new admin role system
-- Update orders policies
DROP POLICY IF EXISTS "Admins can manage all orders" ON public.orders;
CREATE POLICY "Admins can manage all orders"
    ON public.orders
    FOR ALL
    USING (check_admin_permission('junior_admin'));

-- Update products policies  
DROP POLICY IF EXISTS "Admins can manage all products" ON public.products;
CREATE POLICY "Admins can manage all products"
    ON public.products
    FOR ALL
    USING (check_admin_permission('junior_admin'));

-- Update users policies
DROP POLICY IF EXISTS "Admins can manage all users" ON public.users;
CREATE POLICY "Admins can view all users"
    ON public.users
    FOR SELECT
    USING (check_admin_permission('junior_admin'));

CREATE POLICY "Senior admins can update user roles"
    ON public.users
    FOR UPDATE
    USING (check_admin_permission('senior_admin'))
    WITH CHECK (check_admin_permission('senior_admin'));

-- Completion message
DO $$
BEGIN
    RAISE NOTICE 'Enhanced admin system migration completed successfully!';
    RAISE NOTICE 'Admin roles: admin (full access), senior_admin (user management), junior_admin (limited access)';
    RAISE NOTICE 'Activity logging and reporting system enabled.';
END $$;
