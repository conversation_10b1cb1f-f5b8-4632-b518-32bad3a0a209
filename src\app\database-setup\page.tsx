"use client";

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { createBrowserClient } from '@supabase/ssr';
import { CheckCircle, XCircle, AlertCircle, Database, User, Play } from 'lucide-react';

export default function DatabaseSetupPage() {
  const [user, setUser] = useState<any>(null);
  const [setupStatus, setSetupStatus] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(0);

  useEffect(() => {
    loadUser();
  }, []);

  const loadUser = async () => {
    const supabase = createBrowserClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );
    
    const { data: { user } } = await supabase.auth.getUser();
    setUser(user);
  };

  const runCompleteSetup = async () => {
    if (!user) {
      alert('Please sign in first');
      return;
    }

    setLoading(true);
    setStep(0);
    
    const supabase = createBrowserClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    const results = {
      tables: { success: false, error: '' },
      programs: { success: false, error: '' },
      resources: { success: false, error: '' },
      mentor: { success: false, error: '' },
      enrollment: { success: false, error: '' },
      sessions: { success: false, error: '' },
    };

    try {
      // Step 1: Create tables (this might fail due to permissions, but that's ok)
      setStep(1);
      try {
        // We can't create tables from client, but we can check if they exist
        const { data, error } = await supabase.from('mentorship_programs').select('id').limit(1);
        if (!error) {
          results.tables.success = true;
        } else {
          results.tables.error = 'Tables may not exist. Please run the SQL script in Supabase dashboard.';
        }
      } catch (err: any) {
        results.tables.error = err.message;
      }

      // Step 2: Insert sample programs
      setStep(2);
      try {
        const { error: programError } = await supabase
          .from('mentorship_programs')
          .upsert([
            {
              id: '550e8400-e29b-41d4-a716-446655440001',
              name: '6-Month Tennis Mastery',
              description: 'Comprehensive tennis training program for beginners to intermediate players',
              duration_months: 6,
              price_monthly: 299.99,
              price_upfront: 1599.99,
              features: ["Weekly 1-on-1 sessions", "Video analysis", "Training plans", "Progress tracking"]
            },
            {
              id: '550e8400-e29b-41d4-a716-************',
              name: '12-Month Pro Development',
              description: 'Advanced tennis coaching for competitive players',
              duration_months: 12,
              price_monthly: 399.99,
              price_upfront: 4199.99,
              features: ["Bi-weekly sessions", "Tournament preparation", "Mental coaching", "Nutrition guidance"]
            }
          ]);

        if (programError) {
          results.programs.error = programError.message;
        } else {
          results.programs.success = true;
        }
      } catch (err: any) {
        results.programs.error = err.message;
      }

      // Step 3: Insert sample resources
      setStep(3);
      try {
        const { error: resourceError } = await supabase
          .from('resources')
          .upsert([
            {
              id: '550e8400-e29b-41d4-a716-************',
              title: 'Tennis Fundamentals Video Series',
              description: 'Complete video series covering basic tennis techniques and fundamentals',
              type: 'video',
              category: 'Fundamentals',
              format: 'mp4',
              file_path: 'resources/tennis-fundamentals.mp4',
              size_bytes: 524288000,
              download_count: 45,
              created_by: user.id
            },
            {
              id: '550e8400-e29b-41d4-a716-************',
              title: 'Serve Technique Guide',
              description: 'Comprehensive PDF guide to improving your tennis serve',
              type: 'document',
              category: 'Technique',
              format: 'pdf',
              file_path: 'resources/serve-guide.pdf',
              size_bytes: 2048000,
              download_count: 32,
              created_by: user.id
            },
            {
              id: '550e8400-e29b-41d4-a716-************',
              title: 'Weekly Training Plan',
              description: 'Structured training program for intermediate players',
              type: 'training',
              category: 'Training Plans',
              format: 'pdf',
              file_path: 'resources/weekly-plan.pdf',
              size_bytes: 1024000,
              download_count: 28,
              created_by: user.id
            }
          ]);

        if (resourceError) {
          results.resources.error = resourceError.message;
        } else {
          results.resources.success = true;
        }
      } catch (err: any) {
        results.resources.error = err.message;
      }

      // Step 4: Create mentor profile
      setStep(4);
      try {
        const { error: mentorError } = await supabase
          .from('mentors')
          .upsert([
            {
              id: '550e8400-e29b-41d4-a716-446655440003',
              user_id: user.id,
              bio: 'Professional tennis coach with 15 years of experience. Former college player and certified instructor.',
              specialties: ["Serve technique", "Backhand improvement", "Mental game", "Tournament preparation"],
              experience_years: 15,
              availability: {
                "monday": ["09:00", "17:00"],
                "tuesday": ["09:00", "17:00"],
                "wednesday": ["09:00", "17:00"],
                "thursday": ["09:00", "17:00"],
                "friday": ["09:00", "17:00"]
              }
            }
          ]);

        if (mentorError) {
          results.mentor.error = mentorError.message;
        } else {
          results.mentor.success = true;
        }
      } catch (err: any) {
        results.mentor.error = err.message;
      }

      // Step 5: Create student enrollment
      setStep(5);
      try {
        const { error: enrollmentError } = await supabase
          .from('student_enrollments')
          .upsert([
            {
              id: '550e8400-e29b-41d4-a716-446655440008',
              student_id: user.id,
              program_id: '550e8400-e29b-41d4-a716-446655440001',
              mentor_id: '550e8400-e29b-41d4-a716-446655440003',
              start_date: '2025-01-01T00:00:00Z',
              end_date: '2025-07-01T00:00:00Z',
              payment_type: 'monthly',
              status: 'active'
            }
          ]);

        if (enrollmentError) {
          results.enrollment.error = enrollmentError.message;
        } else {
          results.enrollment.success = true;
        }
      } catch (err: any) {
        results.enrollment.error = err.message;
      }

      // Step 6: Create sample sessions
      setStep(6);
      try {
        const { error: sessionError } = await supabase
          .from('mentorship_sessions')
          .upsert([
            {
              id: '550e8400-e29b-41d4-a716-446655440009',
              enrollment_id: '550e8400-e29b-41d4-a716-446655440008',
              scheduled_at: '2025-01-25T10:00:00Z',
              duration_minutes: 60,
              status: 'scheduled',
              notes: 'Focus on serve technique and footwork'
            },
            {
              id: '550e8400-e29b-41d4-a716-44665544000a',
              enrollment_id: '550e8400-e29b-41d4-a716-446655440008',
              scheduled_at: '2025-01-18T10:00:00Z',
              duration_minutes: 60,
              status: 'completed',
              notes: 'Worked on backhand technique - great improvement shown'
            },
            {
              id: '550e8400-e29b-41d4-a716-44665544000b',
              enrollment_id: '550e8400-e29b-41d4-a716-446655440008',
              scheduled_at: '2025-02-01T10:00:00Z',
              duration_minutes: 60,
              status: 'scheduled',
              notes: 'Progress review and goal setting for next month'
            }
          ]);

        if (sessionError) {
          results.sessions.error = sessionError.message;
        } else {
          results.sessions.success = true;
        }
      } catch (err: any) {
        results.sessions.error = err.message;
      }

    } catch (error: any) {
      console.error('Setup failed:', error);
    } finally {
      setSetupStatus(results);
      setLoading(false);
      setStep(0);
    }
  };

  const steps = [
    'Checking database tables...',
    'Creating mentorship programs...',
    'Adding learning resources...',
    'Setting up mentor profile...',
    'Creating student enrollment...',
    'Adding sample sessions...'
  ];

  return (
    <div className="p-6 space-y-6 max-w-4xl mx-auto">
      <div className="text-center space-y-4">
        <Database className="h-16 w-16 mx-auto text-blue-600" />
        <h1 className="text-3xl font-bold">Student Dashboard Database Setup</h1>
        <p className="text-muted-foreground">
          This tool will set up the required database tables and sample data for the student dashboard.
        </p>
      </div>

      {user && (
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <User className="h-5 w-5" />
            <div>
              <p className="font-medium">Current User</p>
              <p className="text-sm text-muted-foreground">{user.email}</p>
              <p className="text-xs text-muted-foreground">ID: {user.id}</p>
            </div>
          </div>
        </Card>
      )}

      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>Important:</strong> Before running this setup, please execute the SQL script 
          <code className="mx-1 px-2 py-1 bg-muted rounded">database-setup-complete.sql</code> 
          in your Supabase SQL Editor to create the required tables.
        </AlertDescription>
      </Alert>

      <div className="flex justify-center">
        <Button 
          onClick={runCompleteSetup} 
          disabled={loading || !user}
          size="lg"
          className="px-8"
        >
          {loading ? (
            <>
              <Play className="h-4 w-4 mr-2 animate-spin" />
              Setting up... ({step > 0 ? steps[step - 1] : 'Starting'})
            </>
          ) : (
            <>
              <Play className="h-4 w-4 mr-2" />
              Run Complete Setup
            </>
          )}
        </Button>
      </div>

      {setupStatus && (
        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Setup Results</h3>
          
          {Object.entries(setupStatus).map(([key, result]: [string, any]) => (
            <Card key={key} className="p-4">
              <div className="flex items-center gap-3">
                {result.success ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-600" />
                )}
                <div className="flex-1">
                  <p className="font-medium capitalize">{key.replace(/([A-Z])/g, ' $1')}</p>
                  {result.error && (
                    <p className="text-sm text-red-600 mt-1">{result.error}</p>
                  )}
                </div>
                <Badge variant={result.success ? "default" : "destructive"}>
                  {result.success ? "Success" : "Failed"}
                </Badge>
              </div>
            </Card>
          ))}

          {Object.values(setupStatus).every((result: any) => result.success) && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Setup Complete!</strong> Your student dashboard should now work correctly. 
                Visit <a href="/student-dashboard" className="underline text-blue-600">/student-dashboard</a> to test it.
              </AlertDescription>
            </Alert>
          )}
        </div>
      )}
    </div>
  );
}
