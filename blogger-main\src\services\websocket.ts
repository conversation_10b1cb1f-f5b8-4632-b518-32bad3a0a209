// 🔌 WEBSOCKET SERVICE FOR REAL-TIME FEATURES
// Real-time user creation, auth updates, and live data synchronization

import { supabase } from '../../supabase/supabase';

// Simple logger for development
const logger = {
  info: (message: string, extra?: any) => console.log(`ℹ️ ${message}`, extra),
  warn: (message: string, extra?: any) => console.warn(`⚠️ ${message}`, extra),
  error: (message: string, error?: any) => console.error(`❌ ${message}`, error),
  debug: (message: string, extra?: any) => console.debug(`🐛 ${message}`, extra),
};

// Environment configuration
const WEBSOCKET_URL = import.meta.env.VITE_WEBSOCKET_URL || 'ws://localhost:5173/ws';
const ENABLE_WEBSOCKETS = import.meta.env.VITE_ENABLE_WEBSOCKETS === 'true';
const RECONNECT_INTERVAL = parseInt(import.meta.env.VITE_WEBSOCKET_RECONNECT_INTERVAL || '5000');
const MAX_RETRIES = parseInt(import.meta.env.VITE_WEBSOCKET_MAX_RETRIES || '5');

// WebSocket event types
export enum WebSocketEventType {
  USER_CREATED = 'user_created',
  USER_UPDATED = 'user_updated',
  USER_DELETED = 'user_deleted',
  AUTH_STATE_CHANGED = 'auth_state_changed',
  PROFILE_UPDATED = 'profile_updated',
  SUBSCRIPTION_CHANGED = 'subscription_changed',
  NOTIFICATION_RECEIVED = 'notification_received',
  ARTICLE_LIKED = 'article_liked',
  COMMENT_ADDED = 'comment_added',
  REAL_TIME_UPDATE = 'real_time_update',
}

// WebSocket message interface
export interface WebSocketMessage {
  type: WebSocketEventType;
  payload: any;
  timestamp: string;
  userId?: string;
  sessionId?: string;
}

// WebSocket connection states
export enum ConnectionState {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}

// Event listener type
export type WebSocketEventListener = (message: WebSocketMessage) => void;

class WebSocketService {
  private ws: WebSocket | null = null;
  private connectionState: ConnectionState = ConnectionState.DISCONNECTED;
  private eventListeners: Map<WebSocketEventType, Set<WebSocketEventListener>> = new Map();
  private reconnectAttempts: number = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private sessionId: string = this.generateSessionId();

  constructor() {
    if (ENABLE_WEBSOCKETS) {
      this.connect();
    }
  }

  // Generate unique session ID
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Connect to WebSocket server
  public connect(): void {
    if (!ENABLE_WEBSOCKETS) {
      logger.warn('WebSocket connections are disabled');
      return;
    }

    if (this.ws?.readyState === WebSocket.CONNECTING || this.ws?.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      this.connectionState = ConnectionState.CONNECTING;
      this.ws = new WebSocket(WEBSOCKET_URL);

      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);

      logger.info('WebSocket connection initiated', { url: WEBSOCKET_URL, sessionId: this.sessionId });
    } catch (error) {
      logger.error('Failed to create WebSocket connection', error);
      this.connectionState = ConnectionState.ERROR;
      this.scheduleReconnect();
    }
  }

  // Handle WebSocket open event
  private handleOpen(): void {
    this.connectionState = ConnectionState.CONNECTED;
    this.reconnectAttempts = 0;
    
    logger.info('WebSocket connected successfully', { sessionId: this.sessionId });
    
    // Send authentication message
    this.sendAuthMessage();
    
    // Start heartbeat
    this.startHeartbeat();
    
    // Emit connection event
    this.emit(WebSocketEventType.AUTH_STATE_CHANGED, {
      connected: true,
      sessionId: this.sessionId,
    });
  }

  // Handle WebSocket message
  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      logger.debug('WebSocket message received', { type: message.type, payload: message.payload });
      
      // Emit to listeners
      this.emit(message.type, message.payload);
      
    } catch (error) {
      logger.error('Failed to parse WebSocket message', error, { data: event.data });
    }
  }

  // Handle WebSocket close event
  private handleClose(event: CloseEvent): void {
    this.connectionState = ConnectionState.DISCONNECTED;
    this.stopHeartbeat();
    
    logger.warn('WebSocket connection closed', { 
      code: event.code, 
      reason: event.reason,
      wasClean: event.wasClean 
    });
    
    // Emit disconnection event
    this.emit(WebSocketEventType.AUTH_STATE_CHANGED, {
      connected: false,
      reason: event.reason,
    });
    
    // Schedule reconnect if not intentional
    if (!event.wasClean && this.reconnectAttempts < MAX_RETRIES) {
      this.scheduleReconnect();
    }
  }

  // Handle WebSocket error
  private handleError(error: Event): void {
    this.connectionState = ConnectionState.ERROR;
    logger.error('WebSocket error occurred', error);
  }

  // Send authentication message
  private sendAuthMessage(): void {
    const user = supabase.auth.getUser();
    
    this.send({
      type: WebSocketEventType.AUTH_STATE_CHANGED,
      payload: {
        action: 'authenticate',
        sessionId: this.sessionId,
        userId: user ? (user as any).id : null,
      },
      timestamp: new Date().toISOString(),
      sessionId: this.sessionId,
    });
  }

  // Start heartbeat to keep connection alive
  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.send({
          type: WebSocketEventType.REAL_TIME_UPDATE,
          payload: { action: 'ping' },
          timestamp: new Date().toISOString(),
          sessionId: this.sessionId,
        });
      }
    }, 30000); // Send ping every 30 seconds
  }

  // Stop heartbeat
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  // Schedule reconnection
  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    this.reconnectAttempts++;
    this.connectionState = ConnectionState.RECONNECTING;
    
    const delay = Math.min(RECONNECT_INTERVAL * Math.pow(2, this.reconnectAttempts - 1), 30000);
    
    logger.info('Scheduling WebSocket reconnection', { 
      attempt: this.reconnectAttempts, 
      delay,
      maxRetries: MAX_RETRIES 
    });
    
    this.reconnectTimer = setTimeout(() => {
      this.connect();
    }, delay);
  }

  // Send message through WebSocket
  public send(message: WebSocketMessage): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
      logger.debug('WebSocket message sent', { type: message.type });
    } else {
      logger.warn('Cannot send WebSocket message - connection not open', { 
        state: this.connectionState,
        messageType: message.type 
      });
    }
  }

  // Add event listener
  public addEventListener(type: WebSocketEventType, listener: WebSocketEventListener): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, new Set());
    }
    this.eventListeners.get(type)!.add(listener);
  }

  // Remove event listener
  public removeEventListener(type: WebSocketEventType, listener: WebSocketEventListener): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      listeners.delete(listener);
    }
  }

  // Emit event to listeners
  private emit(type: WebSocketEventType, payload: any): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      const message: WebSocketMessage = {
        type,
        payload,
        timestamp: new Date().toISOString(),
        sessionId: this.sessionId,
      };
      
      listeners.forEach(listener => {
        try {
          listener(message);
        } catch (error) {
          logger.error('Error in WebSocket event listener', error, { type });
        }
      });
    }
  }

  // Get connection state
  public getConnectionState(): ConnectionState {
    return this.connectionState;
  }

  // Check if connected
  public isConnected(): boolean {
    return this.connectionState === ConnectionState.CONNECTED;
  }

  // Disconnect WebSocket
  public disconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    this.stopHeartbeat();
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    
    this.connectionState = ConnectionState.DISCONNECTED;
    logger.info('WebSocket disconnected by client');
  }

  // User creation event
  public notifyUserCreated(userData: any): void {
    this.send({
      type: WebSocketEventType.USER_CREATED,
      payload: userData,
      timestamp: new Date().toISOString(),
      userId: userData.id,
      sessionId: this.sessionId,
    });
  }

  // Profile update event
  public notifyProfileUpdated(profileData: any): void {
    this.send({
      type: WebSocketEventType.PROFILE_UPDATED,
      payload: profileData,
      timestamp: new Date().toISOString(),
      userId: profileData.id,
      sessionId: this.sessionId,
    });
  }

  // Subscription change event
  public notifySubscriptionChanged(subscriptionData: any): void {
    this.send({
      type: WebSocketEventType.SUBSCRIPTION_CHANGED,
      payload: subscriptionData,
      timestamp: new Date().toISOString(),
      userId: subscriptionData.user_id,
      sessionId: this.sessionId,
    });
  }
}

// Create singleton instance
export const webSocketService = new WebSocketService();

// React hook for WebSocket events
export const useWebSocket = () => {
  const addEventListener = (type: WebSocketEventType, listener: WebSocketEventListener) => {
    webSocketService.addEventListener(type, listener);
    
    // Return cleanup function
    return () => {
      webSocketService.removeEventListener(type, listener);
    };
  };

  return {
    isConnected: webSocketService.isConnected(),
    connectionState: webSocketService.getConnectionState(),
    addEventListener,
    send: webSocketService.send.bind(webSocketService),
    notifyUserCreated: webSocketService.notifyUserCreated.bind(webSocketService),
    notifyProfileUpdated: webSocketService.notifyProfileUpdated.bind(webSocketService),
    notifySubscriptionChanged: webSocketService.notifySubscriptionChanged.bind(webSocketService),
  };
};

export default webSocketService;
