"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import Link from 'next/link';
import { createClient } from '@/utils/supabase/client';
import { RichTextEditor } from '@/components/blog/rich-text-editor';

interface ArticleData {
  title: string;
  excerpt: string;
  content: string;
  tags: string[];
  category: string;
  isFeatured: boolean;
  featuredImage?: string;
  seoTitle?: string;
  seoDescription?: string;
}

interface Category {
  id: string;
  name: string;
  color: string;
}

export default function NewBlogPostPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const supabase = createClient();

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('blog_categories')
        .select('id, name, color')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      setCategories(data || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast({
        title: "Error",
        description: "Failed to load categories",
        variant: "destructive",
      });
    }
  };

  const generateSlug = (title: string): string => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const calculateReadTime = (content: string): number => {
    const wordsPerMinute = 200;
    const wordCount = content.split(/\s+/).length;
    return Math.ceil(wordCount / wordsPerMinute);
  };

  const handleSave = async (data: ArticleData) => {
    if (!data.title || !data.content || !data.excerpt) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSaving(true);

      // Get current user
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        toast({
          title: "Error",
          description: "You must be logged in to create articles",
          variant: "destructive",
        });
        return;
      }

      // Create article in database
      const { data: article, error } = await supabase
        .from('blog_articles')
        .insert({
          title: data.title,
          slug: generateSlug(data.title),
          excerpt: data.excerpt,
          content: data.content,
          author_id: session.user.id,
          category_id: data.category || null,
          featured_image: data.featuredImage || null,
          is_featured: data.isFeatured,
          is_published: false, // Save as draft
          seo_title: data.seoTitle || null,
          seo_description: data.seoDescription || null,
          read_time: calculateReadTime(data.content),
          tags: data.tags,
        })
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "Success!",
        description: "Article saved as draft",
      });

      router.push('/admin/blog/posts');
    } catch (error) {
      console.error('Error saving article:', error);
      toast({
        title: "Error",
        description: "Failed to save article",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handlePublish = async (data: ArticleData) => {
    if (!data.title || !data.content || !data.excerpt) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);

      // Get current user
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        toast({
          title: "Error",
          description: "You must be logged in to publish articles",
          variant: "destructive",
        });
        return;
      }

      // Create and publish article
      const { data: article, error } = await supabase
        .from('blog_articles')
        .insert({
          title: data.title,
          slug: generateSlug(data.title),
          excerpt: data.excerpt,
          content: data.content,
          author_id: session.user.id,
          category_id: data.category || null,
          featured_image: data.featuredImage || null,
          is_featured: data.isFeatured,
          is_published: true,
          published_at: new Date().toISOString(),
          seo_title: data.seoTitle || null,
          seo_description: data.seoDescription || null,
          read_time: calculateReadTime(data.content),
          tags: data.tags,
        })
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "Success!",
        description: "Article published successfully",
      });

      router.push('/admin/blog/posts');
    } catch (error) {
      console.error('Error publishing article:', error);
      toast({
        title: "Error",
        description: "Failed to publish article",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="icon"
            asChild
            className="glass-effect border border-white/20 hover:glass-effect-subtle min-h-[44px] min-w-[44px]"
          >
            <Link href="/admin/blog/posts">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Create New Post</h1>
            <p className="text-muted-foreground">Write and publish your blog content</p>
          </div>
        </div>
      </div>

      {/* Editor */}
      <RichTextEditor
        categories={categories}
        onSave={handleSave}
        onPublish={handlePublish}
        isLoading={isLoading || isSaving}
      />

      {/* Writing Tips */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="glass-effect border border-white/10 neo-shadow p-6 rounded-2xl">
          <h3 className="text-lg font-semibold text-foreground mb-4">Content Guidelines</h3>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li className="flex items-start gap-2">
              <span className="text-primary">•</span>
              Write engaging headlines that grab attention
            </li>
            <li className="flex items-start gap-2">
              <span className="text-primary">•</span>
              Use clear and concise language
            </li>
            <li className="flex items-start gap-2">
              <span className="text-primary">•</span>
              Include relevant images and media
            </li>
            <li className="flex items-start gap-2">
              <span className="text-primary">•</span>
              Structure content with headings and lists
            </li>
            <li className="flex items-start gap-2">
              <span className="text-primary">•</span>
              Keep paragraphs short and scannable
            </li>
          </ul>
        </div>

        <div className="glass-effect border border-white/10 neo-shadow p-6 rounded-2xl">
          <h3 className="text-lg font-semibold text-foreground mb-4">SEO Best Practices</h3>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li className="flex items-start gap-2">
              <span className="text-primary">•</span>
              Include target keywords naturally
            </li>
            <li className="flex items-start gap-2">
              <span className="text-primary">•</span>
              Write compelling meta descriptions
            </li>
            <li className="flex items-start gap-2">
              <span className="text-primary">•</span>
              Use descriptive alt text for images
            </li>
            <li className="flex items-start gap-2">
              <span className="text-primary">•</span>
              Add relevant tags and categories
            </li>
            <li className="flex items-start gap-2">
              <span className="text-primary">•</span>
              Optimize for mobile reading experience
            </li>
          </ul>
        </div>
      </div>

      {/* Tennis Whisperer Integration Tips */}
      <div className="glass-effect border border-white/10 neo-shadow p-6 rounded-2xl">
        <h3 className="text-lg font-semibold text-foreground mb-4">Tennis Whisperer Integration</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-muted-foreground">
          <div>
            <h4 className="font-medium text-foreground mb-2">Product References</h4>
            <ul className="space-y-1">
              <li>• Link to relevant tennis equipment</li>
              <li>• Mention specific product features</li>
              <li>• Include product recommendations</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-foreground mb-2">Mentorship Content</h4>
            <ul className="space-y-1">
              <li>• Share training insights</li>
              <li>• Promote mentorship programs</li>
              <li>• Include success stories</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-foreground mb-2">Community Building</h4>
            <ul className="space-y-1">
              <li>• Encourage reader engagement</li>
              <li>• Ask questions in content</li>
              <li>• Build tennis community</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
