'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';
import AdminActivityMonitor from '@/components/admin/admin-activity-monitor';
import { AlertTriangle } from 'lucide-react';

interface AdminUser {
  id: string;
  email: string;
  full_name: string | null;
  admin_role: 'admin' | 'senior_admin' | 'junior_admin';
}

export default function AdminActivityPage() {
  const [currentUser, setCurrentUser] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    checkAccess();
  }, []);

  const checkAccess = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        router.push('/admin/sign-in');
        return;
      }

      const { data: userData } = await supabase
        .from('users')
        .select('id, email, full_name, admin_role')
        .eq('id', session.user.id)
        .single();

      if (!userData || userData.admin_role !== 'admin') {
        toast({
          title: "Access Denied",
          description: "Only main administrators can access activity monitoring",
          variant: "destructive",
        });
        router.push('/admin');
        return;
      }

      setCurrentUser(userData as AdminUser);
    } catch (error) {
      console.error('Error checking access:', error);
      router.push('/admin/sign-in');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Admin Activity Monitor</h1>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (!currentUser || currentUser.admin_role !== 'admin') {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
              <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
              <p className="text-muted-foreground">
                Only main administrators can access the activity monitoring system.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return <AdminActivityMonitor />;
}
