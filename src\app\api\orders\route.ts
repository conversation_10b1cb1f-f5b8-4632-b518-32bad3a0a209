import { NextResponse } from 'next/server';
import { createClient } from '../../../utils/supabase/server';

export async function GET(request: Request) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { data: orders, error } = await supabase
      .from('orders')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      return NextResponse.json(
        { error: 'Failed to retrieve orders' },
        { status: 500 }
      );
    }

    return NextResponse.json(orders);
  } catch (error: any) {
    console.error('Error retrieving orders:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
