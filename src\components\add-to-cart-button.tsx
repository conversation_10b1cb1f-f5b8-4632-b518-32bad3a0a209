"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useCart } from "@/context/cart-context";
import { ShoppingCart, ArrowRight } from "lucide-react";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";

// Import the Product type from Supabase and extend it
import { Product as SupabaseProduct } from "@/utils/supabase/products";

// Extended product type that includes optional rating and reviews
interface ProductWithRating extends Omit<SupabaseProduct, 'id' | 'image' | 'description'> {
  id: string | number; // Allow both string and number types for id
  image?: string | null; // Handle both undefined and null from database
  description?: string | null; // Handle both undefined and null from database
  rating?: number;
  reviews?: number;
}

interface AddToCartButtonProps {
  product: ProductWithRating;
  variant?: "default" | "outline" | "secondary";
  size?: "default" | "sm" | "lg" | "icon";
  showText?: boolean;
}

export function AddToCartButton({
  product,
  variant = "default",
  size = "default",
  showText = true,
}: AddToCartButtonProps) {
  const { addToCart, cartItems } = useCart();
  const [isAdding, setIsAdding] = useState(false);
  const [isInCart, setIsInCart] = useState(false);
  const router = useRouter();
  
  // Get normalized product ID for consistent comparison
  const getNormalizedId = (id: string | number) => {
    return typeof id === 'string' ? parseInt(id, 10) : id;
  };
  
  // Check if product is already in cart
  useEffect(() => {
    const productId = getNormalizedId(product.id);
    const existingItem = cartItems.find(item => item.id === productId);
    setIsInCart(!!existingItem);
  }, [cartItems, product.id]);

  const handleAddToCart = () => {
    // If product is already in cart, navigate to checkout
    if (isInCart) {
      router.push('/cart');
      return;
    }
    
    setIsAdding(true);

    // Get the product image (handle both image and images array)
    const productImage = product.image || (product.image && product.image.length > 0 ? product.image[0] : '');
    
    // Add the product to cart, converting string ID to number if needed
    addToCart({
      id: getNormalizedId(product.id),
      name: product.name,
      price: product.price,
      image: productImage
    });

    // Set the button state to in-cart immediately for better UX
    setTimeout(() => {
      setIsInCart(true);
      setIsAdding(false);
    }, 500);
  };

  // Determine button text and icon based on cart state
  const getButtonContent = () => {
    if (isAdding) return { text: "Adding...", icon: <ShoppingCart className={`h-4 w-4 ${showText ? "mr-2" : ""}`} /> };
    if (isInCart) return { text: "Proceed to Checkout", icon: <ArrowRight className={`h-4 w-4 ${showText ? "mr-2" : ""}`} /> };
    return { text: "Add to Cart", icon: <ShoppingCart className={`h-4 w-4 ${showText ? "mr-2" : ""}`} /> };
  };
  
  const buttonContent = getButtonContent();
  
  return (
    <Button
      variant={isInCart ? "secondary" : variant}
      size={size}
      className="w-full transition-all duration-300"
      onClick={handleAddToCart}
      disabled={isAdding}
    >
      {buttonContent.icon}
      {showText && buttonContent.text}
    </Button>
  );
}
