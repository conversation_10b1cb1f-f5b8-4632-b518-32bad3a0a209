import supabase from './supabase-client';

/**
 * Authentication utilities for Tennis-Gear application
 * These functions handle user authentication with Supabase
 */

/**
 * Sign up a new user
 * @param email User's email address
 * @param password User's password
 * @param fullName User's full name
 * @param role User's role (admin, mentor, student)
 * @returns The user data or error
 */
export async function signUp(
  email: string, 
  password: string, 
  fullName: string, 
  role: 'admin' | 'mentor' | 'student' = 'student'
) {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: { 
      data: { 
        full_name: fullName, 
        role 
      } 
    }
  });
  
  return { data, error };
}

/**
 * Sign in an existing user
 * @param email User's email address
 * @param password User's password
 * @returns The user session or error
 */
export async function signIn(email: string, password: string) {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  });
  
  return { data, error };
}

/**
 * Sign out the current user
 * @returns Success or error
 */
export async function signOut() {
  const { error } = await supabase.auth.signOut();
  return { error };
}

/**
 * Get the current user
 * @returns The current user or null
 */
export async function getCurrentUser() {
  const { data: { user }, error } = await supabase.auth.getUser();
  return { user, error };
}

/**
 * Get the current session
 * @returns The current session or null
 */
export async function getSession() {
  const { data: { session }, error } = await supabase.auth.getSession();
  return { session, error };
}

/**
 * Reset password
 * @param email User's email address
 * @returns Success or error
 */
export async function resetPassword(email: string) {
  const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${window.location.origin}/reset-password`,
  });
  
  return { data, error };
}

/**
 * Update user password
 * @param password New password
 * @returns Success or error
 */
export async function updatePassword(password: string) {
  const { data, error } = await supabase.auth.updateUser({
    password,
  });
  
  return { data, error };
}

/**
 * Update user profile
 * @param profile User profile data to update
 * @returns Success or error
 */
export async function updateProfile(profile: { 
  full_name?: string;
  avatar_url?: string;
  [key: string]: any;
}) {
  const { data, error } = await supabase.auth.updateUser({
    data: profile
  });
  
  return { data, error };
}
