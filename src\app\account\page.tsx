"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { createClient } from "@/utils/supabase/client";
import { getUserProfileClient, updateUserProfileClient, createUserProfileClient, uploadProfileAvatarClient } from "@/utils/supabase/user-profile-client";
import { UserProfile } from "@/utils/supabase/user-profile";
import { UserCircle, Package, CreditCard, Settings, Upload, Loader2 } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export default function AccountPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [user, setUser] = useState<any>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  
  // Form state
  const [formData, setFormData] = useState({
    full_name: "",
    email: "",
    phone_number: "",
    address: "",
    city: "",
    state: "",
    postal_code: "",
    country: "South Africa",
    date_of_birth: "",
    bio: "",
    avatar_url: ""
  });

  // Check if user is logged in and fetch profile
  useEffect(() => {
    async function checkUserAndFetchProfile() {
      try {
        const supabase = createClient();
        const { data } = await supabase.auth.getUser();
        
        if (!data.user) {
          // Redirect to login if not authenticated
          router.push("/sign-in?redirect=/account");
          return;
        }
        
        setUser(data.user);
        
        // Fetch user profile
        try {
          const response = await fetch('/api/profile');
          if (response.ok) {
            const userProfile = await response.json();

            if (userProfile) {
              setProfile(userProfile);
              setFormData({
                full_name: data.user.user_metadata?.full_name || "",
                email: data.user.email || "",
                phone_number: userProfile.phone_number || "",
                address: userProfile.address || "",
                city: userProfile.city || "",
                state: userProfile.state || "",
                postal_code: userProfile.postal_code || "",
                country: userProfile.country || "South Africa",
                date_of_birth: userProfile.date_of_birth || "",
                bio: userProfile.bio || "",
                avatar_url: userProfile.avatar_url || ""
              });
            } else {
              // Initialize with data from auth if available
              setFormData({
                full_name: data.user.user_metadata?.full_name || "",
                email: data.user.email || "",
                phone_number: "",
                address: "",
                city: "",
                state: "",
                postal_code: "",
                country: "South Africa",
                date_of_birth: "",
                bio: "",
                avatar_url: ""
              });
            }
          }
        } catch (error) {
          console.error("Error fetching profile:", error);
          toast({
            title: "Error",
            description: "Failed to load your profile. Please try again later.",
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error("Auth error:", error);
      } finally {
        setLoading(false);
      }
    }
    
    checkUserAndFetchProfile();
  }, [router, toast]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle avatar file selection
  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setAvatarFile(file);
      
      // Create preview URL
      const reader = new FileReader();
      reader.onload = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to update your profile.",
        variant: "destructive"
      });
      return;
    }

    setSaving(true);

    try {
      // First, upload avatar if selected
      let avatarUrl = formData.avatar_url;
      if (avatarFile) {
        const uploadFormData = new FormData();
        uploadFormData.append('file', avatarFile);

        const response = await fetch('/api/profile/upload-avatar', {
          method: 'POST',
          body: uploadFormData,
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Failed to upload avatar');
        }

        const result = await response.json();
        avatarUrl = result.avatar_url;
      }

      // Update profile data
      const profileData = {
        ...formData,
        avatar_url: avatarUrl
      };

      const response = await fetch('/api/profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profileData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update profile');
      }

      const updatedProfile = await response.json();
      setProfile(updatedProfile);

      // Clear avatar file and preview
      setAvatarFile(null);
      setAvatarPreview(null);

      toast({
        title: "Success",
        description: "Your profile has been updated successfully."
      });
    } catch (error: any) {
      console.error("Error updating profile:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to update your profile. Please try again later.",
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  // Handle sign out
  const handleSignOut = async () => {
    try {
      const supabase = createClient();
      await supabase.auth.signOut();
      router.push("/");
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <main className="flex-grow container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-96">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-background px-4 py-8">
        <div className="w-full max-w-md rounded-lg border border-border bg-card p-6 shadow-sm">
          <div className="space-y-4 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">Sign in to continue</h1>
            <p className="text-sm text-muted-foreground">
              You need to sign in or create an account to view your profile
            </p>
            <div className="flex flex-col space-y-2">
              <Link href="/sign-in?redirect=/account">
                <Button className="w-full">Sign in</Button>
              </Link>
              <Link href="/sign-up?redirect=/account">
                <Button variant="outline" className="w-full">Create account</Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      <main className="py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl font-bold text-foreground mb-8">My Account</h1>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-card border border-border rounded-lg p-6 sticky top-6">
                <div className="flex flex-col items-center mb-6">
                  <div className="w-20 h-20 rounded-full bg-primary/10 flex items-center justify-center mb-4 overflow-hidden relative">
                    {(profile?.avatar_url || avatarPreview) ? (
                      <Image 
                        src={avatarPreview || profile?.avatar_url || ''}
                        alt={formData.full_name}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <UserCircle className="h-10 w-10 text-primary" />
                    )}
                  </div>
                  <h2 className="text-lg font-semibold">{formData.full_name || user.user_metadata?.full_name || "User"}</h2>
                  <p className="text-sm text-muted-foreground">{formData.email || user.email}</p>
                </div>

                <nav className="space-y-1">
                  <Link href="/account" className="flex items-center gap-3 px-3 py-2 rounded-md bg-primary/10 text-primary font-medium">
                    <UserCircle className="h-5 w-5" />
                    <span>Profile</span>
                  </Link>
                  <Link href="/account/orders" className="flex items-center gap-3 px-3 py-2 rounded-md text-muted-foreground hover:bg-muted transition-colors">
                    <Package className="h-5 w-5" />
                    <span>Orders</span>
                  </Link>
                  <Link href="/account/payment" className="flex items-center gap-3 px-3 py-2 rounded-md text-muted-foreground hover:bg-muted transition-colors">
                    <CreditCard className="h-5 w-5" />
                    <span>Payment Methods</span>
                  </Link>
                  <Link href="/account/settings" className="flex items-center gap-3 px-3 py-2 rounded-md text-muted-foreground hover:bg-muted transition-colors">
                    <Settings className="h-5 w-5" />
                    <span>Settings</span>
                  </Link>
                </nav>

                <div className="mt-6 pt-6 border-t border-border">
                  <Button variant="outline" className="w-full" onClick={handleSignOut}>
                    Sign Out
                  </Button>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3">
              <div className="bg-card border border-border rounded-lg p-6">
                <h2 className="text-xl font-semibold mb-6">Profile Information</h2>
                
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Avatar Upload */}
                  <div>
                    <Label htmlFor="avatar" className="block mb-2">Profile Picture</Label>
                    <div className="flex items-center gap-4">
                      <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center overflow-hidden relative">
                        {(avatarPreview || profile?.avatar_url) ? (
                          <Image 
                            src={avatarPreview || profile?.avatar_url || ''}
                            alt={formData.full_name}
                            fill
                            className="object-cover"
                          />
                        ) : (
                          <UserCircle className="h-8 w-8 text-primary" />
                        )}
                      </div>
                      <div>
                        <Button type="button" variant="outline" size="sm" className="mb-1" onClick={() => document.getElementById('avatar')?.click()}>
                          <Upload className="h-4 w-4 mr-2" />
                          Upload Photo
                        </Button>
                        <input 
                          id="avatar" 
                          type="file" 
                          accept="image/*" 
                          className="hidden" 
                          onChange={handleAvatarChange}
                        />
                        <p className="text-xs text-muted-foreground">
                          JPG, PNG or GIF. Max size 2MB.
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Personal Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="full_name">Full Name</Label>
                      <Input
                        id="full_name"
                        name="full_name"
                        value={formData.full_name}
                        onChange={handleInputChange}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        disabled={!!user.email} // Disable if email is from auth
                      />
                      {user.email && (
                        <p className="text-xs text-muted-foreground mt-1">
                          Email address is managed through your account settings.
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="phone_number">Phone Number</Label>
                      <Input
                        id="phone_number"
                        name="phone_number"
                        value={formData.phone_number}
                        onChange={handleInputChange}
                        placeholder="Optional"
                      />
                    </div>
                  </div>

                  {/* Address Information */}
                  <div>
                    <h3 className="text-lg font-medium mb-4">Address Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2 md:col-span-2">
                        <Label htmlFor="address">Street Address</Label>
                        <Input
                          id="address"
                          name="address"
                          value={formData.address}
                          onChange={handleInputChange}
                          placeholder="Optional"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="city">City</Label>
                        <Input
                          id="city"
                          name="city"
                          value={formData.city}
                          onChange={handleInputChange}
                          placeholder="Optional"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="state">State/Province</Label>
                        <Input
                          id="state"
                          name="state"
                          value={formData.state}
                          onChange={handleInputChange}
                          placeholder="Optional"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="postal_code">Postal Code</Label>
                        <Input
                          id="postal_code"
                          name="postal_code"
                          value={formData.postal_code}
                          onChange={handleInputChange}
                          placeholder="Optional"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="country">Country</Label>
                        <Input
                          id="country"
                          name="country"
                          value={formData.country}
                          onChange={handleInputChange}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="date_of_birth">Date of Birth</Label>
                        <Input
                          id="date_of_birth"
                          name="date_of_birth"
                          type="date"
                          value={formData.date_of_birth}
                          onChange={handleInputChange}
                          placeholder="Optional"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Bio Section */}
                  <div>
                    <h3 className="text-lg font-medium mb-4">About You</h3>
                    <div className="space-y-2">
                      <Label htmlFor="bio">Bio</Label>
                      <textarea
                        id="bio"
                        name="bio"
                        value={formData.bio}
                        onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}
                        className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        placeholder="Tell us a bit about yourself (optional)"
                        rows={4}
                      />
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="flex justify-end">
                    <Button type="submit" disabled={saving}>
                      {saving ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        "Save Changes"
                      )}
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
