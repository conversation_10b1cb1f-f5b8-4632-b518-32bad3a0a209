-- =====================================================
-- CREATE ADMIN USER FOR THE CHRONICLE PROJECT
-- This script creates an admin user and updates existing content
-- =====================================================

-- STEP 1: UPDATE EXISTING USER TO ADMIN (if exists)
-- =====================================================
-- Replace '<EMAIL>' with the actual admin email
DO $$
DECLARE
    admin_email text := '<EMAIL>'; -- Change this to your email
    user_id uuid;
    articles_updated INTEGER;
    comments_updated INTEGER;
BEGIN
    -- Check if user exists and update to admin
    SELECT id INTO user_id FROM public.profiles WHERE email = admin_email;
    
    IF user_id IS NOT NULL THEN
        -- Update user role to admin
        UPDATE public.profiles 
        SET role = 'admin', updated_at = NOW() 
        WHERE id = user_id;
        
        -- Update placeholder content to be owned by this admin user
        UPDATE public.articles 
        SET author_id = user_id, updated_at = NOW() 
        WHERE author_id = '00000000-0000-0000-0000-************';
        
        GET DIAGNOSTICS articles_updated = ROW_COUNT;
        
        UPDATE public.comments 
        SET user_id = user_id, updated_at = NOW() 
        WHERE user_id = '00000000-0000-0000-0000-************';
        
        GET DIAGNOSTICS comments_updated = ROW_COUNT;
        
        -- Update likes and favorites
        UPDATE public.likes 
        SET user_id = user_id 
        WHERE user_id = '00000000-0000-0000-0000-************';
        
        UPDATE public.article_likes 
        SET user_id = user_id 
        WHERE user_id = '00000000-0000-0000-0000-************';
        
        UPDATE public.favorites 
        SET user_id = user_id 
        WHERE user_id = '00000000-0000-0000-0000-************';
        
        UPDATE public.analytics 
        SET user_id = user_id 
        WHERE user_id = '00000000-0000-0000-0000-************';
        
        RAISE NOTICE '✅ Updated existing user % to admin role', admin_email;
        RAISE NOTICE '📝 Updated % articles and % comments to be owned by admin', articles_updated, comments_updated;
    ELSE
        RAISE NOTICE '⚠️  User with email % not found. Please sign up first, then run this script.', admin_email;
        RAISE NOTICE '📋 Instructions:';
        RAISE NOTICE '1. Go to your application signup page';
        RAISE NOTICE '2. Create an account with email: %', admin_email;
        RAISE NOTICE '3. Come back and run this script again';
    END IF;
END $$;

-- STEP 2: CREATE ADMIN-SPECIFIC CONTENT
-- =====================================================
DO $$
DECLARE
    admin_user_id uuid;
BEGIN
    -- Get admin user
    SELECT id INTO admin_user_id FROM public.profiles WHERE role = 'admin' LIMIT 1;
    
    IF admin_user_id IS NOT NULL THEN
        -- Create admin notification
        INSERT INTO public.notifications (id, user_id, title, message, type, is_read, created_at) VALUES
        (
            'f50e8400-e29b-41d4-a716-************',
            admin_user_id,
            'Welcome to The Chronicle Admin Dashboard!',
            'Your admin account has been successfully set up. You now have access to all administrative features including content management, user management, and analytics.',
            'success',
            false,
            NOW()
        )
        ON CONFLICT (id) DO NOTHING;
        
        -- Create sample contact message for testing
        INSERT INTO public.contact_messages (id, name, email, subject, message, status, created_at) VALUES
        (
            'g50e8400-e29b-41d4-a716-************',
            'John Doe',
            '<EMAIL>',
            'Great content!',
            'I really enjoy reading The Chronicle. The articles are well-researched and informative. Keep up the excellent work!',
            'unread',
            NOW() - INTERVAL '2 hours'
        ),
        (
            'g50e8400-e29b-41d4-a716-************',
            'Jane Smith',
            '<EMAIL>',
            'Subscription inquiry',
            'I am interested in subscribing to your premium content. Could you please provide more information about the benefits and pricing?',
            'unread',
            NOW() - INTERVAL '1 day'
        ),
        (
            'g50e8400-e29b-41d4-a716-446655440003',
            'Mike Johnson',
            '<EMAIL>',
            'Article suggestion',
            'Would love to see more articles about sustainable technology and green innovations. This is a topic that many readers would find valuable.',
            'unread',
            NOW() - INTERVAL '3 days'
        )
        ON CONFLICT (id) DO NOTHING;
        
        -- Create sample newsletter subscribers
        INSERT INTO public.newsletter_subscribers (id, email, name, status, subscribed_at) VALUES
        ('h50e8400-e29b-41d4-a716-************', '<EMAIL>', 'Alice Brown', 'active', NOW() - INTERVAL '1 week'),
        ('h50e8400-e29b-41d4-a716-************', '<EMAIL>', 'Bob Wilson', 'active', NOW() - INTERVAL '3 days'),
        ('h50e8400-e29b-41d4-a716-446655440003', '<EMAIL>', 'Carol Davis', 'active', NOW() - INTERVAL '1 day')
        ON CONFLICT (email) DO NOTHING;
        
        RAISE NOTICE '✅ Created admin-specific content and test data';
    END IF;
END $$;

-- STEP 3: VERIFY SETUP
-- =====================================================
DO $$
DECLARE
    admin_count INTEGER;
    articles_count INTEGER;
    products_count INTEGER;
    comments_count INTEGER;
    admin_user_email text;
BEGIN
    -- Count admins
    SELECT COUNT(*) INTO admin_count FROM public.profiles WHERE role = 'admin';
    SELECT email INTO admin_user_email FROM public.profiles WHERE role = 'admin' LIMIT 1;
    
    -- Count content
    SELECT COUNT(*) INTO articles_count FROM public.articles WHERE is_published = true;
    SELECT COUNT(*) INTO products_count FROM public.products WHERE status = 'active';
    SELECT COUNT(*) INTO comments_count FROM public.comments WHERE is_approved = true;
    
    RAISE NOTICE '🎉 ADMIN USER SETUP VERIFICATION';
    RAISE NOTICE '==========================================';
    RAISE NOTICE '👤 ADMIN USERS: %', admin_count;
    IF admin_user_email IS NOT NULL THEN
        RAISE NOTICE '📧 Admin Email: %', admin_user_email;
    END IF;
    RAISE NOTICE '📰 Published Articles: %', articles_count;
    RAISE NOTICE '🛍️  Active Products: %', products_count;
    RAISE NOTICE '💬 Approved Comments: %', comments_count;
    RAISE NOTICE '==========================================';
    
    IF admin_count > 0 THEN
        RAISE NOTICE '✅ SETUP SUCCESSFUL!';
        RAISE NOTICE '🚀 You can now:';
        RAISE NOTICE '1. Log in to the admin dashboard';
        RAISE NOTICE '2. Manage articles and products';
        RAISE NOTICE '3. View analytics and user data';
        RAISE NOTICE '4. Handle contact messages';
        RAISE NOTICE '5. Manage user subscriptions';
    ELSE
        RAISE NOTICE '⚠️  NO ADMIN USER FOUND!';
        RAISE NOTICE '📋 Please:';
        RAISE NOTICE '1. Sign up for an account first';
        RAISE NOTICE '2. Update the admin_email variable in this script';
        RAISE NOTICE '3. Run this script again';
    END IF;
    RAISE NOTICE '==========================================';
END $$;
