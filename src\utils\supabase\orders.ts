import { createClient } from './server';
import { CartItem } from '@/context/cart-context';

// Simple function to generate a unique ID
function generateId(prefix = '') {
  return `${prefix}${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
}

// Define the order type
export type Order = {
  id: string;
  user_id: string;
  items: OrderItem[];
  shipping_details: ShippingDetails;
  status: string;
  payment_status: string;
  total_amount: number;
  yoco_payment_id?: string;
  yoco_transaction_id?: string;
  created_at?: string;
  updated_at?: string;
};

// Define the order item type
export type OrderItem = {
  product_id: string;
  name: string;
  price: number;
  quantity: number;
  image?: string;
};

// Define the shipping details type
export type ShippingDetails = {
  name: string;
  email: string;
  address: string;
  city: string;
  postal_code: string;
  country: string;
  phone?: string;
  alternative_phone?: string;
};

// Create a new order
export async function createOrder(userId: string, items: CartItem[], shippingDetails: ShippingDetails, totalAmount: number) {
  const supabase = await createClient();
  
  // Generate a unique order ID with prefix
  const orderId = generateId('TEC-');
  
  // Convert cart items to order items
  const orderItems: OrderItem[] = items.map(item => ({
    product_id: item.id.toString(),
    name: item.name,
    price: item.price,
    quantity: item.quantity,
    image: item.image
  }));
  
  // Create the order object
  const order = {
    id: orderId,
    user_id: userId,
    items: orderItems,
    shipping_details: shippingDetails,
    status: 'pending',
    payment_status: 'pending',
    total_amount: totalAmount,
    yoco_payment_id: orderId // Initially use the order ID as the payment ID
  };
  
  // Insert the order into the database
  const { data, error } = await supabase
    .from('orders')
    .insert([order])
    .select()
    .single();
  
  if (error) {
    console.error('Error creating order:', error);
    throw error;
  }
  
  return data as Order;
}

// Get all orders for a user
export async function getUserOrders(userId: string) {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .from('orders')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });
  
  if (error) {
    console.error('Error fetching user orders:', error);
    throw error;
  }
  
  return data as Order[];
}

// Get a single order by ID
export async function getOrder(orderId: string) {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .from('orders')
    .select('*')
    .eq('id', orderId)
    .single();
  
  if (error) {
    console.error(`Error fetching order ${orderId}:`, error);
    throw error;
  }
  
  return data as Order;
}

// Update an order's status
export async function updateOrderStatus(orderId: string, status: string, paymentStatus?: string) {
  const supabase = await createClient();
  
  const updates: { status: string; payment_status?: string } = { status };
  
  if (paymentStatus) {
    updates.payment_status = paymentStatus;
  }
  
  const { data, error } = await supabase
    .from('orders')
    .update(updates)
    .eq('id', orderId)
    .select()
    .single();
  
  if (error) {
    console.error(`Error updating order ${orderId}:`, error);
    throw error;
  }
  
  return data as Order;
}

// Update an order with Yoco payment information
export async function updateOrderPayment(orderId: string, yocoPaymentId: string, yocoTransactionId?: string) {
  const supabase = await createClient();
  
  const updates: { yoco_payment_id: string; yoco_transaction_id?: string } = { 
    yoco_payment_id: yocoPaymentId
  };
  
  if (yocoTransactionId) {
    updates.yoco_transaction_id = yocoTransactionId;
  }
  
  const { data, error } = await supabase
    .from('orders')
    .update(updates)
    .eq('id', orderId)
    .select()
    .single();
  
  if (error) {
    console.error(`Error updating order payment ${orderId}:`, error);
    throw error;
  }
  
  return data as Order;
}
