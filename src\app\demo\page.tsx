import Link from 'next/link';
import { Button } from '@/components/ui/button';

export default function DemoPage() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-8">
      <div className="text-center max-w-2xl">
        <h1 className="text-4xl font-bold mb-6">Tennis Whisperer Demo Hub</h1>
        <p className="text-muted-foreground mb-8">
          Explore our interactive demos including preloader functionality, hero section variants, and physics simulations.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
          <Link href="/">
            <Button className="w-full">Go to Home</Button>
          </Link>
          <Link href="/shop">
            <Button variant="outline" className="w-full">Go to Shop</Button>
          </Link>
          <Link href="/search">
            <Button variant="outline" className="w-full">Go to Search</Button>
          </Link>
          <Link href="/account">
            <Button variant="outline" className="w-full">Go to Account</Button>
          </Link>
        </div>

        {/* Hero Demo Navigation */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Hero Section Demos</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <Link href="/demo/hero-3d">
              <Button variant="outline" className="w-full">3D Interactive</Button>
            </Link>
            <Link href="/demo/hero-ballpit">
              <Button variant="outline" className="w-full">Physics Ballpit</Button>
            </Link>
            <Link href="/demo/hero-ballpit-cards">
              <Button variant="outline" className="w-full">Ballpit + Cards</Button>
            </Link>
            <Link href="/demo/hero-video">
              <Button variant="outline" className="w-full">Video Background</Button>
            </Link>
            <Link href="/demo/hero-gamified">
              <Button variant="outline" className="w-full">Gamified</Button>
            </Link>
          </div>
        </div>
        
        <div className="bg-muted/30 rounded-2xl p-6 neo-shadow-light">
          <h2 className="text-xl font-semibold mb-4">Preloader Features</h2>
          <ul className="text-left space-y-2 text-sm text-muted-foreground">
            <li>✅ Full-screen overlay with Tennis Whisperer logo</li>
            <li>✅ Neomorphism design with gradient backgrounds</li>
            <li>✅ Smooth animations and progress indicator</li>
            <li>✅ Responsive design for mobile and desktop</li>
            <li>✅ Accessibility support with reduced motion</li>
            <li>✅ Professional loading states and transitions</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
