import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

/**
 * API endpoint for retrieving order details by Yoco payment ID
 * This is used in the order confirmation page to display order details
 */
export async function GET(
  request: Request,
  { params }: { params: { paymentId: string } }
) {
  try {
    const paymentId = params.paymentId;

    if (!paymentId) {
      return NextResponse.json(
        { error: 'Missing payment ID' },
        { status: 400 }
      );
    }

    // Connect to Supabase
    const supabase = await createClient();

    // Query the orders table for the order with this Yoco payment ID
    const { data: order, error } = await supabase
      .from('orders')
      .select('*')
      .eq('yoco_payment_id', paymentId)
      .single();

    if (error) {
      console.error('Error fetching order by Yoco payment ID:', error);
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    if (!order) {
      // If no order found with this payment ID, try consultations
      const { data: consultation, error: consultationError } = await supabase
        .from('consultations')
        .select('*')
        .eq('yoco_payment_id', paymentId)
        .single();

      if (consultationError || !consultation) {
        return NextResponse.json(
          { error: 'No order or consultation found with this payment ID' },
          { status: 404 }
        );
      }

      // Return the consultation data in a format compatible with order display
      return NextResponse.json({
        success: true,
        order: {
          id: consultation.id,
          user_id: consultation.user_id,
          items: [
            {
              id: 'consultation',
              name: 'Tennis Consultation Session',
              price: consultation.payment_amount,
              quantity: 1,
              image: '/images/consultation.jpg'
            }
          ],
          shipping_details: {
            name: `${consultation.first_name} ${consultation.last_name}`,
            email: consultation.email,
            phone: consultation.phone_number,
            address: '',
            city: '',
            postal_code: '',
            province: '',
            country: 'South Africa'
          },
          status: 'completed',
          payment_status: consultation.payment_status,
          total_amount: consultation.payment_amount,
          created_at: consultation.created_at,
          yoco_payment_id: paymentId
        }
      });
    }

    // Return the order data
    return NextResponse.json({
      success: true,
      order
    });
  } catch (error: any) {
    console.error('Error retrieving order by Yoco payment ID:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
