-- Fix Admin Permissions for Product Management
-- Run this in your Supabase SQL Editor to fix RLS policies for admin users

-- 1. Update RLS policies for admin access to products
-- Drop existing policies and create more specific ones
DROP POLICY IF EXISTS "Authenticated users can manage products" ON products;
DROP POLICY IF EXISTS "Enable read access for all users" ON products;

-- Create specific policies for different operations
-- Allow everyone to read products
CREATE POLICY "Enable read access for all users" ON products
  FOR SELECT
  USING (true);

-- Allow admin users to insert products
CREATE POLICY "Admin users can insert products" ON products
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE users.id = auth.uid() 
      AND users.role = 'admin'
    )
  );

-- Allow admin users to update products
CREATE POLICY "Admin users can update products" ON products
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE users.id = auth.uid() 
      AND users.role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE users.id = auth.uid() 
      AND users.role = 'admin'
    )
  );

-- Allow admin users to delete products
CREATE POLICY "Admin users can delete products" ON products
  FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE users.id = auth.uid() 
      AND users.role = 'admin'
    )
  );

-- 2. Verify the policies were created correctly
SELECT 
  'Products RLS Policies' as table_name,
  policyname, 
  cmd, 
  roles,
  CASE 
    WHEN qual IS NOT NULL THEN 'Has USING clause'
    ELSE 'No USING clause'
  END as using_clause,
  CASE 
    WHEN with_check IS NOT NULL THEN 'Has WITH CHECK clause'
    ELSE 'No WITH CHECK clause'
  END as with_check_clause
FROM pg_policies 
WHERE tablename = 'products' 
ORDER BY cmd, policyname;

-- 3. Test admin access (optional - for verification)
-- This will show if the current user has admin role
SELECT 
  'Current User Check' as test_type,
  auth.uid() as user_id,
  users.email,
  users.role,
  CASE 
    WHEN users.role = 'admin' THEN 'Admin access granted'
    ELSE 'Not an admin user'
  END as access_status
FROM public.users 
WHERE users.id = auth.uid();

-- 4. Grant additional permissions if needed
GRANT SELECT, INSERT, UPDATE, DELETE ON products TO authenticated;
GRANT SELECT ON products TO anon;

-- 5. Fix categories table RLS policies
-- Drop existing policies that use JWT role
DROP POLICY IF EXISTS "Only admins can delete categories" ON categories;
DROP POLICY IF EXISTS "Only admins can insert categories" ON categories;
DROP POLICY IF EXISTS "Anyone can view categories" ON categories;
DROP POLICY IF EXISTS "Only admins can update categories" ON categories;

-- Create new consistent policies for categories
CREATE POLICY "Enable read access for all users" ON categories
  FOR SELECT
  USING (true);

CREATE POLICY "Admin users can insert categories" ON categories
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE users.id = auth.uid()
      AND users.role = 'admin'
    )
  );

CREATE POLICY "Admin users can update categories" ON categories
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE users.id = auth.uid()
      AND users.role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE users.id = auth.uid()
      AND users.role = 'admin'
    )
  );

CREATE POLICY "Admin users can delete categories" ON categories
  FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE users.id = auth.uid()
      AND users.role = 'admin'
    )
  );

-- 6. Add admin policies for orders management
CREATE POLICY "Admin users can view all orders" ON orders
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE users.id = auth.uid()
      AND users.role = 'admin'
    )
  );

CREATE POLICY "Admin users can update any order" ON orders
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE users.id = auth.uid()
      AND users.role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE users.id = auth.uid()
      AND users.role = 'admin'
    )
  );

CREATE POLICY "Admin users can delete any order" ON orders
  FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE users.id = auth.uid()
      AND users.role = 'admin'
    )
  );

-- Success message
SELECT 'Admin permissions for products, categories, and orders have been updated successfully!' as status;
