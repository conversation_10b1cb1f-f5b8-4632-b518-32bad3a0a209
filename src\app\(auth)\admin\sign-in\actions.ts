"use server";

import { encodedRedirect } from "@/utils/utils";
import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";

// Admin access codes - in production, these should be environment variables
const ADMIN_ACCESS_CODES = {
  MAIN_ADMIN: process.env.MAIN_ADMIN_ACCESS_CODE || "TENNIS_MAIN_ADMIN_2024",
  SENIOR_ADMIN: process.env.SENIOR_ADMIN_ACCESS_CODE || "TENNIS_SENIOR_ADMIN_2024",
  JUNIOR_ADMIN: process.env.JUNIOR_ADMIN_ACCESS_CODE || "TENNIS_JUNIOR_ADMIN_2024"
};

// Legacy support for existing admin code
const ADMIN_ACCESS_CODE = process.env.ADMIN_ACCESS_CODE || "TENNIS_ADMIN_2024";

export const adminSignInAction = async (formData: FormData, redirectTo: string = '/admin') => {
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;
  const adminCode = formData.get("admin_code") as string;
  const supabase = await createClient();

  if (!email || !password) {
    return encodedRedirect("error", "/admin/sign-in", "Email and password are required");
  }

  if (!adminCode) {
    return encodedRedirect("error", "/admin/sign-in", "Admin access code is required");
  }

  // Verify admin access code
  const validCodes = [
    ADMIN_ACCESS_CODES.MAIN_ADMIN,
    ADMIN_ACCESS_CODES.SENIOR_ADMIN,
    ADMIN_ACCESS_CODES.JUNIOR_ADMIN,
    ADMIN_ACCESS_CODE // legacy support
  ];

  if (!validCodes.includes(adminCode)) {
    return encodedRedirect("error", "/admin/sign-in", "Invalid admin access code");
  }

  const { data: authData, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    console.error("Admin sign in error:", error.code + " " + error.message);
    return encodedRedirect("error", "/admin/sign-in", "Invalid credentials");
  }

  if (authData.user) {
    console.log("Admin authenticated successfully:", authData.user.email);

    // Wait a moment for trigger to complete if user was just created
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Verify user has admin role - retry logic for trigger completion
    let userData: { role: any; full_name: any; name: any; admin_role: any; } | null = null;
    let userError: any = null;
    let retries = 3;

    while (retries > 0 && !userData) {
      const result = await supabase
        .from('users')
        .select('role, full_name, name, admin_role')
        .eq('id', authData.user.id)
        .single();

      userData = result.data;
      userError = result.error;

      if (userError && userError.code === 'PGRST116') {
        console.log(`User profile not found, retries left: ${retries - 1}`);
        retries--;
        if (retries > 0) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } else {
        break;
      }
    }

    if (userError) {
      console.error("Error fetching user data after retries:", userError);

      if (userError.code === 'PGRST116') {
        // User profile still not found after retries
        return encodedRedirect("error", "/admin/sign-in", "User profile not found. Please contact administrator.");
      }

      if (userError.code === '42P17') {
        // Infinite recursion detected - database needs fixing
        return encodedRedirect("error", "/admin/sign-in", "Database configuration error. Please contact administrator.");
      }

      return encodedRedirect("error", "/admin/sign-in", "Error accessing user profile");
    }

    // Check if user has admin role
    if (!userData || userData.role !== 'admin') {
      // Sign out the user since they don't have admin access
      await supabase.auth.signOut();
      return encodedRedirect("error", "/admin/sign-in", "Admin access required. Please contact system administrator.");
    }

    // Log admin access (optional - for security auditing)
    console.log(`Admin login: ${userData.full_name} (${email}) at ${new Date().toISOString()}`);

    // Successful admin login
    return redirect(redirectTo);
  }

  return encodedRedirect("error", "/admin/sign-in", "Authentication failed");
};
