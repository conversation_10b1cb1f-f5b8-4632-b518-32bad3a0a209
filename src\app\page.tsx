import Footer from "@/components/footer";
import HeroBallpitCards from "@/components/demo/hero-ballpit-cards";
import Navbar from "@/components/navbar";
import { ArrowRight, Award, BadgeCheck, Truck } from 'lucide-react';
import { createClient } from "../utils/supabase/server";
import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ProductCard } from "@/components/product-card";
import MentorshipPricing from "@/components/MentorshipPricing";
import CTASection from "@/components/cta-section";

// Mock featured products data
const featuredProducts = [
  {
    id: 1,
    name: "Pro Tour Racket",
    price: 3599.99,
    image: "/images/tennis-racket.png",
    category: "Rackets",
    rating: 4.9,
    reviews: 128,
    status: "in-stock",
    stock: 15
  },
  {
    id: 2,
    name: "Competition Tennis Balls",
    price: 269.99,
    image: "https://images.unsplash.com/photo-1592709823125-a191f07a2a5e?w=800&q=80",
    category: "Balls",
    rating: 4.7,
    reviews: 94,
    status: "in-stock",
    stock: 42
  },
  {
    id: 5,
    name: "Elite Tennis Shoes",
    price: 2349.99,
    image: "https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=800&q=80",
    category: "Footwear",
    rating: 4.9,
    reviews: 87,
    status: "in-stock",
    stock: 8
  },
  {
    id: 20,
    name: "Tennis Dress",
    price: 1099.99,
    image: "/images/tennis-dress.jpeg",
    category: "Apparel",
    rating: 4.8,
    reviews: 71,
    status: "in-stock",
    stock: 23
  }
];

// Mock categories
const categories = [
  {
    name: "Rackets",
    image: "https://images.unsplash.com/photo-1734459553318-1cde555f3c17?q=80&w=1527&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    count: 24
  },
  {
    name: "Apparel",
    image: "https://images.unsplash.com/photo-1618354691792-d1d42acfd860?w=800&q=80",
    count: 36
  },
  {
    name: "Accessories",
    image: "/images/accessories.png",
    count: 18
  }
];

export default async function Home() {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <HeroBallpitCards />

      {/* Categories Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center mb-12">
            <div>
              <h2 className="text-3xl font-bold text-foreground mb-2">Shop by Category</h2>
              <p className="text-muted-foreground">Find the perfect equipment for your game</p>
            </div>
            <Button asChild variant="outline" className="mt-4 md:mt-0">
              <Link href="/shop">
                View All Categories
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {categories.map((category, index) => (
              <Link
                key={index}
                href={`/shop?category=${category.name.toLowerCase()}`}
                className="group relative overflow-hidden rounded-2xl"
              >
                <div className="aspect-[4/3] relative overflow-hidden rounded-2xl">
                  <Image
                    src={category.image}
                    alt={category.name}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                  <div className="absolute bottom-0 left-0 p-6 text-white">
                    <h3 className="text-xl font-semibold mb-1">{category.name}</h3>
                    <p className="text-white/80 text-sm">{category.count} products</p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="py-20 bg-muted/30" id="featured">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center mb-12">
            <div>
              <h2 className="text-3xl font-bold text-foreground mb-2">Featured Products</h2>
              <p className="text-muted-foreground">Our most popular items chosen by tennis enthusiasts</p>
            </div>
            <Button asChild variant="outline" className="mt-4 md:mt-0">
              <Link href="/shop">
                View All Products
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {featuredProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-foreground mb-4">Why Choose Tennis Gear</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">We're committed to providing the highest quality tennis equipment with exceptional service</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                icon: <Award className="w-10 h-10" />,
                title: "Premium Quality",
                description: "We source only the highest quality products from trusted manufacturers"
              },
              {
                icon: <BadgeCheck className="w-10 h-10" />,
                title: "Expert Selection",
                description: "Our team of tennis experts carefully selects each product in our catalog"
              },
              {
                icon: <Truck className="w-10 h-10" />,
                title: "Fast Delivery",
                description: "Enjoy quick shipping and hassle-free returns on all orders"
              }
            ].map((feature, index) => (
              <div key={index} className="p-8 bg-background rounded-2xl border border-border">
                <div className="text-primary mb-5 bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-foreground mb-3">{feature.title}</h3>
                <p className="text-muted-foreground">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Mentorship Pricing Section */}
      <div id="mentorship">
        <MentorshipPricing />
      </div>

      {/* New CTA Section */}
      <CTASection />

      {/* <Footer /> */}
    </div>
  );
}
