'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { createClient } from '@/utils/supabase/client';
import { 
  Consultation, 
  AdminConsultationView, 
  ConsultationFilters, 
  ConsultationStats,
  ConsultationStatusUpdate,
  ConsultationBulkAction,
  formatConsultationDateTime,
  getDaysUntilAppointment,
  isConsultationOverdue
} from '@/types/consultations';
import { useToast } from '@/hooks/use-toast';

/**
 * Hook for fetching consultations with filtering and pagination
 */
export function useConsultations(filters: ConsultationFilters & { page: number; limit: number }) {
  return useQuery({
    queryKey: ['admin', 'consultations', filters],
    queryFn: async () => {
      const response = await fetch('/api/admin/consultations?' + new URLSearchParams({
        page: filters.page.toString(),
        limit: filters.limit.toString(),
        ...(filters.status && { status: filters.status }),
        ...(filters.payment_status && { payment_status: filters.payment_status }),
        ...(filters.date_from && { date_from: filters.date_from }),
        ...(filters.date_to && { date_to: filters.date_to }),
        ...(filters.search && { search: filters.search }),
      }));

      if (!response.ok) {
        throw new Error('Failed to fetch consultations');
      }

      const data = await response.json();
      
      // Transform consultations to admin view format
      const consultations: AdminConsultationView[] = data.consultations.map((consultation: Consultation) => ({
        ...consultation,
        customer_name: `${consultation.first_name} ${consultation.last_name}`,
        formatted_date: formatConsultationDateTime(consultation.scheduled_date, consultation.scheduled_time),
        formatted_time: consultation.scheduled_time,
        formatted_amount: consultation.payment_amount 
          ? `R${consultation.payment_amount.toFixed(2)}` 
          : 'N/A',
        days_until_appointment: getDaysUntilAppointment(consultation.scheduled_date),
        is_overdue: isConsultationOverdue(consultation.scheduled_date, consultation.status),
      }));

      return {
        consultations,
        total: data.total,
        page: data.page,
        limit: data.limit,
        totalPages: data.totalPages,
      };
    },
    staleTime: 30 * 1000, // 30 seconds
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook for fetching consultation statistics
 */
export function useConsultationStats() {
  return useQuery({
    queryKey: ['admin', 'consultations', 'stats'],
    queryFn: async (): Promise<ConsultationStats> => {
      const response = await fetch('/api/admin/consultations/stats');
      
      if (!response.ok) {
        throw new Error('Failed to fetch consultation statistics');
      }

      return response.json();
    },
    staleTime: 60 * 1000, // 1 minute
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook for updating consultation status
 */
export function useUpdateConsultation() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, update }: { id: string; update: ConsultationStatusUpdate }) => {
      const response = await fetch(`/api/admin/consultations/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(update),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to update consultation');
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch consultations
      queryClient.invalidateQueries({ queryKey: ['admin', 'consultations'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'consultations', 'stats'] });
      
      toast({
        title: 'Success',
        description: `Consultation status updated to ${variables.update.status}`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook for bulk consultation actions
 */
export function useBulkConsultationAction() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (action: ConsultationBulkAction) => {
      const response = await fetch('/api/admin/consultations/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(action),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to perform bulk action');
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch consultations
      queryClient.invalidateQueries({ queryKey: ['admin', 'consultations'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'consultations', 'stats'] });
      
      toast({
        title: 'Success',
        description: `Bulk action completed: ${variables.action} applied to ${variables.consultation_ids.length} consultations`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook for deleting a consultation (admin only)
 */
export function useDeleteConsultation() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/admin/consultations/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete consultation');
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate and refetch consultations
      queryClient.invalidateQueries({ queryKey: ['admin', 'consultations'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'consultations', 'stats'] });
      
      toast({
        title: 'Success',
        description: 'Consultation deleted successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
}

/**
 * Utility hook for consultation status colors
 */
export function useConsultationStatusColor() {
  return (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'rescheduled':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };
}

/**
 * Utility hook for payment status colors
 */
export function usePaymentStatusColor() {
  return (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'refunded':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };
}
