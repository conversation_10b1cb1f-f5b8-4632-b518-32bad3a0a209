import { FormMessage, Message } from "@/components/form-message";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { adminSignUpAction } from "./actions";
import { UrlProvider } from "@/components/url-provider";
import { FaReg<PERSON><PERSON>, FaRegEnvelope, FaLock, FaShieldAlt } from "react-icons/fa";
import Image from "next/image";

export default async function AdminSignup(props: {
  searchParams: Promise<Message>;
}) {
  const searchParams = await props.searchParams;
  if ("message" in searchParams) {
    return (
      <div className="flex h-screen w-full flex-1 items-center justify-center p-4 sm:max-w-md">
        <FormMessage message={searchParams} />
      </div>
    );
  }

  return (
    <>
      {/* Desktop Navbar */}
      <div className="hidden md:block">
        {/* <Navbar /> */}
      </div>

      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background via-muted/20 to-background p-4">
        {/* Background decoration */}
        <div className="absolute inset-0 overflow-hidden -z-10">
          <div className="absolute h-[500px] w-[500px] rounded-full gradient-blue top-[-250px] left-[-250px] blur-[120px] opacity-15 animate-pulse"></div>
          <div className="absolute h-[400px] w-[400px] rounded-full gradient-purple bottom-[-200px] right-[-200px] blur-[100px] opacity-20 animate-pulse"></div>
        </div>

        <div className="w-full max-w-md">
          {/* Logo Avatar */}
          <div className="text-center mb-8">
            <div className="relative mx-auto w-28 h-28 mb-6">
              <div className="w-full h-full rounded-full p-1 neo-shadow">
                <div className="w-full h-full rounded-full bg-blur flex items-center justify-center">
                  <Link
                    href="/"
                    className="text-center"
                  >
                  <Image
                    src="/logo.svg"
                    alt="Logo"
                    width={70}
                    height={70}
                    className=" object-contain"
                  />
                  </Link>
                </div>
              </div>
            </div>
            <h1 className="text-2xl font-bold tracking-tight text-foreground mb-2">ADMIN REGISTRATION</h1>
            <p className="text-muted-foreground text-sm">
              Create your admin account for Tennis Whisperer
            </p>
          </div>

          <div className="glass-effect-dark rounded-3xl p-8 neo-shadow">
            <UrlProvider>
              <form className="flex flex-col space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="relative">
                      <Input
                        id="full_name"
                        name="full_name"
                        type="text"
                        placeholder="full name"
                        required
                        className="w-full h-14 pl-12 rounded-2xl bg-muted/30 border-0 neo-shadow-inset placeholder:text-muted-foreground/70"
                      />
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                        <span className="text-sm"><FaRegUser /></span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="relative">
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        placeholder="admin email"
                        required
                        className="w-full h-14 pl-12 rounded-2xl bg-muted/30 border-0 neo-shadow-inset placeholder:text-muted-foreground/70"
                      />
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                          <span className="text-sm"><FaRegEnvelope /></span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="relative">
                      <Input
                        id="password"
                        type="password"
                        name="password"
                        placeholder="password"
                        minLength={6}
                        required
                        className="w-full h-14 pl-12 rounded-2xl bg-muted/30 border-0 neo-shadow-inset placeholder:text-muted-foreground/70"
                      />
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                          <span className="text-sm"><FaLock /></span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="relative">
                      <Input
                        id="admin_code"
                        name="admin_code"
                        type="password"
                        placeholder="admin access code"
                        required
                        className="w-full h-14 pl-12 rounded-2xl bg-muted/30 border-0 neo-shadow-inset placeholder:text-muted-foreground/70"
                      />
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                          <span className="text-sm"><FaShieldAlt /></span>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Contact system administrator for access code
                    </p>
                  </div>
                </div>

                <SubmitButton
                  formAction={adminSignUpAction}
                  pendingText="Creating admin account..."
                  className="w-full h-14 rounded-2xl gradient-blue text-white font-semibold text-lg neo-shadow hover:neo-shadow-light transition-neo border-0"
                >
                  Create Admin Account
                </SubmitButton>

                <FormMessage message={searchParams} />

                <div className="text-center space-y-2">
                  <p className="text-sm text-muted-foreground">
                    Already have an admin account?{" "}
                    <Link
                      className="text-primary font-medium hover:underline"
                      href="/admin/sign-in"
                    >
                      Sign in here
                    </Link>
                  </p>
                  <p className="text-xs text-muted-foreground">
                    <Link
                      className="text-primary hover:underline"
                      href="/sign-up"
                    >
                      Create regular account instead
                    </Link>
                  </p>
                </div>
              </form>
            </UrlProvider>
          </div>
        </div>
      </div>

      {/* <Footer /> */}
    </>
  );
}
