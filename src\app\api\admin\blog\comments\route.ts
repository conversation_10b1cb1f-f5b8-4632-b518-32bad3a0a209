import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Check authentication
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin role
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (userError || userData?.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status'); // 'approved', 'pending', or null for all
    const articleId = searchParams.get('article_id');
    const search = searchParams.get('search');

    // Build query
    let query = supabase
      .from('blog_comments')
      .select(`
        *,
        blog_articles (title, slug),
        users (full_name, email)
      `, { count: 'exact' });

    // Apply filters
    if (status === 'approved') {
      query = query.eq('is_approved', true);
    } else if (status === 'pending') {
      query = query.eq('is_approved', false);
    }

    if (articleId) {
      query = query.eq('article_id', articleId);
    }

    if (search) {
      query = query.or(`content.ilike.%${search}%`);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    const { data: comments, error, count } = await query
      .order('created_at', { ascending: false })
      .range(from, to);

    if (error) {
      console.error('Error fetching comments:', error);
      return NextResponse.json({ error: 'Failed to fetch comments' }, { status: 500 });
    }

    return NextResponse.json({
      comments: comments || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error) {
    console.error('Error in blog comments API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Check authentication
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      content,
      article_id,
      parent_id = null,
      is_approved = true // Admin comments are auto-approved
    } = body;

    // Validate required fields
    if (!content || !article_id) {
      return NextResponse.json({ 
        error: 'Missing required fields: content, article_id' 
      }, { status: 400 });
    }

    // Verify article exists
    const { data: article, error: articleError } = await supabase
      .from('blog_articles')
      .select('id')
      .eq('id', article_id)
      .single();

    if (articleError || !article) {
      return NextResponse.json({ error: 'Article not found' }, { status: 404 });
    }

    // If parent_id is provided, verify parent comment exists
    if (parent_id) {
      const { data: parentComment, error: parentError } = await supabase
        .from('blog_comments')
        .select('id')
        .eq('id', parent_id)
        .single();

      if (parentError || !parentComment) {
        return NextResponse.json({ error: 'Parent comment not found' }, { status: 404 });
      }
    }

    // Create comment
    const commentData = {
      content: content.trim(),
      article_id,
      user_id: session.user.id,
      parent_id,
      is_approved,
    };

    const { data: comment, error } = await supabase
      .from('blog_comments')
      .insert(commentData)
      .select(`
        *,
        blog_articles (title, slug),
        users (full_name, email)
      `)
      .single();

    if (error) {
      console.error('Error creating comment:', error);
      return NextResponse.json({ error: 'Failed to create comment' }, { status: 500 });
    }

    return NextResponse.json({ comment }, { status: 201 });

  } catch (error) {
    console.error('Error in blog comments POST API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
