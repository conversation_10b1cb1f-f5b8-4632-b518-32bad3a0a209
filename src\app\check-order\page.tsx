"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";

export default function CheckOrderPage() {
  const [orderId, setOrderId] = useState("");
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!orderId) {
      toast({
        title: "Order ID Required",
        description: "Please enter an order ID",
        variant: "destructive"
      });
      return;
    }
    
    setLoading(true);
    
    try {
      // Try to fetch the order
      const response = await fetch(`/api/orders/${orderId}`);
      
      if (response.ok) {
        // Order found, redirect to order confirmation page
        router.push(`/order-confirmation?order_id=${orderId}`);
      } else {
        // Order not found
        toast({
          title: "Order Not Found",
          description: "We couldn't find an order with that ID",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Error checking order:", error);
      toast({
        title: "Error",
        description: "An error occurred while checking your order",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow container mx-auto px-4 py-12">
        <div className="max-w-md mx-auto">
          <h1 className="text-2xl font-bold mb-6">Check Order Status</h1>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="orderId">Order ID</Label>
              <Input
                id="orderId"
                placeholder="Enter your order ID (e.g., TEC-12345)"
                value={orderId}
                onChange={(e) => setOrderId(e.target.value)}
              />
            </div>
            
            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? "Checking..." : "Check Order Status"}
            </Button>
          </form>
        </div>
      </main>
      
      <Footer />
    </div>
  );
} 