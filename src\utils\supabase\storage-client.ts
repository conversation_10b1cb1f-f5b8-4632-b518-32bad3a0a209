import { createClient } from "./client";
import { v4 as uuidv4 } from "uuid";

/**
 * Client-side version of storage utility functions
 * These functions use the browser client instead of the server client
 */

/**
 * Uploads a file to Supabase storage and returns the public URL
 * Uses API route for product images to ensure proper admin authentication
 * @param file - The file to upload
 * @param bucket - The storage bucket name
 * @param folder - The folder path within the bucket
 * @returns The public URL of the uploaded file
 */
export async function uploadFileClient(file: File, bucket = 'product-images', folder = 'images') {
  if (bucket === 'product-images') {
    // Use the multiple files upload function for consistency
    const urls = await uploadMultipleFilesClient([file], bucket, folder);
    return urls[0];
  }

  // Direct upload for other buckets
  const supabase = createClient();

  // Create a unique file name to prevent collisions
  const fileExt = file.name.split('.').pop();
  const fileName = `${uuidv4()}.${fileExt}`;
  const filePath = `${folder}/${fileName}`;

  // Upload the file
  const { data, error } = await supabase.storage
    .from(bucket)
    .upload(filePath, file, {
      cacheControl: '3600',
      upsert: false
    });

  if (error) {
    console.error('Error uploading file:', error);
    throw error;
  }

  // Get the public URL
  const { data: { publicUrl } } = supabase.storage
    .from(bucket)
    .getPublicUrl(filePath);

  return publicUrl;
}

/**
 * Uploads multiple files to Supabase storage and returns their public URLs
 * Uses API route for admin authentication and service role client
 * @param files - The files to upload
 * @param bucket - The storage bucket name (currently only supports 'product-images')
 * @param folder - The folder path within the bucket (ignored, uses API route)
 * @returns Array of public URLs for the uploaded files
 */
export async function uploadMultipleFilesClient(files: File[], bucket = 'product-images', folder = 'images') {
  if (bucket !== 'product-images') {
    // Fall back to direct upload for other buckets
    const uploadPromises = files.map(file => uploadFileClient(file, bucket, folder));
    return Promise.all(uploadPromises);
  }

  // Use API route for product images to ensure proper admin authentication
  const supabase = createClient();
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    throw new Error('Authentication required');
  }

  const formData = new FormData();
  files.forEach(file => {
    formData.append('files', file);
  });

  const response = await fetch('/api/upload/product-images', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${session.access_token}`,
    },
    body: formData,
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to upload files');
  }

  const result = await response.json();
  return result.urls;
}

/**
 * Deletes a file from Supabase storage
 * @param url - The public URL of the file to delete
 * @param bucket - The storage bucket name
 * @returns Boolean indicating success
 */
export async function deleteFileByUrlClient(url: string, bucket = 'product-images') {
  const supabase = createClient();
  
  // Extract the path from the URL
  const urlObj = new URL(url);
  const pathMatch = urlObj.pathname.match(new RegExp(`/${bucket}/object/public/(.+)$`));
  
  if (!pathMatch || !pathMatch[1]) {
    console.error('Invalid file URL format');
    return false;
  }
  
  const filePath = decodeURIComponent(pathMatch[1]);
  
  // Delete the file
  const { error } = await supabase.storage
    .from(bucket)
    .remove([filePath]);
  
  if (error) {
    console.error('Error deleting file:', error);
    return false;
  }
  
  return true;
}
