"use client";

import { useEffect, useState } from "react";
import { createClient } from "@/utils/supabase/client";
import { useRouter } from "next/navigation";
import { ChatInterface } from "@/components/chat/chat-interface";
import { sendMessage, getConversations, getMessages } from "@/utils/supabase/chat-utils";
import { uploadFile } from "@/utils/supabase/resource-utils";
import { Loader2 } from "lucide-react";
import { Tables } from "@/utils/supabase/database.types";

export default function MentorChatPage() {
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<Tables<'users'> | null>(null);
  const [contacts, setContacts] = useState<Array<{
    id: string;
    name: string;
    avatar?: string | null;
    lastMessage?: string | null;
    lastMessageTime?: Date | null;
    unreadCount?: number | null;
    status?: 'online' | 'offline' | 'away' | null;
    conversationId?: string;
  }>>([]);
  const [messages, setMessages] = useState<Record<string, any[]>>({});
  const router = useRouter();
  const supabase = createClient();

  // Check if user is authenticated
  useEffect(() => {
    const checkUser = async () => {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error || !session) {
        router.push('/auth/sign-in');
        return;
      }
      
      // Get user details
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', session.user.id)
        .single();
      
      if (userError || !userData) {
        console.error('Error fetching user data:', userError);
        return;
      }
      
      // Verify user is a mentor
      if (userData.role !== 'mentor' && userData.role !== 'admin') {
        console.error('User is not a mentor or admin');
        router.push('/dashboard');
        return;
      }
      
      setUser(userData);
      
      // Load students as contacts
      await loadStudentContacts(userData.id);
      
      setLoading(false);
    };
    
    checkUser();
  }, []);

  // Load students assigned to this mentor
  const loadStudentContacts = async (mentorId: string) => {
    try {
      // Get student enrollments to find assigned students
      const { data: enrollments, error: enrollmentError } = await supabase
        .from('student_enrollments')
        .select('student_id, program_id')
        .eq('mentor_id', mentorId)
        .eq('status', 'active');
      
      if (enrollmentError || !enrollments || enrollments.length === 0) {
        console.error('No active enrollments found');
        return;
      }
      
      // Get student details
      const studentIds = Array.from(new Set(enrollments.map(e => e.student_id)));
      
      const studentContacts: Array<{
        id: string;
        name: string;
        avatar?: string | null;
        lastMessage?: string | null;
        lastMessageTime?: Date | null;
        unreadCount?: number | null;
        status?: 'online' | 'offline' | 'away' | null;
        conversationId?: string;
      }> = [];
      
      for (const studentId of studentIds) {
        // Get student user details
        const { data: studentData, error: studentError } = await supabase
          .from('users')
          .select('*')
          .eq('id', studentId)
          .single();
        
        if (studentError || !studentData) {
          console.error('Error fetching student data:', studentError);
          continue;
        }
        
        // Get or create conversation
        const { data: conversationData, error: convError } = await supabase
          .from('messages')
          .select('conversation_id')
          .or(`and(sender_id.eq.${mentorId},receiver_id.eq.${studentId}),and(sender_id.eq.${studentId},receiver_id.eq.${mentorId})`)
          .limit(1);
        
        let conversationId;
        
        if (convError || !conversationData || conversationData.length === 0) {
          // Create a new conversation ID
          conversationId = `${mentorId}_${studentId}_${Date.now()}`;
        } else {
          conversationId = conversationData[0].conversation_id;
        }
        
        // Get latest message
        const { data: latestMessage, error: msgError } = await supabase
          .from('messages')
          .select('*')
          .eq('conversation_id', conversationId)
          .order('created_at', { ascending: false })
          .limit(1);
        
        // Get unread count
        const { count: unreadCount, error: countError } = await supabase
          .from('messages')
          .select('id', { count: 'exact' })
          .eq('conversation_id', conversationId)
          .eq('receiver_id', mentorId)
          .eq('read', false);

        // Add to contacts
        studentContacts.push({
          id: studentData.id,
          name: studentData.full_name || 'Student',
          avatar: studentData.avatar_url,
          lastMessage: latestMessage && latestMessage.length > 0 ? latestMessage[0].content : 'Start a conversation',
          lastMessageTime: latestMessage && latestMessage.length > 0 ? new Date(latestMessage[0].created_at) : null,
          unreadCount: unreadCount || 0,
          status: 'offline' as const, // We would need a presence system to determine online status
          conversationId: conversationId,
        });
        
        // Load messages for this conversation
        if (conversationId) {
          await loadMessages(conversationId, studentData.id);
        }
      }
      
      setContacts(studentContacts);
    } catch (error) {
      console.error('Error loading student contacts:', error);
    }
  };

  // Load messages for a specific conversation
  const loadMessages = async (conversationId: string, contactId: string) => {
    try {
      const { data: messagesData, error } = await supabase
        .from('messages')
        .select('*')
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: true });
      
      if (error || !messagesData) {
        console.error('Error fetching messages:', error);
        return;
      }
      
      // Format messages
      const formattedMessages = messagesData.map((msg) => ({
        id: msg.id,
        content: msg.content,
        sender: msg.sender_id === user?.id ? 'user' : 'mentor',
        timestamp: new Date(msg.created_at),
        read: msg.read,
        attachmentUrl: msg.attachment_url || undefined,
      }));
      
      // Update messages state
      setMessages((prev) => ({
        ...prev,
        [contactId]: formattedMessages,
      }));
      
      // Mark messages as read if they were sent to the mentor
      if (user) {
        const { error: updateError } = await supabase
          .from('messages')
          .update({ read: true })
          .eq('conversation_id', conversationId)
          .eq('receiver_id', user.id)
          .eq('read', false);
        
        if (updateError) {
          console.error('Error marking messages as read:', updateError);
        }
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    }
  };

  // Handle sending a message
  const handleSendMessage = async (contactId: string, content: string, attachment?: File) => {
    if (!user) return;
    
    try {
      // Find the conversation with this contact
      const contact = contacts.find((c) => c.id === contactId);
      
      if (!contact || !contact.conversationId) {
        console.error('Conversation not found');
        return;
      }
      
      let attachmentUrl: string | undefined = undefined;
      
      // Upload attachment if provided
      if (attachment) {
        const fileData = await uploadFile(
          attachment, 
          'chat-attachments', 
          `${user.id}/${Date.now()}_${attachment.name}`
        );
        
        if (fileData) {
          attachmentUrl = fileData.url;
        }
      }
      
      // Send message to Supabase
      await sendMessage({
        conversationId: contact.conversationId,
        senderId: user.id,
        receiverId: contactId,
        content,
        attachmentUrl,
      });
      
      // Reload messages
      await loadMessages(contact.conversationId, contactId);
      
      // Update last message in contacts list
      setContacts((prev) => 
        prev.map((c) => 
          c.id === contactId
            ? {
                ...c,
                lastMessage: content,
                lastMessageTime: new Date(),
              }
            : c
        )
      );
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-lg">Loading chat...</span>
      </div>
    );
  }

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-6">Chat with Your Students</h1>
      <ChatInterface
        currentUserId={user?.id || ''}
        initialContacts={contacts}
        initialMessages={messages}
        onSendMessage={handleSendMessage}
      />
    </div>
  );
}
