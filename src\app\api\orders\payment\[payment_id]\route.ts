import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(
  request: Request,
  { params }: { params: { payment_id: string } }
) {
  try {
    const paymentId = params.payment_id;
    
    if (!paymentId) {
      return NextResponse.json(
        { error: 'Payment ID is required' },
        { status: 400 }
      );
    }

    const supabase = await createClient();
    
    // Get the order by Yoco payment ID
    const { data: order, error } = await supabase
      .from('orders')
      .select('*')
      .eq('yoco_payment_id', paymentId)
      .single();
    
    if (error) {
      console.error('Error fetching order by payment ID:', error);
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }
    
    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ order });
  } catch (error: any) {
    console.error('Error retrieving order by payment ID:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to retrieve order' },
      { status: 500 }
    );
  }
} 