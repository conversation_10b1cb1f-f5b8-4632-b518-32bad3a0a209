-- Complete Database Setup for Tennis-Gear Student Dashboard
-- Run this script in your Supabase SQL Editor
-- This script is idempotent and can be run multiple times safely

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Helper function to create policies safely
CREATE OR REPLACE FUNCTION create_policy_if_not_exists(
  policy_name TEXT,
  table_name TEXT,
  policy_definition TEXT
) RETURNS VOID AS $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename = table_name
    AND policyname = policy_name
  ) THEN
    EXECUTE policy_definition;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Create users table to support RLS policies
CREATE TABLE IF NOT EXISTS public.users (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  role TEXT NOT NULL DEFAULT 'student' CHECK (role IN ('student', 'mentor', 'admin')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on users table
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Create policies safely (only if they don't exist)
SELECT create_policy_if_not_exists(
  'Users can view own profile',
  'users',
  'CREATE POLICY "Users can view own profile" ON public.users FOR SELECT USING (auth.uid() = id)'
);

SELECT create_policy_if_not_exists(
  'Users can update own profile',
  'users',
  'CREATE POLICY "Users can update own profile" ON public.users FOR UPDATE USING (auth.uid() = id)'
);

SELECT create_policy_if_not_exists(
  'Admins can view all users',
  'users',
  'CREATE POLICY "Admins can view all users" ON public.users FOR SELECT USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = ''admin''))'
);

-- 1. Mentorship Programs Table
CREATE TABLE IF NOT EXISTS public.mentorship_programs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  duration_months INTEGER NOT NULL,
  price_monthly DECIMAL(10, 2) NOT NULL,
  price_upfront DECIMAL(10, 2),
  features JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for mentorship_programs
ALTER TABLE public.mentorship_programs ENABLE ROW LEVEL SECURITY;

-- Create policies safely (only if they don't exist)
SELECT create_policy_if_not_exists(
  'Anyone can view mentorship programs',
  'mentorship_programs',
  'CREATE POLICY "Anyone can view mentorship programs" ON public.mentorship_programs FOR SELECT USING (true)'
);

SELECT create_policy_if_not_exists(
  'Admins can manage mentorship programs',
  'mentorship_programs',
  'CREATE POLICY "Admins can manage mentorship programs" ON public.mentorship_programs FOR ALL USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = ''admin''))'
);

-- 2. Mentors Table
CREATE TABLE IF NOT EXISTS public.mentors (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id),
  bio TEXT,
  specialties TEXT[],
  experience_years INTEGER,
  availability JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for mentors
ALTER TABLE public.mentors ENABLE ROW LEVEL SECURITY;

SELECT create_policy_if_not_exists(
  'Anyone can view mentor profiles',
  'mentors',
  'CREATE POLICY "Anyone can view mentor profiles" ON public.mentors FOR SELECT USING (true)'
);

SELECT create_policy_if_not_exists(
  'Mentors can update their own profiles',
  'mentors',
  'CREATE POLICY "Mentors can update their own profiles" ON public.mentors FOR UPDATE USING (auth.uid() = user_id)'
);

SELECT create_policy_if_not_exists(
  'Admins can manage all mentors',
  'mentors',
  'CREATE POLICY "Admins can manage all mentors" ON public.mentors FOR ALL USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = ''admin''))'
);

-- 3. Student Enrollments Table
CREATE TABLE IF NOT EXISTS public.student_enrollments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id UUID REFERENCES auth.users(id),
  program_id UUID REFERENCES public.mentorship_programs(id),
  mentor_id UUID REFERENCES public.mentors(id),
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  payment_type TEXT NOT NULL CHECK (payment_type IN ('monthly', 'upfront')),
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for student_enrollments
ALTER TABLE public.student_enrollments ENABLE ROW LEVEL SECURITY;

SELECT create_policy_if_not_exists(
  'Students can view their own enrollments',
  'student_enrollments',
  'CREATE POLICY "Students can view their own enrollments" ON public.student_enrollments FOR SELECT USING (auth.uid() = student_id)'
);

SELECT create_policy_if_not_exists(
  'Mentors can view their students'' enrollments',
  'student_enrollments',
  'CREATE POLICY "Mentors can view their students'' enrollments" ON public.student_enrollments FOR SELECT USING (auth.uid() IN (SELECT user_id FROM public.mentors WHERE id = mentor_id))'
);

SELECT create_policy_if_not_exists(
  'Admins can manage all enrollments',
  'student_enrollments',
  'CREATE POLICY "Admins can manage all enrollments" ON public.student_enrollments FOR ALL USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = ''admin''))'
);

-- 4. Mentorship Sessions Table
CREATE TABLE IF NOT EXISTS public.mentorship_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  enrollment_id UUID REFERENCES public.student_enrollments(id),
  scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
  duration_minutes INTEGER NOT NULL,
  status TEXT NOT NULL DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'completed', 'cancelled')),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for mentorship_sessions
ALTER TABLE public.mentorship_sessions ENABLE ROW LEVEL SECURITY;

SELECT create_policy_if_not_exists(
  'Students can view their own sessions',
  'mentorship_sessions',
  'CREATE POLICY "Students can view their own sessions" ON public.mentorship_sessions FOR SELECT USING (EXISTS (SELECT 1 FROM public.student_enrollments WHERE id = enrollment_id AND student_id = auth.uid()))'
);

SELECT create_policy_if_not_exists(
  'Students can insert their own sessions',
  'mentorship_sessions',
  'CREATE POLICY "Students can insert their own sessions" ON public.mentorship_sessions FOR INSERT WITH CHECK (EXISTS (SELECT 1 FROM public.student_enrollments WHERE id = enrollment_id AND student_id = auth.uid()))'
);

SELECT create_policy_if_not_exists(
  'Mentors can manage their students'' sessions',
  'mentorship_sessions',
  'CREATE POLICY "Mentors can manage their students'' sessions" ON public.mentorship_sessions FOR ALL USING (EXISTS (SELECT 1 FROM public.student_enrollments e JOIN public.mentors m ON e.mentor_id = m.id WHERE e.id = enrollment_id AND m.user_id = auth.uid()))'
);

SELECT create_policy_if_not_exists(
  'Admins can manage all sessions',
  'mentorship_sessions',
  'CREATE POLICY "Admins can manage all sessions" ON public.mentorship_sessions FOR ALL USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = ''admin''))'
);

-- 5. Resources Table
CREATE TABLE IF NOT EXISTS public.resources (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  description TEXT,
  type TEXT NOT NULL CHECK (type IN ('document', 'video', 'training', 'progress')),
  category TEXT NOT NULL,
  format TEXT NOT NULL,
  file_path TEXT NOT NULL,
  size_bytes INTEGER,
  download_count INTEGER DEFAULT 0,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for resources
ALTER TABLE public.resources ENABLE ROW LEVEL SECURITY;

SELECT create_policy_if_not_exists(
  'Authenticated users can view resources',
  'resources',
  'CREATE POLICY "Authenticated users can view resources" ON public.resources FOR SELECT USING (auth.role() = ''authenticated'')'
);

SELECT create_policy_if_not_exists(
  'Mentors and admins can create resources',
  'resources',
  'CREATE POLICY "Mentors and admins can create resources" ON public.resources FOR INSERT WITH CHECK (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN (''mentor'', ''admin'')))'
);

SELECT create_policy_if_not_exists(
  'Creators can update their own resources',
  'resources',
  'CREATE POLICY "Creators can update their own resources" ON public.resources FOR UPDATE USING (auth.uid() = created_by)'
);

SELECT create_policy_if_not_exists(
  'Admins can manage all resources',
  'resources',
  'CREATE POLICY "Admins can manage all resources" ON public.resources FOR ALL USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = ''admin''))'
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_student_enrollments_student ON public.student_enrollments(student_id);
CREATE INDEX IF NOT EXISTS idx_student_enrollments_mentor ON public.student_enrollments(mentor_id);
CREATE INDEX IF NOT EXISTS idx_student_enrollments_program ON public.student_enrollments(program_id);
CREATE INDEX IF NOT EXISTS idx_mentorship_sessions_enrollment ON public.mentorship_sessions(enrollment_id);
CREATE INDEX IF NOT EXISTS idx_mentorship_sessions_scheduled ON public.mentorship_sessions(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_resources_type ON public.resources(type);
CREATE INDEX IF NOT EXISTS idx_resources_category ON public.resources(category);

-- Add updated_at triggers
CREATE OR REPLACE FUNCTION public.set_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_mentorship_programs_updated_at
  BEFORE UPDATE ON public.mentorship_programs
  FOR EACH ROW
  EXECUTE FUNCTION public.set_updated_at();

CREATE TRIGGER set_mentors_updated_at
  BEFORE UPDATE ON public.mentors
  FOR EACH ROW
  EXECUTE FUNCTION public.set_updated_at();

CREATE TRIGGER set_student_enrollments_updated_at
  BEFORE UPDATE ON public.student_enrollments
  FOR EACH ROW
  EXECUTE FUNCTION public.set_updated_at();

CREATE TRIGGER set_mentorship_sessions_updated_at
  BEFORE UPDATE ON public.mentorship_sessions
  FOR EACH ROW
  EXECUTE FUNCTION public.set_updated_at();

CREATE TRIGGER set_resources_updated_at
  BEFORE UPDATE ON public.resources
  FOR EACH ROW
  EXECUTE FUNCTION public.set_updated_at();

-- ============================================================================
-- SAMPLE DATA INSERTION
-- ============================================================================
-- Temporarily disable RLS for sample data insertion
-- This allows the initial setup data to be inserted without authentication

ALTER TABLE public.mentorship_programs DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.mentors DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.student_enrollments DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.mentorship_sessions DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.resources DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;

-- Insert sample users (required for foreign key relationships)
INSERT INTO public.users (id, email, full_name, role) VALUES
('550e8400-e29b-41d4-a716-446655440010', '<EMAIL>', 'Tennis Admin', 'admin'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'John Smith', 'mentor'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Sarah Johnson', 'mentor'),
('550e8400-e29b-41d4-a716-446655440013', '<EMAIL>', 'Mike Wilson', 'student'),
('550e8400-e29b-41d4-a716-446655440014', '<EMAIL>', 'Emma Davis', 'student')
ON CONFLICT (id) DO NOTHING;

-- Insert sample mentorship programs
INSERT INTO public.mentorship_programs (id, name, description, duration_months, price_monthly, price_upfront, features) VALUES
('550e8400-e29b-41d4-a716-************', '6-Month Tennis Mastery', 'Comprehensive tennis training program for beginners to intermediate players', 6, 299.99, 1599.99, '["Weekly 1-on-1 sessions", "Video analysis", "Training plans", "Progress tracking"]'),
('550e8400-e29b-41d4-a716-************', '12-Month Pro Development', 'Advanced tennis coaching for competitive players', 12, 399.99, 4199.99, '["Bi-weekly sessions", "Tournament preparation", "Mental coaching", "Nutrition guidance"]')
ON CONFLICT (id) DO NOTHING;

-- Insert sample mentors
INSERT INTO public.mentors (id, user_id, bio, specialties, experience_years, availability) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Professional tennis coach with 15+ years of experience. Specializes in technique development and mental coaching.', '["Technique Development", "Mental Coaching", "Tournament Preparation"]', 15, '{"monday": ["09:00", "17:00"], "tuesday": ["09:00", "17:00"], "wednesday": ["09:00", "17:00"]}'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Former professional player turned coach. Expert in serve technique and match strategy.', '["Serve Technique", "Match Strategy", "Physical Conditioning"]', 12, '{"thursday": ["10:00", "18:00"], "friday": ["10:00", "18:00"], "saturday": ["08:00", "16:00"]}')
ON CONFLICT (id) DO NOTHING;

-- Insert sample student enrollments
INSERT INTO public.student_enrollments (id, student_id, program_id, mentor_id, start_date, end_date, payment_type, status) VALUES
('550e8400-e29b-41d4-a716-446655440030', '550e8400-e29b-41d4-a716-446655440013', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '2024-01-15 10:00:00+00', '2024-07-15 10:00:00+00', 'monthly', 'active'),
('550e8400-e29b-41d4-a716-446655440031', '550e8400-e29b-41d4-a716-446655440014', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '2024-02-01 09:00:00+00', '2025-02-01 09:00:00+00', 'upfront', 'active')
ON CONFLICT (id) DO NOTHING;

-- Insert sample mentorship sessions
INSERT INTO public.mentorship_sessions (id, enrollment_id, scheduled_at, duration_minutes, status, notes) VALUES
('550e8400-e29b-41d4-a716-446655440040', '550e8400-e29b-41d4-a716-446655440030', '2024-01-22 14:00:00+00', 60, 'completed', 'Worked on forehand technique and footwork. Great progress shown.'),
('550e8400-e29b-41d4-a716-446655440041', '550e8400-e29b-41d4-a716-446655440030', '2024-01-29 14:00:00+00', 60, 'completed', 'Focused on serve mechanics. Student is improving consistency.'),
('550e8400-e29b-41d4-a716-446655440042', '550e8400-e29b-41d4-a716-446655440031', '2024-02-08 10:00:00+00', 90, 'completed', 'Tournament preparation session. Worked on match strategy and mental toughness.'),
('550e8400-e29b-41d4-a716-446655440043', '550e8400-e29b-41d4-a716-446655440030', '2024-02-05 14:00:00+00', 60, 'scheduled', 'Upcoming session to work on backhand technique.')
ON CONFLICT (id) DO NOTHING;

-- Insert sample resources
INSERT INTO public.resources (id, title, description, type, category, format, file_path, size_bytes, download_count, created_by) VALUES
('550e8400-e29b-41d4-a716-************', 'Tennis Fundamentals Video Series', 'Complete video series covering basic tennis techniques and fundamentals', 'video', 'Fundamentals', 'mp4', 'resources/tennis-fundamentals.mp4', 524288000, 45, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-************', 'Serve Technique Guide', 'Comprehensive PDF guide to improving your tennis serve', 'document', 'Technique', 'pdf', 'resources/serve-guide.pdf', 2048000, 32, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-************', 'Weekly Training Plan', 'Structured training program for intermediate players', 'training', 'Training Plans', 'pdf', 'resources/weekly-plan.pdf', 1024000, 28, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-************', 'Progress Tracking Template', 'Template for tracking your tennis progress and improvements', 'progress', 'Progress Tracking', 'xlsx', 'resources/progress-template.xlsx', 512000, 15, '550e8400-e29b-41d4-a716-************')
ON CONFLICT (id) DO NOTHING;

-- Re-enable RLS after sample data insertion
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.mentorship_programs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.mentors ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.student_enrollments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.mentorship_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.resources ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- SETUP COMPLETE
-- ============================================================================

-- Create a function to help with user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, full_name, role)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name', 'student');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create user profile on signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Success message
DO $$
BEGIN
  RAISE NOTICE 'Database setup completed successfully!';
  RAISE NOTICE 'Sample data inserted with RLS policies enabled.';
  RAISE NOTICE 'You can now test the student dashboard functionality.';
END $$;
