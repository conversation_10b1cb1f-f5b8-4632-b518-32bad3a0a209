-- Create consultations table function
CREATE OR REPLACE FUNCTION create_consultations_table()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the table already exists
  IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'consultations') THEN
    -- Create the consultations table
    CREATE TABLE public.consultations (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
      first_name TEXT NOT NULL,
      last_name TEXT NOT NULL,
      email TEXT NOT NULL,
      phone_number TEXT NOT NULL,
      scheduled_date TEXT NOT NULL,
      scheduled_time TEXT NOT NULL,
      reason TEXT NOT NULL,
      status TEXT NOT NULL DEFAULT 'pending',
      payment_reference TEXT,
      payment_status TEXT,
      payment_amount NUMERIC,
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );

    -- Add RLS policies
    ALTER TABLE public.consultations ENABLE ROW LEVEL SECURITY;

    -- Create policy for users to view their own consultations
    CREATE POLICY "Users can view their own consultations"
      ON public.consultations
      FOR SELECT
      USING (auth.uid() = user_id);

    -- Create policy for authenticated users to insert consultations
    CREATE POLICY "Authenticated users can insert consultations"
      ON public.consultations
      FOR INSERT
      WITH CHECK (auth.role() = 'authenticated');

    -- Create policy for admins to view all consultations
    CREATE POLICY "Admins can view all consultations"
      ON public.consultations
      FOR SELECT
      USING (auth.role() = 'service_role');

    -- Create policy for admins to modify all consultations
    CREATE POLICY "Admins can modify all consultations"
      ON public.consultations
      USING (auth.role() = 'service_role');

    -- Create updated_at trigger
    CREATE TRIGGER set_updated_at
      BEFORE UPDATE ON public.consultations
      FOR EACH ROW
      EXECUTE FUNCTION public.set_updated_at();

    -- Allow anonymous users to insert consultations (for guest bookings)
    CREATE POLICY "Allow anonymous consultation bookings"
      ON public.consultations
      FOR INSERT
      WITH CHECK (true);
  END IF;
END;
$$;
