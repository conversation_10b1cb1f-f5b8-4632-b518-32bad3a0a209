// Force refresh utility to clear all cached data and reload user state
import { supabase } from '../../supabase/supabase';

export const forceRefreshUserData = async () => {
  try {
    console.log('🔄 Force refreshing all user data...');
    
    // Clear any cached auth state
    await supabase.auth.refreshSession();
    
    // Get fresh user data
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      console.error('❌ Error getting user:', error);
      return null;
    }
    
    // Get fresh profile data
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      console.error('❌ Error getting profile:', profileError);
      return null;
    }
    
    console.log('✅ Fresh user data loaded:', {
      userId: user.id,
      email: user.email,
      role: profileData?.role,
      isAdmin: profileData?.role === 'admin'
    });
    
    return {
      user,
      profile: profileData
    };
  } catch (error) {
    console.error('❌ Error force refreshing user data:', error);
    return null;
  }
};

export const clearBrowserCache = () => {
  try {
    console.log('🧹 Clearing browser cache...');
    
    // Clear localStorage
    localStorage.clear();
    
    // Clear sessionStorage
    sessionStorage.clear();
    
    // Clear any Supabase specific storage
    const supabaseKeys = Object.keys(localStorage).filter(key => 
      key.includes('supabase') || key.includes('sb-')
    );
    
    supabaseKeys.forEach(key => {
      localStorage.removeItem(key);
    });
    
    console.log('✅ Browser cache cleared');
    
    // Force reload the page
    window.location.reload();
  } catch (error) {
    console.error('❌ Error clearing cache:', error);
  }
};
