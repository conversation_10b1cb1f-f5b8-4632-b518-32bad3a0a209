import { NextRequest, NextResponse } from 'next/server';
import { createClient, createServiceRoleClient } from '@/utils/supabase/server';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions
    const { data: adminData } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (!adminData || adminData.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { report_type, file_format, date_range } = body;

    if (!report_type || !file_format) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    console.log('POST /api/admin/business-reports/export - Admin access confirmed');

    // Calculate date range
    let startDate: Date;
    const endDate = new Date();

    switch (date_range) {
      case '7d':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        startDate = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
        break;
      default: // 30d
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }

    // Use service role client for database operations
    const serviceSupabase = createServiceRoleClient();

    // Get business analytics data
    const { data: analyticsData, error } = await serviceSupabase
      .rpc('get_business_analytics', {
        start_date: startDate.toISOString().split('T')[0],
        end_date: endDate.toISOString().split('T')[0]
      });

    if (error) {
      console.error('Export analytics error:', error);
      return NextResponse.json({ error: 'Failed to fetch analytics data' }, { status: 500 });
    }

    const metrics = analyticsData?.[0] || {};

    // Generate report content based on type
    let reportContent: string;
    let filename: string;

    switch (report_type) {
      case 'sales':
        reportContent = generateSalesReport(metrics, startDate, endDate);
        filename = `sales_report_${startDate.toISOString().split('T')[0]}_to_${endDate.toISOString().split('T')[0]}`;
        break;
      case 'products':
        reportContent = generateProductsReport(metrics, startDate, endDate);
        filename = `products_report_${startDate.toISOString().split('T')[0]}_to_${endDate.toISOString().split('T')[0]}`;
        break;
      default:
        return NextResponse.json({ error: 'Invalid report type' }, { status: 400 });
    }

    // Return appropriate format
    if (file_format === 'pdf') {
      // For PDF, we'll return HTML that can be converted to PDF on the client side
      return new NextResponse(reportContent, {
        headers: {
          'Content-Type': 'text/html',
          'Content-Disposition': `attachment; filename="${filename}.html"`
        }
      });
    } else if (file_format === 'xlsx') {
      // For Excel, we'll return CSV format (simplified)
      const csvContent = convertToCSV(metrics, report_type);
      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="${filename}.csv"`
        }
      });
    }

    return NextResponse.json({ error: 'Unsupported file format' }, { status: 400 });

  } catch (error: any) {
    console.error('POST /api/admin/business-reports/export - General error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

function generateSalesReport(metrics: any, startDate: Date, endDate: Date): string {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount);
  };

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Sales Report</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric-card { border: 1px solid #ddd; padding: 15px; border-radius: 5px; }
        .metric-value { font-size: 24px; font-weight: bold; color: #2563eb; }
        .metric-label { color: #666; margin-top: 5px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f5f5f5; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Sales Report</h1>
        <p>Period: ${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}</p>
        <p>Generated: ${new Date().toLocaleString()}</p>
      </div>
      
      <div class="metrics">
        <div class="metric-card">
          <div class="metric-value">${formatCurrency(metrics.total_sales || 0)}</div>
          <div class="metric-label">Total Sales</div>
        </div>
        <div class="metric-card">
          <div class="metric-value">${(metrics.total_orders || 0).toLocaleString()}</div>
          <div class="metric-label">Total Orders</div>
        </div>
        <div class="metric-card">
          <div class="metric-value">${formatCurrency(metrics.average_order_value || 0)}</div>
          <div class="metric-label">Average Order Value</div>
        </div>
        <div class="metric-card">
          <div class="metric-value">${metrics.customer_metrics?.total_customers || 0}</div>
          <div class="metric-label">Total Customers</div>
        </div>
      </div>

      <h2>Sales by Category</h2>
      <table>
        <thead>
          <tr>
            <th>Category</th>
            <th>Total Sales</th>
            <th>Quantity Sold</th>
          </tr>
        </thead>
        <tbody>
          ${(metrics.sales_by_category || []).map((category: any) => `
            <tr>
              <td>${category.category}</td>
              <td>${formatCurrency(category.total_sales)}</td>
              <td>${category.total_quantity}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>

      <h2>Top Selling Products</h2>
      <table>
        <thead>
          <tr>
            <th>Product Name</th>
            <th>Quantity Sold</th>
            <th>Total Revenue</th>
          </tr>
        </thead>
        <tbody>
          ${(metrics.top_selling_products || []).slice(0, 10).map((product: any) => `
            <tr>
              <td>${product.product_name}</td>
              <td>${product.total_quantity}</td>
              <td>${formatCurrency(product.total_revenue)}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </body>
    </html>
  `;
}

function generateProductsReport(metrics: any, startDate: Date, endDate: Date): string {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount);
  };

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Product Performance Report</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f5f5f5; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Product Performance Report</h1>
        <p>Period: ${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}</p>
        <p>Generated: ${new Date().toLocaleString()}</p>
      </div>
      
      <h2>Top Performing Products</h2>
      <table>
        <thead>
          <tr>
            <th>Rank</th>
            <th>Product Name</th>
            <th>Quantity Sold</th>
            <th>Total Revenue</th>
            <th>Avg. Price</th>
          </tr>
        </thead>
        <tbody>
          ${(metrics.top_selling_products || []).map((product: any, index: number) => `
            <tr>
              <td>${index + 1}</td>
              <td>${product.product_name}</td>
              <td>${product.total_quantity}</td>
              <td>${formatCurrency(product.total_revenue)}</td>
              <td>${formatCurrency(product.total_revenue / product.total_quantity)}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>

      <h2>Sales by Category</h2>
      <table>
        <thead>
          <tr>
            <th>Category</th>
            <th>Total Sales</th>
            <th>Quantity Sold</th>
            <th>Percentage of Total</th>
          </tr>
        </thead>
        <tbody>
          ${(metrics.sales_by_category || []).map((category: any) => {
            const percentage = metrics.total_sales > 0 ? (category.total_sales / metrics.total_sales * 100).toFixed(1) : '0.0';
            return `
              <tr>
                <td>${category.category}</td>
                <td>${formatCurrency(category.total_sales)}</td>
                <td>${category.total_quantity}</td>
                <td>${percentage}%</td>
              </tr>
            `;
          }).join('')}
        </tbody>
      </table>
    </body>
    </html>
  `;
}

function convertToCSV(metrics: any, reportType: string): string {
  if (reportType === 'sales') {
    let csv = 'Category,Total Sales,Quantity Sold\n';
    (metrics.sales_by_category || []).forEach((category: any) => {
      csv += `"${category.category}",${category.total_sales},${category.total_quantity}\n`;
    });
    return csv;
  } else if (reportType === 'products') {
    let csv = 'Product Name,Quantity Sold,Total Revenue\n';
    (metrics.top_selling_products || []).forEach((product: any) => {
      csv += `"${product.product_name}",${product.total_quantity},${product.total_revenue}\n`;
    });
    return csv;
  }
  return '';
}
