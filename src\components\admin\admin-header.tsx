"use client";

import { useState, useEffect } from "react";
import { Search, User, LogOut, Settings, UserCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import ThemeSwitcher from "@/components/theme-switcher";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useRouter } from "next/navigation";
import { createClient } from '@/utils/supabase/client';
import { RealTimeNotifications } from './real-time-notifications';

interface AdminUser {
  id: string;
  email: string;
  full_name: string | null;
  admin_role: string | null;
  avatar_url: string | null;
}

export function AdminHeader() {
  const router = useRouter();
  const [currentUser, setCurrentUser] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);

  // Fetch current admin user data
  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const supabase = createClient();
        const { data: { session } } = await supabase.auth.getSession();

        if (!session) {
          router.push('/admin/sign-in');
          return;
        }

        // Get user profile data
        const { data: userData } = await supabase
          .from('users')
          .select('id, email, full_name, admin_role, avatar_url')
          .eq('id', session.user.id)
          .single();

        if (userData) {
          setCurrentUser(userData);
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCurrentUser();
  }, [router]);
  
  const handleLogout = async () => {
    try {
      const { createClient } = await import('@/utils/supabase/client');
      const supabase = createClient();
      await supabase.auth.signOut();
      router.push('/admin/sign-in');
    } catch (error) {
      console.error('Error signing out:', error);
      router.push('/admin/sign-in');
    }
  };
  
  return (
    <header className="glass-effect-dark border-b border-white/10 py-4 px-6 neo-shadow">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4 flex-1">
          <form className="relative max-w-md w-full">
            <Search className="absolute left-4 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search admin panel..."
              className="pl-12 w-full h-12 rounded-2xl bg-muted/30 border-0 neo-shadow-inset placeholder:text-muted-foreground/70 focus:neo-shadow-light transition-neo"
            />
          </form>
        </div>

        <div className="flex items-center gap-4">
          {/* Theme Switcher with Enhanced Styling */}
          <div className="glass-effect-subtle rounded-2xl p-2 neo-shadow-light">
            <ThemeSwitcher />
          </div>

          {/* Real-time Notifications with Enhanced Styling */}
          <div className="glass-effect-subtle rounded-2xl neo-shadow-light">
            <RealTimeNotifications />
          </div>

          {/* User Profile Dropdown with Enhanced Styling */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center gap-3 cursor-pointer glass-effect-subtle rounded-2xl p-3 neo-shadow-light hover:neo-shadow transition-neo min-h-[44px]">
                <Avatar className="h-10 w-10 neo-shadow">
                  <AvatarImage
                    src={currentUser?.avatar_url || "/images/admin-avatar.png"}
                    alt={currentUser?.full_name || "Admin User"}
                  />
                  <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-semibold">
                    {currentUser?.full_name
                      ? currentUser.full_name.split(' ').map(n => n[0]).join('').toUpperCase()
                      : <User className="h-5 w-5 text-primary" />
                    }
                  </AvatarFallback>
                </Avatar>
                {!loading && currentUser && (
                  <div className="hidden md:block text-left">
                    <p className="text-sm font-semibold leading-none text-foreground">
                      {currentUser.full_name || 'Admin User'}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {currentUser.admin_role === 'admin' ? 'Main Admin' :
                       currentUser.admin_role === 'senior_admin' ? 'Senior Admin' :
                       currentUser.admin_role === 'junior_admin' ? 'Junior Admin' : 'Admin'}
                    </p>
                  </div>
                )}
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-64 glass-effect-dark border border-white/20 rounded-2xl neo-shadow p-2">
              <DropdownMenuLabel className="px-3 py-2">
                <div className="glass-effect-subtle rounded-xl p-3 neo-shadow-light">
                  <p className="font-semibold text-foreground">{currentUser?.full_name || 'Admin User'}</p>
                  <p className="text-xs text-muted-foreground font-normal mt-1">{currentUser?.email}</p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-white/10 my-2" />
              <DropdownMenuItem
                onClick={() => router.push('/admin/profile')}
                className="rounded-xl px-3 py-3 hover:glass-effect-subtle hover:neo-shadow-light transition-all duration-300 cursor-pointer min-h-[44px] flex items-center"
              >
                <UserCircle className="mr-3 h-5 w-5" />
                <span className="font-medium">Profile</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => router.push('/admin/settings')}
                className="rounded-xl px-3 py-3 hover:glass-effect-subtle hover:neo-shadow-light transition-all duration-300 cursor-pointer min-h-[44px] flex items-center"
              >
                <Settings className="mr-3 h-5 w-5" />
                <span className="font-medium">Settings</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-white/10 my-2" />
              <DropdownMenuItem
                onClick={handleLogout}
                className="rounded-xl px-3 py-3 hover:glass-effect-subtle hover:neo-shadow-light transition-all duration-300 cursor-pointer min-h-[44px] flex items-center text-destructive hover:text-destructive"
              >
                <LogOut className="mr-3 h-5 w-5" />
                <span className="font-medium">Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
