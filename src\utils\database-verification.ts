import { createBrowserClient } from '@supabase/ssr';

export async function verifyDatabaseTables() {
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  const results = {
    authentication: false,
    tables: {
      mentorship_programs: false,
      mentors: false,
      student_enrollments: false,
      mentorship_sessions: false,
      resources: false,
    },
    sampleData: {
      programs: 0,
      mentors: 0,
      enrollments: 0,
      sessions: 0,
      resources: 0,
    },
    errors: [] as string[]
  };

  try {
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError) {
      results.errors.push(`Auth Error: ${authError.message}`);
    } else {
      results.authentication = !!user;
    }

    // Check mentorship_programs table
    try {
      const { data, error } = await supabase
        .from('mentorship_programs')
        .select('id')
        .limit(1);
      
      if (error) {
        results.errors.push(`mentorship_programs: ${error.message}`);
      } else {
        results.tables.mentorship_programs = true;
        
        // Count sample data
        const { count } = await supabase
          .from('mentorship_programs')
          .select('*', { count: 'exact', head: true });
        results.sampleData.programs = count || 0;
      }
    } catch (err: any) {
      results.errors.push(`mentorship_programs: ${err.message}`);
    }

    // Check mentors table
    try {
      const { data, error } = await supabase
        .from('mentors')
        .select('id')
        .limit(1);
      
      if (error) {
        results.errors.push(`mentors: ${error.message}`);
      } else {
        results.tables.mentors = true;
        
        const { count } = await supabase
          .from('mentors')
          .select('*', { count: 'exact', head: true });
        results.sampleData.mentors = count || 0;
      }
    } catch (err: any) {
      results.errors.push(`mentors: ${err.message}`);
    }

    // Check student_enrollments table
    try {
      const { data, error } = await supabase
        .from('student_enrollments')
        .select('id')
        .limit(1);
      
      if (error) {
        results.errors.push(`student_enrollments: ${error.message}`);
      } else {
        results.tables.student_enrollments = true;
        
        const { count } = await supabase
          .from('student_enrollments')
          .select('*', { count: 'exact', head: true });
        results.sampleData.enrollments = count || 0;
      }
    } catch (err: any) {
      results.errors.push(`student_enrollments: ${err.message}`);
    }

    // Check mentorship_sessions table
    try {
      const { data, error } = await supabase
        .from('mentorship_sessions')
        .select('id')
        .limit(1);
      
      if (error) {
        results.errors.push(`mentorship_sessions: ${error.message}`);
      } else {
        results.tables.mentorship_sessions = true;
        
        const { count } = await supabase
          .from('mentorship_sessions')
          .select('*', { count: 'exact', head: true });
        results.sampleData.sessions = count || 0;
      }
    } catch (err: any) {
      results.errors.push(`mentorship_sessions: ${err.message}`);
    }

    // Check resources table
    try {
      const { data, error } = await supabase
        .from('resources')
        .select('id')
        .limit(1);
      
      if (error) {
        results.errors.push(`resources: ${error.message}`);
      } else {
        results.tables.resources = true;
        
        const { count } = await supabase
          .from('resources')
          .select('*', { count: 'exact', head: true });
        results.sampleData.resources = count || 0;
      }
    } catch (err: any) {
      results.errors.push(`resources: ${err.message}`);
    }

  } catch (error: any) {
    results.errors.push(`General Error: ${error.message}`);
  }

  return results;
}

export async function testStudentDashboardQueries(userId?: string) {
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  const testResults = {
    userId: userId || 'No user provided',
    queries: {
      enrollments: { success: false, error: '', count: 0 },
      sessions: { success: false, error: '', count: 0 },
      resources: { success: false, error: '', count: 0 },
    }
  };

  if (!userId) {
    const { data: { user } } = await supabase.auth.getUser();
    userId = user?.id;
    testResults.userId = userId || 'No authenticated user';
  }

  if (!userId) {
    testResults.queries.enrollments.error = 'No user ID available';
    testResults.queries.sessions.error = 'No user ID available';
    testResults.queries.resources.error = 'No user ID available';
    return testResults;
  }

  // Test enrollments query
  try {
    const { data, error } = await supabase
      .from('student_enrollments')
      .select(`
        *,
        program:program_id (*),
        mentor:mentor_id (
          *,
          user:user_id (
            id,
            full_name,
            email,
            avatar_url
          )
        )
      `)
      .eq('student_id', userId);

    if (error) {
      testResults.queries.enrollments.error = error.message;
    } else {
      testResults.queries.enrollments.success = true;
      testResults.queries.enrollments.count = data?.length || 0;
    }
  } catch (err: any) {
    testResults.queries.enrollments.error = err.message;
  }

  // Test sessions query
  try {
    const { data: enrollmentIds, error: enrollmentError } = await supabase
      .from('student_enrollments')
      .select('id')
      .eq('student_id', userId);

    if (enrollmentError) {
      testResults.queries.sessions.error = `Enrollment fetch: ${enrollmentError.message}`;
    } else if (enrollmentIds && enrollmentIds.length > 0) {
      const { data, error } = await supabase
        .from('mentorship_sessions')
        .select('*')
        .in('enrollment_id', enrollmentIds.map(e => e.id));

      if (error) {
        testResults.queries.sessions.error = error.message;
      } else {
        testResults.queries.sessions.success = true;
        testResults.queries.sessions.count = data?.length || 0;
      }
    } else {
      testResults.queries.sessions.success = true;
      testResults.queries.sessions.count = 0;
      testResults.queries.sessions.error = 'No enrollments found';
    }
  } catch (err: any) {
    testResults.queries.sessions.error = err.message;
  }

  // Test resources query
  try {
    const { data, error } = await supabase
      .from('resources')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      testResults.queries.resources.error = error.message;
    } else {
      testResults.queries.resources.success = true;
      testResults.queries.resources.count = data?.length || 0;
    }
  } catch (err: any) {
    testResults.queries.resources.error = err.message;
  }

  return testResults;
}
