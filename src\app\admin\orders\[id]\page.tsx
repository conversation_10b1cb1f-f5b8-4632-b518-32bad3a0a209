'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { ArrowLeft, Edit, Trash2, RefreshCw, AlertCircle, Package, User, CreditCard, Truck } from "lucide-react";
import { useOrder, useUpdateOrder, useDeleteOrder, useOrderStatusColor, usePaymentStatusColor } from '@/hooks/useOrders';
import { ORDER_STATUS_OPTIONS, PAYMENT_STATUS_OPTIONS, OrderStatus, PaymentStatus } from '@/types/orders';
// import { toast } from 'sonner'; // TODO: Install sonner package

export default function OrderDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    status: '' as OrderStatus,
    payment_status: '' as PaymentStatus,
    notes: '',
    shipping_method: '',
  });

  // Fetch order data
  const { 
    data: order, 
    isLoading, 
    error,
    refetch 
  } = useOrder(params.id);

  // Mutations
  const updateOrderMutation = useUpdateOrder();
  const deleteOrderMutation = useDeleteOrder();

  // Initialize edit data when order loads
  useState(() => {
    if (order && !isEditing) {
      setEditData({
        status: order.status,
        payment_status: order.payment_status,
        notes: order.notes || '',
        shipping_method: order.shipping_method || '',
      });
    }
  });

  const statusColor = useOrderStatusColor(order?.status || '');
  const paymentColor = usePaymentStatusColor(order?.payment_status || '');

  const handleEdit = () => {
    if (order) {
      setEditData({
        status: order.status,
        payment_status: order.payment_status,
        notes: order.notes || '',
        shipping_method: order.shipping_method || '',
      });
      setIsEditing(true);
    }
  };

  const handleSave = async () => {
    if (!order) return;

    try {
      await updateOrderMutation.mutateAsync({
        id: order.id,
        data: editData,
      });
      setIsEditing(false);
      // toast.success('Order updated successfully');
      console.log('Order updated successfully');
    } catch (error: any) {
      // toast.error(error.message || 'Failed to update order');
      console.error('Failed to update order:', error.message);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    if (order) {
      setEditData({
        status: order.status,
        payment_status: order.payment_status,
        notes: order.notes || '',
        shipping_method: order.shipping_method || '',
      });
    }
  };

  const handleDelete = async () => {
    if (!order) return;
    
    if (!['cancelled', 'refunded'].includes(order.status)) {
      // toast.error('Only cancelled or refunded orders can be deleted');
      alert('Only cancelled or refunded orders can be deleted');
      return;
    }

    if (confirm('Are you sure you want to delete this order? This action cannot be undone.')) {
      try {
        await deleteOrderMutation.mutateAsync(order.id);
        // toast.success('Order deleted successfully');
        console.log('Order deleted successfully');
        router.push('/admin/orders');
      } catch (error: any) {
        // toast.error(error.message || 'Failed to delete order');
        console.error('Failed to delete order:', error.message);
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-ZA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getItemCount = (items: any[]) => {
    return items.reduce((total, item) => total + (item.quantity || 1), 0);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <div className="h-8 w-8 bg-muted rounded animate-pulse" />
          <div className="h-8 bg-muted rounded animate-pulse flex-1" />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {Array.from({ length: 3 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <div className="h-6 bg-muted rounded animate-pulse" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-4 bg-muted rounded animate-pulse" />
                  <div className="h-4 bg-muted rounded animate-pulse w-2/3" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Link href="/admin/orders">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-3xl font-bold tracking-tight">Order Details</h1>
        </div>

        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center max-w-md">
              <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Failed to load order</h3>
              <p className="text-muted-foreground mb-4">
                {error.message || 'An error occurred while fetching the order'}
              </p>

              {/* Show specific error guidance */}
              {error.message?.includes('Authentication') && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4 text-left">
                  <h4 className="font-medium text-yellow-800 mb-2">Authentication Issue</h4>
                  <p className="text-sm text-yellow-700">
                    Please sign in again or check if your session has expired.
                  </p>
                </div>
              )}

              {error.message?.includes('Admin access') && (
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4 text-left">
                  <h4 className="font-medium text-orange-800 mb-2">Permission Issue</h4>
                  <p className="text-sm text-orange-700">
                    Admin access is required to view order details. Please contact an administrator.
                  </p>
                </div>
              )}

              {error.message?.includes('not found') && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4 text-left">
                  <h4 className="font-medium text-blue-800 mb-2">Order Not Found</h4>
                  <p className="text-sm text-blue-700">
                    The order with ID "{params.id}" doesn't exist or has been removed.
                  </p>
                </div>
              )}

              <div className="flex flex-col sm:flex-row gap-2 justify-center">
                <Button onClick={() => refetch()} variant="outline">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
                <Link href="/admin/debug">
                  <Button variant="ghost" size="sm">
                    Debug Console
                  </Button>
                </Link>
              </div>

              {/* Debug information for development */}
              {process.env.NODE_ENV === 'development' && (
                <details className="mt-4 text-left">
                  <summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground">
                    Debug Information
                  </summary>
                  <div className="mt-2 p-3 bg-muted rounded text-xs font-mono text-left">
                    <div><strong>Order ID:</strong> {params.id}</div>
                    <div><strong>Error:</strong> {error.message}</div>
                    <div><strong>Timestamp:</strong> {new Date().toISOString()}</div>
                  </div>
                </details>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Link href="/admin/orders">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-3xl font-bold tracking-tight">Order Not Found</h1>
        </div>
        
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="h-12 w-12 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                <Package className="h-6 w-6 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Order not found</h3>
              <p className="text-muted-foreground mb-4">
                The order you're looking for doesn't exist or has been removed.
              </p>
              <Link href="/admin/orders">
                <Button variant="outline">
                  Back to Orders
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Link href="/admin/orders">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Order {order.id}</h1>
            <p className="text-muted-foreground">
              Placed on {formatDate(order.created_at)}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {isEditing ? (
            <>
              <Button 
                variant="outline" 
                onClick={handleCancel}
                disabled={updateOrderMutation.isPending}
              >
                Cancel
              </Button>
              <Button 
                onClick={handleSave}
                disabled={updateOrderMutation.isPending}
              >
                {updateOrderMutation.isPending ? 'Saving...' : 'Save Changes'}
              </Button>
            </>
          ) : (
            <>
              <Button variant="outline" onClick={handleEdit}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
              {['cancelled', 'refunded'].includes(order.status) && (
                <Button 
                  variant="destructive" 
                  onClick={handleDelete}
                  disabled={deleteOrderMutation.isPending}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  {deleteOrderMutation.isPending ? 'Deleting...' : 'Delete'}
                </Button>
              )}
            </>
          )}
        </div>
      </div>

      {/* Order Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Package className="h-4 w-4" />
              Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isEditing ? (
              <Select
                value={editData.status}
                onValueChange={(value) => setEditData(prev => ({ ...prev, status: value as OrderStatus }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {ORDER_STATUS_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <Badge variant="secondary" className={statusColor}>
                {order.status}
              </Badge>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              Payment
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isEditing ? (
              <Select
                value={editData.payment_status}
                onValueChange={(value) => setEditData(prev => ({ ...prev, payment_status: value as PaymentStatus }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {PAYMENT_STATUS_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <Badge variant="secondary" className={paymentColor}>
                {order.payment_status}
              </Badge>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Items</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{getItemCount(order.items)}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              R {Number(order.total_amount).toLocaleString('en-ZA', { minimumFractionDigits: 2 })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Order Items */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Order Items</CardTitle>
              <CardDescription>
                Products included in this order
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {order.items.map((item, index) => (
                  <div key={index} className="flex items-center gap-4 p-4 border rounded-lg">
                    <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center overflow-hidden">
                      {item.image ? (
                        <img
                          src={item.image}
                          alt={item.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <Package className="h-6 w-6 text-muted-foreground" />
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium">{item.name}</h4>
                      <p className="text-sm text-muted-foreground">
                        Quantity: {item.quantity || 1}
                      </p>
                      {item.product_id && (
                        <p className="text-xs text-muted-foreground">
                          Product ID: {item.product_id}
                        </p>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        R {Number(item.price).toLocaleString('en-ZA', { minimumFractionDigits: 2 })}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        each
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              <Separator className="my-4" />

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Subtotal</span>
                  <span>R {Number(order.total_amount).toLocaleString('en-ZA', { minimumFractionDigits: 2 })}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Shipping</span>
                  <span>R 0.00</span>
                </div>
                <Separator />
                <div className="flex justify-between font-medium">
                  <span>Total</span>
                  <span>R {Number(order.total_amount).toLocaleString('en-ZA', { minimumFractionDigits: 2 })}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Customer
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <p className="font-medium">{order.customer_name || 'Unknown Customer'}</p>
                <p className="text-sm text-muted-foreground">{order.customer_email}</p>
              </div>
            </CardContent>
          </Card>

          {/* Shipping Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Truck className="h-4 w-4" />
                Shipping Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <p className="font-medium">{order.shipping_details.name}</p>
                <p className="text-sm text-muted-foreground">{order.shipping_details.email}</p>
                <p className="text-sm text-muted-foreground">{order.shipping_details.phone}</p>
              </div>

              <Separator />

              <div>
                <p className="text-sm font-medium mb-1">Address</p>
                <div className="text-sm text-muted-foreground space-y-1">
                  <p>{order.shipping_details.address}</p>
                  <p>{order.shipping_details.city}, {order.shipping_details.postal_code}</p>
                  <p>{order.shipping_details.country}</p>
                </div>
              </div>

              {isEditing ? (
                <div className="space-y-2">
                  <Label htmlFor="shipping_method">Shipping Method</Label>
                  <Select
                    value={editData.shipping_method}
                    onValueChange={(value) => setEditData(prev => ({ ...prev, shipping_method: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select shipping method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="standard">Standard Delivery</SelectItem>
                      <SelectItem value="express">Express Delivery</SelectItem>
                      <SelectItem value="overnight">Overnight Delivery</SelectItem>
                      <SelectItem value="pickup">Store Pickup</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              ) : order.shipping_method ? (
                <div>
                  <p className="text-sm font-medium">Shipping Method</p>
                  <p className="text-sm text-muted-foreground capitalize">{order.shipping_method}</p>
                </div>
              ) : null}
            </CardContent>
          </Card>

          {/* Payment Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-4 w-4" />
                Payment Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Payment Status</span>
                  <Badge variant="secondary" className={paymentColor}>
                    {order.payment_status}
                  </Badge>
                </div>

                {order.stripe_session_id && (
                  <div className="flex justify-between text-sm">
                    <span>Stripe Session</span>
                    <span className="font-mono text-xs">{order.stripe_session_id}</span>
                  </div>
                )}

                {order.yoco_payment_id && (
                  <div className="flex justify-between text-sm">
                    <span>Yoco Payment</span>
                    <span className="font-mono text-xs">{order.yoco_payment_id}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Notes */}
          <Card>
            <CardHeader>
              <CardTitle>Notes</CardTitle>
            </CardHeader>
            <CardContent>
              {isEditing ? (
                <div className="space-y-2">
                  <Label htmlFor="notes">Order Notes</Label>
                  <Textarea
                    id="notes"
                    placeholder="Add notes about this order..."
                    value={editData.notes}
                    onChange={(e) => setEditData(prev => ({ ...prev, notes: e.target.value }))}
                    rows={4}
                  />
                </div>
              ) : order.notes ? (
                <p className="text-sm">{order.notes}</p>
              ) : (
                <p className="text-sm text-muted-foreground italic">No notes added</p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
