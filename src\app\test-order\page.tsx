"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { OrderDetails } from "@/components/order-details";
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import { Loader2 } from "lucide-react";

export default function TestOrderPage() {
  const [orderId, setOrderId] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createTestOrder = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/test-order');
      const data = await response.json();
      
      if (response.ok) {
        setOrderId(data.orderId);
        console.log('Test order created:', data);
      } else {
        setError(data.error || 'Failed to create test order');
      }
    } catch (err: any) {
      console.error('Error creating test order:', err);
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow container mx-auto px-4 py-12">
        <h1 className="text-2xl font-bold mb-6">Test Order Page</h1>
        
        <div className="mb-8">
          <Button 
            onClick={createTestOrder}
            disabled={loading}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating Test Order...
              </>
            ) : (
              'Create Test Order'
            )}
          </Button>
          
          {error && (
            <div className="mt-4 p-4 bg-red-50 text-red-700 rounded-md">
              {error}
            </div>
          )}
        </div>
        
        {orderId && (
          <div className="mt-8">
            <h2 className="text-xl font-semibold mb-4">Order Details</h2>
            <p className="mb-4">Order ID: {orderId}</p>
            <OrderDetails sessionId={orderId} />
          </div>
        )}
      </main>
      
      <Footer />
    </div>
  );
} 