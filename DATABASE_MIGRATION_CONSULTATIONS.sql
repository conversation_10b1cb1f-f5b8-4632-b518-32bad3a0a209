-- Migration: Add location column to consultations table and fix payment amount
-- Run this in your Supabase SQL editor

-- 1. Add location column to consultations table
ALTER TABLE public.consultations 
ADD COLUMN IF NOT EXISTS location TEXT;

-- 2. Make location column NOT NULL with a default value for existing records
UPDATE public.consultations 
SET location = 'Location TBD' 
WHERE location IS NULL;

ALTER TABLE public.consultations 
ALTER COLUMN location SET NOT NULL;

-- 3. Update any existing consultations with incorrect payment amounts
-- Convert R30000 (30000 cents) to R300 (300 as whole number)
UPDATE public.consultations 
SET payment_amount = 300 
WHERE payment_amount = 30000;

-- 4. Verify the changes
SELECT 
  id,
  first_name,
  last_name,
  email,
  phone_number,
  location,
  scheduled_date,
  scheduled_time,
  payment_amount,
  payment_status,
  status,
  created_at
FROM public.consultations 
ORDER BY created_at DESC 
LIMIT 10;

-- 5. Check table structure
SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'consultations' 
  AND table_schema = 'public'
ORDER BY ordinal_position;
