import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import { StudentDashboardLayout } from "@/components/student-dashboard/layout";
import { Card } from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";

export default async function CalendarPage() {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    redirect("/auth/sign-in");
  }

  // TODO: Fetch actual sessions from Supabase
  const sessions = [
    {
      id: 1,
      date: new Date("2025-05-22T15:00:00Z"),
      title: "Serve Practice",
      duration: 60,
    },
    {
      id: 2,
      date: new Date("2025-05-25T16:00:00Z"),
      title: "Backhand Technique",
      duration: 60,
    },
  ];

  return (
    <StudentDashboardLayout activePage="calendar">
      <div className="space-y-6">
        <h1 className="text-3xl font-bold">Session Calendar</h1>
        
        <div className="grid gap-6 md:grid-cols-[1fr_300px]">
          <Card className="p-6">
            <Calendar
              mode="single"
              selected={new Date()}
              className="rounded-md border"
            />
          </Card>

          <div className="space-y-4">
            <h2 className="font-semibold">Upcoming Sessions</h2>
            {sessions.map((session) => (
              <Card key={session.id} className="p-4">
                <h3 className="font-medium">{session.title}</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  {session.date.toLocaleString()}
                </p>
                <p className="text-sm text-muted-foreground">
                  Duration: {session.duration} minutes
                </p>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </StudentDashboardLayout>
  );
}
