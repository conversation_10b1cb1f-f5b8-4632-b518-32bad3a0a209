/* Mobile-First Responsive Optimizations for Thabo Bester Platform */

/* Base Mobile Styles (320px+) */
@media (min-width: 320px) {
  /* Ensure minimum touch targets */
  button, a, input, select, textarea {
    min-height: 44px;
    min-width: 44px;
  }

  /* Optimize text for mobile reading */
  body {
    font-size: 16px;
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
  }

  /* Prevent horizontal scroll */
  html, body {
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* Mobile-optimized containers */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Mobile navigation optimizations */
  .mobile-nav-item {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }

  /* Mobile form optimizations */
  input, textarea, select {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 0.75rem;
  }

  /* Mobile card optimizations */
  .card-mobile {
    margin: 0.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  /* Mobile dashboard optimizations */
  .dashboard-mobile {
    padding: 0.75rem;
  }

  .dashboard-card {
    margin-bottom: 1rem;
    padding: 1rem;
  }

  /* Mobile table optimizations */
  .table-mobile {
    font-size: 0.875rem;
  }

  .table-mobile th,
  .table-mobile td {
    padding: 0.5rem 0.25rem;
  }

  /* Mobile modal optimizations */
  .modal-mobile {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
    overflow-y: auto;
  }
}

/* Small Mobile (375px+) */
@media (min-width: 375px) {
  .container {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .dashboard-card {
    padding: 1.25rem;
  }

  .mobile-nav-item {
    padding: 1rem 1.25rem;
  }
}

/* Large Mobile (425px+) */
@media (min-width: 425px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .dashboard-card {
    padding: 1.5rem;
  }

  /* Slightly larger text for better readability */
  .mobile-nav-item {
    font-size: 1rem;
  }
}

/* Tablet Portrait (768px+) */
@media (min-width: 768px) {
  /* Tablet-optimized containers */
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  /* Tablet dashboard optimizations */
  .dashboard-tablet {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .dashboard-card {
    padding: 2rem;
  }

  /* Tablet navigation */
  .nav-tablet {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  /* Tablet forms */
  .form-tablet {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  /* Tablet tables */
  .table-tablet {
    font-size: 1rem;
  }

  .table-tablet th,
  .table-tablet td {
    padding: 0.75rem 1rem;
  }

  /* Tablet sidebar */
  .sidebar-tablet {
    width: 240px;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
  }

  /* Tablet main content with sidebar */
  .main-content-tablet {
    margin-left: 240px;
    min-height: 100vh;
  }
}

/* Tablet Landscape (1024px+) */
@media (min-width: 1024px) {
  /* Desktop-like optimizations for large tablets */
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .dashboard-desktop {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  .dashboard-card {
    padding: 2.5rem;
  }

  /* Large tablet sidebar */
  .sidebar-desktop {
    width: 280px;
  }

  .main-content-desktop {
    margin-left: 280px;
  }
}

/* Desktop (1440px+) */
@media (min-width: 1440px) {
  .container {
    max-width: 1400px;
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .dashboard-wide {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2.5rem;
  }

  .dashboard-card {
    padding: 3rem;
  }
}

/* Large Desktop (1920px+) */
@media (min-width: 1920px) {
  .container {
    max-width: 1800px;
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .dashboard-ultra-wide {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 3rem;
  }
}

/* Performance Optimizations */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High DPI Display Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Crisp images and icons */
  img, svg {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark Mode Optimizations */
@media (prefers-color-scheme: dark) {
  .auto-dark {
    background-color: #1a1a1a;
    color: #ffffff;
  }

  .auto-dark-card {
    background-color: #2a2a2a;
    border-color: #3a3a3a;
  }
}

/* Print Optimizations */
@media print {
  .no-print {
    display: none !important;
  }

  .print-friendly {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }

  /* Optimize page breaks */
  .dashboard-card {
    break-inside: avoid;
    page-break-inside: avoid;
  }
}

/* Accessibility Optimizations */
@media (prefers-contrast: high) {
  .high-contrast {
    border: 2px solid currentColor;
  }

  .high-contrast-text {
    font-weight: 600;
  }
}

/* Focus Management */
.focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Smooth Scrolling */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Mobile Safari Optimizations */
@supports (-webkit-touch-callout: none) {
  /* iOS Safari specific fixes */
  .ios-fix {
    -webkit-overflow-scrolling: touch;
  }

  /* Fix viewport height issues */
  .full-height-ios {
    height: -webkit-fill-available;
  }
}

/* Performance: GPU Acceleration for Animations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* Responsive Images */
.responsive-image {
  max-width: 100%;
  height: auto;
  object-fit: cover;
}

/* Responsive Videos */
.responsive-video {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
}

.responsive-video iframe,
.responsive-video video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
