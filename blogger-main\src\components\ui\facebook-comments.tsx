import React, { useState, useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '../../../supabase/auth';
import { supabase } from '../../../supabase/supabase';
import { NeomorphismCard } from './neomorphism-card';
import {
  Heart,
  MessageCircle,
  Share2,
  MoreHorizontal,
  Reply,
  ThumbsUp,
  Send,
  Smile,
  Image,
  Gift,
  ChevronDown,
  ChevronUp,
  Copy,
  Flag,
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface Comment {
  id: string;
  content: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  parent_id?: string;
  likes_count: number;
  replies_count: number;
  user_liked: boolean;
  user: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
    avatar_url?: string;
    display_name: string;
  };
  replies?: Comment[];
}

interface FacebookCommentsProps {
  entityId: string;
  entityType: 'article' | 'prayer' | 'product';
  comments: Comment[];
  onCommentsUpdate: () => void;
  className?: string;
}

export function FacebookComments({
  entityId,
  entityType,
  comments,
  onCommentsUpdate,
  className,
}: FacebookCommentsProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyText, setReplyText] = useState('');
  const [showReplies, setShowReplies] = useState<Set<string>>(new Set());
  const [expandedComments, setExpandedComments] = useState<Set<string>>(new Set());
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState<string | null>(null);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.emoji-picker-container') && !target.closest('.more-options-container')) {
        setShowEmojiPicker(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user || !newComment.trim()) return;

    setIsSubmitting(true);
    try {
      // Create the insert object with proper column name
      const insertData: any = {
        content: newComment.trim(),
        user_id: user.id,
        is_approved: true,
      };

      // Add the correct foreign key column based on entity type
      if (entityType === 'article') {
        insertData.article_id = entityId;
      } else if (entityType === 'prayer') {
        insertData.prayer_id = entityId;
      } else if (entityType === 'product') {
        insertData.product_id = entityId;
      }

      console.log('Inserting comment:', insertData);

      const { data, error } = await supabase
        .from('comments')
        .insert(insertData)
        .select();

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      console.log('Comment inserted successfully:', data);

      setNewComment('');
      onCommentsUpdate();
      toast({
        title: 'Comment posted',
        description: 'Your comment has been posted successfully',
      });
    } catch (error) {
      console.error('Error posting comment:', error);
      toast({
        title: 'Error',
        description: 'Failed to post comment. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmitReply = async (parentId: string) => {
    if (!user || !replyText.trim()) return;

    setIsSubmitting(true);
    try {
      // Create the insert object with proper column name
      const insertData: any = {
        content: replyText.trim(),
        user_id: user.id,
        parent_id: parentId,
        is_approved: true,
      };

      // Add the correct foreign key column based on entity type
      if (entityType === 'article') {
        insertData.article_id = entityId;
      } else if (entityType === 'prayer') {
        insertData.prayer_id = entityId;
      } else if (entityType === 'product') {
        insertData.product_id = entityId;
      }

      console.log('Inserting reply:', insertData);

      const { data, error } = await supabase
        .from('comments')
        .insert(insertData)
        .select();

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      console.log('Reply inserted successfully:', data);

      setReplyText('');
      setReplyingTo(null);
      onCommentsUpdate();
      toast({
        title: 'Reply posted',
        description: 'Your reply has been posted successfully',
      });
    } catch (error) {
      console.error('Error posting reply:', error);
      toast({
        title: 'Error',
        description: 'Failed to post reply. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLikeComment = async (commentId: string, currentlyLiked: boolean) => {
    if (!user) {
      toast({
        title: 'Please sign in',
        description: 'You need to be signed in to like comments',
      });
      return;
    }

    try {
      if (currentlyLiked) {
        // Unlike the comment
        await supabase
          .from('comment_likes')
          .delete()
          .eq('comment_id', commentId)
          .eq('user_id', user.id);
      } else {
        // Like the comment
        await supabase
          .from('comment_likes')
          .insert({
            comment_id: commentId,
            user_id: user.id,
          });
      }

      onCommentsUpdate();
    } catch (error) {
      console.error('Error updating like:', error);
      toast({
        title: 'Error',
        description: 'Failed to update like',
        variant: 'destructive',
      });
    }
  };

  const toggleReplies = (commentId: string) => {
    const newShowReplies = new Set(showReplies);
    if (newShowReplies.has(commentId)) {
      newShowReplies.delete(commentId);
    } else {
      newShowReplies.add(commentId);
    }
    setShowReplies(newShowReplies);
  };

  const toggleCommentExpansion = (commentId: string) => {
    const newExpandedComments = new Set(expandedComments);
    if (newExpandedComments.has(commentId)) {
      newExpandedComments.delete(commentId);
    } else {
      newExpandedComments.add(commentId);
    }
    setExpandedComments(newExpandedComments);
  };

  const addEmoji = (emoji: string, targetType: 'comment' | 'reply') => {
    if (targetType === 'comment') {
      setNewComment(prev => prev + emoji);
    } else {
      setReplyText(prev => prev + emoji);
    }
    setShowEmojiPicker(null);
  };

  const handleShare = async (comment: Comment) => {
    const shareText = `Check out this comment: "${comment.content.substring(0, 100)}${comment.content.length > 100 ? '...' : ''}"`;
    const shareUrl = window.location.href;

    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Shared Comment',
          text: shareText,
          url: shareUrl,
        });
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(`${shareText} - ${shareUrl}`);
      toast({
        title: 'Link copied',
        description: 'Comment link copied to clipboard',
      });
    }
  };

  const handleCopyComment = (comment: Comment) => {
    navigator.clipboard.writeText(comment.content);
    toast({
      title: 'Comment copied',
      description: 'Comment text copied to clipboard',
    });
  };

  const truncateText = (text: string, maxLength: number = 300) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  const EmojiPicker = ({ onEmojiSelect, onClose }: { onEmojiSelect: (emoji: string) => void; onClose: () => void }) => {
    const emojis = ['😀', '😂', '😍', '🥰', '😊', '😎', '🤔', '😢', '😡', '👍', '👎', '❤️', '🔥', '💯', '🎉', '👏', '🙏', '💪', '✨', '🌟'];

    return (
      <NeomorphismCard className="absolute z-10 p-2 bg-white shadow-lg" variant="elevated">
        <div className="grid grid-cols-5 gap-1 w-48">
          {emojis.map((emoji) => (
            <button
              key={emoji}
              onClick={() => onEmojiSelect(emoji)}
              className="p-2 hover:bg-gray-100 rounded text-lg transition-colors"
            >
              {emoji}
            </button>
          ))}
        </div>
        <div className="flex justify-end mt-2">
          <Button variant="ghost" size="sm" onClick={onClose}>
            Close
          </Button>
        </div>
      </NeomorphismCard>
    );
  };

  const CommentItem = ({ comment, isReply = false }: { comment: Comment; isReply?: boolean }) => {
    const isExpanded = expandedComments.has(comment.id);
    const shouldTruncate = comment.content.length > 300;
    const displayContent = shouldTruncate && !isExpanded
      ? truncateText(comment.content, 300)
      : comment.content;

    return (
    <NeomorphismCard
      className={`p-4 ${isReply ? 'ml-12 mt-2' : 'mb-4'}`}
      variant="flat"
    >
      <div className="flex space-x-3">
        <Avatar className="h-8 w-8">
          <AvatarImage
            src={comment.user.avatar_url || `https://api.dicebear.com/7.x/avataaars/svg?seed=${comment.user.email}`}
            alt={comment.user.display_name}
          />
          <AvatarFallback className="text-xs">
            {comment.user.first_name?.[0] || comment.user.last_name?.[0] || comment.user.email[0].toUpperCase()}
          </AvatarFallback>
        </Avatar>

        <div className="flex-1 min-w-0">
          {/* Comment Content */}
          <NeomorphismCard className="p-3 bg-gray-50" variant="pressed">
            <div className="flex items-center space-x-2 mb-1">
              <span className="font-semibold text-sm text-gray-900">
                {comment.user.display_name}
              </span>
              <span className="text-xs text-gray-500">
                {formatDistanceToNow(new Date(comment.created_at), { addSuffix: true })}
              </span>
            </div>
            <p className="text-sm text-gray-800 leading-relaxed whitespace-pre-wrap">
              {displayContent}
            </p>
            {shouldTruncate && (
              <button
                onClick={() => toggleCommentExpansion(comment.id)}
                className="text-xs text-blue-600 hover:text-blue-700 font-medium mt-1 flex items-center gap-1"
              >
                {isExpanded ? (
                  <>
                    <ChevronUp className="h-3 w-3" />
                    Show less
                  </>
                ) : (
                  <>
                    <ChevronDown className="h-3 w-3" />
                    Show more
                  </>
                )}
              </button>
            )}
          </NeomorphismCard>

          {/* Action Buttons */}
          <div className="flex items-center space-x-4 mt-2 text-xs">
            <button
              onClick={() => handleLikeComment(comment.id, comment.user_liked)}
              className={`flex items-center space-x-1 hover:text-blue-600 transition-colors ${
                comment.user_liked ? 'text-blue-600 font-semibold' : 'text-gray-500'
              }`}
            >
              <ThumbsUp className={`h-3 w-3 ${comment.user_liked ? 'fill-current' : ''}`} />
              <span>{comment.likes_count > 0 ? comment.likes_count : 'Like'}</span>
            </button>

            {!isReply && (
              <button
                onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
                className="flex items-center space-x-1 text-gray-500 hover:text-blue-600 transition-colors"
              >
                <Reply className="h-3 w-3" />
                <span>Reply</span>
              </button>
            )}

            <button
              onClick={() => handleShare(comment)}
              className="flex items-center space-x-1 text-gray-500 hover:text-blue-600 transition-colors"
            >
              <Share2 className="h-3 w-3" />
              <span>Share</span>
            </button>

            <div className="relative more-options-container">
              <button
                onClick={() => setShowEmojiPicker(showEmojiPicker === comment.id ? null : comment.id)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <MoreHorizontal className="h-3 w-3" />
              </button>

              {showEmojiPicker === comment.id && (
                <div className="absolute top-6 right-0 z-20 more-options-container">
                  <NeomorphismCard className="p-2 bg-white shadow-lg" variant="elevated">
                    <div className="flex flex-col space-y-1 min-w-[120px]">
                      <button
                        onClick={() => handleCopyComment(comment)}
                        className="flex items-center space-x-2 px-2 py-1 text-xs text-gray-600 hover:bg-gray-100 rounded transition-colors"
                      >
                        <Copy className="h-3 w-3" />
                        <span>Copy text</span>
                      </button>
                      <button
                        onClick={() => handleShare(comment)}
                        className="flex items-center space-x-2 px-2 py-1 text-xs text-gray-600 hover:bg-gray-100 rounded transition-colors"
                      >
                        <Share2 className="h-3 w-3" />
                        <span>Share</span>
                      </button>
                      <button
                        className="flex items-center space-x-2 px-2 py-1 text-xs text-red-600 hover:bg-red-50 rounded transition-colors"
                      >
                        <Flag className="h-3 w-3" />
                        <span>Report</span>
                      </button>
                    </div>
                  </NeomorphismCard>
                </div>
              )}
            </div>
          </div>

          {/* Reply Form */}
          {replyingTo === comment.id && (
            <div className="mt-3 flex space-x-2">
              <Avatar className="h-6 w-6">
                <AvatarImage
                  src={user?.user_metadata?.avatar_url || `https://api.dicebear.com/7.x/avataaars/svg?seed=${user?.email}`}
                  alt="Your avatar"
                />
                <AvatarFallback className="text-xs">
                  {user?.email?.[0]?.toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <Textarea
                  value={replyText}
                  onChange={(e) => setReplyText(e.target.value)}
                  placeholder={`Reply to ${comment.user.display_name}...`}
                  className="min-h-[60px] text-sm resize-none"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSubmitReply(comment.id);
                    }
                  }}
                />
                <div className="flex justify-between items-center mt-2">
                  <div className="flex space-x-1 relative emoji-picker-container">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={() => setShowEmojiPicker(showEmojiPicker === `reply-${comment.id}` ? null : `reply-${comment.id}`)}
                    >
                      <Smile className="h-3 w-3" />
                    </Button>
                    <Button variant="ghost" size="sm" className="h-6 w-6 p-0" disabled>
                      <Image className="h-3 w-3" />
                    </Button>
                    <Button variant="ghost" size="sm" className="h-6 w-6 p-0" disabled>
                      <Gift className="h-3 w-3" />
                    </Button>

                    {showEmojiPicker === `reply-${comment.id}` && (
                      <div className="absolute top-8 left-0 z-20 emoji-picker-container">
                        <EmojiPicker
                          onEmojiSelect={(emoji) => addEmoji(emoji, 'reply')}
                          onClose={() => setShowEmojiPicker(null)}
                        />
                      </div>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setReplyingTo(null);
                        setReplyText('');
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleSubmitReply(comment.id)}
                      disabled={!replyText.trim() || isSubmitting}
                    >
                      <Send className="h-3 w-3 mr-1" />
                      Reply
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Show Replies Button */}
          {comment.replies_count > 0 && !isReply && (
            <button
              onClick={() => toggleReplies(comment.id)}
              className="flex items-center space-x-1 mt-2 text-xs text-gray-500 hover:text-blue-600 transition-colors"
            >
              <MessageCircle className="h-3 w-3" />
              <span>
                {showReplies.has(comment.id) ? 'Hide' : 'View'} {comment.replies_count} {comment.replies_count === 1 ? 'reply' : 'replies'}
              </span>
            </button>
          )}

          {/* Replies */}
          {showReplies.has(comment.id) && comment.replies && comment.replies.length > 0 && (
            <div className="mt-2">
              {comment.replies.map((reply) => (
                <CommentItem key={reply.id} comment={reply} isReply={true} />
              ))}
            </div>
          )}
        </div>
      </div>
    </NeomorphismCard>
    );
  };

  return (
    <div className={className}>
      {/* Comments Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          Comments ({comments.length})
        </h3>
      </div>

      {/* Comment Form */}
      {user ? (
        <NeomorphismCard className="p-4 mb-6" variant="default">
          <form onSubmit={handleSubmitComment}>
            <div className="flex space-x-3">
              <Avatar className="h-8 w-8">
                <AvatarImage
                  src={user.user_metadata?.avatar_url || `https://api.dicebear.com/7.x/avataaars/svg?seed=${user.email}`}
                  alt="Your avatar"
                />
                <AvatarFallback className="text-xs">
                  {user.email?.[0]?.toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <Textarea
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder="Write a comment..."
                  className="min-h-[80px] resize-none border-0 bg-gray-50 focus:bg-white transition-colors"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSubmitComment(e);
                    }
                  }}
                />
                <div className="flex justify-between items-center mt-3">
                  <div className="flex space-x-1 relative emoji-picker-container">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={() => setShowEmojiPicker(showEmojiPicker === 'main-comment' ? null : 'main-comment')}
                    >
                      <Smile className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0" disabled>
                      <Image className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0" disabled>
                      <Gift className="h-4 w-4" />
                    </Button>

                    {showEmojiPicker === 'main-comment' && (
                      <div className="absolute top-10 left-0 z-20 emoji-picker-container">
                        <EmojiPicker
                          onEmojiSelect={(emoji) => addEmoji(emoji, 'comment')}
                          onClose={() => setShowEmojiPicker(null)}
                        />
                      </div>
                    )}
                  </div>
                  <Button
                    type="submit"
                    disabled={!newComment.trim() || isSubmitting}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <Send className="h-4 w-4 mr-2" />
                    {isSubmitting ? 'Posting...' : 'Post'}
                  </Button>
                </div>
              </div>
            </div>
          </form>
        </NeomorphismCard>
      ) : (
        <NeomorphismCard className="p-6 mb-6 text-center" variant="flat">
          <p className="text-gray-600 mb-3">Sign in to join the conversation</p>
          <Button>Sign In</Button>
        </NeomorphismCard>
      )}

      {/* Comments List */}
      <div className="space-y-1">
        {comments.length > 0 ? (
          comments.map((comment) => (
            <CommentItem key={comment.id} comment={comment} />
          ))
        ) : (
          <NeomorphismCard className="p-8 text-center" variant="flat">
            <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-500">No comments yet. Be the first to share your thoughts!</p>
          </NeomorphismCard>
        )}
      </div>
    </div>
  );
}
