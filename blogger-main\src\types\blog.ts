export interface Article {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
    bio?: string;
  };
  category: {
    id: string;
    name: string;
    slug: string;
  };
  tags: string[];
  featuredImage?: string;
  isPremium: boolean;
  isPublished: boolean;
  publishedAt: Date;
  createdAt: Date;
  updatedAt: Date;
  readTime: number; // in minutes
  views: number;
  likes: number;
  comments: number;
  shares: number;
  seoTitle?: string;
  seoDescription?: string;
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  color?: string;
  articleCount: number;
}

export interface Comment {
  id: string;
  articleId: string;
  userId: string;
  author: {
    name: string;
    avatar?: string;
  };
  content: string;
  parentId?: string; // for nested comments
  likes: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Newsletter {
  id: string;
  email: string;
  isActive: boolean;
  subscribedAt: Date;
  preferences: {
    daily: boolean;
    weekly: boolean;
    breaking: boolean;
    categories: string[];
  };
}

export interface Subscription {
  id: string;
  userId: string;
  plan: 'free' | 'premium' | 'pro';
  status: 'active' | 'cancelled' | 'expired';
  startDate: Date;
  endDate?: Date;
  stripeSubscriptionId?: string;
}

export interface ArticleEngagement {
  articleId: string;
  userId: string;
  liked: boolean;
  bookmarked: boolean;
  shared: boolean;
  readProgress: number; // percentage
  timeSpent: number; // in seconds
}
