"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { ArrowR<PERSON>, Check, Star, Users, Award, Z<PERSON>, MouseP<PERSON>er, <PERSON>rk<PERSON> } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import Ballpit from "../ballpit";

export default function HeroBallpit() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [ballCount, setBallCount] = useState(150);
  const [gravity, setGravity] = useState(0.5);
  const [friction, setFriction] = useState(0.98);

  useEffect(() => {
    // Simulate loading delay for smooth transition
    const timer = setTimeout(() => setIsLoaded(true), 500);
    return () => clearTimeout(timer);
  }, []);

  // Adjust ball count based on screen size for performance
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setBallCount(80); // Mobile: fewer balls for performance
      } else if (width < 1024) {
        setBallCount(120); // Tablet: moderate ball count
      } else {
        setBallCount(150); // Desktop: full ball count
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-slate-50 to-green-50/30 py-20">
      {/* Subtle court texture background */}
      <div className="absolute inset-0 bg-[radial-gradient(#10b981_0.5px,transparent_0.5px)] [background-size:24px_24px] opacity-10" />
      
      {/* Subtle atmospheric gradient */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-green-500/5 to-transparent" />

      <div className="container mx-auto px-4">
        <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-16">
          {/* Left Column - Text + CTA */}
          <div className="lg:w-1/2 text-center lg:text-left relative z-10">
            {/* Main Headline */}
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 tracking-tight leading-tight text-slate-900">
              Interactive tennis gear
              <span className="block text-green-600">with physics simulation</span>
            </h1>

            {/* Subheadline */}
            <p className="text-lg md:text-xl text-slate-600 mb-10 max-w-2xl mx-auto lg:mx-0 leading-relaxed">
              Experience our premium tennis equipment through an immersive 3D physics playground. 
              Move your cursor to interact with floating tennis balls.
            </p>

            {/* Feature highlights */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-10">
              <div className="flex items-center gap-3 bg-white/80 backdrop-blur-sm rounded-xl p-4 neo-shadow-light">
                <MousePointer className="w-5 h-5 text-green-600 flex-shrink-0" />
                <span className="text-sm font-medium text-slate-700">Interactive Physics</span>
              </div>
              <div className="flex items-center gap-3 bg-white/80 backdrop-blur-sm rounded-xl p-4 neo-shadow-light">
                <Sparkles className="w-5 h-5 text-green-600 flex-shrink-0" />
                <span className="text-sm font-medium text-slate-700">Real-time Simulation</span>
              </div>
              <div className="flex items-center gap-3 bg-white/80 backdrop-blur-sm rounded-xl p-4 neo-shadow-light">
                <Zap className="w-5 h-5 text-green-600 flex-shrink-0" />
                <span className="text-sm font-medium text-slate-700">High Performance</span>
              </div>
              <div className="flex items-center gap-3 bg-white/80 backdrop-blur-sm rounded-xl p-4 neo-shadow-light">
                <Award className="w-5 h-5 text-green-600 flex-shrink-0" />
                <span className="text-sm font-medium text-slate-700">Premium Quality</span>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Button 
                asChild 
                size="lg" 
                className="bg-green-600 hover:bg-green-700 text-white px-8 py-4 rounded-xl neo-shadow hover:neo-shadow-hover transition-all duration-300 min-h-[44px]"
              >
                <Link href="/shop" className="flex items-center gap-2">
                  Shop Tennis Gear
                  <ArrowRight className="w-5 h-5" />
                </Link>
              </Button>
              
              <Button 
                asChild 
                variant="outline" 
                size="lg" 
                className="border-green-600 text-green-600 hover:bg-green-50 px-8 py-4 rounded-xl neo-shadow-light hover:neo-shadow transition-all duration-300 min-h-[44px]"
              >
                <Link href="/mentorship">
                  Explore Mentorship
                </Link>
              </Button>
            </div>

            {/* Trust indicators */}
            <div className="flex flex-wrap items-center justify-center lg:justify-start gap-6 mt-8 text-sm text-slate-600">
              <div className="flex items-center gap-2">
                <Star className="w-4 h-4 text-yellow-500" />
                <span>4.9/5 Rating</span>
              </div>
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4 text-green-600" />
                <span>2,000+ Players</span>
              </div>
              <div className="flex items-center gap-2">
                <Check className="w-4 h-4 text-green-600" />
                <span>Pro Approved</span>
              </div>
            </div>
          </div>

          {/* Right Column - Interactive Ballpit */}
          <div className="lg:w-1/2 relative w-full">
            <div className="relative h-[400px] sm:h-[500px] lg:h-[600px] w-full rounded-2xl overflow-hidden neo-shadow-hover">
              {/* Ballpit Container */}
              <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-slate-800 to-green-900">
                {isLoaded && (
                  <Ballpit
                    count={ballCount}
                    gravity={gravity}
                    friction={friction}
                    wallBounce={0.95}
                    followCursor={true}
                    colors={[0x10b981, 0x059669, 0x047857, 0x065f46]} // Green gradient colors
                    className="w-full h-full"
                  />
                )}
              </div>

              {/* Loading overlay */}
              {!isLoaded && (
                <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-slate-800 to-green-900 flex items-center justify-center">
                  <div className="text-center text-white">
                    <div className="w-16 h-16 mx-auto mb-4 bg-green-600 rounded-full flex items-center justify-center animate-pulse">
                      <Zap className="w-8 h-8" />
                    </div>
                    <p className="text-lg font-medium">Loading Physics Engine...</p>
                  </div>
                </div>
              )}

              {/* Interactive hint overlay */}
              <div className="absolute top-4 left-4 right-4">
                <div className="bg-white/90 backdrop-blur-sm rounded-lg p-3 neo-shadow-light">
                  <div className="flex items-center gap-2 text-sm">
                    <MousePointer className="w-4 h-4 text-green-600" />
                    <span className="text-slate-700 font-medium">Move your cursor to interact with the tennis balls</span>
                  </div>
                </div>
              </div>

              {/* Physics controls overlay */}
              <div className="absolute bottom-4 left-4 right-4">
                <div className="bg-white/90 backdrop-blur-sm rounded-lg p-4 neo-shadow-light">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-xs text-slate-500 mb-1">Balls</div>
                      <div className="text-lg font-bold text-green-600">{ballCount}</div>
                    </div>
                    <div>
                      <div className="text-xs text-slate-500 mb-1">Gravity</div>
                      <div className="text-lg font-bold text-green-600">{gravity}</div>
                    </div>
                    <div>
                      <div className="text-xs text-slate-500 mb-1">Friction</div>
                      <div className="text-lg font-bold text-green-600">{friction}</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Subtle accent border */}
              <div className="absolute inset-0 rounded-2xl ring-1 ring-green-500/20"></div>
            </div>

            {/* Mobile: Additional engaging content below ballpit */}
            <div className="mt-6 lg:hidden">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 neo-shadow-light border border-green-100">
                  <div className="flex items-center gap-2 mb-2">
                    <Zap className="w-4 h-4 text-green-600" />
                    <span className="text-sm font-semibold text-slate-800">Real-time</span>
                  </div>
                  <p className="text-xs text-slate-600">Physics Simulation</p>
                </div>
                
                <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 neo-shadow-light border border-green-100">
                  <div className="flex items-center gap-2 mb-2">
                    <MousePointer className="w-4 h-4 text-green-600" />
                    <span className="text-sm font-semibold text-slate-800">Interactive</span>
                  </div>
                  <p className="text-xs text-slate-600">Cursor Following</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Feature bar underneath */}
      <div className="mt-16 pt-8 border-t border-green-100/50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
            <div className="flex flex-col items-center gap-2">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <Zap className="w-6 h-6 text-green-600" />
              </div>
              <span className="text-sm font-medium text-slate-700">High Performance</span>
            </div>
            
            <div className="flex flex-col items-center gap-2">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <MousePointer className="w-6 h-6 text-green-600" />
              </div>
              <span className="text-sm font-medium text-slate-700">Interactive Experience</span>
            </div>
            
            <div className="flex flex-col items-center gap-2">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <Sparkles className="w-6 h-6 text-green-600" />
              </div>
              <span className="text-sm font-medium text-slate-700">Realistic Physics</span>
            </div>
            
            <div className="flex flex-col items-center gap-2">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <Award className="w-6 h-6 text-green-600" />
              </div>
              <span className="text-sm font-medium text-slate-700">Premium Quality</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
