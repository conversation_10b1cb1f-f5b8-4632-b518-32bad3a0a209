import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { createClient } from '@/utils/supabase/client';
import { 
  Order, 
  OrderFilters, 
  OrderUpdateData, 
  OrderStats,
  PaginatedOrders,
  OrdersResponse,
  OrderResponse,
  OrderStatsResponse 
} from '@/types/orders';

/**
 * React Query hooks for Orders Management
 * Follows the established pattern: hooks → API routes → admin auth → service role client
 */

// Query Keys
export const orderKeys = {
  all: ['orders'] as const,
  lists: () => [...orderKeys.all, 'list'] as const,
  list: (filters: OrderFilters & { page?: number; limit?: number }) => 
    [...orderKeys.lists(), filters] as const,
  details: () => [...orderKeys.all, 'detail'] as const,
  detail: (id: string) => [...orderKeys.details(), id] as const,
  stats: () => [...orderKeys.all, 'stats'] as const,
};

/**
 * Fetch paginated orders with filtering
 */
export function useOrders(
  filters: OrderFilters & { page?: number; limit?: number } = {}
) {
  return useQuery({
    queryKey: orderKeys.list(filters),
    queryFn: async (): Promise<PaginatedOrders> => {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('Authentication required');
      }

      // Build query parameters
      const params = new URLSearchParams();
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());
      if (filters.status) params.append('status', filters.status);
      if (filters.payment_status) params.append('payment_status', filters.payment_status);
      if (filters.search) params.append('search', filters.search);
      if (filters.date_from) params.append('date_from', filters.date_from);
      if (filters.date_to) params.append('date_to', filters.date_to);

      const response = await fetch(`/api/admin/orders?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to fetch orders');
      }

      const result: OrdersResponse = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch orders');
      }

      return result.data;
    },
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Fetch a specific order by ID
 */
export function useOrder(id: string) {
  return useQuery({
    queryKey: orderKeys.detail(id),
    queryFn: async (): Promise<Order> => {
      console.log('🔍 useOrder - Fetching order:', id);

      const supabase = createClient();
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      if (sessionError) {
        console.error('❌ useOrder - Session error:', sessionError);
        throw new Error(`Authentication error: ${sessionError.message}`);
      }

      if (!session) {
        console.error('❌ useOrder - No session found');
        throw new Error('Authentication required - please sign in');
      }

      console.log('✅ useOrder - Session found, making API request');

      const response = await fetch(`/api/admin/orders/${id}`, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      console.log('📡 useOrder - API response status:', response.status);

      if (!response.ok) {
        let errorMessage = 'Failed to fetch order';
        let errorDetails = '';

        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
          errorDetails = errorData.details || '';

          console.error('❌ useOrder - API error:', {
            status: response.status,
            error: errorMessage,
            details: errorDetails,
            debug: errorData.debug
          });

          // Provide more specific error messages based on status
          switch (response.status) {
            case 401:
              throw new Error('Authentication failed - please sign in again');
            case 403:
              throw new Error('Admin access required - insufficient permissions');
            case 404:
              throw new Error(`Order not found - ID: ${id}`);
            case 500:
              throw new Error(`Server error: ${errorMessage}${errorDetails ? ` (${errorDetails})` : ''}`);
            default:
              throw new Error(`${errorMessage}${errorDetails ? ` - ${errorDetails}` : ''}`);
          }
        } catch (parseError) {
          console.error('❌ useOrder - Failed to parse error response:', parseError);
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      }

      try {
        const result: OrderResponse = await response.json();

        console.log('📦 useOrder - API response:', {
          success: result.success,
          hasData: !!result.data,
          orderId: result.data?.id
        });

        if (!result.success) {
          throw new Error(result.error || 'API returned unsuccessful response');
        }

        if (!result.data) {
          throw new Error('No order data received from API');
        }

        console.log('✅ useOrder - Order fetched successfully:', result.data.id);
        return result.data;

      } catch (parseError) {
        console.error('❌ useOrder - Failed to parse response:', parseError);
        throw new Error('Invalid response format from server');
      }
    },
    enabled: !!id && id.trim() !== '',
    staleTime: 30 * 1000,
    gcTime: 5 * 60 * 1000,
    retry: (failureCount, error) => {
      console.log(`🔄 useOrder - Retry attempt ${failureCount} for error:`, error.message);

      // Don't retry on authentication or permission errors
      if (error.message.includes('Authentication') ||
          error.message.includes('Admin access') ||
          error.message.includes('not found')) {
        return false;
      }

      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

/**
 * Fetch order statistics for dashboard
 */
export function useOrderStats() {
  return useQuery({
    queryKey: orderKeys.stats(),
    queryFn: async (): Promise<OrderStats> => {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('Authentication required');
      }

      const response = await fetch('/api/admin/orders', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to fetch order statistics');
      }

      const result: OrderStatsResponse = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch order statistics');
      }

      return result.data;
    },
    staleTime: 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000,
  });
}

/**
 * Update an order (status, notes, shipping method, etc.)
 */
export function useUpdateOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: OrderUpdateData }): Promise<Order> => {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('Authentication required');
      }

      const response = await fetch(`/api/admin/orders/${id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update order');
      }

      const result: OrderResponse = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Failed to update order');
      }

      return result.data;
    },
    onSuccess: (updatedOrder) => {
      // Update the specific order in cache
      queryClient.setQueryData(orderKeys.detail(updatedOrder.id), updatedOrder);
      
      // Invalidate orders list to refresh with updated data
      queryClient.invalidateQueries({ queryKey: orderKeys.lists() });
      
      // Invalidate stats to refresh dashboard metrics
      queryClient.invalidateQueries({ queryKey: orderKeys.stats() });
    },
  });
}

/**
 * Delete an order (admin only, for cancelled/refunded orders)
 */
export function useDeleteOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('Authentication required');
      }

      const response = await fetch(`/api/admin/orders/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete order');
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete order');
      }
    },
    onSuccess: (_, deletedId) => {
      // Remove the order from cache
      queryClient.removeQueries({ queryKey: orderKeys.detail(deletedId) });
      
      // Invalidate orders list to refresh
      queryClient.invalidateQueries({ queryKey: orderKeys.lists() });
      
      // Invalidate stats to refresh dashboard metrics
      queryClient.invalidateQueries({ queryKey: orderKeys.stats() });
    },
  });
}

/**
 * Utility hook to get order status color
 */
export function useOrderStatusColor(status: string) {
  const colors = {
    pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    confirmed: 'bg-blue-100 text-blue-800 border-blue-200',
    processing: 'bg-purple-100 text-purple-800 border-purple-200',
    shipped: 'bg-indigo-100 text-indigo-800 border-indigo-200',
    delivered: 'bg-green-100 text-green-800 border-green-200',
    cancelled: 'bg-red-100 text-red-800 border-red-200',
    refunded: 'bg-gray-100 text-gray-800 border-gray-200',
  };
  
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200';
}

/**
 * Utility hook to get payment status color
 */
export function usePaymentStatusColor(status: string) {
  const colors = {
    pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    paid: 'bg-green-100 text-green-800 border-green-200',
    failed: 'bg-red-100 text-red-800 border-red-200',
    refunded: 'bg-gray-100 text-gray-800 border-gray-200',
    partially_refunded: 'bg-orange-100 text-orange-800 border-orange-200',
  };
  
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200';
}
