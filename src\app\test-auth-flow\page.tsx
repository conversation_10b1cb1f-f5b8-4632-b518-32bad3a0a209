"use client";

import { useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function TestAuthFlow() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [adminCode, setAdminCode] = useState('TENNIS_ADMIN_2024');
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const supabase = createClient();

  const testDatabaseInsert = async () => {
    setLoading(true);
    setResults([]);

    try {
      console.log("Testing direct database insert...");

      const response = await fetch('/api/test-db-insert', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email,
          fullName: fullName,
          role: 'admin'
        }),
      });

      const result = await response.json();

      if (result.success) {
        setResults([{
          test: "Direct Database Insert",
          status: "passed",
          message: "Database insert works correctly"
        }]);
      } else {
        setResults([{
          test: "Direct Database Insert",
          status: "failed",
          error: `${result.error.message} (Code: ${result.error.code})`
        }]);
      }
    } catch (error: any) {
      setResults([{
        test: "Direct Database Insert",
        status: "failed",
        error: error.message
      }]);
    }

    setLoading(false);
  };

  const testAuthSignUp = async () => {
    setLoading(true);
    setResults([]);

    try {
      console.log("Testing auth.signUp API...");

      const response = await fetch('/api/test-auth-signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email,
          password: password,
          fullName: fullName
        }),
      });

      const result = await response.json();

      if (result.success) {
        setResults([{
          test: "Auth SignUp API",
          status: "passed",
          message: `Auth signup successful. Profile role: ${result.profile.role}`
        }]);
      } else {
        setResults([{
          test: "Auth SignUp API",
          status: "failed",
          error: `${result.error.message} (Code: ${result.error.code || 'unknown'})`
        }]);
      }
    } catch (error: any) {
      setResults([{
        test: "Auth SignUp API",
        status: "failed",
        error: error.message
      }]);
    }

    setLoading(false);
  };

  const testAdminSignUp = async () => {
    setLoading(true);
    setResults([]);

    try {
      console.log("Testing admin sign-up...");

      // Test admin sign-up
      const { data: { user }, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            email: email,
            role: 'admin',
          }
        },
      });

      if (error) {
        setResults([{
          test: "Admin Sign-Up",
          status: "failed",
          error: `${error.message} (Code: ${error.code || 'unknown'})`
        }]);
        setLoading(false);
        return;
      }

      if (user) {
        setResults([{
          test: "Admin Sign-Up",
          status: "passed",
          message: `User created in auth.users: ${user.id}`
        }]);

        // Wait for trigger to create profile
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Check if profile was created
        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .single();

        if (profileError) {
          setResults(prev => [...prev, {
            test: "Profile Creation",
            status: "failed",
            error: `${profileError.message} (Code: ${profileError.code})`
          }]);
        } else {
          setResults(prev => [...prev, {
            test: "Profile Creation",
            status: "passed",
            message: `Profile created with role: ${profile.role}`
          }]);
        }
      }
    } catch (error: any) {
      setResults([{
        test: "Admin Sign-Up",
        status: "failed",
        error: error.message
      }]);
    }

    setLoading(false);
  };

  const testAdminSignIn = async () => {
    setLoading(true);
    
    try {
      console.log("Testing admin sign-in...");
      
      const { data: { session }, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        setResults([{
          test: "Admin Sign-In",
          status: "failed",
          error: error.message
        }]);
        setLoading(false);
        return;
      }

      if (session) {
        setResults([{
          test: "Admin Sign-In",
          status: "passed",
          message: `Signed in successfully: ${session.user.email}`
        }]);

        // Check user profile
        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('*')
          .eq('id', session.user.id)
          .single();

        if (profileError) {
          setResults(prev => [...prev, {
            test: "Profile Access",
            status: "failed",
            error: profileError.message
          }]);
        } else {
          setResults(prev => [...prev, {
            test: "Profile Access",
            status: "passed",
            message: `Profile accessed with role: ${profile.role}`
          }]);
        }
      }
    } catch (error: any) {
      setResults([{
        test: "Admin Sign-In",
        status: "failed",
        error: error.message
      }]);
    }
    
    setLoading(false);
  };

  const testCurrentSession = async () => {
    setLoading(true);
    
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        setResults([{
          test: "Current Session",
          status: "failed",
          error: error.message
        }]);
      } else if (session) {
        setResults([{
          test: "Current Session",
          status: "passed",
          message: `Active session for: ${session.user.email}`
        }]);
      } else {
        setResults([{
          test: "Current Session",
          status: "info",
          message: "No active session"
        }]);
      }
    } catch (error: any) {
      setResults([{
        test: "Current Session",
        status: "failed",
        error: error.message
      }]);
    }
    
    setLoading(false);
  };

  const signOut = async () => {
    await supabase.auth.signOut();
    setResults([{
      test: "Sign Out",
      status: "passed",
      message: "Signed out successfully"
    }]);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'passed': return 'text-green-600';
      case 'failed': return 'text-red-600';
      case 'info': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'passed': return '✅';
      case 'failed': return '❌';
      case 'info': return 'ℹ️';
      default: return '❓';
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>Authentication Flow Test</CardTitle>
          <CardDescription>
            Test admin sign-up and sign-in functionality
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Email</label>
              <Input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Password</label>
              <Input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="SecurePassword123"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Full Name</label>
              <Input
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
                placeholder="Test Admin"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Admin Code</label>
              <Input
                value={adminCode}
                onChange={(e) => setAdminCode(e.target.value)}
                placeholder="TENNIS_ADMIN_2024"
              />
            </div>
          </div>

          <div className="flex flex-wrap gap-2">
            <Button onClick={testDatabaseInsert} disabled={loading || !email || !fullName}>
              Test Database Insert
            </Button>
            <Button onClick={testAuthSignUp} disabled={loading || !email || !password || !fullName}>
              Test Auth SignUp API
            </Button>
            <Button onClick={testAdminSignUp} disabled={loading || !email || !password || !fullName}>
              Test Admin Sign-Up
            </Button>
            <Button onClick={testAdminSignIn} disabled={loading || !email || !password}>
              Test Admin Sign-In
            </Button>
            <Button onClick={testCurrentSession} disabled={loading}>
              Check Current Session
            </Button>
            <Button onClick={signOut} variant="outline">
              Sign Out
            </Button>
          </div>

          {results.length > 0 && (
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Test Results:</h3>
              {results.map((result, index) => (
                <Alert key={index}>
                  <AlertDescription>
                    <div className="flex items-center gap-2">
                      <span className="text-xl">{getStatusIcon(result.status)}</span>
                      <span className="font-medium">{result.test}</span>
                      <span className={`text-sm ${getStatusColor(result.status)}`}>
                        {result.status.toUpperCase()}
                      </span>
                    </div>
                    {result.message && (
                      <p className="text-sm text-gray-600 mt-1">{result.message}</p>
                    )}
                    {result.error && (
                      <p className="text-sm text-red-600 mt-1 font-mono bg-red-50 p-2 rounded">
                        {result.error}
                      </p>
                    )}
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          )}

          <Alert>
            <AlertDescription>
              <strong>Instructions:</strong>
              <ol className="list-decimal list-inside mt-2 space-y-1 text-sm">
                <li>Fill in the form with test credentials</li>
                <li>Click "Test Admin Sign-Up" to test the sign-up flow</li>
                <li>Click "Test Admin Sign-In" to test the sign-in flow</li>
                <li>Use "Check Current Session" to verify authentication state</li>
                <li>Use "Sign Out" to clear the session</li>
              </ol>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  );
}
