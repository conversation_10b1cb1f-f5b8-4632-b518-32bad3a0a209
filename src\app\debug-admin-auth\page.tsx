"use client";

import { useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function DebugAdminAuth() {
  const [results, setResults] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const supabase = createClient();

  const log = (message: string) => {
    console.log(message);
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const runFullDiagnostic = async () => {
    setLoading(true);
    setResults([]);
    
    try {
      log("🔍 Starting comprehensive admin auth diagnostic...");

      // Test 1: Check database connection
      log("📡 Testing database connection...");
      const { data: connectionTest, error: connectionError } = await supabase
        .from('users')
        .select('count')
        .limit(1);
      
      if (connectionError) {
        log(`❌ Database connection failed: ${connectionError.message}`);
        return;
      }
      log("✅ Database connection successful");

      // Test 2: Check current session
      log("🔐 Checking current session...");
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      if (sessionError) {
        log(`❌ Session check failed: ${sessionError.message}`);
      } else if (session) {
        log(`✅ Active session for: ${session.user.email}`);
      } else {
        log("ℹ️ No active session");
      }

      // Test 3: Test direct database insert
      log("💾 Testing direct database insert...");
      const testId = crypto.randomUUID();
      const { data: insertData, error: insertError } = await supabase
        .from('users')
        .insert([{
          id: testId,
          email: '<EMAIL>',
          full_name: 'Debug Test User',
          name: 'Debug Test User',
          role: 'admin'
        }])
        .select()
        .single();

      if (insertError) {
        log(`❌ Direct insert failed: ${insertError.message} (Code: ${insertError.code})`);
        log(`   Details: ${JSON.stringify(insertError.details)}`);
        log(`   Hint: ${insertError.hint}`);
      } else {
        log("✅ Direct insert successful");
        // Clean up
        await supabase.from('users').delete().eq('id', testId);
        log("🧹 Test user cleaned up");
      }

      // Test 4: Test auth.signUp
      log("🔑 Testing auth.signUp...");
      const testEmail = `debug-${Date.now()}@example.com`;
      const { data: { user }, error: signUpError } = await supabase.auth.signUp({
        email: testEmail,
        password: 'TestPassword123!',
        options: {
          data: {
            full_name: 'Debug Sign Up Test',
            email: testEmail,
            role: 'admin'
          }
        }
      });

      if (signUpError) {
        log(`❌ Auth sign-up failed: ${signUpError.message} (Code: ${signUpError.code})`);
      } else if (user) {
        log(`✅ Auth sign-up successful: ${user.id}`);
        log(`   User metadata: ${JSON.stringify(user.user_metadata)}`);
        
        // Wait for trigger
        log("⏳ Waiting 3 seconds for trigger to create profile...");
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Check if profile was created
        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .single();
        
        if (profileError) {
          log(`❌ Profile check failed: ${profileError.message} (Code: ${profileError.code})`);
          
          // Try manual creation
          log("🔧 Attempting manual profile creation...");
          const { data: manualProfile, error: manualError } = await supabase
            .from('users')
            .insert([{
              id: user.id,
              email: testEmail,
              full_name: 'Debug Sign Up Test',
              name: 'Debug Sign Up Test',
              role: 'admin'
            }])
            .select()
            .single();
          
          if (manualError) {
            log(`❌ Manual profile creation failed: ${manualError.message} (Code: ${manualError.code})`);
            log(`   Details: ${JSON.stringify(manualError.details)}`);
            log(`   Hint: ${manualError.hint}`);
          } else {
            log("✅ Manual profile creation successful");
          }
        } else {
          log(`✅ Profile created by trigger: Role = ${profile.role}`);
        }
      }

      log("🎯 Diagnostic complete!");

    } catch (error: any) {
      log(`💥 Unexpected error: ${error.message}`);
      console.error("Full error:", error);
    }
    
    setLoading(false);
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>Admin Authentication Debug Tool</CardTitle>
          <CardDescription>
            Comprehensive diagnostic tool to identify admin authentication issues
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={runFullDiagnostic} disabled={loading}>
              {loading ? 'Running Diagnostic...' : 'Run Full Diagnostic'}
            </Button>
            <Button onClick={clearResults} variant="outline" disabled={loading}>
              Clear Results
            </Button>
          </div>

          {results.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Diagnostic Results:</h3>
              <div className="bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
                <pre className="text-sm whitespace-pre-wrap font-mono">
                  {results.join('\n')}
                </pre>
              </div>
            </div>
          )}

          <Alert>
            <AlertDescription>
              <strong>This tool will:</strong>
              <ul className="list-disc list-inside mt-2 space-y-1 text-sm">
                <li>Test database connectivity and permissions</li>
                <li>Check current authentication session</li>
                <li>Test direct database insertion</li>
                <li>Test auth.signUp with trigger functionality</li>
                <li>Attempt manual profile creation if trigger fails</li>
                <li>Provide detailed error messages and codes</li>
              </ul>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  );
}
