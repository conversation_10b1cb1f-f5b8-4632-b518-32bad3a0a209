import { NextResponse } from 'next/server';
import yocoConfig from '@/lib/yoco';
import { createClient } from '@/utils/supabase/server';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const orderId = searchParams.get('order_id');

    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      );
    }

    // Fetch the order from Supabase
    try {
      const supabase = await createClient();
      
      // Check if this is a mentorship subscription or regular order
      const isMentorship = orderId.startsWith('TEC-MENTOR');
      
      if (isMentorship) {
        const { data: subscription, error } = await supabase
          .from('mentorship_subscriptions')
          .select('*')
          .eq('id', orderId)
          .single();
          
        if (error) {
          throw new Error('Subscription not found');
        }
        
        return NextResponse.json({ session: subscription });
      } else {
        const { data: order, error } = await supabase
          .from('orders')
          .select('*')
          .eq('id', orderId)
          .single();
          
        if (error) {
          throw new Error('Order not found');
        }
        
        return NextResponse.json({ session: order });
      }
    } catch (dbError: any) {
      console.error('Error retrieving order:', dbError);
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }
  } catch (error: any) {
    console.error('Error retrieving session:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
