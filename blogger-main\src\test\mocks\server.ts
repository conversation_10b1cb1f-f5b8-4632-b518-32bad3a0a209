// MSW server setup for API mocking
import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';

// Mock data
const mockArticles = [
  {
    id: '1',
    title: 'Test Article 1',
    slug: 'test-article-1',
    excerpt: 'This is a test article excerpt',
    content: 'This is the full content of the test article',
    author: {
      id: '1',
      name: 'Test Author',
      avatar: 'https://example.com/avatar.jpg',
    },
    category: {
      id: '1',
      name: 'Technology',
      slug: 'technology',
    },
    tags: ['test', 'article'],
    featuredImage: 'https://example.com/image.jpg',
    isPremium: false,
    isPublished: true,
    publishedAt: new Date('2024-01-01'),
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    readTime: 5,
    views: 100,
    likes: 10,
    comments: 5,
    shares: 2,
  },
];

const mockProducts = [
  {
    id: '1',
    name: 'Test Product',
    slug: 'test-product',
    description: 'This is a test product',
    shortDescription: 'Test product description',
    price: 29.99,
    comparePrice: 39.99,
    type: 'digital',
    category: {
      id: '1',
      name: 'Digital Products',
      slug: 'digital-products',
    },
    images: ['https://example.com/product.jpg'],
    stock: 100,
    status: 'active',
    featured: true,
    tags: ['test', 'product'],
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
];

const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  user_metadata: {
    firstName: 'Test',
    lastName: 'User',
  },
  created_at: new Date('2024-01-01').toISOString(),
  updated_at: new Date('2024-01-01').toISOString(),
};

// API handlers
export const handlers = [
  // Auth endpoints
  http.post('/auth/v1/signup', () => {
    return HttpResponse.json({
      user: mockUser,
      session: {
        access_token: 'mock-access-token',
        refresh_token: 'mock-refresh-token',
        user: mockUser,
      },
    });
  }),

  http.post('/auth/v1/token', () => {
    return HttpResponse.json({
      user: mockUser,
      session: {
        access_token: 'mock-access-token',
        refresh_token: 'mock-refresh-token',
        user: mockUser,
      },
    });
  }),

  http.post('/auth/v1/logout', () => {
    return HttpResponse.json({});
  }),

  http.get('/auth/v1/user', () => {
    return HttpResponse.json({ user: mockUser });
  }),

  // Articles endpoints
  http.get('/rest/v1/articles', ({ request }) => {
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const offset = parseInt(url.searchParams.get('offset') || '0');
    
    const paginatedArticles = mockArticles.slice(offset, offset + limit);
    
    return HttpResponse.json(paginatedArticles, {
      headers: {
        'Content-Range': `0-${paginatedArticles.length - 1}/${mockArticles.length}`,
      },
    });
  }),

  http.get('/rest/v1/articles/:id', ({ params }) => {
    const article = mockArticles.find(a => a.id === params.id);
    if (!article) {
      return HttpResponse.json({ error: 'Article not found' }, { status: 404 });
    }
    return HttpResponse.json(article);
  }),

  http.post('/rest/v1/articles', async ({ request }) => {
    const body = await request.json();
    const newArticle = {
      id: Date.now().toString(),
      ...body,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    mockArticles.push(newArticle);
    return HttpResponse.json(newArticle, { status: 201 });
  }),

  http.patch('/rest/v1/articles/:id', async ({ params, request }) => {
    const body = await request.json();
    const articleIndex = mockArticles.findIndex(a => a.id === params.id);
    if (articleIndex === -1) {
      return HttpResponse.json({ error: 'Article not found' }, { status: 404 });
    }
    
    mockArticles[articleIndex] = {
      ...mockArticles[articleIndex],
      ...body,
      updatedAt: new Date(),
    };
    
    return HttpResponse.json(mockArticles[articleIndex]);
  }),

  http.delete('/rest/v1/articles/:id', ({ params }) => {
    const articleIndex = mockArticles.findIndex(a => a.id === params.id);
    if (articleIndex === -1) {
      return HttpResponse.json({ error: 'Article not found' }, { status: 404 });
    }
    
    mockArticles.splice(articleIndex, 1);
    return HttpResponse.json({}, { status: 204 });
  }),

  // Products endpoints
  http.get('/rest/v1/products', ({ request }) => {
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const offset = parseInt(url.searchParams.get('offset') || '0');
    
    const paginatedProducts = mockProducts.slice(offset, offset + limit);
    
    return HttpResponse.json(paginatedProducts, {
      headers: {
        'Content-Range': `0-${paginatedProducts.length - 1}/${mockProducts.length}`,
      },
    });
  }),

  http.get('/rest/v1/products/:id', ({ params }) => {
    const product = mockProducts.find(p => p.id === params.id);
    if (!product) {
      return HttpResponse.json({ error: 'Product not found' }, { status: 404 });
    }
    return HttpResponse.json(product);
  }),

  // Stripe endpoints
  http.post('https://api.stripe.com/v1/payment_intents', () => {
    return HttpResponse.json({
      id: 'pi_test_123',
      client_secret: 'pi_test_123_secret',
      status: 'requires_payment_method',
    });
  }),

  http.post('https://api.stripe.com/v1/setup_intents', () => {
    return HttpResponse.json({
      id: 'seti_test_123',
      client_secret: 'seti_test_123_secret',
      status: 'requires_payment_method',
    });
  }),

  // Analytics endpoints
  http.post('https://www.google-analytics.com/mp/collect', () => {
    return HttpResponse.json({});
  }),

  // Newsletter endpoints
  http.post('/api/newsletter/subscribe', async ({ request }) => {
    const body = await request.json();
    return HttpResponse.json({
      success: true,
      message: 'Successfully subscribed to newsletter',
      email: body.email,
    });
  }),

  // Contact form endpoints
  http.post('/api/contact', async ({ request }) => {
    const body = await request.json();
    return HttpResponse.json({
      success: true,
      message: 'Message sent successfully',
      id: Date.now().toString(),
    });
  }),

  // File upload endpoints
  http.post('/storage/v1/object/*', () => {
    return HttpResponse.json({
      Key: 'test-file-key',
      ETag: 'test-etag',
    });
  }),

  // Search endpoints
  http.get('/api/search', ({ request }) => {
    const url = new URL(request.url);
    const query = url.searchParams.get('q') || '';
    
    const results = [
      ...mockArticles.filter(a => 
        a.title.toLowerCase().includes(query.toLowerCase()) ||
        a.content.toLowerCase().includes(query.toLowerCase())
      ),
      ...mockProducts.filter(p => 
        p.name.toLowerCase().includes(query.toLowerCase()) ||
        p.description.toLowerCase().includes(query.toLowerCase())
      ),
    ];
    
    return HttpResponse.json({
      results,
      total: results.length,
      query,
    });
  }),

  // Error simulation endpoints
  http.get('/api/error/500', () => {
    return HttpResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }),

  http.get('/api/error/404', () => {
    return HttpResponse.json(
      { error: 'Not Found' },
      { status: 404 }
    );
  }),

  http.get('/api/error/timeout', () => {
    return new Promise(() => {}); // Never resolves (timeout)
  }),
];

// Create server instance
export const server = setupServer(...handlers);

// Export utilities for tests
export const mockData = {
  articles: mockArticles,
  products: mockProducts,
  user: mockUser,
};

export const resetMockData = () => {
  mockArticles.length = 0;
  mockArticles.push({
    id: '1',
    title: 'Test Article 1',
    slug: 'test-article-1',
    excerpt: 'This is a test article excerpt',
    content: 'This is the full content of the test article',
    author: {
      id: '1',
      name: 'Test Author',
      avatar: 'https://example.com/avatar.jpg',
    },
    category: {
      id: '1',
      name: 'Technology',
      slug: 'technology',
    },
    tags: ['test', 'article'],
    featuredImage: 'https://example.com/image.jpg',
    isPremium: false,
    isPublished: true,
    publishedAt: new Date('2024-01-01'),
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    readTime: 5,
    views: 100,
    likes: 10,
    comments: 5,
    shares: 2,
  });
};
