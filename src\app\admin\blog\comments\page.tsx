"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { 
  MessageSquare, 
  Search, 
  Filter, 
  Eye, 
  Check, 
  X,
  Trash2,
  Calendar,
  User,
  FileText,
  AlertTriangle
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { createClient } from '@/utils/supabase/client';

interface BlogComment {
  id: string;
  content: string;
  article_id: string;
  user_id: string;
  parent_id: string | null;
  is_approved: boolean;
  created_at: string;
  updated_at: string;
  blog_articles?: {
    title: string;
    slug: string;
  };
  users?: {
    full_name: string;
    email: string;
  };
  replies?: BlogComment[];
}

export default function BlogCommentsPage() {
  const [comments, setComments] = useState<BlogComment[]>([]);
  const [filteredComments, setFilteredComments] = useState<BlogComment[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'approved' | 'pending'>('all');
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const supabase = createClient();

  useEffect(() => {
    fetchComments();
  }, []);

  useEffect(() => {
    filterComments();
  }, [comments, searchTerm, statusFilter]);

  const fetchComments = async () => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('blog_comments')
        .select(`
          *,
          blog_articles (title, slug),
          users (full_name, email)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setComments(data || []);
    } catch (error) {
      console.error('Error fetching comments:', error);
      toast({
        title: "Error",
        description: "Failed to fetch comments",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filterComments = () => {
    let filtered = comments;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(comment =>
        comment.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        comment.users?.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        comment.blog_articles?.title?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(comment =>
        statusFilter === 'approved' ? comment.is_approved : !comment.is_approved
      );
    }

    setFilteredComments(filtered);
  };

  const handleApproveComment = async (commentId: string) => {
    try {
      const { error } = await supabase
        .from('blog_comments')
        .update({ is_approved: true })
        .eq('id', commentId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Comment approved successfully",
      });

      fetchComments();
    } catch (error) {
      console.error('Error approving comment:', error);
      toast({
        title: "Error",
        description: "Failed to approve comment",
        variant: "destructive",
      });
    }
  };

  const handleRejectComment = async (commentId: string) => {
    try {
      const { error } = await supabase
        .from('blog_comments')
        .update({ is_approved: false })
        .eq('id', commentId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Comment rejected successfully",
      });

      fetchComments();
    } catch (error) {
      console.error('Error rejecting comment:', error);
      toast({
        title: "Error",
        description: "Failed to reject comment",
        variant: "destructive",
      });
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    if (!confirm('Are you sure you want to delete this comment? This action cannot be undone.')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('blog_comments')
        .delete()
        .eq('id', commentId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Comment deleted successfully",
      });

      fetchComments();
    } catch (error) {
      console.error('Error deleting comment:', error);
      toast({
        title: "Error",
        description: "Failed to delete comment",
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const truncateContent = (content: string, maxLength: number = 150) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Blog Comments</h1>
            <p className="text-muted-foreground">Moderate and manage blog comments</p>
          </div>
        </div>
        <div className="space-y-4">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="glass-effect border border-white/10 neo-shadow animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-white/20 rounded mb-2"></div>
                <div className="h-3 bg-white/10 rounded mb-4"></div>
                <div className="flex gap-2">
                  <div className="h-6 w-16 bg-white/10 rounded"></div>
                  <div className="h-6 w-20 bg-white/10 rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Blog Comments</h1>
          <p className="text-muted-foreground">Moderate and manage blog comments</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="glass-effect border border-white/10 neo-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Comments</p>
                <p className="text-2xl font-bold text-foreground">{comments.length}</p>
              </div>
              <MessageSquare className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-effect border border-white/10 neo-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Approved</p>
                <p className="text-2xl font-bold text-foreground">
                  {comments.filter(c => c.is_approved).length}
                </p>
              </div>
              <Check className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-effect border border-white/10 neo-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending</p>
                <p className="text-2xl font-bold text-foreground">
                  {comments.filter(c => !c.is_approved).length}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-effect border border-white/10 neo-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">This Month</p>
                <p className="text-2xl font-bold text-foreground">
                  {comments.filter(c => {
                    const commentDate = new Date(c.created_at);
                    const now = new Date();
                    return commentDate.getMonth() === now.getMonth() && 
                           commentDate.getFullYear() === now.getFullYear();
                  }).length}
                </p>
              </div>
              <Calendar className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="glass-effect border border-white/10 neo-shadow">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search comments, authors, or articles..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 glass-effect border-white/20 text-foreground"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant={statusFilter === 'all' ? 'default' : 'outline'}
                onClick={() => setStatusFilter('all')}
                className="min-h-[44px]"
              >
                All
              </Button>
              <Button
                variant={statusFilter === 'approved' ? 'default' : 'outline'}
                onClick={() => setStatusFilter('approved')}
                className="min-h-[44px]"
              >
                Approved
              </Button>
              <Button
                variant={statusFilter === 'pending' ? 'default' : 'outline'}
                onClick={() => setStatusFilter('pending')}
                className="min-h-[44px]"
              >
                Pending
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Comments List */}
      <div className="space-y-4">
        {filteredComments.map((comment) => (
          <Card key={comment.id} className="glass-effect border border-white/10 neo-shadow hover:neo-shadow-light transition-all duration-300">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge 
                      variant={comment.is_approved ? "default" : "secondary"}
                      className={comment.is_approved ? "bg-green-500/20 text-green-400 border-green-500/30" : "bg-yellow-500/20 text-yellow-400 border-yellow-500/30"}
                    >
                      {comment.is_approved ? 'Approved' : 'Pending'}
                    </Badge>
                    {comment.parent_id && (
                      <Badge variant="outline" className="border-white/20 text-muted-foreground">
                        Reply
                      </Badge>
                    )}
                  </div>
                  <CardTitle className="text-lg font-semibold text-foreground">
                    {comment.users?.full_name || 'Anonymous User'}
                  </CardTitle>
                  <CardDescription className="text-muted-foreground">
                    on "{comment.blog_articles?.title || 'Unknown Article'}"
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Comment Content */}
              <div className="glass-effect border border-white/10 p-4 rounded-xl">
                <p className="text-foreground text-sm leading-relaxed">
                  {truncateContent(comment.content)}
                </p>
              </div>

              {/* Meta Information */}
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <User className="h-3 w-3" />
                  {comment.users?.email || 'No email'}
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {formatDate(comment.created_at)}
                </div>
                <div className="flex items-center gap-1">
                  <FileText className="h-3 w-3" />
                  {comment.blog_articles?.slug || 'unknown-article'}
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center gap-2 pt-2">
                {!comment.is_approved ? (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleApproveComment(comment.id)}
                    className="glass-effect border-green-500/20 hover:bg-green-500/20 text-green-400 min-h-[36px]"
                  >
                    <Check className="h-3 w-3 mr-1" />
                    Approve
                  </Button>
                ) : (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRejectComment(comment.id)}
                    className="glass-effect border-yellow-500/20 hover:bg-yellow-500/20 text-yellow-400 min-h-[36px]"
                  >
                    <X className="h-3 w-3 mr-1" />
                    Reject
                  </Button>
                )}
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDeleteComment(comment.id)}
                  className="glass-effect border-destructive/20 hover:bg-destructive/20 text-destructive min-h-[36px]"
                >
                  <Trash2 className="h-3 w-3 mr-1" />
                  Delete
                </Button>

                <div className="ml-auto text-xs text-muted-foreground">
                  ID: {comment.id.substring(0, 8)}...
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredComments.length === 0 && !isLoading && (
        <Card className="glass-effect border border-white/10 neo-shadow">
          <CardContent className="p-12 text-center">
            <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">No comments found</h3>
            <p className="text-muted-foreground">
              {searchTerm || statusFilter !== 'all' 
                ? 'Try adjusting your search or filters'
                : 'Comments will appear here when readers engage with your blog posts'
              }
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
