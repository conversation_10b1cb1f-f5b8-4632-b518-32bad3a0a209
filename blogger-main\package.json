{"name": "professional-blog-ecommerce", "private": true, "version": "1.0.0", "description": "Professional Blog & Ecommerce Platform with news-style layout, paywall system, and integrated shopping", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:check": "npm run type-check && vite build", "build-no-errors": "tsc --noEmit && vite build", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint . --fix", "type-check": "tsc --noEmit", "preview": "vite preview", "types:supabase": "npx supabase gen types typescript --project-id $SUPABASE_PROJECT_ID > src/types/supabase.ts", "check": "npm run type-check && npm run lint", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "test:integration": "vitest run --config vitest.integration.config.ts", "test:e2e": "playwright test", "test:all": "npm run test:run && npm run test:integration && npm run test:e2e", "db:help": "node scripts/setup-database.js", "db:setup": "node scripts/setup-database.js setup", "db:seed": "node scripts/setup-database.js seed", "db:reset": "node scripts/setup-database.js reset", "db:fix": "node scripts/setup-database.js fix", "db:sync": "node scripts/setup-database.js sync", "db:master": "node scripts/setup-database.js master", "db:check": "node scripts/setup-database.js check", "db:force-sync": "node scripts/setup-database.js force-sync", "db:diagnose": "node scripts/setup-database.js diagnose", "db:fix-users": "node scripts/setup-database.js fix-users", "db:fix-all": "node scripts/setup-database.js fix-all", "db:check-roles": "node scripts/setup-database.js check-roles", "db:create-admin": "node scripts/setup-database.js create-admin", "db:create-admin-safe": "node scripts/setup-database.js create-admin-safe", "db:complete-sync": "node scripts/setup-database.js complete-sync", "db:admin-simple": "node scripts/setup-database.js admin-simple", "db:check-structure": "node scripts/setup-database.js check-structure", "db:create-missing": "node scripts/setup-database.js create-missing", "db:ping-compare": "node scripts/setup-database.js ping-compare", "db:complete-admin-sync": "node scripts/setup-database.js complete-admin-sync", "docker:setup": "bash scripts/docker-setup.sh", "docker:setup:windows": "powershell -ExecutionPolicy Bypass -File scripts/docker-setup.ps1", "docker:start": "docker-compose up -d", "docker:stop": "docker-compose down", "docker:restart": "docker-compose restart", "docker:logs": "docker-compose logs -f", "docker:clean": "docker-compose down -v --remove-orphans", "yoco:test": "node -e \"console.log('🧪 Testing Yoco payment integration...'); console.log('Currency: ZAR'); console.log('Provider: Yoco'); console.log('Environment: Test');\""}, "dependencies": {"@hookform/resolvers": "^3.6.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-tooltip": "^1.0.7", "@supabase/supabase-js": "^2.45.6", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.1.5", "framer-motion": "^11.18.0", "lucide-react": "^0.394.0", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.51.5", "react-intersection-observer": "^9.8.2", "react-markdown": "^9.0.1", "react-resizable-panels": "^2.0.19", "react-router": "^6.23.1", "react-router-dom": "^6.23.1", "react-share": "^5.1.0", "recharts": "^2.15.3", "rehype-highlight": "^7.0.0", "remark-gfm": "^4.0.0", "swiper": "^11.0.7", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.1", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.0.0", "@swc/core": "1.3.96", "@types/node": "^20.14.2", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "@vitejs/plugin-react-swc": "3.5.0", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "globals": "^15.0.0", "postcss": "^8.4.38", "tailwindcss": "3.4.1", "tempo-devtools": "^2.0.106", "typescript": "^5.2.2", "vite": "^5.2.0"}}