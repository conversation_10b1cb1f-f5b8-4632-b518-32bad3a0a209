"use server";

import { signInAction } from "@/app/actions";

// Create a bound action creator for each possible redirect
export async function signInWithDashboardRedirect(formData: FormData) {
  formData.append("redirect_to", "/dashboard");
  return signInAction(formData);
}

export async function signInWithCustomRedirect(formData: FormData, redirectTo: string) {
  formData.append("redirect_to", redirectTo);
  return signInAction(formData);
}
