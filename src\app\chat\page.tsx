'use client';

import React, { useEffect, useState } from 'react';
import { ChatInterface, Contact as ChatContact } from '@/components/chat/chat-interface';
import { createClient } from '@/utils/supabase/client';
import { useRouter } from 'next/navigation';
import { Tables } from '@/utils/supabase/database.types';
import { sendMessage, getConversations, getMessages } from '@/utils/supabase/chat-utils';
import { uploadFile } from '@/utils/supabase/resource-utils';
import { Loader2 } from 'lucide-react';

interface Contact {
  id: string;
  name: string;
  avatar?: string | null;
  lastMessage?: string | null;
  lastMessageTime?: Date | null;
  unreadCount?: number | null;
  status?: 'online' | 'offline' | 'away' | null;
  conversationId?: string | null; // Added to store the conversation ID
}

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'mentor';
  timestamp: Date;
  read: boolean;
  attachmentUrl?: string;
}

// We're already importing ChatContact at the top of the file

export default function ChatPage() {
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<any>(null);
  const [contacts, setContacts] = useState<ChatContact[]>([]);
  const [messages, setMessages] = useState<Record<string, Message[]>>({});
  const router = useRouter();
  const supabase = createClient();

  // Check if user is authenticated
  useEffect(() => {
    const checkUser = async () => {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error || !session) {
        router.push('/login');
        return;
      }
      
      // Get user details
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', session.user.id)
        .single();
      
      if (userError || !userData) {
        console.error('Error fetching user data:', userError);
        return;
      }
      
      setUser(userData);
      
      // Load conversations
      await loadConversations(session.user.id);
      
      setLoading(false);
    };
    
    checkUser();
  }, []);

  // Load user's conversations
  const loadConversations = async (userId: string) => {
    try {
      const conversations = await getConversations(userId);
      
      if (!conversations) {
        return;
      }
      
      // Format conversations as contacts
      const formattedContacts = await Promise.all(
        conversations.map(async (conv): Promise<ChatContact | null> => {
          // Determine the other user in the conversation
          const otherUserId = conv.user1_id === userId ? conv.user2_id : conv.user1_id;
          
          // Get other user details
          const { data: otherUser, error } = await supabase
            .from('users')
            .select('*')
            .eq('id', otherUserId)
            .single();
          
          if (error || !otherUser) {
            console.error('Error fetching other user:', error);
            return null;
          }
          
          // Get latest message
          const { data: latestMessage, error: msgError } = await supabase
            .from('messages')
            .select('*')
            .eq('conversation_id', conv.id)
            .order('created_at', { ascending: false })
            .limit(1)
            .single();
          
          // Get unread count
          const { count: unreadCount, error: countError } = await supabase
            .from('messages')
            .select('id', { count: 'exact' })
            .eq('conversation_id', conv.id)
            .eq('receiver_id', userId)
            .eq('read', false);
          
          return {
            id: otherUser.id,
            name: otherUser.full_name || 'User',
            avatar: otherUser.avatar_url,
            lastMessage: latestMessage?.content || '',
            lastMessageTime: latestMessage ? new Date(latestMessage.created_at) : undefined,
            unreadCount: unreadCount || 0,
            status: 'offline', // We would need a presence system to determine online status
            conversationId: conv.id, // Store the conversation ID for later use
          };
        })
      );
      
      // Filter out null values (from errors)
      setContacts(formattedContacts.filter((contact): contact is ChatContact => contact !== null));
      
      // Load messages for each conversation
      for (const contact of formattedContacts) {
        if (contact && contact.conversationId) {
          await loadMessages(contact.conversationId, contact.id, userId);
        }
      }
    } catch (error) {
      console.error('Error loading conversations:', error);
    }
  };

  // Load messages for a specific conversation
  const loadMessages = async (conversationId: string, contactId: string, userId: string) => {
    try {
      const messagesData = await getMessages(conversationId);
      
      if (!messagesData) {
        return;
      }
      
      // Format messages
      const formattedMessages: Message[] = messagesData.map((msg) => ({
        id: msg.id,
        content: msg.content,
        sender: msg.sender_id === userId ? 'user' : 'mentor',
        timestamp: new Date(msg.created_at),
        read: msg.read,
        attachmentUrl: msg.attachment_url || undefined,
      }));
      
      // Update messages state
      setMessages((prev) => ({
        ...prev,
        [contactId]: formattedMessages,
      }));
    } catch (error) {
      console.error('Error loading messages:', error);
    }
  };

  // Handle sending a message
  const handleSendMessage = async (contactId: string, content: string, attachment?: File) => {
    if (!user) return;
    
    try {
      // Find the conversation with this contact
      const contact = contacts.find((c) => c.id === contactId);
      
      if (!contact || !contact.conversationId) {
        console.error('Conversation not found');
        return;
      }
      
      let attachmentUrl: string | undefined = undefined;
      
      // Upload attachment if provided
      if (attachment) {
        const fileData = await uploadFile(
          attachment, 
          'chat-attachments', 
          `${user.id}/${Date.now()}_${attachment.name}`
        );
        
        if (fileData) {
          attachmentUrl = fileData.url;
        }
      }
      
      // Send message to Supabase
      await sendMessage({
        conversationId: contact.conversationId,
        senderId: user.id,
        receiverId: contactId,
        content,
        attachmentUrl,
      });
      
      // Reload messages
      await loadMessages(contact.conversationId, contactId, user.id);
      
      // Update last message in contacts list
      setContacts((prev) => 
        prev.map((c) => 
          c.id === contactId
            ? {
                ...c,
                lastMessage: content,
                lastMessageTime: new Date(),
              }
            : c
        )
      );
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-lg">Loading chat...</span>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Mentorship Chat</h1>
      <ChatInterface
        currentUserId={user?.id}
        initialContacts={contacts}
        initialMessages={messages}
        onSendMessage={handleSendMessage}
      />
    </div>
  );
}
