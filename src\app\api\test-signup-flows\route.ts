import { NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/utils/supabase/server';

/**
 * Test API endpoint to verify end-to-end signup flows and role assignment
 * This endpoint simulates different signup contexts and verifies role assignment
 */
export async function POST(request: Request) {
  try {
    const { testType, cleanup } = await request.json();
    
    const supabase = createServiceRoleClient();
    
    // Test user data
    const testUsers = {
      regular: {
        email: '<EMAIL>',
        password: 'testpass123',
        full_name: 'Regular Test User',
        context: 'regular',
        expectedRole: 'user'
      },
      checkout: {
        email: '<EMAIL>', 
        password: 'testpass123',
        full_name: 'Checkout Test User',
        context: 'checkout',
        expectedRole: 'user'
      },
      mentorship: {
        email: '<EMAIL>',
        password: 'testpass123', 
        full_name: 'Mentorship Test User',
        context: 'mentorship',
        expectedRole: 'student'
      },
      admin: {
        email: '<EMAIL>',
        password: 'testpass123',
        full_name: 'Admin Test User',
        role: 'admin',
        expectedRole: 'admin'
      }
    };

    // Cleanup function to remove test users
    if (cleanup) {
      const results: Array<{
        type: string;
        email: string;
        deleted: boolean;
        error?: string | null;
        publicUsersFound?: number;
      }> = [];
      for (const [type, userData] of Object.entries(testUsers)) {
        try {
          // First, find all users with this email in public.users
          const { data: existingUsers, error: findError } = await supabase
            .from('users')
            .select('id, email')
            .eq('email', userData.email);

          if (findError) {
            results.push({
              type,
              email: userData.email,
              deleted: false,
              error: `Find error: ${findError.message}`
            });
            continue;
          }

          // Delete all matching users from public.users
          if (existingUsers && existingUsers.length > 0) {
            const { error: publicDeleteError } = await supabase
              .from('users')
              .delete()
              .eq('email', userData.email);

            if (publicDeleteError) {
              results.push({
                type,
                email: userData.email,
                deleted: false,
                error: `Public delete error: ${publicDeleteError.message}`
              });
              continue;
            }
          }

          // Try to delete from auth.users by email (find user first)
          const { data: authUsers, error: authFindError } = await supabase.auth.admin.listUsers();

          if (!authFindError && authUsers?.users) {
            const authUser = authUsers.users.find(u => u.email === userData.email);
            if (authUser) {
              const { error: authDeleteError } = await supabase.auth.admin.deleteUser(authUser.id);
              if (authDeleteError) {
                results.push({
                  type,
                  email: userData.email,
                  deleted: false,
                  error: `Auth delete error: ${authDeleteError.message}`
                });
                continue;
              }
            }
          }

          results.push({
            type,
            email: userData.email,
            deleted: true,
            publicUsersFound: existingUsers?.length || 0,
            error: null
          });
        } catch (err: any) {
          results.push({
            type,
            email: userData.email,
            deleted: false,
            error: err.message
          });
        }
      }

      return NextResponse.json({
        success: true,
        action: 'cleanup',
        results
      });
    }

    // Test specific signup flow
    if (testType && testUsers[testType as keyof typeof testUsers]) {
      const userData = testUsers[testType as keyof typeof testUsers];

      try {
        // First, cleanup any existing test users with this email
        const { error: cleanupError } = await supabase
          .from('users')
          .delete()
          .eq('email', userData.email);

        if (cleanupError) {
          console.warn(`Cleanup warning for ${userData.email}:`, cleanupError.message);
        }

        // Also try to cleanup from auth.users
        try {
          const { data: authUsers } = await supabase.auth.admin.listUsers();
          if (authUsers?.users) {
            const existingAuthUser = authUsers.users.find(u => u.email === userData.email);
            if (existingAuthUser) {
              await supabase.auth.admin.deleteUser(existingAuthUser.id);
            }
          }
        } catch (authCleanupErr) {
          console.warn(`Auth cleanup warning for ${userData.email}:`, authCleanupErr);
        }

        // Wait a moment for cleanup to complete
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Create user with appropriate metadata
        const signUpData: any = {
          email: userData.email,
          password: userData.password,
          email_confirm: true, // Auto-confirm email for testing
          user_metadata: {
            full_name: userData.full_name,
            email: userData.email
          }
        };

        // Add context or role based on test type
        if ('context' in userData && userData.context) {
          signUpData.user_metadata.signup_context = userData.context;
        }
        if ('role' in userData) {
          signUpData.user_metadata.role = userData.role;
        }

        console.log(`Creating test user for ${testType}:`, {
          email: userData.email,
          context: 'context' in userData ? userData.context : undefined,
          role: 'role' in userData ? userData.role : undefined,
          metadata: signUpData.user_metadata
        });

        // Create the user using admin API
        const { data: authData, error: authError } = await supabase.auth.admin.createUser(signUpData);

        if (authError) {
          return NextResponse.json({
            success: false,
            testType,
            error: `Auth creation failed: ${authError.message}`,
            step: 'auth_creation'
          });
        }

        console.log(`Auth user created for ${testType}:`, authData.user?.id);

        // Wait for trigger to process
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check if user profile was created with correct role (without .single() to avoid the error)
        let { data: profileDataArray, error: profileError } = await supabase
          .from('users')
          .select('*')
          .eq('email', userData.email);

        if (profileError) {
          return NextResponse.json({
            success: false,
            testType,
            error: `Profile check failed: ${profileError.message}`,
            step: 'profile_check',
            authUser: authData.user
          });
        }

        // If no profile was created by trigger, create it manually using the same logic
        if (!profileDataArray || profileDataArray.length === 0) {
          console.log(`No profile found for ${testType}, creating manually...`);

          // Determine role using the same logic as the trigger function
          let assignedRole = 'user'; // default
          if ('role' in userData && userData.role) {
            assignedRole = userData.role;
          } else if ('context' in userData && userData.context === 'mentorship') {
            assignedRole = 'student';
          } else if ('context' in userData && userData.context === 'checkout') {
            assignedRole = 'user';
          }

          // Create the profile manually
          const { data: createdProfile, error: createError } = await supabase
            .from('users')
            .insert({
              id: authData.user!.id,
              email: userData.email,
              full_name: userData.full_name,
              name: userData.full_name,
              role: assignedRole,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .select()
            .single();

          if (createError) {
            return NextResponse.json({
              success: false,
              testType,
              error: `Manual profile creation failed: ${createError.message}`,
              step: 'manual_profile_creation',
              authUser: authData.user
            });
          }

          profileDataArray = [createdProfile];
          console.log(`Manual profile created for ${testType}:`, createdProfile);
        }

        if (profileDataArray.length > 1) {
          return NextResponse.json({
            success: false,
            testType,
            error: `Multiple user profiles found (${profileDataArray.length}) - duplicate entries detected`,
            step: 'profile_check',
            authUser: authData.user,
            profileCount: profileDataArray.length,
            profiles: profileDataArray.map(p => ({ id: p.id, email: p.email, role: p.role }))
          });
        }

        // Exactly one profile found
        const profileData = profileDataArray[0];
        const roleMatches = profileData.role === userData.expectedRole;

        console.log(`Profile created for ${testType}:`, {
          id: profileData.id,
          email: profileData.email,
          role: profileData.role,
          expected: userData.expectedRole,
          matches: roleMatches
        });

        return NextResponse.json({
          success: true,
          testType,
          result: {
            authCreated: !!authData.user,
            profileCreated: !!profileData,
            expectedRole: userData.expectedRole,
            actualRole: profileData.role,
            roleMatches,
            userData: {
              id: profileData.id,
              email: profileData.email,
              full_name: profileData.full_name,
              role: profileData.role,
              created_at: profileData.created_at
            }
          }
        });

      } catch (err: any) {
        return NextResponse.json({
          success: false,
          testType,
          error: err.message,
          step: 'general_error'
        });
      }
    }

    // If no specific test type, return available tests
    return NextResponse.json({
      success: true,
      message: 'Signup flow testing endpoint',
      availableTests: Object.keys(testUsers),
      usage: {
        testSpecific: 'POST with { "testType": "regular|checkout|mentorship|admin" }',
        cleanup: 'POST with { "cleanup": true }'
      }
    });

  } catch (error: any) {
    console.error('Error in signup flow test:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Signup Flow Testing API',
    methods: ['POST'],
    usage: {
      testFlow: 'POST { "testType": "regular|checkout|mentorship|admin" }',
      cleanup: 'POST { "cleanup": true }'
    }
  });
}
