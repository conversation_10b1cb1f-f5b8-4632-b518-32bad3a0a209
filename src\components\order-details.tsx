"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Package, Truck, Calendar, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { formatCurrency } from "@/lib/format";

// Define types for order items
interface OrderItem {
  id: string | number;
  name: string;
  quantity: number;
  price: number;
  image: string;
}

// Simple function to generate a unique ID
function generateId(prefix = '') {
  return `${prefix}${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
}

interface OrderDetailsProps {
  sessionId: string; // This is now the Yoco payment ID
}

// Define types for order data
interface OrderData {
  id: string;
  user_id: string;
  items: any[];
  shipping_details: {
    name: string;
    address: string;
    city: string;
    postal_code: string;
    province: string;
    country: string;
    phone: string;
    alternative_phone?: string;
    email: string;
  };
  status: string;
  payment_status: string;
  total_amount: number;
  created_at: string;
  yoco_payment_id: string;
}

export function OrderDetails({ sessionId }: OrderDetailsProps) {
  const [order, setOrder] = useState<OrderData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { toast } = useToast();

  useEffect(() => {
    const fetchOrderDetails = async () => {
      try {
        // Try to fetch the order directly by ID first
        try {
          console.log('Fetching order by ID:', sessionId);
          const orderResponse = await fetch(`/api/orders/${sessionId}`);
          
          if (orderResponse.ok) {
            const orderData = await orderResponse.json();
            if (orderData) {
              console.log('Order found by ID:', orderData);
              setOrder(orderData);
              setLoading(false);
              return;
            }
          } else {
            console.log('Order not found by ID, status:', orderResponse.status);
          }
        } catch (directError) {
          console.error('Error fetching order directly:', directError);
        }

        // If we're still here, try by Yoco payment ID
        try {
          console.log('Fetching order by payment ID:', sessionId);
          const response = await fetch(`/api/orders/payment/${sessionId}`);
          
          if (response.ok) {
            const data = await response.json();
            
            if (data && data.order) {
              console.log('Order found by payment ID:', data.order);
              setOrder(data.order);
            } else {
              throw new Error('Order not found');
            }
          } else {
            console.error('Failed to fetch order by payment ID, status:', response.status);
            throw new Error('Failed to retrieve order details');
          }
        } catch (yocoError) {
          console.error('Error fetching order by payment ID:', yocoError);
          
          // Last resort: check if sessionId is the order ID directly
          if (sessionId.startsWith('TEC-')) {
            setError('We could not find your order. The payment may still be processing. Please check again in a few minutes or contact customer support.');
          } else {
            setError('We could not find your order. Please contact customer support.');
          }
        }
      } catch (err: any) {
        console.error('Error fetching order details:', err);
        setError(err.message || 'Failed to load order details');
        toast({
          title: "Error",
          description: err.message || 'Failed to load order details',
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    if (sessionId) {
      fetchOrderDetails();
    }
  }, [sessionId, toast, router]);

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Loading order details...</p>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="bg-card border border-border rounded-lg p-6 mb-8 text-center">
        <h2 className="text-xl font-semibold mb-4">Order Details Unavailable</h2>
        <p className="text-muted-foreground mb-4">
          {error || "We couldn't find your order details. Please contact customer support."}
        </p>
      </div>
    );
  }

  // Mock data for now - in a real app, this would come from the order
  const orderNumber = order.id;
  const orderDate = new Date(order.created_at).toLocaleDateString("en-ZA", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
  const estimatedDelivery = new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toLocaleDateString("en-ZA", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  const orderItems = order.items;

  // Calculate subtotal - if we have prices, use them, otherwise use the total amount
  let subtotal = 0;
  const hasItemPrices = orderItems.some((item: any) => item.price > 0);

  if (hasItemPrices) {
    subtotal = orderItems.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0);
  } else {
    // If we don't have individual prices, use the total amount minus shipping
    subtotal = order.total_amount > 99.99 ? order.total_amount - 99.99 : order.total_amount;
  }

  const shipping = subtotal > 1000 ? 0 : 99.99;
  const total = order.total_amount;

  return (
    <>
      {/* Order Details */}
      <div className="bg-card border border-border rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold mb-6">Order Details</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">Order Number</h3>
            <p className="font-medium">{orderNumber}</p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">Order Date</h3>
            <p>{orderDate}</p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">Payment Status</h3>
            <p className="capitalize">{order.payment_status}</p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">Order Status</h3>
            <p className="capitalize">{order.status}</p>
          </div>
        </div>

        <div className="border-t border-border pt-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-3">Shipping Address</h3>
          <p>{order.shipping_details.name}</p>
          <p>{order.shipping_details.address}</p>
          <p>{order.shipping_details.city}, {order.shipping_details.province} {order.shipping_details.postal_code}</p>
          <p>{order.shipping_details.country}</p>
          <p>{order.shipping_details.phone}</p>
          {order.shipping_details.alternative_phone && (
            <p>Alt: {order.shipping_details.alternative_phone}</p>
          )}
        </div>
      </div>

      {/* Order Items */}
      <div className="bg-card border border-border rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold mb-6">Order Items</h2>

        <div className="space-y-6 mb-6">
          {orderItems.map((item: any, index: number) => (
            <div key={index} className="flex gap-4">
              <div className="relative w-20 h-20 rounded-md overflow-hidden border border-border flex-shrink-0">
                <img src={item.image} alt={item.name} className="object-cover w-full h-full" />
              </div>
              <div className="flex-grow">
                <h3 className="font-medium">{item.name}</h3>
                <p className="text-muted-foreground text-sm">Quantity: {item.quantity}</p>
              </div>
              {item.price > 0 ? (
                <div className="text-right">
                  <p className="font-medium">
                    {formatCurrency(item.price * item.quantity)}
                  </p>
                  <p className="text-muted-foreground text-sm">
                    {formatCurrency(item.price)} each
                  </p>
                </div>
              ) : (
                <div className="text-right">
                  <p className="text-muted-foreground text-sm">Price included in total</p>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="border-t border-border pt-4 space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Subtotal</span>
            <span>{formatCurrency(subtotal)}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Shipping</span>
            <span>
              {shipping === 0 ? 'Free' : formatCurrency(shipping)}
            </span>
          </div>
          <div className="flex justify-between font-medium text-base pt-2 border-t border-border">
            <span>Total</span>
            <span className="text-primary">{formatCurrency(total)}</span>
          </div>
        </div>
      </div>

      {/* Delivery Timeline */}
      <div className="bg-card border border-border rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold mb-6">Delivery Information</h2>

        <div className="relative">
          <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-border"></div>

          <div className="relative pl-12 pb-8">
            <div className="absolute left-0 w-8 h-8 rounded-full bg-primary flex items-center justify-center">
              <Package className="h-4 w-4 text-primary-foreground" />
            </div>
            <h3 className="font-medium">Order Placed</h3>
            <p className="text-sm text-muted-foreground">{orderDate}</p>
          </div>

          <div className="relative pl-12 pb-8">
            <div className={`absolute left-0 w-8 h-8 rounded-full ${order.status === 'processing' || order.status === 'shipped' ? 'bg-primary' : 'bg-muted'} flex items-center justify-center`}>
              <Package className={`h-4 w-4 ${order.status === 'processing' || order.status === 'shipped' ? 'text-primary-foreground' : 'text-muted-foreground'}`} />
            </div>
            <h3 className="font-medium">Processing</h3>
            <p className="text-sm text-muted-foreground">Your order is being prepared</p>
          </div>

          <div className="relative pl-12 pb-8">
            <div className={`absolute left-0 w-8 h-8 rounded-full ${order.status === 'shipped' ? 'bg-primary' : 'bg-muted'} flex items-center justify-center`}>
              <Truck className={`h-4 w-4 ${order.status === 'shipped' ? 'text-primary-foreground' : 'text-muted-foreground'}`} />
            </div>
            <h3 className="font-medium">Shipping</h3>
            <p className="text-sm text-muted-foreground">
              {order.status === 'shipped' ? 'Your order has been shipped' : 'Your order will be shipped soon'}
            </p>
          </div>

          <div className="relative pl-12">
            <div className={`absolute left-0 w-8 h-8 rounded-full ${order.status === 'delivered' ? 'bg-primary' : 'bg-muted'} flex items-center justify-center`}>
              <Calendar className={`h-4 w-4 ${order.status === 'delivered' ? 'text-primary-foreground' : 'text-muted-foreground'}`} />
            </div>
            <h3 className="font-medium">Estimated Delivery</h3>
            <p className="text-sm text-muted-foreground">{estimatedDelivery}</p>
          </div>
        </div>
      </div>
    </>
  );
}
