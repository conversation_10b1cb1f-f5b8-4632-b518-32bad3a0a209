'use client';

import { ZodFormValidation } from '@/components/examples/zod-form-validation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

/**
 * Zod Validation Example Page
 * 
 * This page demonstrates the integration of Zod with React Hook Form for form validation.
 * It showcases how to use Zod schemas for runtime type checking and form validation.
 */
export default function ZodValidationExamplePage() {
  return (
    <div className="container py-10 space-y-8">
      <div className="flex items-center gap-2">
        <Link href="/admin">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-3xl font-bold tracking-tight">Zod Validation with React Hook Form</h1>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Implementation Overview</CardTitle>
          <CardDescription>
            This page demonstrates the implementation of Zod with React Hook Form for form validation.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Zod</h3>
              <p className="text-sm text-muted-foreground">
                TypeScript-first schema validation with static type inference.
                Ensures data integrity and provides runtime type checking.
              </p>
            </div>
            
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">React Hook Form</h3>
              <p className="text-sm text-muted-foreground">
                Performant, flexible and extensible forms with easy-to-use validation.
                Reduces the amount of code you need to write while removing unnecessary re-renders.
              </p>
            </div>
          </div>
          
          <div className="pt-4 border-t">
            <h3 className="text-lg font-semibold mb-2">Key Files</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <strong className="font-medium">/src/lib/zod-schemas.ts</strong> - Zod schemas for database tables
              </li>
              <li>
                <strong className="font-medium">/src/components/examples/zod-form-validation.tsx</strong> - Example component using Zod with React Hook Form
              </li>
            </ul>
          </div>
        </CardContent>
      </Card>
      
      <Tabs defaultValue="demo" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="demo">Live Demo</TabsTrigger>
          <TabsTrigger value="usage">Usage Guide</TabsTrigger>
        </TabsList>
        
        <TabsContent value="demo" className="p-4 border rounded-md mt-2">
          <ZodFormValidation />
        </TabsContent>
        
        <TabsContent value="usage" className="p-4 border rounded-md mt-2 space-y-6">
          <div>
            <h3 className="text-xl font-semibold mb-2">How to Use</h3>
            
            <div className="space-y-4">
              <div>
                <h4 className="text-lg font-medium">1. Define Zod Schema</h4>
                <pre className="p-4 bg-muted rounded-md overflow-x-auto text-sm mt-2">
                  {`// Define your form schema with Zod
const formSchema = z.object({
  name: z.string().min(2, {
    message: "Name must be at least 2 characters.",
  }),
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  // Add more fields as needed
});

// Extract the type from the schema
type FormValues = z.infer<typeof formSchema>;`}
                </pre>
              </div>
              
              <div>
                <h4 className="text-lg font-medium">2. Set Up React Hook Form with Zod Resolver</h4>
                <pre className="p-4 bg-muted rounded-md overflow-x-auto text-sm mt-2">
                  {`// Import necessary dependencies
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

// Initialize the form with Zod resolver
const form = useForm<FormValues>({
  resolver: zodResolver(formSchema),
  defaultValues: {
    name: "",
    email: "",
    // Set default values for other fields
  },
});`}
                </pre>
              </div>
              
              <div>
                <h4 className="text-lg font-medium">3. Create Form Components</h4>
                <pre className="p-4 bg-muted rounded-md overflow-x-auto text-sm mt-2">
                  {`// Create your form with shadcn/ui components
<Form {...form}>
  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
    <FormField
      control={form.control}
      name="name"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Name</FormLabel>
          <FormControl>
            <Input {...field} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
    
    {/* Add more form fields */}
    
    <Button type="submit">Submit</Button>
  </form>
</Form>`}
                </pre>
              </div>
              
              <div>
                <h4 className="text-lg font-medium">4. Handle Form Submission</h4>
                <pre className="p-4 bg-muted rounded-md overflow-x-auto text-sm mt-2">
                  {`// Handle form submission
const onSubmit = async (data: FormValues) => {
  try {
    // Process the validated data
    console.log('Form data:', data);
    
    // Make API calls, update state, etc.
    
    // Reset the form on success
    form.reset();
  } catch (error) {
    console.error('Error:', error);
  }
};`}
                </pre>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
