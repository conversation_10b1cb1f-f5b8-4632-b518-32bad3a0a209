import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App.tsx";
import "./index.css";
import "./styles/responsive.css";
import { <PERSON>rowserRouter } from "react-router-dom";

// Simple logger for development
const logger = {
  info: (message: string, extra?: any) => console.log(`ℹ️ ${message}`, extra),
  warn: (message: string, extra?: any) => console.warn(`⚠️ ${message}`, extra),
  error: (message: string, error?: any) => console.error(`❌ ${message}`, error),
  debug: (message: string, extra?: any) => console.debug(`🐛 ${message}`, extra),
};

// Apply theme CSS variables
import { cssVariables } from "./config/theme";
const root = document.documentElement;
Object.entries(cssVariables).forEach(([key, value]) => {
  root.style.setProperty(key, value);
});

// Test database connection on startup
import { testDatabaseConnection } from "./utils/database-test";
testDatabaseConnection().catch((error) => {
  logger.error("Database connection test failed", error);
});

// Initialize development tools
import { TempoDevtools } from "tempo-devtools";
if (import.meta.env.VITE_NODE_ENV === 'development') {
  TempoDevtools.init();
}

const basename = import.meta.env.BASE_URL;

// Log application startup
logger.info("🚀 Thabo Bester application starting", {
  environment: import.meta.env.VITE_NODE_ENV,
  basename,
  theme: "gold-black",
});

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <BrowserRouter basename={basename}>
      <App />
    </BrowserRouter>
  </React.StrictMode>,
);
