# 🐳 DOCKER SETUP SCRIPT FOR THABO BESTER PROJECT (PowerShell)
# Sets up local development environment with Supabase and all services

param(
    [switch]$Force,
    [switch]$SkipMigrations
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Red
}

Write-Host "🚀 Setting up Thabo Bester Project with Docker..." -ForegroundColor $Blue

# Check if Docker is installed
try {
    docker --version | Out-Null
    Write-Success "Docker is installed"
} catch {
    Write-Error "Docker is not installed. Please install Docker Desktop first."
    exit 1
}

# Check if Docker Compose is available
try {
    docker-compose --version | Out-Null
    Write-Success "Docker Compose is available"
} catch {
    Write-Error "Docker Compose is not available. Please install Docker Compose."
    exit 1
}

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Warning ".env file not found."
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
        Write-Success ".env file created from .env.example"
        Write-Warning "Please update the .env file with your actual values before continuing."
        if (-not $Force) {
            exit 1
        }
    } else {
        Write-Error ".env.example file not found. Cannot create .env file."
        exit 1
    }
}

# Load environment variables from .env file
Write-Status "Loading environment variables..."
Get-Content ".env" | ForEach-Object {
    if ($_ -match "^([^#][^=]+)=(.*)$") {
        [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
    }
}

# Validate required environment variables
$requiredVars = @(
    "VITE_SUPABASE_URL",
    "VITE_SUPABASE_ANON_KEY"
)

foreach ($var in $requiredVars) {
    if (-not [Environment]::GetEnvironmentVariable($var)) {
        Write-Error "Required environment variable $var is not set in .env file"
        exit 1
    }
}

Write-Success "Environment variables validated successfully"

# Create necessary directories
Write-Status "Creating necessary directories..."
$directories = @("logs", "data\postgres", "data\redis", "data\supabase", "reports")
foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
}

# Stop any existing containers
Write-Status "Stopping any existing containers..."
docker-compose down --remove-orphans

# Pull latest images
Write-Status "Pulling latest Docker images..."
docker-compose pull

# Build the application
Write-Status "Building application container..."
docker-compose build --no-cache

# Start the services
Write-Status "Starting all services..."
docker-compose up -d

# Wait for services to be ready
Write-Status "Waiting for services to be ready..."

# Wait for PostgreSQL
Write-Status "Waiting for PostgreSQL to be ready..."
$timeout = 60
$counter = 0
do {
    Start-Sleep 1
    $counter++
    if ($counter -ge $timeout) {
        Write-Error "PostgreSQL failed to start within $timeout seconds"
        exit 1
    }
    $result = docker-compose exec -T supabase-db pg_isready -U postgres 2>$null
} while ($LASTEXITCODE -ne 0)
Write-Success "PostgreSQL is ready"

# Wait for Supabase REST API
Write-Status "Waiting for Supabase REST API to be ready..."
$timeout = 60
$counter = 0
do {
    Start-Sleep 1
    $counter++
    if ($counter -ge $timeout) {
        Write-Error "Supabase REST API failed to start within $timeout seconds"
        exit 1
    }
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:54326/rest/v1/" -TimeoutSec 1 -ErrorAction SilentlyContinue
        $ready = $response.StatusCode -eq 200
    } catch {
        $ready = $false
    }
} while (-not $ready)
Write-Success "Supabase REST API is ready"

# Run database migrations
if (-not $SkipMigrations) {
    Write-Status "Running database setup scripts..."
    
    $scripts = @(
        "supabase\complete-setup.sql",
        "supabase\create-missing-tables.sql",
        "supabase\complete-admin-sync.sql"
    )
    
    foreach ($script in $scripts) {
        if (Test-Path $script) {
            $scriptName = Split-Path $script -Leaf
            Write-Status "Running script: $scriptName"
            
            # Copy script to container and execute
            docker cp $script "$(docker-compose ps -q supabase-db):/tmp/$scriptName"
            docker-compose exec -T supabase-db psql -U postgres -d postgres -f "/tmp/$scriptName"
            
            Write-Success "Script $scriptName executed successfully"
        }
    }
}

# Check service health
Write-Status "Checking service health..."

$services = @(
    @{Name="app"; Port="5173"},
    @{Name="supabase-db"; Port="54321"},
    @{Name="supabase-rest"; Port="54326"},
    @{Name="supabase-realtime"; Port="54327"},
    @{Name="redis"; Port="6379"}
)

foreach ($service in $services) {
    $status = docker-compose ps $service.Name
    if ($status -match "Up") {
        Write-Success "$($service.Name) is running on port $($service.Port)"
    } else {
        Write-Error "$($service.Name) is not running"
    }
}

# Display service URLs
Write-Success "🎉 Docker setup completed successfully!"
Write-Host ""
Write-Host "📋 Service URLs:" -ForegroundColor $Blue
Write-Host "   🌐 Application:        http://localhost:5173"
Write-Host "   🗄️  Supabase REST API:  http://localhost:54326"
Write-Host "   ⚡ Supabase Realtime:  ws://localhost:54327"
Write-Host "   🐘 PostgreSQL:        localhost:54321"
Write-Host "   🔴 Redis:             localhost:6379"
Write-Host ""
Write-Host "📊 Monitoring:" -ForegroundColor $Blue
Write-Host "   📈 Prometheus:        http://localhost:9090"
Write-Host "   📊 Grafana:          http://localhost:3001"
Write-Host ""
Write-Host "🔧 Management Commands:" -ForegroundColor $Blue
Write-Host "   📜 View logs:         docker-compose logs -f"
Write-Host "   🛑 Stop services:     docker-compose down"
Write-Host "   🔄 Restart services:  docker-compose restart"
Write-Host "   🧹 Clean up:         docker-compose down -v --remove-orphans"
Write-Host ""
Write-Host "🎯 Next Steps:" -ForegroundColor $Blue
Write-Host "   1. Open http://localhost:5173 in your browser"
Write-Host "   2. Check that all services are working correctly"
Write-Host "   3. Run tests: npm run test"
Write-Host "   4. Start developing! 🚀"
Write-Host ""

# Create a status report
$reportContent = @"
# Docker Setup Report

**Date:** $(Get-Date)
**Status:** ✅ Success

## Services Started

$(docker-compose ps)

## Environment Configuration

- **Database URL:** postgresql://postgres:postgres@localhost:54321/postgres
- **Supabase URL:** http://localhost:54326
- **Realtime URL:** ws://localhost:54327
- **Application URL:** http://localhost:5173

## Database Setup

- ✅ PostgreSQL started successfully
- ✅ Supabase REST API started successfully
- ✅ Database migrations executed
- ✅ Setup scripts executed
- ✅ Admin user sync completed

## Next Steps

1. Verify all services are accessible
2. Run application tests
3. Check real-time functionality
4. Validate payment integration (Yoco)
5. Test WebSocket connections

## Troubleshooting

If you encounter issues:

1. Check logs: ``docker-compose logs -f``
2. Restart services: ``docker-compose restart``
3. Clean restart: ``docker-compose down && docker-compose up -d``
"@

$reportContent | Out-File -FilePath "reports\docker-setup-report.md" -Encoding UTF8
Write-Success "Setup report saved to reports\docker-setup-report.md"
