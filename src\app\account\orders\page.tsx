import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import { Button } from "@/components/ui/button";
import { createClient } from "../../../utils/supabase/server";
import Link from "next/link";
import { UserCircle, Package, CreditCard, Settings } from "lucide-react";
import { UserOrders } from "@/components/user-orders";

export default async function OrdersPage() {
  // Try to get user, but don't block rendering if it fails
  let user: any = null;
  try {
    const supabase = await createClient();
    const { data } = await supabase.auth.getUser();
    user = data.user;
  } catch (error) {
    console.error("Error fetching user:", error);
  }

  // Redirect to sign-in if not authenticated
  if (!user) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-background px-4 py-8">
        <div className="w-full max-w-md rounded-lg border border-border bg-card p-6 shadow-sm">
          <div className="space-y-4 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">Sign in to continue</h1>
            <p className="text-sm text-muted-foreground">
              You need to sign in or create an account to view your orders
            </p>
            <div className="flex flex-col space-y-2">
              <Link href="/sign-in?redirect_to=/account/orders">
                <Button className="w-full">Sign in</Button>
              </Link>
              <Link href="/sign-up?redirect_to=/account/orders">
                <Button variant="outline" className="w-full">Create account</Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // We'll use the UserOrders component to fetch and display orders

  // Mock user data
  const userData = {
    name: user.user_metadata?.full_name || "User",
    email: user.email
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      <main className="py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl font-bold text-foreground mb-8">My Account</h1>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-card border border-border rounded-lg p-6 sticky top-6">
                <div className="flex flex-col items-center mb-6">
                  <div className="w-20 h-20 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                    <UserCircle className="h-10 w-10 text-primary" />
                  </div>
                  <h2 className="text-lg font-semibold">{userData.name}</h2>
                  <p className="text-sm text-muted-foreground">{userData.email}</p>
                </div>

                <nav className="space-y-1">
                  <Link href="/account" className="flex items-center gap-3 px-3 py-2 rounded-md text-muted-foreground hover:bg-muted transition-colors">
                    <UserCircle className="h-5 w-5" />
                    <span>Profile</span>
                  </Link>
                  <Link href="/account/orders" className="flex items-center gap-3 px-3 py-2 rounded-md bg-primary/10 text-primary font-medium">
                    <Package className="h-5 w-5" />
                    <span>Orders</span>
                  </Link>
                  <Link href="/account/payment" className="flex items-center gap-3 px-3 py-2 rounded-md text-muted-foreground hover:bg-muted transition-colors">
                    <CreditCard className="h-5 w-5" />
                    <span>Payment Methods</span>
                  </Link>
                  <Link href="/account/settings" className="flex items-center gap-3 px-3 py-2 rounded-md text-muted-foreground hover:bg-muted transition-colors">
                    <Settings className="h-5 w-5" />
                    <span>Settings</span>
                  </Link>
                </nav>
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3">
              <div className="bg-card border border-border rounded-lg p-6">
                <h2 className="text-xl font-semibold mb-6">Order History</h2>
                <UserOrders />
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
