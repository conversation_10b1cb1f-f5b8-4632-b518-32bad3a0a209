import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  ShieldCheck,
  UserCheck,
  Loader2,
  Search,
  UserPlus,
  Mail,
  User,
  Calendar,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "../../../../supabase/auth";
import { supabase } from "../../../../supabase/supabase";
import { Tables } from "@/types/supabase";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

type UserWithRole = Tables<"users">;

type Subscription = {
  id: string;
  userId: string;
  plan: "free" | "basic" | "premium";
  status: "active" | "canceled" | "expired";
  startDate: Date;
  endDate: Date;
  autoRenew: boolean;
};

// Mock subscription data
const mockSubscriptions: Subscription[] = [
  {
    id: "1",
    userId: "1",
    plan: "premium",
    status: "active",
    startDate: new Date(2023, 4, 15),
    endDate: new Date(2024, 4, 15),
    autoRenew: true,
  },
  {
    id: "2",
    userId: "2",
    plan: "basic",
    status: "active",
    startDate: new Date(2023, 3, 10),
    endDate: new Date(2024, 3, 10),
    autoRenew: true,
  },
];

export default function UserManagement() {
  const [users, setUsers] = useState<UserWithRole[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [processingUser, setProcessingUser] = useState<string | null>(null);
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false);
  const [isUserDetailsDialogOpen, setIsUserDetailsDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserWithRole | null>(null);
  const [activeTab, setActiveTab] = useState("users");

  const { updateUserRole, isAdmin, user: currentUser } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .rpc('get_users_paginated', {
          page_size: 50,
          page_offset: 0,
          search_term: null,
          role_filter: null
        });

      if (error) throw error;
      setUsers(data || []);
    } catch (error) {
      console.error("Error fetching users:", error);
      toast({
        title: "Error",
        description: "Failed to load users",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRoleChange = async (userId: string, newRole: string) => {
    if (!isAdmin) {
      toast({
        title: "Permission Denied",
        description: "Only admins can change user roles",
        variant: "destructive",
      });
      return;
    }

    setProcessingUser(userId);
    try {
      await updateUserRole(userId, newRole);

      // Update local state
      setUsers(
        users.map((u) => (u.id === userId ? { ...u, role: newRole } : u)),
      );

      toast({
        title: "Role Updated",
        description: `User role changed to ${newRole}`,
      });
    } catch (error) {
      console.error("Error updating role:", error);
      toast({
        title: "Error",
        description: "Failed to update user role",
        variant: "destructive",
      });
    } finally {
      setProcessingUser(null);
    }
  };

  const handleViewUser = (user: UserWithRole) => {
    setSelectedUser(user);
    setIsUserDetailsDialogOpen(true);
  };

  const getUserSubscription = (userId: string): Subscription | undefined => {
    return mockSubscriptions.find((sub) => sub.userId === userId);
  };

  const filteredUsers = users.filter(
    (user) =>
      user.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.full_name?.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  if (!isAdmin) {
    return (
      <Card className="bg-white/90 backdrop-blur-sm border border-gray-100 rounded-2xl shadow-sm overflow-hidden">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-gray-900">
            Access Denied
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">
            You don't have permission to access the user management panel.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="bg-white/90 backdrop-blur-sm border border-gray-100 rounded-2xl shadow-sm overflow-hidden">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl font-semibold text-gray-900">
              User Management
            </CardTitle>
            <div className="flex gap-2">
              <Button
                onClick={() => setIsAddUserDialogOpen(true)}
                variant="outline"
                size="sm"
                className="h-9 gap-1"
              >
                <UserPlus className="h-4 w-4" />
                Add User
              </Button>
              <Button
                onClick={fetchUsers}
                variant="outline"
                size="sm"
                className="h-9 gap-1"
                disabled={loading}
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  "Refresh"
                )}
              </Button>
            </div>
          </div>
          <div className="relative mt-2">
            <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search users..."
              className="pl-9 h-10 bg-gray-50 border-gray-200"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </CardHeader>
        <CardContent>
          <Tabs
            defaultValue="users"
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="users">All Users</TabsTrigger>
              <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
            </TabsList>

            <TabsContent value="users">
              {loading ? (
                <div className="flex justify-center items-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredUsers.length === 0 ? (
                    <p className="text-center py-4 text-gray-500">
                      No users found
                    </p>
                  ) : (
                    filteredUsers.map((user) => (
                      <div
                        key={user.id}
                        className="flex items-center justify-between p-4 bg-gray-50 rounded-xl border border-gray-100"
                      >
                        <div className="flex items-center gap-3">
                          <Avatar className="h-10 w-10 border-2 border-white shadow-sm">
                            <AvatarImage
                              src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${user.email}`}
                              alt={user.email || ""}
                            />
                            <AvatarFallback>
                              {user.email?.[0].toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-gray-900">
                              {user.full_name || "Unnamed User"}
                            </p>
                            <p className="text-sm text-gray-500">
                              {user.email}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          {user.role === "admin" ? (
                            <Badge className="bg-purple-100 text-purple-800 border-purple-200 flex items-center gap-1">
                              <ShieldCheck className="h-3 w-3" />
                              Admin
                            </Badge>
                          ) : (
                            <Badge className="bg-blue-50 text-blue-700 border-blue-100">
                              <UserCheck className="h-3 w-3 mr-1" />
                              User
                            </Badge>
                          )}

                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-gray-600"
                            onClick={() => handleViewUser(user)}
                          >
                            View Details
                          </Button>

                          {/* Don't allow changing your own role */}
                          {user.id !== currentUser?.id && (
                            <Button
                              variant="outline"
                              size="sm"
                              className={
                                user.role === "admin"
                                  ? "border-purple-200 text-purple-700"
                                  : "border-blue-200 text-blue-700"
                              }
                              onClick={() =>
                                handleRoleChange(
                                  user.id,
                                  user.role === "admin" ? "user" : "admin",
                                )
                              }
                              disabled={!!processingUser}
                            >
                              {processingUser === user.id ? (
                                <Loader2 className="h-3 w-3 animate-spin" />
                              ) : user.role === "admin" ? (
                                "Remove Admin"
                              ) : (
                                "Make Admin"
                              )}
                            </Button>
                          )}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              )}
            </TabsContent>

            <TabsContent value="subscriptions">
              <div className="space-y-4">
                {filteredUsers.map((user) => {
                  const subscription = getUserSubscription(user.id);
                  if (!subscription) return null;

                  return (
                    <div
                      key={user.id}
                      className="flex items-center justify-between p-4 bg-gray-50 rounded-xl border border-gray-100"
                    >
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10 border-2 border-white shadow-sm">
                          <AvatarImage
                            src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${user.email}`}
                            alt={user.email || ""}
                          />
                          <AvatarFallback>
                            {user.email?.[0].toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium text-gray-900">
                            {user.full_name || "Unnamed User"}
                          </p>
                          <p className="text-sm text-gray-500">{user.email}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-6">
                        <div>
                          <p className="text-xs text-gray-500">Plan</p>
                          <Badge
                            className={
                              subscription.plan === "premium"
                                ? "bg-purple-100 text-purple-800"
                                : subscription.plan === "basic"
                                  ? "bg-blue-100 text-blue-800"
                                  : "bg-gray-100 text-gray-800"
                            }
                          >
                            {subscription.plan.charAt(0).toUpperCase() +
                              subscription.plan.slice(1)}
                          </Badge>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Status</p>
                          <Badge
                            className={
                              subscription.status === "active"
                                ? "bg-green-100 text-green-800"
                                : subscription.status === "canceled"
                                  ? "bg-yellow-100 text-yellow-800"
                                  : "bg-red-100 text-red-800"
                            }
                          >
                            {subscription.status.charAt(0).toUpperCase() +
                              subscription.status.slice(1)}
                          </Badge>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Expires</p>
                          <p className="text-sm font-medium">
                            {subscription.endDate.toLocaleDateString()}
                          </p>
                        </div>
                        <Button variant="outline" size="sm">
                          Manage
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Add User Dialog */}
      <Dialog open={isAddUserDialogOpen} onOpenChange={setIsAddUserDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New User</DialogTitle>
            <DialogDescription>
              Create a new user account. They will receive an email to set their
              password.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Full Name</Label>
              <Input id="name" placeholder="John Doe" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input id="email" type="email" placeholder="<EMAIL>" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="role">Role</Label>
              <Select defaultValue="user">
                <SelectTrigger>
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="user">User</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsAddUserDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button>Create User</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* User Details Dialog */}
      <Dialog
        open={isUserDetailsDialogOpen}
        onOpenChange={setIsUserDetailsDialogOpen}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>User Details</DialogTitle>
          </DialogHeader>

          {selectedUser && (
            <div className="space-y-6">
              <div className="flex flex-col items-center justify-center gap-4">
                <Avatar className="h-20 w-20 border-2 border-white shadow-md">
                  <AvatarImage
                    src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${selectedUser.email}`}
                    alt={selectedUser.email || ""}
                  />
                  <AvatarFallback className="text-xl">
                    {selectedUser.email?.[0].toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="text-center">
                  <h3 className="text-lg font-medium">
                    {selectedUser.full_name || "Unnamed User"}
                  </h3>
                  {selectedUser.role === "admin" ? (
                    <Badge className="bg-purple-100 text-purple-800 border-purple-200 mt-1">
                      <ShieldCheck className="h-3 w-3 mr-1" />
                      Admin
                    </Badge>
                  ) : (
                    <Badge className="bg-blue-50 text-blue-700 border-blue-100 mt-1">
                      <UserCheck className="h-3 w-3 mr-1" />
                      User
                    </Badge>
                  )}
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2 text-gray-700">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span>{selectedUser.email}</span>
                </div>
                <div className="flex items-center gap-2 text-gray-700">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span>
                    Joined{" "}
                    {new Date(selectedUser.created_at).toLocaleDateString()}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-gray-700">
                  <User className="h-4 w-4 text-gray-500" />
                  <span>ID: {selectedUser.id}</span>
                </div>
              </div>

              {/* Subscription information if available */}
              {getUserSubscription(selectedUser.id) && (
                <div className="border-t border-gray-100 pt-4 mt-4">
                  <h4 className="font-medium mb-2">Subscription</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <p className="text-gray-500">Plan</p>
                      <p className="font-medium">
                        {(() => {
                          const subscription = getUserSubscription(selectedUser.id);
                          return subscription?.plan
                            ? subscription.plan.charAt(0).toUpperCase() + subscription.plan.slice(1)
                            : 'Free';
                        })()}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-500">Status</p>
                      <p className="font-medium">
                        {(() => {
                          const subscription = getUserSubscription(selectedUser.id);
                          return subscription?.status
                            ? subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)
                            : 'Inactive';
                        })()}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-500">Start Date</p>
                      <p className="font-medium">
                        {getUserSubscription(
                          selectedUser.id,
                        )?.startDate.toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-500">End Date</p>
                      <p className="font-medium">
                        {getUserSubscription(
                          selectedUser.id,
                        )?.endDate.toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-500">Auto Renew</p>
                      <p className="font-medium">
                        {getUserSubscription(selectedUser.id)?.autoRenew
                          ? "Yes"
                          : "No"}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setIsUserDetailsDialogOpen(false)}
                >
                  Close
                </Button>
                <Button>Edit User</Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
