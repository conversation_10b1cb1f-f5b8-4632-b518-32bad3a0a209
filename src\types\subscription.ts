export interface MentorshipSubscription {
  id: string;
  user_id: string;
  program_id: string;
  program_name: string;
  program_type: string;
  duration: number;
  billing_cycle: string;
  amount: number;
  status: 'pending' | 'active' | 'cancelled' | 'completed';
  payment_status: 'pending' | 'paid' | 'failed';
  created_at: string;
  updated_at: string;
}

export interface Student {
  id: string;
  user_id: string;
  email: string;
  first_name: string;
  last_name: string;
  status: 'active' | 'inactive';
  program_type: string;
  program_duration: number;
  start_date: string;
  created_at: string;
  updated_at: string;
}

export interface Order {
  id: string;
  user_id: string;
  amount: number;
  status: 'pending' | 'completed' | 'cancelled';
  payment_status: 'pending' | 'paid' | 'failed';
  created_at: string;
  updated_at: string;
}
