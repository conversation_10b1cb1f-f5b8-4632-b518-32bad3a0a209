-- FINAL ADMIN AUTH FIX - NO MORE RECURSION
-- This permanently fixes the infinite recursion issue
-- Run this in Supabase SQL Editor

-- Step 1: Drop ALL existing policies that could cause recursion
DROP POLICY IF EXISTS "users_view_own_profile" ON public.users;
DROP POLICY IF EXISTS "users_update_own_profile" ON public.users;
DROP POLICY IF EXISTS "users_insert_own_profile" ON public.users;
DROP POLICY IF EXISTS "service_role_full_access" ON public.users;
DROP POLICY IF EXISTS "admins_view_all_users" ON public.users;
DROP POLICY IF EXISTS "admins_can_access_all" ON public.users;
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Admins can view all users" ON public.users;
DROP POLICY IF EXISTS "Ad<PERSON> can manage all users" ON public.users;
DROP POLICY IF EXISTS "Users can view own data" ON public.users;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.users;
DROP POLICY IF EXISTS "Allow trigger to insert user profiles" ON public.users;

-- Step 2: Create SIMPLE, NON-RECURSIVE policies
-- Policy 1: Users can view their own profile
CREATE POLICY "user_own_select" 
  ON public.users 
  FOR SELECT 
  USING (auth.uid() = id);

-- Policy 2: Users can update their own profile
CREATE POLICY "user_own_update" 
  ON public.users 
  FOR UPDATE 
  USING (auth.uid() = id);

-- Policy 3: Users can insert their own profile
CREATE POLICY "user_own_insert" 
  ON public.users 
  FOR INSERT 
  WITH CHECK (auth.uid() = id);

-- Policy 4: Service role has full access (for triggers)
CREATE POLICY "service_role_access" 
  ON public.users 
  FOR ALL 
  USING (auth.role() = 'service_role');

-- Policy 5: Admin access using JWT (NO TABLE LOOKUP - NO RECURSION)
CREATE POLICY "admin_jwt_access" 
  ON public.users 
  FOR ALL 
  USING (
    auth.role() = 'service_role' OR
    (auth.jwt() ->> 'role' = 'admin') OR
    (auth.jwt() -> 'user_metadata' ->> 'role' = 'admin')
  );

-- Step 3: Update admin users' JWT metadata to include role
-- This ensures the JWT-based policy works
UPDATE auth.users 
SET raw_user_meta_data = 
  CASE 
    WHEN raw_user_meta_data IS NULL THEN 
      jsonb_build_object('role', 'admin')
    ELSE 
      raw_user_meta_data || jsonb_build_object('role', 'admin')
  END
WHERE id IN (
  SELECT id FROM public.users WHERE role = 'admin'
);

-- Step 4: Ensure all admin users exist in public.users
INSERT INTO public.users (
    id,
    email,
    full_name,
    name,
    role,
    admin_role,
    token_identifier,
    created_at,
    updated_at
)
SELECT 
    au.id,
    au.email,
    COALESCE(au.raw_user_meta_data->>'full_name', 'Admin User'),
    COALESCE(au.raw_user_meta_data->>'full_name', 'Admin User'),
    'admin'::user_role,
    'admin',
    au.id::text,
    au.created_at,
    NOW()
FROM auth.users au
LEFT JOIN public.users pu ON au.id = pu.id
WHERE au.raw_user_meta_data->>'role' = 'admin'
AND pu.id IS NULL
ON CONFLICT (id) DO UPDATE SET
    role = 'admin'::user_role,
    admin_role = 'admin',
    updated_at = NOW();

-- Step 5: Grant permissions
GRANT USAGE ON TYPE user_role TO authenticated;
GRANT USAGE ON TYPE user_role TO anon;
GRANT SELECT, INSERT, UPDATE ON public.users TO authenticated;
GRANT SELECT ON public.users TO anon;

-- Step 6: Verify the fix
SELECT 
    'VERIFICATION - Admin users in public.users:' as info,
    id,
    email,
    role,
    admin_role
FROM public.users 
WHERE role = 'admin';

SELECT 
    'VERIFICATION - Admin users in auth.users:' as info,
    id,
    email,
    raw_user_meta_data->>'role' as metadata_role
FROM auth.users 
WHERE raw_user_meta_data->>'role' = 'admin';

-- Step 7: Success message
DO $$
BEGIN
    RAISE NOTICE 'FINAL ADMIN AUTH FIX COMPLETED - NO MORE RECURSION!';
    RAISE NOTICE 'Admin users can now sign in without infinite recursion errors.';
END
$$;
