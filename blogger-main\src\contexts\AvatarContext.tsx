import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from '../../supabase/auth';
import { supabase } from '../../supabase/supabase';

interface AvatarContextType {
  avatarUrl: string | null;
  updateAvatar: (url: string) => void;
  refreshAvatar: () => Promise<void>;
}

const AvatarContext = createContext<AvatarContextType | undefined>(undefined);

export function AvatarProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null);

  // Function to get avatar URL from multiple sources
  const getAvatarUrl = async (userId: string, userEmail?: string) => {
    try {
      console.log('🔍 Getting avatar URL for user:', userId);

      // First try to get from profiles table
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('avatar_url')
        .eq('id', userId)
        .single();

      if (!profileError && profileData?.avatar_url) {
        console.log('✅ Found avatar in profiles table:', profileData.avatar_url);
        return profileData.avatar_url;
      }

      // Then try to get from auth user metadata
      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser();

      if (!authError && authUser?.user_metadata?.avatar_url) {
        console.log('✅ Found avatar in auth metadata:', authUser.user_metadata.avatar_url);
        return authUser.user_metadata.avatar_url;
      }

      // Fallback to generated avatar
      const fallbackUrl = `https://api.dicebear.com/7.x/avataaars/svg?seed=${userEmail || user?.email || userId}`;
      console.log('📷 Using fallback avatar:', fallbackUrl);
      return fallbackUrl;
    } catch (error) {
      console.error('❌ Error getting avatar URL:', error);
      return `https://api.dicebear.com/7.x/avataaars/svg?seed=${userEmail || user?.email || userId}`;
    }
  };

  // Function to refresh avatar from database
  const refreshAvatar = async () => {
    if (!user) return;

    console.log('🔄 Refreshing avatar for user:', user.email);
    const url = await getAvatarUrl(user.id, user.email);
    setAvatarUrl(url);
    console.log('✅ Avatar refreshed:', url);
  };

  // Function to update avatar URL
  const updateAvatar = (url: string) => {
    setAvatarUrl(url);
  };

  // Load avatar when user changes
  useEffect(() => {
    if (user) {
      refreshAvatar();
    } else {
      setAvatarUrl(null);
    }
  }, [user]);

  return (
    <AvatarContext.Provider value={{ avatarUrl, updateAvatar, refreshAvatar }}>
      {children}
    </AvatarContext.Provider>
  );
}

export function useAvatar() {
  const context = useContext(AvatarContext);
  if (context === undefined) {
    throw new Error('useAvatar must be used within an AvatarProvider');
  }
  return context;
}
