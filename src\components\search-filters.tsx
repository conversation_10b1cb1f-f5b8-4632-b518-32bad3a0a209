"use client";

import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Slider } from "@/components/ui/slider";
import { Filter } from "lucide-react";

export function SearchFilters() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [categories, setCategories] = useState<string[]>(
    searchParams.get("category")?.split(",") || []
  );
  const [priceRange, setPriceRange] = useState<[number, number]>([
    parseInt(searchParams.get("minPrice") || "0"),
    parseInt(searchParams.get("maxPrice") || "5000"),
  ]);
  const [rating, setRating] = useState<number>(
    parseInt(searchParams.get("rating") || "0")
  );

  const handleCategoryChange = (category: string, checked: boolean) => {
    if (checked) {
      setCategories([...categories, category]);
    } else {
      setCategories(categories.filter((c) => c !== category));
    }
  };

  const handlePriceChange = (value: number[]) => {
    setPriceRange([value[0], value[1]]);
  };

  const handleRatingChange = (value: number) => {
    setRating(value);
  };

  const applyFilters = () => {
    const params = new URLSearchParams(searchParams.toString());
    
    // Update category filter
    if (categories.length > 0) {
      params.set("category", categories.join(","));
    } else {
      params.delete("category");
    }
    
    // Update price range filter
    if (priceRange[0] > 0) {
      params.set("minPrice", priceRange[0].toString());
    } else {
      params.delete("minPrice");
    }
    
    if (priceRange[1] < 5000) {
      params.set("maxPrice", priceRange[1].toString());
    } else {
      params.delete("maxPrice");
    }
    
    // Update rating filter
    if (rating > 0) {
      params.set("rating", rating.toString());
    } else {
      params.delete("rating");
    }
    
    // Navigate to the new URL with filters
    router.push(`/search?${params.toString()}`);
  };

  const resetFilters = () => {
    setCategories([]);
    setPriceRange([0, 5000]);
    setRating(0);
    
    // Remove all filter params but keep the search query
    const query = searchParams.get("q");
    if (query) {
      router.push(`/search?q=${query}`);
    } else {
      router.push("/search");
    }
  };

  return (
    <div className="bg-card border border-border rounded-lg p-6 sticky top-24">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold flex items-center">
          <Filter className="h-5 w-5 mr-2" />
          Filters
        </h2>
        <Button variant="ghost" size="sm" className="text-xs" onClick={resetFilters}>
          Reset All
        </Button>
      </div>

      {/* Category Filter */}
      <div className="mb-6">
        <h3 className="font-medium mb-3">Category</h3>
        <div className="space-y-2">
          {["Rackets", "Balls", "Apparel", "Footwear", "Accessories"].map((category) => (
            <div key={category} className="flex items-center">
              <Checkbox
                id={`category-${category.toLowerCase()}`}
                checked={categories.includes(category.toLowerCase())}
                onCheckedChange={(checked) => 
                  handleCategoryChange(category.toLowerCase(), checked as boolean)
                }
              />
              <label
                htmlFor={`category-${category.toLowerCase()}`}
                className="ml-2 text-sm text-foreground"
              >
                {category}
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Price Range Filter */}
      <div className="mb-6">
        <h3 className="font-medium mb-3">Price Range</h3>
        <div className="px-2">
          <Slider
            defaultValue={priceRange}
            min={0}
            max={5000}
            step={100}
            onValueChange={handlePriceChange}
            className="my-6"
          />
          <div className="flex items-center justify-between">
            <div>
              <span className="text-xs text-muted-foreground">Min</span>
              <p className="font-medium">R {priceRange[0]}</p>
            </div>
            <div className="text-right">
              <span className="text-xs text-muted-foreground">Max</span>
              <p className="font-medium">R {priceRange[1]}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Rating Filter */}
      <div className="mb-6">
        <h3 className="font-medium mb-3">Rating</h3>
        <div className="space-y-2">
          {[4, 3, 2, 1].map((r) => (
            <div key={r} className="flex items-center">
              <Checkbox
                id={`rating-${r}`}
                checked={rating === r}
                onCheckedChange={(checked) => {
                  if (checked) setRating(r);
                  else if (rating === r) setRating(0);
                }}
              />
              <label
                htmlFor={`rating-${r}`}
                className="ml-2 text-sm text-foreground flex items-center"
              >
                {r}+ ★
              </label>
            </div>
          ))}
        </div>
      </div>

      <Button className="w-full" onClick={applyFilters}>
        Apply Filters
      </Button>
    </div>
  );
}
