'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useState, ReactNode } from 'react';

/**
 * QueryProvider component
 * 
 * This component provides the TanStack Query client to the application.
 * It creates a new QueryClient instance for each client session.
 */
export function QueryProvider({ children }: { children: ReactNode }) {
  // Create a new QueryClient instance for each client session
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        // Default query options
        staleTime: 60 * 1000, // 1 minute
        refetchOnWindowFocus: false,
        retry: 1,
      },
    },
  }));

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
}
