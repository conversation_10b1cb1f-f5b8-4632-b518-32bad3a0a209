import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CheckCircle, Package, ArrowRight } from "lucide-react";
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import { redirect } from "next/navigation";
import { OrderDetails } from "@/components/order-details";
import { createClient } from "@/utils/supabase/server";

export default async function OrderConfirmation({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined };
}) {
  // Get user if authenticated
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();

  // Get order_id from query parameters
  const orderId = searchParams.order_id as string | undefined;

  if (!orderId) {
    // If no order ID is provided, redirect to home
    redirect("/");
  }

  console.log("Order confirmation page - Order ID:", orderId);

  // Check if order exists first
  const { data: orderExists, error: checkError } = await supabase
    .from('orders')
    .select('id')
    .eq('id', orderId)
    .maybeSingle();

  console.log("Order exists check:", orderExists, checkError);

  // If user is authenticated and order exists, handle user association
  if (user && orderExists) {
    try {
      // Check if this order exists in our database
      const { data: orderData, error: orderError } = await supabase
        .from('orders')
        .select('*')
        .eq('id', orderId)
        .single();

      console.log("Order data:", orderData);
      
      if (orderError) {
        console.error("Error fetching order:", orderError);
      }

      // If order exists but isn't associated with this user, update it
      if (orderData && orderData.user_id !== user.id) {
        // This could happen if the user logged in during the checkout process
        const { error } = await supabase
          .from('orders')
          .update({ user_id: user.id })
          .eq('id', orderId);

        if (error) {
          console.error('Error associating order with user:', error);
        }
      }
      
      // Update order status to confirmed if it exists
      if (orderData) {
        const { error } = await supabase
          .from('orders')
          .update({ 
            status: 'confirmed',
            payment_status: 'paid'
          })
          .eq('id', orderId);
          
        if (error) {
          console.error('Error updating order status:', error);
        }
      }
    } catch (error) {
      console.error('Error checking order ownership:', error);
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      <main className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            {/* Order Confirmation Header */}
            <div className="text-center mb-12">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 mb-4">
                <CheckCircle className="h-8 w-8 text-primary" />
              </div>
              <h1 className="text-3xl font-bold text-foreground mb-2">Order Confirmed!</h1>
              <p className="text-muted-foreground">
                Thank you for your purchase. Your order has been received and is being processed.
              </p>
            </div>

            {/* Order Details Component */}
            <OrderDetails sessionId={orderId} />

            {/* Actions */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild>
                <Link href="/shop">
                  Continue Shopping
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>

              <Button variant="outline" asChild>
                <Link href="/account/orders">
                  <Package className="mr-2 h-4 w-4" />
                  View All Orders
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
