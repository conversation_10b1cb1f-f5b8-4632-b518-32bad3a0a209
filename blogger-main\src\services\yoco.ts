// 💳 YOCO PAYMENT GATEWAY INTEGRATION
// South African Payment Processing with ZAR Currency

// Simple logger for development
const logger = {
  info: (message: string, extra?: any) => console.log(`ℹ️ ${message}`, extra),
  warn: (message: string, extra?: any) => console.warn(`⚠️ ${message}`, extra),
  error: (message: string, error?: any) => console.error(`❌ ${message}`, error),
  debug: (message: string, extra?: any) => console.debug(`🐛 ${message}`, extra),
};

const trackPaymentError = (error: any, service: string, amount?: number) => {
  console.error(`💳 Payment Error [${service}]:`, { error, amount });
};

// Environment variables
const YOCO_PUBLIC_KEY = import.meta.env.VITE_YOCO_PUBLIC_KEY;
const YOCO_SECRET_KEY = import.meta.env.YOCO_SECRET_KEY;
const PAYMENT_CURRENCY = import.meta.env.VITE_PAYMENT_CURRENCY || 'ZAR';

// Yoco API endpoints
const YOCO_API_BASE = 'https://online.yoco.com/v1';
const YOCO_CHECKOUT_BASE = 'https://checkout.yoco.com';

// Types
export interface YocoPaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'processing' | 'succeeded' | 'failed' | 'cancelled';
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface YocoCheckoutSession {
  id: string;
  url: string;
  amount: number;
  currency: string;
  metadata?: Record<string, any>;
  success_url: string;
  cancel_url: string;
  expires_at: string;
}

export interface YocoPaymentMethod {
  id: string;
  type: 'card' | 'eft' | 'mobile_money';
  card?: {
    brand: string;
    last4: string;
    exp_month: number;
    exp_year: number;
  };
}

export interface YocoCustomer {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  created_at: string;
}

// Validation
if (!YOCO_PUBLIC_KEY) {
  logger.warn('Yoco public key not configured');
}

// Yoco SDK wrapper
class YocoSDK {
  private publicKey: string;
  private isInitialized: boolean = false;

  constructor(publicKey: string) {
    this.publicKey = publicKey;
  }

  // Initialize Yoco SDK
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Load Yoco SDK script
      await this.loadScript();
      
      // Initialize with public key
      if (window.YocoSDK) {
        window.YocoSDK.initialize({
          publicKey: this.publicKey,
          currency: PAYMENT_CURRENCY,
        });
        this.isInitialized = true;
        logger.info('Yoco SDK initialized successfully');
      } else {
        throw new Error('Yoco SDK not loaded');
      }
    } catch (error) {
      logger.error('Failed to initialize Yoco SDK', error);
      throw error;
    }
  }

  // Load Yoco SDK script
  private loadScript(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (document.querySelector('script[src*="yoco"]')) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://js.yoco.com/sdk/v1/yoco-sdk-web.js';
      script.async = true;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load Yoco SDK'));
      document.head.appendChild(script);
    });
  }

  // Create payment intent
  async createPaymentIntent(amount: number, metadata?: Record<string, any>): Promise<YocoPaymentIntent> {
    await this.initialize();

    try {
      const response = await fetch(`${YOCO_API_BASE}/charges`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${YOCO_SECRET_KEY}`,
        },
        body: JSON.stringify({
          amount: Math.round(amount * 100), // Convert to cents
          currency: PAYMENT_CURRENCY,
          metadata: metadata || {},
        }),
      });

      if (!response.ok) {
        throw new Error(`Yoco API error: ${response.status}`);
      }

      const paymentIntent = await response.json();
      logger.info('Payment intent created', { id: paymentIntent.id, amount });
      
      return paymentIntent;
    } catch (error) {
      trackPaymentError(error, 'yoco', amount);
      throw error;
    }
  }

  // Create checkout session
  async createCheckoutSession(
    amount: number,
    successUrl: string,
    cancelUrl: string,
    metadata?: Record<string, any>
  ): Promise<YocoCheckoutSession> {
    await this.initialize();

    try {
      const response = await fetch(`${YOCO_CHECKOUT_BASE}/sessions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${YOCO_SECRET_KEY}`,
        },
        body: JSON.stringify({
          amount: Math.round(amount * 100), // Convert to cents
          currency: PAYMENT_CURRENCY,
          success_url: successUrl,
          cancel_url: cancelUrl,
          metadata: metadata || {},
          payment_method_types: ['card', 'eft'],
        }),
      });

      if (!response.ok) {
        throw new Error(`Yoco Checkout API error: ${response.status}`);
      }

      const session = await response.json();
      logger.info('Checkout session created', { id: session.id, amount });
      
      return session;
    } catch (error) {
      trackPaymentError(error, 'yoco_checkout', amount);
      throw error;
    }
  }

  // Process card payment
  async processCardPayment(
    paymentIntentId: string,
    cardDetails: {
      number: string;
      expiry: string;
      cvv: string;
      name: string;
    }
  ): Promise<YocoPaymentIntent> {
    await this.initialize();

    try {
      if (!window.YocoSDK) {
        throw new Error('Yoco SDK not initialized');
      }

      // Tokenize card details
      const token = await window.YocoSDK.card.tokenize({
        number: cardDetails.number,
        expiry: cardDetails.expiry,
        cvv: cardDetails.cvv,
        name: cardDetails.name,
      });

      // Confirm payment with token
      const response = await fetch(`${YOCO_API_BASE}/charges/${paymentIntentId}/confirm`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${YOCO_SECRET_KEY}`,
        },
        body: JSON.stringify({
          token: token.id,
        }),
      });

      if (!response.ok) {
        throw new Error(`Yoco payment confirmation error: ${response.status}`);
      }

      const result = await response.json();
      logger.info('Card payment processed', { id: result.id, status: result.status });
      
      return result;
    } catch (error) {
      trackPaymentError(error, 'yoco_card', undefined);
      throw error;
    }
  }

  // Get payment status
  async getPaymentStatus(paymentIntentId: string): Promise<YocoPaymentIntent> {
    try {
      const response = await fetch(`${YOCO_API_BASE}/charges/${paymentIntentId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${YOCO_SECRET_KEY}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Yoco API error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      logger.error('Failed to get payment status', error);
      throw error;
    }
  }

  // Refund payment
  async refundPayment(paymentIntentId: string, amount?: number): Promise<any> {
    try {
      const response = await fetch(`${YOCO_API_BASE}/charges/${paymentIntentId}/refunds`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${YOCO_SECRET_KEY}`,
        },
        body: JSON.stringify({
          amount: amount ? Math.round(amount * 100) : undefined,
        }),
      });

      if (!response.ok) {
        throw new Error(`Yoco refund error: ${response.status}`);
      }

      const result = await response.json();
      logger.info('Payment refunded', { id: result.id, amount: result.amount });
      
      return result;
    } catch (error) {
      logger.error('Failed to refund payment', error);
      throw error;
    }
  }
}

// Create singleton instance
export const yoco = new YocoSDK(YOCO_PUBLIC_KEY || '');

// Utility functions
export const formatYocoAmount = (amount: number): number => {
  return Math.round(amount * 100); // Convert to cents
};

export const parseYocoAmount = (amount: number): number => {
  return amount / 100; // Convert from cents
};

// Payment method helpers
export const getPaymentMethodIcon = (type: string): string => {
  switch (type) {
    case 'card':
      return '💳';
    case 'eft':
      return '🏦';
    case 'mobile_money':
      return '📱';
    default:
      return '💰';
  }
};

// Error handling
export const handleYocoError = (error: any): string => {
  if (error.code) {
    switch (error.code) {
      case 'card_declined':
        return 'Your card was declined. Please try a different payment method.';
      case 'insufficient_funds':
        return 'Insufficient funds. Please check your account balance.';
      case 'invalid_card':
        return 'Invalid card details. Please check and try again.';
      case 'expired_card':
        return 'Your card has expired. Please use a different card.';
      case 'processing_error':
        return 'Payment processing error. Please try again.';
      default:
        return 'Payment failed. Please try again or contact support.';
    }
  }
  
  return error.message || 'An unexpected error occurred during payment.';
};

// Export types
export type {
  YocoPaymentIntent,
  YocoCheckoutSession,
  YocoPaymentMethod,
  YocoCustomer,
};

// Global type declarations
declare global {
  interface Window {
    YocoSDK: any;
  }
}

export default yoco;
