import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import {
  MessageSquare,
  Search,
  Reply,
  Trash2,
  Edit,
  Eye,
  Calendar,
  User,
  ExternalLink,
} from 'lucide-react';
import { useAuth } from '../../../../supabase/auth';
import { supabase } from '../../../../supabase/supabase';

interface Comment {
  id: string;
  content: string;
  created_at: string;
  updated_at: string;
  likes: number;
  is_approved: boolean;
  user_name: string;
  article: {
    id: string;
    title: string;
    slug: string;
  };
  parent_comment?: {
    id: string;
    content: string;
    user_name: string;
  };
}

export function Comments() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [comments, setComments] = useState<Comment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [editingComment, setEditingComment] = useState<string | null>(null);
  const [editContent, setEditContent] = useState('');

  useEffect(() => {
    if (user) {
      loadComments();
    }
  }, [user]);

  const loadComments = async () => {
    try {
      setIsLoading(true);

      // Get comments with proper joins to get real data
      const { data, error } = await supabase
        .from('comments')
        .select(`
          *,
          articles!inner(id, title, slug),
          profiles!inner(first_name, last_name, email)
        `)
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Format the data with real article and user data
      const commentsWithRealData = (data || []).map(comment => ({
        ...comment,
        article: {
          id: comment.articles.id,
          title: comment.articles.title,
          slug: comment.articles.slug
        },
        user_name: `${comment.profiles.first_name || ''} ${comment.profiles.last_name || ''}`.trim() ||
                   comment.profiles.email.split('@')[0],
        parent_comment: comment.parent_id ? {
          id: comment.parent_id,
          content: 'Parent comment content', // We'll need to fetch this separately if needed
          user_name: 'Another User' // We'll need to fetch this separately if needed
        } : null
      }));

      setComments(commentsWithRealData);
    } catch (error) {
      console.error('Error loading comments:', error);

      // Show empty state instead of fallback data
      setComments([]);

      toast({
        title: 'Error Loading Comments',
        description: 'Failed to load comments from database',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    if (!confirm('Are you sure you want to delete this comment?')) return;

    try {
      const { error } = await supabase
        .from('comments')
        .delete()
        .eq('id', commentId);

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'Comment deleted successfully',
      });

      loadComments();
    } catch (error) {
      console.error('Error deleting comment:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete comment',
        variant: 'destructive',
      });
    }
  };

  const handleEditComment = async (commentId: string) => {
    if (!editContent.trim()) return;

    try {
      const { error } = await supabase
        .from('comments')
        .update({
          content: editContent,
          updated_at: new Date().toISOString(),
        })
        .eq('id', commentId);

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'Comment updated successfully',
      });

      setEditingComment(null);
      setEditContent('');
      loadComments();
    } catch (error) {
      console.error('Error updating comment:', error);
      toast({
        title: 'Error',
        description: 'Failed to update comment',
        variant: 'destructive',
      });
    }
  };

  const startEditing = (comment: Comment) => {
    setEditingComment(comment.id);
    setEditContent(comment.content);
  };

  const cancelEditing = () => {
    setEditingComment(null);
    setEditContent('');
  };

  const filteredComments = comments.filter(comment =>
    comment.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
    comment.article?.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const stats = {
    total: comments.length,
    approved: comments.filter(c => c.is_approved).length,
    pending: comments.filter(c => !c.is_approved).length,
    replies: comments.filter(c => c.parent_comment).length,
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <MessageSquare className="h-8 w-8 text-blue-600" />
            My Comments
          </h1>
          <p className="text-gray-600 mt-1">
            Manage your comments and replies
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Comments</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <MessageSquare className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Approved</p>
                <p className="text-2xl font-bold">{stats.approved}</p>
              </div>
              <Eye className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-bold">{stats.pending}</p>
              </div>
              <Calendar className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Replies</p>
                <p className="text-2xl font-bold">{stats.replies}</p>
              </div>
              <Reply className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search your comments..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Comments List */}
      <div className="space-y-4">
        {filteredComments.length > 0 ? (
          filteredComments.map((comment) => (
            <Card key={comment.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="space-y-4">
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Link 
                          to={`/articles/${comment.article?.slug}`}
                          className="font-medium text-blue-600 hover:text-blue-700 flex items-center gap-1"
                        >
                          {comment.article?.title}
                          <ExternalLink className="h-3 w-3" />
                        </Link>
                        <Badge variant={comment.is_approved ? "default" : "secondary"}>
                          {comment.is_approved ? "Approved" : "Pending"}
                        </Badge>
                        {comment.parent_comment && (
                          <Badge variant="outline">Reply</Badge>
                        )}
                      </div>
                      
                      {/* Parent comment if this is a reply */}
                      {comment.parent_comment && (
                        <div className="bg-gray-50 p-3 rounded-lg mb-3 border-l-4 border-gray-300">
                          <p className="text-sm text-gray-600 mb-1">
                            Replying to {comment.parent_comment.user_name}:
                          </p>
                          <p className="text-sm text-gray-700 italic">
                            "{comment.parent_comment.content.substring(0, 100)}..."
                          </p>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => startEditing(comment)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteComment(comment.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Comment Content */}
                  {editingComment === comment.id ? (
                    <div className="space-y-3">
                      <Textarea
                        value={editContent}
                        onChange={(e) => setEditContent(e.target.value)}
                        className="min-h-[100px]"
                      />
                      <div className="flex gap-2">
                        <Button 
                          size="sm" 
                          onClick={() => handleEditComment(comment.id)}
                        >
                          Save Changes
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline" 
                          onClick={cancelEditing}
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="prose prose-sm max-w-none">
                      <p className="text-gray-700">{comment.content}</p>
                    </div>
                  )}

                  {/* Footer */}
                  <div className="flex items-center justify-between text-sm text-gray-500 pt-3 border-t">
                    <div className="flex items-center gap-4">
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {new Date(comment.created_at).toLocaleDateString()}
                      </span>
                      {comment.updated_at !== comment.created_at && (
                        <span className="text-gray-400">
                          (edited {new Date(comment.updated_at).toLocaleDateString()})
                        </span>
                      )}
                    </div>
                    <span className="flex items-center gap-1">
                      <MessageSquare className="h-3 w-3" />
                      {comment.likes || 0} likes
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <Card>
            <CardContent className="p-12 text-center">
              <MessageSquare className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-medium text-gray-900 mb-2">
                {searchTerm ? 'No matching comments' : 'No comments yet'}
              </h3>
              <p className="text-gray-500 mb-6 max-w-md mx-auto">
                {searchTerm 
                  ? 'Try adjusting your search terms to find what you\'re looking for.'
                  : 'Start engaging with articles by leaving thoughtful comments.'
                }
              </p>
              {!searchTerm && (
                <Link to="/articles">
                  <Button>
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Explore Articles
                  </Button>
                </Link>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
