import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { format } from "date-fns";
import { MentorshipSubscription, Student, Order } from "@/types/subscription";

// This prevents the page from being statically generated
export const dynamic = 'force-dynamic';
export const dynamicParams = true;
export const revalidate = 0;

interface SuccessPageProps {
  searchParams: { order_id: string };
}

export default async function SuccessPage({
  searchParams,
}: SuccessPageProps) {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  const orderId = searchParams.order_id;

  // If no order ID, redirect to home
  if (!orderId) {
    redirect("/");
  }

  // Extract data from URL parameters
  const isMentorship = orderId.startsWith('TEC-MENTOR');
  
  // Extract program type and duration from order ID
  let programType = '';
  let programDuration = 0;
  
  if (isMentorship) {
    // Parse program details from order ID format: TEC-MENTOR-[timestamp]-[type]-[duration]
    const parts = orderId.split('-');
    if (parts.length >= 5) {
      programType = parts[3] || '';
      programDuration = parseInt(parts[4] || '0', 10);
    } else {
      // Fallback if order ID format doesn't match expected pattern
      // Check if order ID contains program type indicators
      if (orderId.includes('6-month')) {
        programType = '6-month';
        programDuration = 6;
      } else if (orderId.includes('12-month')) {
        programType = '12-month';
        programDuration = 12;
      } else {
        // Default to consultation
        programType = 'consultation';
        programDuration = 1;
      }
    }
  }
  
  // If this is a mentorship order and user is not logged in, redirect to sign in
  if (isMentorship && !user) {
    redirect(`/(auth)/sign-in?redirect=success&order_id=${orderId}`);
  }

  try {
    // Handle mentorship subscription orders
    if (isMentorship) {
      // Fetch mentorship subscription details
      const { data: subscriptionData, error: subscriptionError } = await supabase
        .from("mentorship_subscriptions")
        .select("*")
        .eq("id", orderId)
        .single();

      // Check if error is due to missing table
      if (subscriptionError && subscriptionError.message.includes("does not exist")) {
        console.log("Mentorship subscriptions table does not exist yet. Using fallback data for development.");
        
        // Create fallback subscription data for development/testing
        const fallbackSubscription: MentorshipSubscription = {
          id: orderId,
          user_id: user?.id || 'anonymous',
          program_id: 'dev-program-id',
          program_name: programType === 'consultation' ? 'Tennis Consultation' : 
                       programType === '6-month' ? 'Tennis 6-Month Program' : 'Tennis 12-Month Program',
          program_type: programType,
          duration: programDuration,
          billing_cycle: 'monthly',
          amount: programType === 'consultation' ? 500 : 
                 programType === '6-month' ? 2500 : 4500,
          status: 'active',
          payment_status: 'paid',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        
        // Return success UI with fallback data
        return renderMentorshipSuccessUI(fallbackSubscription, programType, programDuration);
      } else if (subscriptionError) {
        throw new Error(`Failed to fetch subscription data: ${subscriptionError.message}`);
      }

      if (!subscriptionData) {
        throw new Error('Subscription not found');
      }

      // Only update these if they weren't already set from the order ID
      if (!programType) programType = subscriptionData.program_type || "";
      if (!programDuration) programDuration = subscriptionData.duration || 0;

      // Verify payment status
      if (subscriptionData.payment_status !== 'pending') {
        // Payment has already been processed
        if (subscriptionData.payment_status === 'paid') {
          return renderMentorshipSuccessUI(subscriptionData, programType, programDuration);
        } else {
          throw new Error('Payment was not successful');
        }
      }

      // Verify payment with Yoco (this would be the actual payment verification in production)
      // For now, we'll simulate a successful payment after a short delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Update subscription status
      const { error: updateError } = await supabase
        .from("mentorship_subscriptions")
        .update({ 
          payment_status: "paid", 
          status: "active",
          updated_at: new Date().toISOString()
        })
        .eq("id", orderId);

      if (updateError) {
        throw new Error(`Failed to update subscription status: ${updateError.message}`);
      }

      // Create student account if it doesn't exist
      if (user) {
        const { data: studentData, error: studentError } = await supabase
          .from("students")
          .select("*")
          .eq("user_id", user.id)
          .single();

        if (studentError && studentError.code !== 'PGRST116') { // PGRST116 is 'not found'
          console.error('Error checking student account:', studentError);
        }

        if (!studentData) {
          try {
            const { error: insertError } = await supabase
              .from("students")
              .insert({
                user_id: user.id,
                email: user.email,
                first_name: user.user_metadata?.first_name || "",
                last_name: user.user_metadata?.last_name || "",
                status: "active",
                program_type: programType,
                program_duration: programDuration,
                start_date: new Date().toISOString(),
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              });

            if (insertError) {
              console.error('Error creating student account:', insertError);
              throw new Error('Failed to create student account');
            }
          } catch (error) {
            console.error('Error creating student account:', error);
            throw new Error('Failed to create student account');
          }
        }
      }

      // Fetch updated subscription data
      const { data: updatedSubscription, error: refreshError } = await supabase
        .from("mentorship_subscriptions")
        .select("*")
        .eq("id", orderId)
        .single();

      if (refreshError) {
        throw new Error(`Failed to fetch updated subscription data: ${refreshError.message}`);
      }

      return renderMentorshipSuccessUI(updatedSubscription, programType, programDuration);
    } else {
      // Handle regular orders
      const { data: orderData, error: orderError } = await supabase
        .from("orders")
        .select("*")
        .eq("id", orderId)
        .single();

      // Check if error is due to missing table
      if (orderError && orderError.message.includes("does not exist")) {
        console.log("Orders table does not exist yet. Using fallback data for development.");
        
        // Create fallback order data for development/testing
        const fallbackOrder: Order = {
          id: orderId,
          user_id: user?.id || 'anonymous',
          amount: 1250,
          status: 'completed',
          payment_status: 'paid',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        
        // Return success UI with fallback data
        return renderRegularOrderSuccessUI(fallbackOrder);
      } else if (orderError) {
        throw new Error(`Failed to fetch order data: ${orderError.message}`);
      }

      if (!orderData) {
        throw new Error('Order not found');
      }

      // Update order status if needed
      if (orderData.status === 'pending') {
        const { error: updateError } = await supabase
          .from("orders")
          .update({ 
            status: "completed",
            updated_at: new Date().toISOString()
          })
          .eq("id", orderId);

        if (updateError) {
          throw new Error(`Failed to update order status: ${updateError.message}`);
        }
      }

      return renderRegularOrderSuccessUI(orderData);
    }
  } catch (error: any) {
    console.error("Error processing payment success:", error);
    
    // Error state UI
    return (
      <div className="flex min-h-screen items-center justify-center bg-background py-12 px-4">
        <div className="w-full max-w-2xl">
          <Card className="border border-red-200 shadow-lg overflow-hidden">
            <CardHeader className="text-center border-b border-border pb-6 bg-gradient-to-r from-red-50 to-orange-50">
              <div className="flex justify-center mb-4">
                <div className="rounded-full bg-red-100 p-3">
                  <svg className="w-12 h-12 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                  </svg>
                </div>
              </div>
              <CardTitle className="text-2xl font-bold text-red-600">
                Payment Verification Failed
              </CardTitle>
              <CardDescription className="text-gray-600 mt-2">
                We couldn't verify your payment or there was an issue with your order.
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6 px-6">
              <div className="space-y-6">
                <div className="bg-red-50 border border-red-200 rounded-lg p-5">
                  <h3 className="font-semibold text-red-800 text-lg mb-3">Error Details</h3>
                  <p className="text-gray-700">{error.message || "An unknown error occurred"}</p>
                </div>
                
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-5">
                  <h3 className="font-semibold text-gray-800 text-lg mb-3">What to do next</h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <div className="bg-gray-200 rounded-full p-1 mr-3 mt-0.5 flex-shrink-0">
                        <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                        </svg>
                      </div>
                      <div className="text-gray-700">Try again later or use a different payment method</div>
                    </li>
                    <li className="flex items-start">
                      <div className="bg-gray-200 rounded-full p-1 mr-3 mt-0.5 flex-shrink-0">
                        <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <div className="text-gray-700">Contact our support team for assistance</div>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col sm:flex-row gap-4 pt-6 px-6 pb-6 border-t border-border">
              <Button className="w-full sm:w-auto" asChild>
                <Link href="/">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                  </svg>
                  Return Home
                </Link>
              </Button>
              <Button variant="outline" className="w-full sm:w-auto" asChild>
                <Link href="/contact">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  Contact Support
                </Link>
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    );
  }
}

// Helper function to render mentorship success UI
function renderMentorshipSuccessUI(
  subscriptionData: MentorshipSubscription,
  programType: string,
  programDuration: number
) {
  return (
    <div className="flex min-h-screen items-center justify-center bg-background py-12 px-4">
      <div className="w-full max-w-2xl">
        <Card className="border border-green-200 shadow-lg overflow-hidden">
          <CardHeader className="text-center border-b border-border pb-6 bg-gradient-to-r from-green-50 to-blue-50">
            <div className="flex justify-center mb-4">
              <div className="rounded-full bg-green-100 p-3">
                <svg className="w-12 h-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
            <CardTitle className="text-2xl font-bold text-green-600">
              Payment Confirmed
            </CardTitle>
            <CardDescription className="text-gray-600 mt-2">
              Your {programType} program subscription has been successfully activated.
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6 px-6">
            <div className="space-y-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-5">
                <h3 className="font-semibold text-blue-800 text-lg mb-3">Your Mentorship Program</h3>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="bg-blue-100 rounded-full p-1 mr-3 mt-0.5 flex-shrink-0">
                      <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">Program</p>
                      <p className="text-gray-600">{subscriptionData.program_name}</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="bg-blue-100 rounded-full p-1 mr-3 mt-0.5 flex-shrink-0">
                      <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">Duration</p>
                      <p className="text-gray-600">{programDuration} months</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="bg-blue-100 rounded-full p-1 mr-3 mt-0.5 flex-shrink-0">
                      <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">Amount</p>
                      <p className="text-gray-600">R{subscriptionData.amount.toLocaleString()}</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="bg-blue-100 rounded-full p-1 mr-3 mt-0.5 flex-shrink-0">
                      <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">Start Date</p>
                      <p className="text-gray-600">{format(new Date(subscriptionData.created_at), 'MMMM d, yyyy')}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-5">
                <h3 className="font-semibold text-gray-800 text-lg mb-3">Next Steps</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <div className="bg-gray-200 rounded-full p-1 mr-3 mt-0.5 flex-shrink-0">
                      <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                    </div>
                    <div className="text-gray-700">Visit your dashboard to access your mentorship resources</div>
                  </li>
                  <li className="flex items-start">
                    <div className="bg-gray-200 rounded-full p-1 mr-3 mt-0.5 flex-shrink-0">
                      <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                      </svg>
                    </div>
                    <div className="text-gray-700">Check your email for a welcome message from your mentor</div>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
          
          <CardFooter className="flex flex-col sm:flex-row gap-4 pt-6 px-6 pb-6 border-t border-border">
            <Button className="w-full sm:w-auto" asChild>
              <Link href="/student-dashboard">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                Go to Dashboard
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}

// Helper function to render regular order success UI
function renderRegularOrderSuccessUI(orderData: Order) {
  return (
    <div className="flex min-h-screen items-center justify-center bg-background py-12 px-4">
      <div className="w-full max-w-2xl">
        <Card className="border border-green-200 shadow-lg overflow-hidden">
          <CardHeader className="text-center border-b border-border pb-6 bg-gradient-to-r from-green-50 to-blue-50">
            <div className="flex justify-center mb-4">
              <div className="rounded-full bg-green-100 p-3">
                <svg className="w-12 h-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
            <CardTitle className="text-2xl font-bold text-green-600">
              Order Confirmed
            </CardTitle>
            <CardDescription className="text-gray-600 mt-2">
              Your order has been successfully processed.
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6 px-6">
            <div className="space-y-6">
              <div className="bg-green-50 border border-green-200 rounded-lg p-5">
                <h3 className="font-semibold text-green-800 text-lg mb-3">Order Details</h3>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="bg-green-100 rounded-full p-1 mr-3 mt-0.5 flex-shrink-0">
                      <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">Order ID</p>
                      <p className="text-gray-600">{orderData.id}</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="bg-green-100 rounded-full p-1 mr-3 mt-0.5 flex-shrink-0">
                      <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">Total Amount</p>
                      <p className="text-gray-600">R{orderData.amount?.toLocaleString() || '0'}</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="bg-green-100 rounded-full p-1 mr-3 mt-0.5 flex-shrink-0">
                      <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">Order Date</p>
                      <p className="text-gray-600">{format(new Date(orderData.created_at), 'MMMM d, yyyy')}</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="bg-green-100 rounded-full p-1 mr-3 mt-0.5 flex-shrink-0">
                      <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">Status</p>
                      <p className="text-gray-600 capitalize">{orderData.status || 'Completed'}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-5">
                <h3 className="font-semibold text-gray-800 text-lg mb-3">What's Next?</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <div className="bg-gray-200 rounded-full p-1 mr-3 mt-0.5 flex-shrink-0">
                      <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div className="text-gray-700">Check your email for order confirmation and details</div>
                  </li>
                  <li className="flex items-start">
                    <div className="bg-gray-200 rounded-full p-1 mr-3 mt-0.5 flex-shrink-0">
                      <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                      </svg>
                    </div>
                    <div className="text-gray-700">View your order history in your account</div>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
          
          <CardFooter className="flex flex-col sm:flex-row gap-4 pt-6 px-6 pb-6 border-t border-border">
            <Button className="w-full sm:w-auto" asChild>
              <Link href="/">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                Continue Shopping
              </Link>
            </Button>
            <Button variant="outline" className="w-full sm:w-auto" asChild>
              <Link href="/account/orders">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
                View Orders
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
