"use client";

import { useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestAdminAuth() {
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const supabase = createClient();

  const runTests = async () => {
    setLoading(true);
    const testResults: any[] = [];

    try {
      // Test 1: Check if users table exists and has correct structure
      testResults.push({ test: "Users Table Structure", status: "running" });
      
      const { data: tableData, error: tableError } = await supabase
        .from('users')
        .select('*')
        .limit(1);
      
      if (tableError) {
        testResults[testResults.length - 1] = {
          test: "Users Table Structure",
          status: "failed",
          error: tableError.message
        };
      } else {
        testResults[testResults.length - 1] = {
          test: "Users Table Structure",
          status: "passed",
          message: "Users table exists and is accessible"
        };
      }

      // Test 2: Check current user session
      testResults.push({ test: "Current User Session", status: "running" });
      
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        testResults[testResults.length - 1] = {
          test: "Current User Session",
          status: "failed",
          error: sessionError.message
        };
      } else {
        testResults[testResults.length - 1] = {
          test: "Current User Session",
          status: session ? "passed" : "info",
          message: session ? `Logged in as: ${session.user.email}` : "No active session"
        };
      }

      // Test 3: Test user creation (simulation)
      testResults.push({ test: "User Creation Test", status: "running" });
      
      try {
        // This is just a test to see if we can prepare the insert statement
        const testUserId = '00000000-0000-0000-0000-000000000000';
        const { error: insertError } = await supabase
          .from('users')
          .insert([
            {
              id: testUserId,
              email: '<EMAIL>',
              full_name: 'Test User',
              name: 'Test User',
              role: 'admin',
            }
          ])
          .select();

        if (insertError && insertError.code === '23505') {
          // Duplicate key error is expected for test UUID
          testResults[testResults.length - 1] = {
            test: "User Creation Test",
            status: "passed",
            message: "Insert statement structure is correct (duplicate key expected)"
          };
        } else if (insertError) {
          testResults[testResults.length - 1] = {
            test: "User Creation Test",
            status: "failed",
            error: `${insertError.message} (Code: ${insertError.code})`
          };
        } else {
          // Clean up the test user if it was actually created
          await supabase.from('users').delete().eq('id', testUserId);
          testResults[testResults.length - 1] = {
            test: "User Creation Test",
            status: "passed",
            message: "User creation works correctly"
          };
        }
      } catch (error: any) {
        testResults[testResults.length - 1] = {
          test: "User Creation Test",
          status: "failed",
          error: error.message
        };
      }

      // Test 4: Check RLS policies
      testResults.push({ test: "RLS Policies", status: "running" });
      
      const { data: policyData, error: policyError } = await supabase
        .rpc('check_user_permissions');
      
      if (policyError && policyError.code === '42883') {
        // Function doesn't exist, which is fine
        testResults[testResults.length - 1] = {
          test: "RLS Policies",
          status: "info",
          message: "RLS policies are active (cannot check without custom function)"
        };
      } else {
        testResults[testResults.length - 1] = {
          test: "RLS Policies",
          status: "passed",
          message: "RLS policies are configured"
        };
      }

      // Test 5: Test admin sign-up endpoint availability
      testResults.push({ test: "Admin Sign-up Endpoint", status: "running" });
      
      try {
        const response = await fetch('/admin/sign-up', { method: 'HEAD' });
        testResults[testResults.length - 1] = {
          test: "Admin Sign-up Endpoint",
          status: response.ok ? "passed" : "failed",
          message: response.ok ? "Admin sign-up page is accessible" : `HTTP ${response.status}`
        };
      } catch (error: any) {
        testResults[testResults.length - 1] = {
          test: "Admin Sign-up Endpoint",
          status: "failed",
          error: error.message
        };
      }

    } catch (error: any) {
      testResults.push({
        test: "General Error",
        status: "failed",
        error: error.message
      });
    }

    setResults(testResults);
    setLoading(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'passed': return 'text-green-600';
      case 'failed': return 'text-red-600';
      case 'running': return 'text-blue-600';
      case 'info': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'passed': return '✅';
      case 'failed': return '❌';
      case 'running': return '🔄';
      case 'info': return 'ℹ️';
      default: return '❓';
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>Admin Authentication Test Suite</CardTitle>
          <CardDescription>
            Test the admin authentication system to ensure everything is working correctly
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button 
            onClick={runTests} 
            disabled={loading}
            className="w-full"
          >
            {loading ? 'Running Tests...' : 'Run Admin Auth Tests'}
          </Button>

          {results.length > 0 && (
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Test Results:</h3>
              {results.map((result, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-xl">{getStatusIcon(result.status)}</span>
                    <span className="font-medium">{result.test}</span>
                    <span className={`text-sm ${getStatusColor(result.status)}`}>
                      {result.status.toUpperCase()}
                    </span>
                  </div>
                  {result.message && (
                    <p className="text-sm text-gray-600 ml-6">{result.message}</p>
                  )}
                  {result.error && (
                    <p className="text-sm text-red-600 ml-6 font-mono bg-red-50 p-2 rounded">
                      {result.error}
                    </p>
                  )}
                </div>
              ))}
            </div>
          )}

          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">Next Steps:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>1. Run the tests above to check current status</li>
              <li>2. If any tests fail, run <code>fix-missing-trigger.sql</code> in Supabase</li>
              <li>3. Test admin sign-up at <a href="/admin/sign-up" className="underline">/admin/sign-up</a></li>
              <li>4. Use access code: <code>TENNIS_ADMIN_2024</code></li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
