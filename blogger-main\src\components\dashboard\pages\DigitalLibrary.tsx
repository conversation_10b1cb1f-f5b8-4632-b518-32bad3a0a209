import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BookOpen, 
  Search, 
  Calendar,
  Clock,
  FileText,
  Video,
  Music,
  Package,
  Play,
  Eye,
  Shield,
  Download,
  Lock
} from 'lucide-react';
import { supabase } from '../../../../supabase/supabase';
import { useAuth } from '../../../../supabase/auth';
import { SecureContentViewer } from '../user/SecureContentViewer';

interface DigitalPurchase {
  id: string;
  product_id: string;
  access_granted_at: string;
  access_expires_at?: string;
  downloads_used: number;
  last_accessed_at?: string;
  product: {
    id: string;
    name: string;
    description: string;
    price: number;
    file_type: string;
    download_limit?: number;
    access_duration_days?: number;
    image_url?: string;
    category?: {
      name: string;
      color: string;
    };
  };
  digital_files: Array<{
    id: string;
    file_name: string;
    file_type: string;
    file_size: number;
    mime_type: string;
  }>;
}

export function DigitalLibrary() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [purchases, setPurchases] = useState<DigitalPurchase[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [selectedContent, setSelectedContent] = useState<{
    productId: string;
    fileId: string;
    fileName: string;
    fileType: string;
    mimeType: string;
  } | null>(null);

  useEffect(() => {
    if (user) {
      loadDigitalPurchases();
    }
  }, [user]);

  const loadDigitalPurchases = async () => {
    try {
      setIsLoading(true);
      
      const { data, error } = await supabase
        .from('user_digital_purchases')
        .select(`
          *,
          product:products!inner(
            id,
            name,
            description,
            price,
            file_type,
            download_limit,
            access_duration_days,
            image_url,
            category:product_categories(name, color)
          )
        `)
        .eq('user_id', user?.id)
        .order('access_granted_at', { ascending: false });

      if (error) throw error;

      // Load digital files for each purchase
      const purchasesWithFiles = await Promise.all(
        (data || []).map(async (purchase) => {
          const { data: files, error: filesError } = await supabase
            .from('digital_product_files')
            .select('*')
            .eq('product_id', purchase.product_id);

          if (filesError) {
            console.error('Error loading files:', filesError);
            return { ...purchase, digital_files: [] };
          }

          return { ...purchase, digital_files: files || [] };
        })
      );

      setPurchases(purchasesWithFiles);
    } catch (error) {
      console.error('Error loading digital purchases:', error);
      toast({
        title: 'Error',
        description: 'Failed to load your digital library',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const getFileTypeIcon = (fileType: string) => {
    switch (fileType) {
      case 'ebook':
      case 'pdf':
        return <FileText className="w-5 h-5 text-green-600" />;
      case 'audiobook':
      case 'audio':
        return <Music className="w-5 h-5 text-blue-600" />;
      case 'video':
      case 'course':
        return <Video className="w-5 h-5 text-purple-600" />;
      default:
        return <Package className="w-5 h-5 text-gray-600" />;
    }
  };

  const isAccessExpired = (purchase: DigitalPurchase) => {
    if (!purchase.access_expires_at) return false;
    return new Date(purchase.access_expires_at) < new Date();
  };

  const openSecureViewer = (purchase: DigitalPurchase, file: any) => {
    if (isAccessExpired(purchase)) {
      toast({
        title: 'Access Expired',
        description: 'Your access to this content has expired.',
        variant: 'destructive',
      });
      return;
    }

    setSelectedContent({
      productId: purchase.product_id,
      fileId: file.id,
      fileName: file.file_name,
      fileType: purchase.product.file_type,
      mimeType: file.mime_type
    });
  };

  const filteredPurchases = purchases.filter(purchase => {
    const matchesSearch = purchase.product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         purchase.product.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || purchase.product.file_type === filterType;
    return matchesSearch && matchesType;
  });

  // Group purchases by file type
  const groupedPurchases = {
    ebook: filteredPurchases.filter(p => p.product.file_type === 'ebook' || p.product.file_type === 'pdf'),
    audiobook: filteredPurchases.filter(p => p.product.file_type === 'audiobook'),
    video: filteredPurchases.filter(p => p.product.file_type === 'video' || p.product.file_type === 'course'),
    other: filteredPurchases.filter(p => !['ebook', 'pdf', 'audiobook', 'video', 'course'].includes(p.product.file_type))
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 p-3 sm:p-4 lg:p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">Digital Library</h1>
          <p className="text-sm sm:text-base text-gray-600">Access your purchased digital content</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search your library..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-full sm:w-64 text-sm"
            />
          </div>
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-full sm:w-40 text-sm">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="ebook">E-books</SelectItem>
              <SelectItem value="audiobook">Audio Books</SelectItem>
              <SelectItem value="video">Videos</SelectItem>
              <SelectItem value="course">Courses</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4">
        <Card>
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Total Items</p>
                <p className="text-lg sm:text-2xl font-bold text-gray-900">{purchases.length}</p>
              </div>
              <Package className="h-6 w-6 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">E-books</p>
                <p className="text-lg sm:text-2xl font-bold text-gray-900">{groupedPurchases.ebook.length}</p>
              </div>
              <FileText className="h-6 w-6 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Audio Books</p>
                <p className="text-lg sm:text-2xl font-bold text-gray-900">{groupedPurchases.audiobook.length}</p>
              </div>
              <Music className="h-6 w-6 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Videos</p>
                <p className="text-lg sm:text-2xl font-bold text-gray-900">{groupedPurchases.video.length}</p>
              </div>
              <Video className="h-6 w-6 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Content Grid */}
      {filteredPurchases.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <BookOpen className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No digital content</h3>
            <p className="text-gray-600">You haven't purchased any digital products yet.</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredPurchases.map((purchase) => (
            <Card key={purchase.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  {getFileTypeIcon(purchase.product.file_type)}
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-sm truncate">{purchase.product.name}</h3>
                    <p className="text-xs text-gray-600 line-clamp-2 mt-1">{purchase.product.description}</p>
                    
                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant="secondary" className="text-xs">
                        {purchase.product.file_type}
                      </Badge>
                      {purchase.product.category && (
                        <div className="flex items-center gap-1">
                          <div 
                            className="w-2 h-2 rounded-full" 
                            style={{ backgroundColor: purchase.product.category.color }}
                          />
                          <span className="text-xs text-gray-500">{purchase.product.category.name}</span>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center justify-between mt-3">
                      <div className="text-xs text-gray-500">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          <span>Purchased {new Date(purchase.access_granted_at).toLocaleDateString()}</span>
                        </div>
                        {purchase.access_expires_at && (
                          <div className="flex items-center gap-1 mt-1">
                            <Clock className="w-3 h-3" />
                            <span className={isAccessExpired(purchase) ? 'text-red-500' : ''}>
                              {isAccessExpired(purchase) ? 'Expired' : `Expires ${new Date(purchase.access_expires_at).toLocaleDateString()}`}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {purchase.digital_files.length > 0 && (
                      <div className="mt-3 space-y-1">
                        {purchase.digital_files.map((file) => (
                          <div key={file.id} className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <span className="text-xs text-gray-600 truncate">{file.file_name}</span>
                              <span className="text-xs text-gray-400">({formatFileSize(file.file_size)})</span>
                            </div>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => openSecureViewer(purchase, file)}
                              disabled={isAccessExpired(purchase)}
                              className="text-xs"
                            >
                              {purchase.product.file_type === 'audiobook' ? (
                                <>
                                  <Play className="w-3 h-3 mr-1" />
                                  Listen
                                </>
                              ) : (
                                <>
                                  <Eye className="w-3 h-3 mr-1" />
                                  View
                                </>
                              )}
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}

                    {isAccessExpired(purchase) && (
                      <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                        <div className="flex items-center gap-1">
                          <Lock className="w-3 h-3" />
                          <span>Access expired</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Secure Content Viewer Dialog */}
      <Dialog open={!!selectedContent} onOpenChange={() => setSelectedContent(null)}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden p-0">
          {selectedContent && (
            <SecureContentViewer
              productId={selectedContent.productId}
              fileId={selectedContent.fileId}
              fileName={selectedContent.fileName}
              fileType={selectedContent.fileType}
              mimeType={selectedContent.mimeType}
              onClose={() => setSelectedContent(null)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
