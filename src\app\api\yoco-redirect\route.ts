import { NextResponse } from 'next/server';
import { redirect } from 'next/navigation';
import yocoConfig from '@/lib/yoco';

/**
 * This route handles redirecting to Yoco's payment page
 * In a real implementation, this would create a payment with Yoco's API
 * and redirect to their hosted payment page
 */
export async function GET(request: Request) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const amount = url.searchParams.get('amount');
    const description = url.searchParams.get('description') || 'Tennis Whisperer Purchase';
    const successUrl = url.searchParams.get('successUrl') || '';
    const cancelUrl = url.searchParams.get('cancelUrl') || '';
    
    if (!amount) {
      return NextResponse.json(
        { error: 'Missing amount parameter' },
        { status: 400 }
      );
    }
    
    // Extract payment type from description
    const isConsultation = description.toLowerCase().includes('consultation');

    // In a real implementation, you would:
    // 1. Create a payment with Yoco's API
    // 2. Redirect to their hosted payment page
    
    // For now, we'll create a simple HTML page that simulates the Yoco payment form
    // This is just for demonstration purposes
    const html = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Yoco Payment</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f5f5f5;
          }
          .container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            width: 100%;
            max-width: 500px;
          }
          .header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
          }
          .logo {
            height: 40px;
            margin-right: 1rem;
          }
          h1 {
            font-size: 1.5rem;
            color: #333;
            margin: 0;
          }
          .amount {
            font-size: 2rem;
            font-weight: bold;
            margin: 1.5rem 0;
            color: #333;
          }
          .form-group {
            margin-bottom: 1.5rem;
          }
          label {
            display: block;
            margin-bottom: 0.5rem;
            color: #555;
          }
          input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
          }
          .card-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
          }
          button {
            background-color: #0C6;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 0.75rem;
            font-size: 1rem;
            font-weight: bold;
            width: 100%;
            cursor: pointer;
            transition: background-color 0.2s;
          }
          button:hover {
            background-color: #0B5;
          }
          .footer {
            margin-top: 1.5rem;
            text-align: center;
            color: #777;
            font-size: 0.875rem;
          }
          .secure {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 1rem;
          }
          .secure svg {
            margin-right: 0.5rem;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <img src="https://cdn.yoco.com/images/logos/yoco-logo-green-dot.svg" alt="Yoco Logo" class="logo">
            <h1>Secure Payment</h1>
          </div>
          
          <div class="amount">
            R${(parseInt(amount) / 100).toFixed(2)}
          </div>
          
          <form id="payment-form">
            <div class="form-group">
              <label for="card-number">Card Number</label>
              <input type="text" id="card-number" placeholder="1234 5678 9012 3456" maxlength="19">
            </div>
            
            <div class="card-details">
              <div class="form-group">
                <label for="expiry">Expiry Date</label>
                <input type="text" id="expiry" placeholder="MM/YY" maxlength="5">
              </div>
              
              <div class="form-group">
                <label for="cvv">CVV</label>
                <input type="text" id="cvv" placeholder="123" maxlength="3">
              </div>
            </div>
            
            <div class="form-group">
              <label for="name">Cardholder Name</label>
              <input type="text" id="name" placeholder="John Doe">
            </div>
            
            <button type="button" id="pay-button">Pay Now</button>
          </form>
          
          <div class="footer">
            <div class="secure">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
              </svg>
              Secure payment processed by Yoco
            </div>
          </div>
        </div>
        
        <script>
          document.getElementById('pay-button').addEventListener('click', function() {
            // Simulate payment processing
            this.textContent = 'Processing...';
            this.disabled = true;
            
            // Redirect to success page after 2 seconds
            setTimeout(() => {
              // Get order ID from URL parameters
              const orderId = '${url.searchParams.get("orderId") || ""}';
              const isMentorshipProgram = orderId.includes('TEC-MENTOR');
              
              // Determine the appropriate success URL
              let redirectUrl;
              
              // Get success URL from server
              const successUrlFromServer = '${successUrl}';
              
              if (successUrlFromServer) {
                // Use provided success URL if available
                redirectUrl = successUrlFromServer;
              } else if (isMentorshipProgram) {
                // For 6-month and 12-month mentorship programs, use the main success page
                redirectUrl = '${process.env.NEXT_PUBLIC_BASE_URL || ""}/success?order_id=' + orderId;
              } else {
                // For consultations and other orders, use the consultation success page
                redirectUrl = '${process.env.NEXT_PUBLIC_BASE_URL || ""}/consultation/success';
              }
              
              // Make sure the reference is preserved in the URL
              const reference = '${url.searchParams.get('reference')}';
              if (reference && !redirectUrl.includes('reference=')) {
                // Add reference parameter if it doesn't exist
                redirectUrl += (redirectUrl.includes('?') ? '&' : '?') + 'reference=' + reference;
              }
              
              console.log('Redirecting to:', redirectUrl);
              window.location.href = redirectUrl;
            }, 2000);
          });
          
          // Format card number with spaces
          document.getElementById('card-number').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\\s+/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = '';
            for (let i = 0; i < value.length; i++) {
              if (i > 0 && i % 4 === 0) {
                formattedValue += ' ';
              }
              formattedValue += value[i];
            }
            e.target.value = formattedValue;
          });
          
          // Format expiry date
          document.getElementById('expiry').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\\s+/g, '').replace(/[^0-9]/gi, '');
            if (value.length > 2) {
              value = value.substring(0, 2) + '/' + value.substring(2);
            }
            e.target.value = value;
          });
        </script>
      </body>
      </html>
    `;

    return new NextResponse(html, {
      headers: {
        'Content-Type': 'text/html',
      },
    });
  } catch (error: any) {
    console.error('Error in Yoco redirect:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
