import * as React from "react";
import { cn } from "@/lib/utils";
import { Upload } from "lucide-react";

export interface InputFileProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  icon?: React.ReactNode;
  onValueChange?: (value: File | null) => void;
}

const InputFile = React.forwardRef<HTMLInputElement, InputFileProps>(
  ({ className, icon, onValueChange, ...props }, ref) => {
    const [fileName, setFileName] = React.useState<string>("");
    const inputRef = React.useRef<HTMLInputElement>(null);

    const handleClick = () => {
      inputRef.current?.click();
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0] || null;
      if (file) {
        setFileName(file.name);
        onValueChange?.(file);
      } else {
        setFileName("");
        onValueChange?.(null);
      }
    };

    return (
      <div
        onClick={handleClick}
        className={cn(
          "flex items-center gap-2 px-3 py-2 border border-input rounded-md bg-background hover:bg-accent hover:text-accent-foreground cursor-pointer",
          className,
        )}
      >
        {icon || <Upload className="h-4 w-4" />}
        <span className="text-sm">{fileName || "Click to upload file..."}</span>
        <input
          type="file"
          className="hidden"
          ref={inputRef}
          onChange={handleChange}
          {...props}
        />
      </div>
    );
  },
);

InputFile.displayName = "InputFile";

export { InputFile };
