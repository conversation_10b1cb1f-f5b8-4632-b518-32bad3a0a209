# Yoco Payment Integration

This document provides instructions for setting up Yoco payment integration for the Tennis Equipment Commerce website.

## Overview

We've replaced <PERSON><PERSON> with Yoco for payment processing. Yoco is a South African payment gateway that allows businesses to accept card payments.

## Environment Variables

Add the following environment variables to your `.env.local` file:

```
# Yoco API Keys
YOCO_PUBLIC_KEY=your_yoco_public_key
YOCO_SECRET_KEY=your_yoco_secret_key
YOCO_WEBHOOK_SECRET=your_yoco_webhook_secret

# Yoco Product IDs for Mentorship Programs
NEXT_PUBLIC_YOCO_CONSULTATION_PRICE_ID=consultation-price-id
NEXT_PUBLIC_YOCO_6MONTH_PRICE_ID=6month-price-id
NEXT_PUBLIC_YOCO_6MONTH_FULL_PRICE_ID=6month-full-price-id
NEXT_PUBLIC_YOCO_12MONTH_PRICE_ID=12month-price-id
NEXT_PUBLIC_YOCO_12MONTH_FULL_PRICE_ID=12month-full-price-id
```

## Integration Points

The Yoco integration has been implemented in the following files:

1. `/src/lib/yoco.ts` - Core Yoco configuration and utilities
2. `/src/app/api/yoco-checkout/route.ts` - API route for regular checkout
3. `/src/app/api/yoco-checkout/mentorship/route.ts` - API route for mentorship program checkout
4. `/src/app/api/yoco-redirect/route.ts` - API route for redirecting to Yoco payment page
5. `/src/app/api/yoco-webhook/route.ts` - API route for handling Yoco webhooks
6. `/src/components/MentorshipPricing.tsx` - Updated to use Yoco instead of Stripe
7. `/src/app/success/page.tsx` - Updated to handle Yoco payment success

## Database Changes

The following database tables need to be updated to support Yoco payments:

1. `orders` table - Add `yoco_payment_id` column
2. `mentorship_subscriptions` table - Add `yoco_payment_id` column

## Testing

To test the Yoco integration:

1. Set up your Yoco account at [https://www.yoco.com/za/](https://www.yoco.com/za/)
2. Get your API keys from the Yoco Dashboard
3. Add the environment variables to your `.env.local` file
4. Start the development server
5. Test the checkout process for both regular products and mentorship programs

## Production Deployment

For production deployment:

1. Set up your Yoco account with production credentials
2. Update the environment variables with production values
3. Configure webhooks in the Yoco Dashboard to point to your production webhook URL
4. Test the entire payment flow in production

## Resources

- [Yoco API Documentation](https://developer.yoco.com/)
- [Yoco Dashboard](https://business.yoco.com/za/login)
