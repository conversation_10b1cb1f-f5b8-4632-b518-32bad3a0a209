-- Sample Data Only Script for Tennis Whisperer Student Dashboard
-- Use this script if tables and policies already exist
-- This script only inserts sample data safely

-- ============================================================================
-- SAMPLE DATA INSERTION ONLY
-- ============================================================================
-- Temporarily disable RLS for sample data insertion
-- This allows the initial setup data to be inserted without authentication

DO $$
BEGIN
  -- Check if tables exist before disabling RLS
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mentorship_programs') THEN
    ALTER TABLE public.mentorship_programs DISABLE ROW LEVEL SECURITY;
  END IF;
  
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mentors') THEN
    ALTER TABLE public.mentors DISABLE ROW LEVEL SECURITY;
  END IF;
  
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'student_enrollments') THEN
    ALTER TABLE public.student_enrollments DISABLE ROW LEVEL SECURITY;
  END IF;
  
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mentorship_sessions') THEN
    ALTER TABLE public.mentorship_sessions DISABLE ROW LEVEL SECURITY;
  END IF;
  
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'resources') THEN
    ALTER TABLE public.resources DISABLE ROW LEVEL SECURITY;
  END IF;
  
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') THEN
    ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
  END IF;
END $$;

-- Insert sample users (required for foreign key relationships)
INSERT INTO public.users (id, email, full_name, role) VALUES
('550e8400-e29b-41d4-a716-446655440010', '<EMAIL>', 'Tennis Admin', 'admin'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'John Smith', 'mentor'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Sarah Johnson', 'mentor'),
('550e8400-e29b-41d4-a716-446655440013', '<EMAIL>', 'Mike Wilson', 'student'),
('550e8400-e29b-41d4-a716-446655440014', '<EMAIL>', 'Emma Davis', 'student')
ON CONFLICT (id) DO NOTHING;

-- Insert sample mentorship programs
INSERT INTO public.mentorship_programs (id, name, description, duration_months, price_monthly, price_upfront, features) VALUES
('550e8400-e29b-41d4-a716-************', '6-Month Tennis Mastery', 'Comprehensive tennis training program for beginners to intermediate players', 6, 299.99, 1599.99, '["Weekly 1-on-1 sessions", "Video analysis", "Training plans", "Progress tracking"]'),
('550e8400-e29b-41d4-a716-************', '12-Month Pro Development', 'Advanced tennis coaching for competitive players', 12, 399.99, 4199.99, '["Bi-weekly sessions", "Tournament preparation", "Mental coaching", "Nutrition guidance"]')
ON CONFLICT (id) DO NOTHING;

-- Insert sample mentors
INSERT INTO public.mentors (id, user_id, bio, specialties, experience_years, availability) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Professional tennis coach with 15+ years of experience. Specializes in technique development and mental coaching.', '["Technique Development", "Mental Coaching", "Tournament Preparation"]', 15, '{"monday": ["09:00", "17:00"], "tuesday": ["09:00", "17:00"], "wednesday": ["09:00", "17:00"]}'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Former professional player turned coach. Expert in serve technique and match strategy.', '["Serve Technique", "Match Strategy", "Physical Conditioning"]', 12, '{"thursday": ["10:00", "18:00"], "friday": ["10:00", "18:00"], "saturday": ["08:00", "16:00"]}')
ON CONFLICT (id) DO NOTHING;

-- Insert sample student enrollments
INSERT INTO public.student_enrollments (id, student_id, program_id, mentor_id, start_date, end_date, payment_type, status) VALUES
('550e8400-e29b-41d4-a716-446655440030', '550e8400-e29b-41d4-a716-446655440013', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '2024-01-15 10:00:00+00', '2024-07-15 10:00:00+00', 'monthly', 'active'),
('550e8400-e29b-41d4-a716-446655440031', '550e8400-e29b-41d4-a716-446655440014', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '2024-02-01 09:00:00+00', '2025-02-01 09:00:00+00', 'upfront', 'active')
ON CONFLICT (id) DO NOTHING;

-- Insert sample mentorship sessions
INSERT INTO public.mentorship_sessions (id, enrollment_id, scheduled_at, duration_minutes, status, notes) VALUES
('550e8400-e29b-41d4-a716-446655440040', '550e8400-e29b-41d4-a716-446655440030', '2024-01-22 14:00:00+00', 60, 'completed', 'Worked on forehand technique and footwork. Great progress shown.'),
('550e8400-e29b-41d4-a716-446655440041', '550e8400-e29b-41d4-a716-446655440030', '2024-01-29 14:00:00+00', 60, 'completed', 'Focused on serve mechanics. Student is improving consistency.'),
('550e8400-e29b-41d4-a716-446655440042', '550e8400-e29b-41d4-a716-446655440031', '2024-02-08 10:00:00+00', 90, 'completed', 'Tournament preparation session. Worked on match strategy and mental toughness.'),
('550e8400-e29b-41d4-a716-446655440043', '550e8400-e29b-41d4-a716-446655440030', '2024-02-05 14:00:00+00', 60, 'scheduled', 'Upcoming session to work on backhand technique.')
ON CONFLICT (id) DO NOTHING;

-- Insert sample resources
INSERT INTO public.resources (id, title, description, type, category, format, file_path, size_bytes, download_count, created_by) VALUES
('550e8400-e29b-41d4-a716-************', 'Tennis Fundamentals Video Series', 'Complete video series covering basic tennis techniques and fundamentals', 'video', 'Fundamentals', 'mp4', 'resources/tennis-fundamentals.mp4', 524288000, 45, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-************', 'Serve Technique Guide', 'Comprehensive PDF guide to improving your tennis serve', 'document', 'Technique', 'pdf', 'resources/serve-guide.pdf', 2048000, 32, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-************', 'Weekly Training Plan', 'Structured training program for intermediate players', 'training', 'Training Plans', 'pdf', 'resources/weekly-plan.pdf', 1024000, 28, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-************', 'Progress Tracking Template', 'Template for tracking your tennis progress and improvements', 'progress', 'Progress Tracking', 'xlsx', 'resources/progress-template.xlsx', 512000, 15, '550e8400-e29b-41d4-a716-************')
ON CONFLICT (id) DO NOTHING;

-- Re-enable RLS after sample data insertion
DO $$
BEGIN
  -- Check if tables exist before re-enabling RLS
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') THEN
    ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
  END IF;
  
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mentorship_programs') THEN
    ALTER TABLE public.mentorship_programs ENABLE ROW LEVEL SECURITY;
  END IF;
  
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mentors') THEN
    ALTER TABLE public.mentors ENABLE ROW LEVEL SECURITY;
  END IF;
  
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'student_enrollments') THEN
    ALTER TABLE public.student_enrollments ENABLE ROW LEVEL SECURITY;
  END IF;
  
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mentorship_sessions') THEN
    ALTER TABLE public.mentorship_sessions ENABLE ROW LEVEL SECURITY;
  END IF;
  
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'resources') THEN
    ALTER TABLE public.resources ENABLE ROW LEVEL SECURITY;
  END IF;
END $$;

-- Success message
DO $$
BEGIN
  RAISE NOTICE 'Sample data insertion completed successfully!';
  RAISE NOTICE 'All RLS policies have been re-enabled.';
  RAISE NOTICE 'You can now test the student dashboard functionality.';
END $$;
