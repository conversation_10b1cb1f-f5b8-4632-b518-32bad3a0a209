'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { createClient } from '@/utils/supabase/client';

interface PricingTier {
  id: string;
  name: string;
  price: number;
  duration: string;
  features: string[];
  isPopular?: boolean;
  fullPaymentPrice?: number;
  priceId: string;
  fullPriceId?: string;
}

const pricingTiers: PricingTier[] = [
  {
    id: 'consultation',
    name: 'Consultation',
    price: 300,
    duration: 'one-time',
    priceId: process.env.NEXT_PUBLIC_YOCO_CONSULTATION_PRICE_ID || 'consultation-price',
    features: [
      '1-hour personal consultation',
      'Training plan outline',
      'Equipment recommendations',
      'Basic resource access',
      'Follow-up email support'
    ]
  },
  {
    id: '6-month',
    name: '6-Month Program',
    price: 600,
    duration: 'monthly',
    fullPaymentPrice: 3060,
    priceId: process.env.NEXT_PUBLIC_YOCO_6MONTH_PRICE_ID || '6month-price',
    fullPriceId: process.env.NEXT_PUBLIC_YOCO_6MONTH_FULL_PRICE_ID || '6month-full-price',
    features: [
      'Monthly 1-on-1 sessions',
      'Student dashboard access',
      'Full resource library',
      'Direct mentor chat',
      'Progress tracking'
    ],
    isPopular: true
  },
  {
    id: '12-month',
    name: '12-Month Program',
    price: 1000,
    duration: 'monthly',
    fullPaymentPrice: 10200,
    priceId: process.env.NEXT_PUBLIC_YOCO_12MONTH_PRICE_ID || '12month-price',
    fullPriceId: process.env.NEXT_PUBLIC_YOCO_12MONTH_FULL_PRICE_ID || '12month-full-price',
    features: [
      'Everything in 6-Month',
      'Priority mentor access',
      'Advanced resources',
      'Video analysis',
      'Quarterly assessments'
    ]
  }
];

// No need for Stripe promise with Yoco integration

export default function MentorshipPricing() {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'full'>('monthly');
  const [isLoading, setIsLoading] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  return (
    <div className="relative py-12 overflow-hidden bg-background">
        <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] [background-size:16px_16px] opacity-25" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-4xl font-medium md:text-5xl">
            Mentorship Programs
          </h2>
          <p className="mt-4 text-xl text-muted-foreground">
            Choose your path to tennis excellence
          </p>
        </div>

        {/* Billing toggle */}
        <div className="mt-12 flex justify-center">
          <div className="relative">
            <div className="flex items-center space-x-3">
              <span className={`text-sm ${billingCycle === 'monthly' ? 'text-primary' : 'text-muted-foreground'}`}>
                Monthly billing
              </span>
              <button
                onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'full' : 'monthly')}
                className={`
                  relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent 
                  transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2
                  ${billingCycle === 'full' ? 'bg-primary' : 'bg-muted-foreground'}
                `}
                aria-label="Toggle billing cycle"
              >
                <span className={`
                  pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 
                  transition duration-200 ease-in-out
                  ${billingCycle === 'full' ? 'translate-x-5' : 'translate-x-0'}
                `} />
              </button>
              <span className={`text-sm ${billingCycle === 'full' ? 'text-primary' : 'text-muted-foreground'}`}>
                Full payment (save 15%)
              </span>
            </div>
          </div>
        </div>

        {/* Error message display */}
        {errorMessage && (
          <div className="mt-6 mx-auto max-w-2xl">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-700">
              <div className="flex items-start">
                <svg className="h-5 w-5 text-red-600 mt-0.5 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <div>
                  <h3 className="text-sm font-medium text-red-800">Payment Error</h3>
                  <p className="mt-1 text-sm">{errorMessage}</p>
                  <p className="mt-2 text-sm">Please try again or contact support if the problem persists.</p>
                </div>
              </div>
            </div>
          </div>
        )}
        
        <div className="mt-12 space-y-4 sm:mt-16 sm:space-y-0 sm:grid sm:grid-cols-3 sm:gap-6 lg:max-w-4xl lg:mx-auto xl:max-w-none xl:mx-0 ">
        {pricingTiers.map((tier) => (
        <motion.div
          key={tier.id}
          whileHover={{ scale: 1.02 }}
          className={`
            rounded-2xl 
            shadow-xl 
            backdrop-blur-sm
            bg-black/10
            border border-primary/20 
            divide-y divide-muted-foreground 
            transition-transform duration-300
            ${tier.isPopular ? 'border-primary/80' : ''}
          `}
        >
          <div className="p-6">
            {tier.isPopular && (
              <span className="inline-flex px-4 py-1 rounded-full text-sm font-semibold tracking-wide uppercase bg-primary/10 text-primary">
                Most Popular
              </span>
                )}
                <h3 className="text-2xl font-semibold text-foreground mt-4">{tier.name}</h3>
                <p className="mt-8">
                  <span className="text-4xl font-extrabold text-primary">
                    R{billingCycle === 'full' && tier.fullPaymentPrice ? 
                      (tier.id === '12-month' || tier.id === '6-month' ? 
                        Math.round(tier.fullPaymentPrice / (tier.id === '12-month' ? 12 : 6)) : 
                        tier.fullPaymentPrice).toLocaleString() : 
                      tier.price.toLocaleString()}
                  </span>
                  <span className="text-base font-medium text-muted-foreground">
                    {tier.duration === 'one-time' ? '' : '/month'}
                  </span>
                  {billingCycle === 'full' && (tier.id === '12-month' || tier.id === '6-month') && (
                    <span className="block mt-2 text-sm text-blue-500">
                      Save 15% with full payment (Total: R{tier.fullPaymentPrice?.toLocaleString()})
                    </span>
                  )}
                </p>
                <ul className="mt-6 space-y-4">
                  {tier.features.map((feature) => (
                    <li key={feature} className="flex space-x-3">
                      <svg
                        className="flex-shrink-0 h-5 w-5 text-blue-400"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <span className="text-base text-muted-foreground">{feature}</span>
                    </li>
                  ))}
                </ul>
                <button
                  onClick={async () => {
                    setIsLoading(tier.id);
                    setErrorMessage(null); // Clear any previous errors
                    try {
                      // For consultation tier, navigate to booking form
                      if (tier.id === 'consultation') {
                        window.location.href = '/consultation/booking';
                        return;
                      }

                      // Check if user is authenticated first
                      const supabase = createClient();
                      const { data: { user } } = await supabase.auth.getUser();

                      if (!user) {
                        // Redirect to sign-up with mentorship context
                        window.location.href = `/sign-up?redirect=${encodeURIComponent(`/mentorship?program=${tier.id}&billing=${billingCycle}`)}`;
                        return;
                      }

                      // For other tiers, create a checkout session with Yoco
                      const response = await fetch('/api/yoco-checkout/mentorship', {
                        method: 'POST',
                        headers: {
                          'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                          priceId: billingCycle === 'full' && tier.fullPriceId
                            ? tier.fullPriceId
                            : tier.priceId,
                          billingCycle,
                          programName: tier.name,
                          programId: tier.id,
                          programType: tier.id, // Add programType (same as tier.id: '6-month' or '12-month')
                          duration: tier.id === '6-month' ? 6 : tier.id === '12-month' ? 12 : 1, // Add duration
                          amount: billingCycle === 'full' && tier.fullPaymentPrice
                            ? tier.fullPaymentPrice
                            : tier.price,
                        }),
                      });

                      if (!response.ok) {
                        // If unauthorized, redirect to sign-up with mentorship context
                        if (response.status === 401) {
                          window.location.href = `/sign-up?redirect=${encodeURIComponent(`/mentorship?program=${tier.id}&billing=${billingCycle}`)}`;
                          return;
                        }
                        throw new Error(`HTTP error! Status: ${response.status}`);
                      }

                      const data = await response.json();
                      console.log('Payment response:', data); // Debug log

                      const { paymentUrl, error } = data;
                      if (error) throw new Error(error);
                      if (!paymentUrl) throw new Error('No payment URL returned');

                      // Redirect to Yoco payment page
                      window.location.href = paymentUrl;
                    } catch (err: any) {
                      console.error('Payment Error:', err);
                      setErrorMessage(err.message || 'An error occurred during payment processing. Please try again.');
                    } finally {
                      setIsLoading(null);
                    }
                  }}
                  disabled={isLoading === tier.id}
                  className={`mt-8 block w-full px-6 py-3 rounded-md text-center font-medium ${
                    tier.isPopular
                      ? 'bg-primary text-white hover:bg-primary/90'
                      : 'bg-primary/10 text-primary hover:bg-primary/10'
                  } ${isLoading === tier.id ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  {isLoading === tier.id ? 'Loading...' : 'Get started'}
                </button>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}
