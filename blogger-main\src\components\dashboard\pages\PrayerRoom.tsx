import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Church,
  Heart,
  MessageSquare,
  Share2,
  Plus,
  Image as ImageIcon,
  Video,
  FileText,
  Calendar,
  Clock,
  User,
  Send,
  Upload,
  Edit,
  Trash2,
  Star,
  MoreVertical,
} from 'lucide-react';
import { useAuth } from '../../../../supabase/auth';
import { supabase } from '../../../../supabase/supabase';
import { useToast } from '@/components/ui/use-toast';

interface Prayer {
  id: string;
  title: string;
  content: string;
  type: 'text' | 'image' | 'video' | 'mixed';
  image_url?: string;
  video_url?: string;
  author_id: string;
  created_at: string;
  updated_at: string;
  likes_count: number;
  comments_count: number;
  author: {
    first_name: string;
    last_name: string;
    email: string;
    avatar_url?: string;
  };
  user_liked: boolean;
}

interface PrayerComment {
  id: string;
  content: string;
  user_id: string;
  prayer_id: string;
  created_at: string;
  user: {
    first_name: string;
    last_name: string;
    email: string;
    avatar_url?: string;
  };
}

export function PrayerRoom() {
  const { user, isAdmin } = useAuth();
  const { toast } = useToast();
  const [prayers, setPrayers] = useState<Prayer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingPrayer, setEditingPrayer] = useState<Prayer | null>(null);
  const [selectedPrayer, setSelectedPrayer] = useState<Prayer | null>(null);
  const [comments, setComments] = useState<PrayerComment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [isCommentsDialogOpen, setIsCommentsDialogOpen] = useState(false);

  // Create prayer form state
  const [newPrayer, setNewPrayer] = useState({
    title: '',
    content: '',
    type: 'text' as 'text' | 'image' | 'video' | 'mixed',
    image_url: '',
    video_url: '',
    image_file: null as File | null,
    video_file: null as File | null,
  });

  useEffect(() => {
    loadPrayers();
  }, []);

  const loadPrayers = async () => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('prayers')
        .select(`
          *,
          profiles(first_name, last_name, email, avatar_url),
          prayer_likes(user_id),
          prayer_comments(id)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const formattedPrayers = data?.map(prayer => ({
        ...prayer,
        author: {
          first_name: prayer.profiles?.first_name || '',
          last_name: prayer.profiles?.last_name || '',
          email: prayer.profiles?.email || 'Unknown',
          avatar_url: prayer.profiles?.avatar_url || '',
        },
        likes_count: prayer.prayer_likes?.length || 0,
        comments_count: prayer.prayer_comments?.length || 0,
        user_liked: prayer.prayer_likes?.some((like: any) => like.user_id === user?.id) || false,
      })) || [];

      setPrayers(formattedPrayers);
    } catch (error) {
      console.error('Error loading prayers:', error);
      toast({
        title: 'Error',
        description: 'Failed to load prayers',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const createPrayer = async () => {
    if (!isAdmin) {
      toast({
        title: 'Access Denied',
        description: 'Only administrators can create prayers',
        variant: 'destructive',
      });
      return;
    }

    if (!newPrayer.title.trim() || !newPrayer.content.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      });
      return;
    }

    try {
      let imageUrl = newPrayer.image_url;
      let videoUrl = newPrayer.video_url;

      // Upload image file if provided
      if (newPrayer.image_file) {
        const fileExt = newPrayer.image_file.name.split('.').pop();
        const fileName = `prayer-image-${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('prayer-media')
          .upload(fileName, newPrayer.image_file);

        if (uploadError) {
          console.error('Image upload error:', uploadError);
          toast({
            title: 'Warning',
            description: 'Failed to upload image, but prayer will be created without image',
            variant: 'destructive',
          });
        } else {
          const { data: { publicUrl } } = supabase.storage
            .from('prayer-media')
            .getPublicUrl(fileName);
          imageUrl = publicUrl;
        }
      }

      // Upload video file if provided
      if (newPrayer.video_file) {
        const fileExt = newPrayer.video_file.name.split('.').pop();
        const fileName = `prayer-video-${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('prayer-media')
          .upload(fileName, newPrayer.video_file);

        if (uploadError) {
          console.error('Video upload error:', uploadError);
          toast({
            title: 'Warning',
            description: 'Failed to upload video, but prayer will be created without video',
            variant: 'destructive',
          });
        } else {
          const { data: { publicUrl } } = supabase.storage
            .from('prayer-media')
            .getPublicUrl(fileName);
          videoUrl = publicUrl;
        }
      }

      // Process YouTube URL if provided
      if (videoUrl && (videoUrl.includes('youtube.com') || videoUrl.includes('youtu.be'))) {
        videoUrl = convertYouTubeToEmbed(videoUrl);
      }

      const { error } = await supabase
        .from('prayers')
        .insert({
          title: newPrayer.title,
          content: newPrayer.content,
          type: newPrayer.type,
          image_url: imageUrl || null,
          video_url: videoUrl || null,
          author_id: user?.id,
        });

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'Prayer created successfully',
      });

      setNewPrayer({
        title: '',
        content: '',
        type: 'text',
        image_url: '',
        video_url: '',
        image_file: null,
        video_file: null,
      });
      setIsCreateDialogOpen(false);
      loadPrayers();
    } catch (error) {
      console.error('Error creating prayer:', error);
      toast({
        title: 'Error',
        description: 'Failed to create prayer',
        variant: 'destructive',
      });
    }
  };

  const convertYouTubeToEmbed = (url: string): string => {
    // Convert YouTube URLs to embed format
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);

    if (match && match[2].length === 11) {
      return `https://www.youtube.com/embed/${match[2]}`;
    }

    return url; // Return original URL if not a valid YouTube URL
  };

  const editPrayer = (prayer: Prayer) => {
    setEditingPrayer(prayer);
    setNewPrayer({
      title: prayer.title,
      content: prayer.content,
      type: prayer.type,
      image_url: prayer.image_url || '',
      video_url: prayer.video_url || '',
      image_file: null,
      video_file: null,
    });
    setIsEditDialogOpen(true);
  };

  const updatePrayer = async () => {
    if (!isAdmin || !editingPrayer) {
      toast({
        title: 'Access Denied',
        description: 'Only administrators can edit prayers',
        variant: 'destructive',
      });
      return;
    }

    if (!newPrayer.title.trim() || !newPrayer.content.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      });
      return;
    }

    try {
      let imageUrl = newPrayer.image_url;
      let videoUrl = newPrayer.video_url;

      // Upload new image file if provided
      if (newPrayer.image_file) {
        const fileExt = newPrayer.image_file.name.split('.').pop();
        const fileName = `prayer-image-${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('prayer-media')
          .upload(fileName, newPrayer.image_file);

        if (!uploadError) {
          const { data: { publicUrl } } = supabase.storage
            .from('prayer-media')
            .getPublicUrl(fileName);
          imageUrl = publicUrl;
        }
      }

      // Upload new video file if provided
      if (newPrayer.video_file) {
        const fileExt = newPrayer.video_file.name.split('.').pop();
        const fileName = `prayer-video-${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('prayer-media')
          .upload(fileName, newPrayer.video_file);

        if (!uploadError) {
          const { data: { publicUrl } } = supabase.storage
            .from('prayer-media')
            .getPublicUrl(fileName);
          videoUrl = publicUrl;
        }
      }

      // Process YouTube URL if provided
      if (videoUrl && (videoUrl.includes('youtube.com') || videoUrl.includes('youtu.be'))) {
        videoUrl = convertYouTubeToEmbed(videoUrl);
      }

      const { error } = await supabase
        .from('prayers')
        .update({
          title: newPrayer.title,
          content: newPrayer.content,
          type: newPrayer.type,
          image_url: imageUrl || null,
          video_url: videoUrl || null,
          updated_at: new Date().toISOString(),
        })
        .eq('id', editingPrayer.id);

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'Prayer updated successfully',
      });

      setNewPrayer({
        title: '',
        content: '',
        type: 'text',
        image_url: '',
        video_url: '',
        image_file: null,
        video_file: null,
      });
      setIsEditDialogOpen(false);
      setEditingPrayer(null);
      loadPrayers();
    } catch (error) {
      console.error('Error updating prayer:', error);
      toast({
        title: 'Error',
        description: 'Failed to update prayer',
        variant: 'destructive',
      });
    }
  };

  const deletePrayer = async (prayerId: string) => {
    if (!isAdmin) {
      toast({
        title: 'Access Denied',
        description: 'Only administrators can delete prayers',
        variant: 'destructive',
      });
      return;
    }

    if (!confirm('Are you sure you want to delete this prayer? This action cannot be undone.')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('prayers')
        .delete()
        .eq('id', prayerId);

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'Prayer deleted successfully',
      });

      loadPrayers();
    } catch (error) {
      console.error('Error deleting prayer:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete prayer',
        variant: 'destructive',
      });
    }
  };

  const toggleFeatured = async (prayerId: string, featured: boolean) => {
    if (!isAdmin) {
      toast({
        title: 'Access Denied',
        description: 'Only administrators can feature prayers',
        variant: 'destructive',
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('prayers')
        .update({ is_featured: featured })
        .eq('id', prayerId);

      if (error) throw error;

      toast({
        title: 'Success',
        description: `Prayer ${featured ? 'featured' : 'unfeatured'} successfully`,
      });

      loadPrayers();
    } catch (error) {
      console.error('Error updating featured status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update featured status',
        variant: 'destructive',
      });
    }
  };

  const toggleLike = async (prayerId: string) => {
    if (!user) return;

    try {
      const prayer = prayers.find(p => p.id === prayerId);
      if (!prayer) return;

      if (prayer.user_liked) {
        // Unlike
        const { error } = await supabase
          .from('prayer_likes')
          .delete()
          .eq('prayer_id', prayerId)
          .eq('user_id', user.id);

        if (error) throw error;
      } else {
        // Like
        const { error } = await supabase
          .from('prayer_likes')
          .insert({
            prayer_id: prayerId,
            user_id: user.id,
          });

        if (error) throw error;
      }

      loadPrayers();
    } catch (error) {
      console.error('Error toggling like:', error);
      toast({
        title: 'Error',
        description: 'Failed to update like',
        variant: 'destructive',
      });
    }
  };

  const loadComments = async (prayerId: string) => {
    try {
      const { data, error } = await supabase
        .from('prayer_comments')
        .select(`
          *,
          profiles(first_name, last_name, email, avatar_url)
        `)
        .eq('prayer_id', prayerId)
        .order('created_at', { ascending: true });

      if (error) throw error;

      const formattedComments = data?.map(comment => ({
        ...comment,
        user: {
          first_name: comment.profiles?.first_name || '',
          last_name: comment.profiles?.last_name || '',
          email: comment.profiles?.email || 'Unknown',
          avatar_url: comment.profiles?.avatar_url || '',
        },
      })) || [];

      setComments(formattedComments);
    } catch (error) {
      console.error('Error loading comments:', error);
      toast({
        title: 'Error',
        description: 'Failed to load comments',
        variant: 'destructive',
      });
    }
  };

  const addComment = async () => {
    if (!user || !selectedPrayer || !newComment.trim()) return;

    try {
      const { error } = await supabase
        .from('prayer_comments')
        .insert({
          content: newComment,
          prayer_id: selectedPrayer.id,
          user_id: user.id,
        });

      if (error) throw error;

      setNewComment('');
      loadComments(selectedPrayer.id);
      loadPrayers(); // Refresh to update comment count
    } catch (error) {
      console.error('Error adding comment:', error);
      toast({
        title: 'Error',
        description: 'Failed to add comment',
        variant: 'destructive',
      });
    }
  };

  const openComments = (prayer: Prayer) => {
    setSelectedPrayer(prayer);
    loadComments(prayer.id);
    setIsCommentsDialogOpen(true);
  };

  const getDisplayName = (author: any) => {
    return `${author.first_name || ''} ${author.last_name || ''}`.trim() || 
           author.email.split('@')[0];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Church className="h-8 w-8 mr-3 text-blue-600" />
            Prayer Room
          </h1>
          <p className="text-gray-600">Daily prayers and spiritual guidance</p>
        </div>
        {isAdmin && (
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Prayer
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Prayer</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="title">Prayer Title</Label>
                  <Input
                    id="title"
                    value={newPrayer.title}
                    onChange={(e) => setNewPrayer({ ...newPrayer, title: e.target.value })}
                    placeholder="Enter prayer title..."
                  />
                </div>
                
                <div>
                  <Label htmlFor="type">Prayer Type</Label>
                  <Tabs value={newPrayer.type} onValueChange={(value: any) => setNewPrayer({ ...newPrayer, type: value })}>
                    <TabsList className="grid w-full grid-cols-4">
                      <TabsTrigger value="text">Text</TabsTrigger>
                      <TabsTrigger value="image">Image</TabsTrigger>
                      <TabsTrigger value="video">Video</TabsTrigger>
                      <TabsTrigger value="mixed">Mixed</TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>

                <div>
                  <Label htmlFor="content">Prayer Content</Label>
                  <Textarea
                    id="content"
                    value={newPrayer.content}
                    onChange={(e) => setNewPrayer({ ...newPrayer, content: e.target.value })}
                    placeholder="Enter prayer content..."
                    rows={4}
                  />
                </div>

                {(newPrayer.type === 'image' || newPrayer.type === 'mixed') && (
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="image_url">Image URL</Label>
                      <Input
                        id="image_url"
                        value={newPrayer.image_url}
                        onChange={(e) => setNewPrayer({ ...newPrayer, image_url: e.target.value })}
                        placeholder="Enter image URL or upload file below..."
                      />
                    </div>
                    <div>
                      <Label htmlFor="image_file">Or Upload Image File</Label>
                      <Input
                        id="image_file"
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0] || null;
                          setNewPrayer({ ...newPrayer, image_file: file });
                        }}
                      />
                      {newPrayer.image_file && (
                        <p className="text-sm text-gray-600 mt-1">
                          Selected: {newPrayer.image_file.name}
                        </p>
                      )}
                    </div>
                  </div>
                )}

                {(newPrayer.type === 'video' || newPrayer.type === 'mixed') && (
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="video_url">Video URL</Label>
                      <Input
                        id="video_url"
                        value={newPrayer.video_url}
                        onChange={(e) => setNewPrayer({ ...newPrayer, video_url: e.target.value })}
                        placeholder="Enter video URL, YouTube link, or upload file below..."
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Supports YouTube links, direct video URLs, or file upload
                      </p>
                    </div>
                    <div>
                      <Label htmlFor="video_file">Or Upload Video File</Label>
                      <Input
                        id="video_file"
                        type="file"
                        accept="video/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0] || null;
                          setNewPrayer({ ...newPrayer, video_file: file });
                        }}
                      />
                      {newPrayer.video_file && (
                        <p className="text-sm text-gray-600 mt-1">
                          Selected: {newPrayer.video_file.name}
                        </p>
                      )}
                    </div>
                  </div>
                )}

                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={createPrayer}>
                    Create Prayer
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}

        {/* Edit Prayer Dialog */}
        {isAdmin && (
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Edit Prayer</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="edit-title">Title</Label>
                  <Input
                    id="edit-title"
                    value={newPrayer.title}
                    onChange={(e) => setNewPrayer({ ...newPrayer, title: e.target.value })}
                    placeholder="Enter prayer title..."
                  />
                </div>

                <div>
                  <Label htmlFor="edit-content">Content</Label>
                  <Textarea
                    id="edit-content"
                    value={newPrayer.content}
                    onChange={(e) => setNewPrayer({ ...newPrayer, content: e.target.value })}
                    placeholder="Enter prayer content..."
                    rows={6}
                  />
                </div>

                <div>
                  <Label htmlFor="edit-type">Type</Label>
                  <select
                    id="edit-type"
                    value={newPrayer.type}
                    onChange={(e) => setNewPrayer({ ...newPrayer, type: e.target.value as any })}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  >
                    <option value="text">Text Only</option>
                    <option value="image">Image</option>
                    <option value="video">Video</option>
                    <option value="mixed">Mixed (Image + Video)</option>
                  </select>
                </div>

                {(newPrayer.type === 'image' || newPrayer.type === 'mixed') && (
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="edit-image_url">Image URL</Label>
                      <Input
                        id="edit-image_url"
                        value={newPrayer.image_url}
                        onChange={(e) => setNewPrayer({ ...newPrayer, image_url: e.target.value })}
                        placeholder="Enter image URL or upload file below..."
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-image_file">Or Upload New Image File</Label>
                      <Input
                        id="edit-image_file"
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0] || null;
                          setNewPrayer({ ...newPrayer, image_file: file });
                        }}
                      />
                      {newPrayer.image_file && (
                        <p className="text-sm text-gray-600 mt-1">
                          Selected: {newPrayer.image_file.name}
                        </p>
                      )}
                    </div>
                  </div>
                )}

                {(newPrayer.type === 'video' || newPrayer.type === 'mixed') && (
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="edit-video_url">Video URL</Label>
                      <Input
                        id="edit-video_url"
                        value={newPrayer.video_url}
                        onChange={(e) => setNewPrayer({ ...newPrayer, video_url: e.target.value })}
                        placeholder="Enter video URL, YouTube link, or upload file below..."
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Supports YouTube links, direct video URLs, or file upload
                      </p>
                    </div>
                    <div>
                      <Label htmlFor="edit-video_file">Or Upload New Video File</Label>
                      <Input
                        id="edit-video_file"
                        type="file"
                        accept="video/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0] || null;
                          setNewPrayer({ ...newPrayer, video_file: file });
                        }}
                      />
                      {newPrayer.video_file && (
                        <p className="text-sm text-gray-600 mt-1">
                          Selected: {newPrayer.video_file.name}
                        </p>
                      )}
                    </div>
                  </div>
                )}

                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={updatePrayer}>
                    Update Prayer
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Prayers List */}
      {prayers.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <Church className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No prayers yet</h3>
            <p className="text-gray-600">
              {isAdmin ? 'Create the first prayer to get started.' : 'Check back later for daily prayers.'}
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {prayers.map((prayer) => (
            <Card key={prayer.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarImage
                        src={prayer.author.avatar_url || `https://api.dicebear.com/7.x/avataaars/svg?seed=${prayer.author.email}`}
                        alt={getDisplayName(prayer.author)}
                      />
                      <AvatarFallback>
                        {prayer.author.first_name?.[0] || prayer.author.last_name?.[0] || prayer.author.email[0].toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{getDisplayName(prayer.author)}</p>
                      <p className="text-sm text-gray-500 flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        {formatDate(prayer.created_at)}
                      </p>
                    </div>
                  </div>
                  <Badge variant="outline" className="flex items-center space-x-1">
                    {prayer.type === 'text' && <FileText className="h-3 w-3" />}
                    {prayer.type === 'image' && <ImageIcon className="h-3 w-3" />}
                    {prayer.type === 'video' && <Video className="h-3 w-3" />}
                    {prayer.type === 'mixed' && <Plus className="h-3 w-3" />}
                    <span className="capitalize">{prayer.type}</span>
                  </Badge>
                </div>
                <CardTitle className="text-xl">{prayer.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-gray-700 whitespace-pre-wrap">{prayer.content}</p>
                  
                  {prayer.image_url && (
                    <div className="rounded-lg overflow-hidden">
                      <img
                        src={prayer.image_url}
                        alt={prayer.title}
                        className="w-full h-auto max-h-96 object-cover"
                      />
                    </div>
                  )}
                  
                  {prayer.video_url && (
                    <div className="rounded-lg overflow-hidden">
                      {prayer.video_url.includes('youtube.com/embed') || prayer.video_url.includes('youtu.be') ? (
                        <iframe
                          src={prayer.video_url.includes('youtube.com/embed')
                            ? prayer.video_url
                            : convertYouTubeToEmbed(prayer.video_url)
                          }
                          title={prayer.title}
                          className="w-full h-auto"
                          style={{ height: '300px' }}
                          frameBorder="0"
                          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                          allowFullScreen
                        />
                      ) : (
                        <video
                          src={prayer.video_url}
                          controls
                          className="w-full h-auto max-h-96"
                          onError={(e) => console.error('Video error:', e)}
                        >
                          Your browser does not support the video tag.
                        </video>
                      )}
                    </div>
                  )}
                  
                  {/* Admin Controls */}
                  {isAdmin && (
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg mb-4">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-600">Featured:</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleFeatured(prayer.id, !prayer.is_featured)}
                          className={prayer.is_featured ? 'text-yellow-600' : 'text-gray-400'}
                        >
                          <Star className={`h-4 w-4 ${prayer.is_featured ? 'fill-current' : ''}`} />
                        </Button>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => editPrayer(prayer)}
                          className="text-blue-600 hover:text-blue-700"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deletePrayer(prayer.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}

                  <div className="flex items-center justify-between pt-4 border-t">
                    <div className="flex items-center space-x-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleLike(prayer.id)}
                        className={prayer.user_liked ? 'text-red-600' : 'text-gray-600'}
                      >
                        <Heart className={`h-4 w-4 mr-1 ${prayer.user_liked ? 'fill-current' : ''}`} />
                        {prayer.likes_count}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openComments(prayer)}
                      >
                        <MessageSquare className="h-4 w-4 mr-1" />
                        {prayer.comments_count}
                      </Button>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Share2 className="h-4 w-4 mr-1" />
                      Share
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Comments Dialog */}
      <Dialog open={isCommentsDialogOpen} onOpenChange={setIsCommentsDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Prayer Comments</DialogTitle>
          </DialogHeader>
          {selectedPrayer && (
            <div className="space-y-4">
              <div className="border-b pb-4">
                <h3 className="font-medium">{selectedPrayer.title}</h3>
                <p className="text-sm text-gray-600">by {getDisplayName(selectedPrayer.author)}</p>
              </div>
              
              <div className="space-y-4 max-h-60 overflow-y-auto">
                {comments.map((comment) => (
                  <div key={comment.id} className="flex space-x-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage
                        src={comment.user.avatar_url || `https://api.dicebear.com/7.x/avataaars/svg?seed=${comment.user.email}`}
                        alt={getDisplayName(comment.user)}
                      />
                      <AvatarFallback>
                        {comment.user.first_name?.[0] || comment.user.last_name?.[0] || comment.user.email[0].toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-sm">{getDisplayName(comment.user)}</span>
                        <span className="text-xs text-gray-500">
                          {new Date(comment.created_at).toLocaleDateString()}
                        </span>
                      </div>
                      <p className="text-sm text-gray-700 mt-1">{comment.content}</p>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="flex space-x-2 pt-4 border-t">
                <Input
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder="Add a comment..."
                  onKeyPress={(e) => e.key === 'Enter' && addComment()}
                />
                <Button onClick={addComment} size="sm">
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
