// Advanced Analytics Dashboard for The Chronicle
// Real-time analytics, user behavior tracking, and business intelligence

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  TrendingUp,
  TrendingDown,
  Users,
  Eye,
  Clock,
  DollarSign,
  Heart,
  MessageSquare,
  Download,
  RefreshCw,
  Globe,
  Smartphone,
  Monitor,
  Tablet
} from 'lucide-react';
import { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

interface AnalyticsData {
  overview: {
    totalUsers: number;
    activeUsers: number;
    pageViews: number;
    sessions: number;
    bounceRate: number;
    avgSessionDuration: number;
    conversionRate: number;
    revenue: number;
  };
  traffic: {
    daily: Array<{
      date: string;
      users: number;
      pageViews: number;
      sessions: number;
    }>;
    sources: Array<{
      source: string;
      users: number;
      percentage: number;
    }>;
    devices: Array<{
      device: string;
      users: number;
      percentage: number;
    }>;
  };
  content: {
    topArticles: Array<{
      id: string;
      title: string;
      views: number;
      likes: number;
      comments: number;
      shares: number;
    }>;
    topCategories: Array<{
      category: string;
      views: number;
      articles: number;
    }>;
  };
  ecommerce: {
    sales: Array<{
      date: string;
      revenue: number;
      orders: number;
    }>;
    topProducts: Array<{
      id: string;
      name: string;
      sales: number;
      revenue: number;
    }>;
    conversionFunnel: Array<{
      stage: string;
      users: number;
      percentage: number;
    }>;
  };
  realTime: {
    activeUsers: number;
    currentPageViews: number;
    topPages: Array<{
      page: string;
      users: number;
    }>;
    recentEvents: Array<{
      timestamp: Date;
      event: string;
      user: string;
      page: string;
    }>;
  };
}

export function AdvancedAnalyticsDashboard() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [dateRange, setDateRange] = useState('7d');
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  useEffect(() => {
    loadAnalyticsData();
    const interval = setInterval(loadAnalyticsData, 60000); // Update every minute
    return () => clearInterval(interval);
  }, [dateRange]);

  const loadAnalyticsData = useCallback(async () => {
    setIsLoading(true);
    try {
      // This would integrate with your analytics service (Google Analytics, Mixpanel, etc.)
      const data = await fetchAnalyticsData(dateRange);
      setAnalyticsData(data);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [dateRange]);

  const exportData = useCallback(() => {
    if (!analyticsData) return;
    
    const exportData = {
      timestamp: new Date().toISOString(),
      dateRange,
      data: analyticsData,
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `analytics-${dateRange}-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [analyticsData, dateRange]);

  if (isLoading && !analyticsData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-center text-gray-500">Failed to load analytics data</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
          <p className="text-gray-600">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </p>
        </div>
        <div className="flex space-x-2">
          <select 
            value={dateRange} 
            onChange={(e) => setDateRange(e.target.value)}
            className="px-3 py-2 border rounded-md"
          >
            <option value="1d">Last 24 hours</option>
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>
          <Button variant="outline" onClick={exportData}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button onClick={loadAnalyticsData} disabled={isLoading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Total Users"
          value={analyticsData.overview.totalUsers.toLocaleString()}
          change={12.5}
          icon={<Users className="w-4 h-4" />}
        />
        <MetricCard
          title="Page Views"
          value={analyticsData.overview.pageViews.toLocaleString()}
          change={8.2}
          icon={<Eye className="w-4 h-4" />}
        />
        <MetricCard
          title="Avg. Session"
          value={`${Math.floor(analyticsData.overview.avgSessionDuration / 60)}m ${analyticsData.overview.avgSessionDuration % 60}s`}
          change={-2.1}
          icon={<Clock className="w-4 h-4" />}
        />
        <MetricCard
          title="Revenue"
          value={`$${analyticsData.overview.revenue.toLocaleString()}`}
          change={15.8}
          icon={<DollarSign className="w-4 h-4" />}
        />
      </div>

      {/* Main Analytics */}
      <Tabs defaultValue="traffic" className="space-y-4">
        <TabsList>
          <TabsTrigger value="traffic">Traffic</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
          <TabsTrigger value="ecommerce">E-commerce</TabsTrigger>
          <TabsTrigger value="realtime">Real-time</TabsTrigger>
        </TabsList>

        {/* Traffic Analytics */}
        <TabsContent value="traffic" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Traffic Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={analyticsData.traffic.daily}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area type="monotone" dataKey="users" stackId="1" stroke="#8884d8" fill="#8884d8" />
                    <Area type="monotone" dataKey="pageViews" stackId="1" stroke="#82ca9d" fill="#82ca9d" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Traffic Sources</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={analyticsData.traffic.sources}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percentage }) => `${name} ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="users"
                    >
                      {analyticsData.traffic.sources.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Device Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {analyticsData.traffic.devices.map((device) => (
                  <div key={device.device} className="flex items-center space-x-3 p-3 border rounded-lg">
                    <DeviceIcon device={device.device} />
                    <div className="flex-1">
                      <div className="font-medium">{device.device}</div>
                      <div className="text-sm text-gray-600">{device.users.toLocaleString()} users</div>
                    </div>
                    <Badge variant="secondary">{device.percentage}%</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Content Analytics */}
        <TabsContent value="content" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Top Articles</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analyticsData.content.topArticles.map((article, index) => (
                    <div key={article.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                      <div className="flex-shrink-0 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold">
                        {index + 1}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium truncate">{article.title}</div>
                        <div className="flex space-x-4 text-sm text-gray-600">
                          <span className="flex items-center">
                            <Eye className="w-3 h-3 mr-1" />
                            {article.views}
                          </span>
                          <span className="flex items-center">
                            <Heart className="w-3 h-3 mr-1" />
                            {article.likes}
                          </span>
                          <span className="flex items-center">
                            <MessageSquare className="w-3 h-3 mr-1" />
                            {article.comments}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Categories</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={analyticsData.content.topCategories}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="category" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="views" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* E-commerce Analytics */}
        <TabsContent value="ecommerce" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Sales Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={analyticsData.ecommerce.sales}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="revenue" stroke="#8884d8" />
                    <Line type="monotone" dataKey="orders" stroke="#82ca9d" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Conversion Funnel</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analyticsData.ecommerce.conversionFunnel.map((stage, index) => (
                    <div key={stage.stage} className="relative">
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium">{stage.stage}</span>
                        <span className="text-sm text-gray-600">{stage.percentage}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full transition-all duration-300"
                          style={{ width: `${stage.percentage}%` }}
                        ></div>
                      </div>
                      <div className="text-sm text-gray-600 mt-1">{stage.users.toLocaleString()} users</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Real-time Analytics */}
        <TabsContent value="realtime" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium">Active Users</span>
                </div>
                <div className="text-2xl font-bold mt-2">{analyticsData.realTime.activeUsers}</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Eye className="w-4 h-4 text-blue-500" />
                  <span className="text-sm font-medium">Page Views</span>
                </div>
                <div className="text-2xl font-bold mt-2">{analyticsData.realTime.currentPageViews}</div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Top Active Pages</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {analyticsData.realTime.topPages.map((page) => (
                    <div key={page.page} className="flex items-center justify-between p-2 border rounded">
                      <span className="truncate">{page.page}</span>
                      <Badge variant="secondary">{page.users} users</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {analyticsData.realTime.recentEvents.map((event, index) => (
                    <div key={index} className="flex items-center space-x-2 text-sm p-2 border rounded">
                      <div className="text-gray-500">{event.timestamp.toLocaleTimeString()}</div>
                      <div className="font-medium">{event.event}</div>
                      <div className="text-gray-600">by {event.user}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Helper Components

interface MetricCardProps {
  title: string;
  value: string;
  change: number;
  icon: React.ReactNode;
}

function MetricCard({ title, value, change, icon }: MetricCardProps) {
  const isPositive = change > 0;
  
  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
          </div>
          <div className="flex flex-col items-end">
            {icon}
            <div className={`flex items-center text-sm ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
              {isPositive ? <TrendingUp className="w-3 h-3 mr-1" /> : <TrendingDown className="w-3 h-3 mr-1" />}
              {Math.abs(change)}%
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function DeviceIcon({ device }: { device: string }) {
  switch (device.toLowerCase()) {
    case 'desktop':
      return <Monitor className="w-5 h-5 text-blue-500" />;
    case 'mobile':
      return <Smartphone className="w-5 h-5 text-green-500" />;
    case 'tablet':
      return <Tablet className="w-5 h-5 text-purple-500" />;
    default:
      return <Globe className="w-5 h-5 text-gray-500" />;
  }
}

// Mock data fetcher
async function fetchAnalyticsData(dateRange: string): Promise<AnalyticsData> {
  // This would integrate with your actual analytics service
  return {
    overview: {
      totalUsers: 12543,
      activeUsers: 1234,
      pageViews: 45678,
      sessions: 8901,
      bounceRate: 45.2,
      avgSessionDuration: 185,
      conversionRate: 3.2,
      revenue: 23456,
    },
    traffic: {
      daily: [
        { date: '2024-01-01', users: 1200, pageViews: 3400, sessions: 890 },
        { date: '2024-01-02', users: 1350, pageViews: 3800, sessions: 950 },
        { date: '2024-01-03', users: 1100, pageViews: 3200, sessions: 820 },
        { date: '2024-01-04', users: 1450, pageViews: 4100, sessions: 1020 },
        { date: '2024-01-05', users: 1600, pageViews: 4500, sessions: 1150 },
        { date: '2024-01-06', users: 1380, pageViews: 3900, sessions: 980 },
        { date: '2024-01-07', users: 1520, pageViews: 4300, sessions: 1080 },
      ],
      sources: [
        { source: 'Direct', users: 4500, percentage: 36 },
        { source: 'Google', users: 3200, percentage: 25 },
        { source: 'Social Media', users: 2800, percentage: 22 },
        { source: 'Referral', users: 1500, percentage: 12 },
        { source: 'Email', users: 543, percentage: 5 },
      ],
      devices: [
        { device: 'Desktop', users: 6500, percentage: 52 },
        { device: 'Mobile', users: 4800, percentage: 38 },
        { device: 'Tablet', users: 1243, percentage: 10 },
      ],
    },
    content: {
      topArticles: [
        { id: '1', title: 'The Future of AI in Journalism', views: 2340, likes: 156, comments: 23, shares: 45 },
        { id: '2', title: 'Climate Change: A Global Perspective', views: 1890, likes: 134, comments: 18, shares: 32 },
        { id: '3', title: 'Tech Trends 2024', views: 1650, likes: 98, comments: 15, shares: 28 },
        { id: '4', title: 'Economic Outlook', views: 1420, likes: 87, comments: 12, shares: 19 },
        { id: '5', title: 'Health & Wellness Guide', views: 1230, likes: 76, comments: 9, shares: 15 },
      ],
      topCategories: [
        { category: 'Technology', views: 8900, articles: 45 },
        { category: 'Politics', views: 7600, articles: 38 },
        { category: 'Business', views: 6800, articles: 32 },
        { category: 'Health', views: 5400, articles: 28 },
        { category: 'Sports', views: 4200, articles: 22 },
      ],
    },
    ecommerce: {
      sales: [
        { date: '2024-01-01', revenue: 2340, orders: 23 },
        { date: '2024-01-02', revenue: 2890, orders: 28 },
        { date: '2024-01-03', revenue: 2100, orders: 21 },
        { date: '2024-01-04', revenue: 3200, orders: 32 },
        { date: '2024-01-05', revenue: 3800, orders: 38 },
        { date: '2024-01-06', revenue: 3400, orders: 34 },
        { date: '2024-01-07', revenue: 4100, orders: 41 },
      ],
      topProducts: [
        { id: '1', name: 'Premium Subscription', sales: 234, revenue: 23400 },
        { id: '2', name: 'Digital Magazine', sales: 156, revenue: 7800 },
        { id: '3', name: 'Merchandise Bundle', sales: 89, revenue: 4450 },
      ],
      conversionFunnel: [
        { stage: 'Visitors', users: 10000, percentage: 100 },
        { stage: 'Product Views', users: 3200, percentage: 32 },
        { stage: 'Add to Cart', users: 960, percentage: 9.6 },
        { stage: 'Checkout', users: 480, percentage: 4.8 },
        { stage: 'Purchase', users: 320, percentage: 3.2 },
      ],
    },
    realTime: {
      activeUsers: 234,
      currentPageViews: 567,
      topPages: [
        { page: '/articles/ai-journalism', users: 45 },
        { page: '/products/premium', users: 32 },
        { page: '/', users: 28 },
        { page: '/articles/climate-change', users: 23 },
        { page: '/about', users: 18 },
      ],
      recentEvents: [
        { timestamp: new Date(), event: 'Page View', user: 'Anonymous', page: '/articles/ai-journalism' },
        { timestamp: new Date(), event: 'Purchase', user: 'User123', page: '/checkout' },
        { timestamp: new Date(), event: 'Sign Up', user: 'NewUser456', page: '/register' },
        { timestamp: new Date(), event: 'Article Like', user: 'Reader789', page: '/articles/tech-trends' },
      ],
    },
  };
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export default AdvancedAnalyticsDashboard;
