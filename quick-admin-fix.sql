-- Quick Admin Access Fix
-- Run this in your Supabase SQL Editor to fix admin access issues

-- Step 1: First, let's check the auth.users table structure
SELECT
    'Auth users table structure:' as info,
    column_name,
    data_type
FROM information_schema.columns
WHERE table_name = 'users'
AND table_schema = 'auth'
ORDER BY ordinal_position;

-- Step 1b: Check what users exist in auth.users (all columns)
SELECT
    'All auth users:' as info,
    id,
    email,
    created_at,
    -- Try different possible metadata column names
    CASE
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND table_schema = 'auth' AND column_name = 'user_metadata')
        THEN user_metadata->>'role'
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND table_schema = 'auth' AND column_name = 'raw_user_meta_data')
        THEN raw_user_meta_data->>'role'
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND table_schema = 'auth' AND column_name = 'raw_app_meta_data')
        THEN raw_app_meta_data->>'role'
        ELSE 'no_metadata_column'
    END as metadata_role
FROM auth.users
ORDER BY created_at DESC
LIMIT 5;

-- Step 2: Check what users exist in public.users
SELECT 
    'Public users:' as info,
    id,
    email,
    role,iiii
    created_at
FROM public.users
ORDER BY created_at DESC
LIMIT 10;

-- Step 3: Since we can't rely on metadata, let's create admin users manually
-- First, let's see if there are any users in auth.users that might be admins
SELECT
    'Users that might be admins (check email):' as info,
    id,
    email,
    created_at
FROM auth.users
ORDER BY created_at DESC;

-- Step 3b: Manual admin user creation
-- REPLACE '<EMAIL>' with your actual admin email
-- You can run this multiple times for different admin emails

-- Uncomment and modify the email below to create your admin user:
/*
INSERT INTO public.users (
    id,
    email,
    full_name,
    name,
    role,
    admin_role,
    token_identifier,
    created_at,
    updated_at
)
SELECT
    au.id,
    au.email,
    'Admin User',
    'Admin User',
    'admin',
    'admin',
    au.id::text,
    au.created_at,
    NOW()
FROM auth.users au
LEFT JOIN public.users pu ON au.id = pu.id
WHERE au.email = '<EMAIL>'  -- CHANGE THIS EMAIL
AND pu.id IS NULL;
*/

-- Step 4: Update existing users to have admin role manually
-- REPLACE '<EMAIL>' with your actual admin email

-- Uncomment and modify the email below to update your user to admin:
/*
UPDATE public.users
SET
    role = 'admin',
    admin_role = 'admin',
    updated_at = NOW()
WHERE email = '<EMAIL>'  -- CHANGE THIS EMAIL
AND role != 'admin';
*/

-- Step 5: Add admin_role column if it doesn't exist
DO $$
BEGIN
    -- Create admin_role enum if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'admin_role') THEN
        CREATE TYPE admin_role AS ENUM ('admin', 'senior_admin', 'junior_admin');
    END IF;
    
    -- Add admin_role column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'admin_role'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.users ADD COLUMN admin_role admin_role;
    END IF;
END $$;

-- Step 6: Set admin_role for admin users
UPDATE public.users 
SET admin_role = 'admin'
WHERE role = 'admin' 
AND (admin_role IS NULL OR admin_role != 'admin');

-- Step 7: Verify the fix
SELECT 
    'Final admin users:' as info,
    id,
    email,
    full_name,
    role,
    admin_role,
    created_at
FROM public.users
WHERE role = 'admin'
ORDER BY created_at DESC;

-- Step 8: Check if RLS is blocking access (temporarily disable for testing)
-- UNCOMMENT THE NEXT LINE ONLY FOR TESTING
-- ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;

-- Instructions
SELECT 
    'INSTRUCTIONS:' as info,
    'If you still get admin_access_required error:' as step1,
    '1. Clear browser cache and cookies' as step2,
    '2. Sign out and sign in again' as step3,
    '3. Check browser console for errors' as step4,
    '4. Visit /admin/debug-auth to see detailed diagnostics' as step5;
