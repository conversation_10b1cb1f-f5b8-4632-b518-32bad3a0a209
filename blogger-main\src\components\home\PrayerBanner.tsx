import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Church,
  Heart,
  MessageSquare,
  Share2,
  ChevronLeft,
  ChevronRight,
  Calendar,
  Clock,
  User,
  Play,
  Pause,
  Volume2,
  VolumeX,
} from 'lucide-react';
import { supabase } from '../../../supabase/supabase';
import { useAuth } from '../../../supabase/auth';
import { useToast } from '@/components/ui/use-toast';
import { Link } from 'react-router-dom';

interface Prayer {
  id: string;
  title: string;
  content: string;
  type: 'text' | 'image' | 'video' | 'mixed';
  image_url?: string;
  video_url?: string;
  author_id: string;
  created_at: string;
  updated_at: string;
  likes_count: number;
  comments_count: number;
  author: {
    first_name: string;
    last_name: string;
    email: string;
    avatar_url?: string;
  };
  user_liked: boolean;
}

export function PrayerBanner() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [prayers, setPrayers] = useState<Prayer[]>([]);
  const [currentPrayerIndex, setCurrentPrayerIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(true);

  useEffect(() => {
    loadTodaysPrayers();
  }, []);

  const loadTodaysPrayers = async () => {
    try {
      setIsLoading(true);
      
      // Get today's date in YYYY-MM-DD format
      const today = new Date().toISOString().split('T')[0];
      
      const { data, error } = await supabase
        .from('prayers')
        .select(`
          *,
          profiles(first_name, last_name, email, avatar_url),
          prayer_likes(user_id),
          prayer_comments(id)
        `)
        .gte('created_at', `${today}T00:00:00.000Z`)
        .lt('created_at', `${today}T23:59:59.999Z`)
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) {
        // If no prayers today, get the latest prayers
        const { data: latestData, error: latestError } = await supabase
          .from('prayers')
          .select(`
            *,
            profiles(first_name, last_name, email, avatar_url),
            prayer_likes(user_id),
            prayer_comments(id)
          `)
          .order('created_at', { ascending: false })
          .limit(3);

        if (latestError) throw latestError;
        
        const formattedPrayers = formatPrayersData(latestData || []);
        setPrayers(formattedPrayers);
      } else {
        const formattedPrayers = formatPrayersData(data || []);
        setPrayers(formattedPrayers);
      }
    } catch (error) {
      console.error('Error loading prayers:', error);
      // Don't show error toast on home page, just fail silently
    } finally {
      setIsLoading(false);
    }
  };

  const formatPrayersData = (data: any[]) => {
    return data.map(prayer => ({
      ...prayer,
      author: {
        first_name: prayer.profiles?.first_name || '',
        last_name: prayer.profiles?.last_name || '',
        email: prayer.profiles?.email || 'Unknown',
        avatar_url: prayer.profiles?.avatar_url || '',
      },
      likes_count: prayer.prayer_likes?.length || 0,
      comments_count: prayer.prayer_comments?.length || 0,
      user_liked: prayer.prayer_likes?.some((like: any) => like.user_id === user?.id) || false,
    }));
  };

  const toggleLike = async (prayerId: string) => {
    if (!user) {
      toast({
        title: 'Login Required',
        description: 'Please login to like prayers',
        variant: 'destructive',
      });
      return;
    }

    try {
      const prayer = prayers.find(p => p.id === prayerId);
      if (!prayer) return;

      if (prayer.user_liked) {
        // Unlike
        const { error } = await supabase
          .from('prayer_likes')
          .delete()
          .eq('prayer_id', prayerId)
          .eq('user_id', user.id);

        if (error) throw error;
      } else {
        // Like
        const { error } = await supabase
          .from('prayer_likes')
          .insert({
            prayer_id: prayerId,
            user_id: user.id,
          });

        if (error) throw error;
      }

      // Update local state
      setPrayers(prev => prev.map(p => 
        p.id === prayerId 
          ? { 
              ...p, 
              user_liked: !p.user_liked,
              likes_count: p.user_liked ? p.likes_count - 1 : p.likes_count + 1
            }
          : p
      ));
    } catch (error) {
      console.error('Error toggling like:', error);
      toast({
        title: 'Error',
        description: 'Failed to update like',
        variant: 'destructive',
      });
    }
  };

  const nextPrayer = () => {
    setCurrentPrayerIndex((prev) => (prev + 1) % prayers.length);
  };

  const prevPrayer = () => {
    setCurrentPrayerIndex((prev) => (prev - 1 + prayers.length) % prayers.length);
  };

  const getDisplayName = (author: any) => {
    return `${author.first_name || ''} ${author.last_name || ''}`.trim() || 
           author.email.split('@')[0];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const toggleVideoPlay = () => {
    const video = document.querySelector('.prayer-video') as HTMLVideoElement;
    if (video) {
      if (isVideoPlaying) {
        video.pause();
      } else {
        video.play();
      }
      setIsVideoPlaying(!isVideoPlaying);
    }
  };

  const toggleMute = () => {
    const video = document.querySelector('.prayer-video') as HTMLVideoElement;
    if (video) {
      video.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  if (isLoading) {
    return (
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 py-8">
        <div className="max-w-7xl mx-auto px-4">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (prayers.length === 0) {
    return null; // Don't show banner if no prayers
  }

  const currentPrayer = prayers[currentPrayerIndex];

  return (
    <div className="bg-gradient-to-r from-blue-50 via-purple-50 to-pink-50 py-12">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Church className="h-8 w-8 text-blue-600 mr-3" />
            <h2 className="text-3xl font-bold text-gray-900">Daily Prayer</h2>
          </div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Find peace and inspiration through our daily prayers. Take a moment to reflect and connect with the divine.
          </p>
        </div>

        {/* Prayer Card */}
        <Card className="max-w-4xl mx-auto shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardContent className="p-8">
            {/* Prayer Navigation */}
            {prayers.length > 1 && (
              <div className="flex justify-between items-center mb-6">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={prevPrayer}
                  className="text-gray-600 hover:text-gray-900"
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </Button>
                <div className="flex space-x-2">
                  {prayers.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentPrayerIndex(index)}
                      className={`w-2 h-2 rounded-full transition-colors ${
                        index === currentPrayerIndex ? 'bg-blue-600' : 'bg-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={nextPrayer}
                  className="text-gray-600 hover:text-gray-900"
                >
                  Next
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            )}

            {/* Prayer Header */}
            <div className="text-center mb-6">
              <Badge variant="outline" className="mb-3">
                {currentPrayer.type.charAt(0).toUpperCase() + currentPrayer.type.slice(1)} Prayer
              </Badge>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">
                {currentPrayer.title}
              </h3>
              <div className="flex items-center justify-center text-sm text-gray-600">
                <User className="h-4 w-4 mr-1" />
                <span className="mr-4">by {getDisplayName(currentPrayer.author)}</span>
                <Calendar className="h-4 w-4 mr-1" />
                <span>{formatDate(currentPrayer.created_at)}</span>
              </div>
            </div>

            {/* Prayer Content */}
            <div className="space-y-6">
              <div className="text-center">
                <p className="text-lg text-gray-700 leading-relaxed italic">
                  "{currentPrayer.content}"
                </p>
              </div>

              {/* Media Content */}
              {currentPrayer.image_url && (
                <div className="rounded-lg overflow-hidden">
                  <img
                    src={currentPrayer.image_url}
                    alt={currentPrayer.title}
                    className="w-full h-64 object-cover"
                  />
                </div>
              )}

              {currentPrayer.video_url && (
                <div className="relative rounded-lg overflow-hidden">
                  {currentPrayer.video_url.includes('youtube.com/embed') ? (
                    <iframe
                      className="w-full h-64"
                      src={currentPrayer.video_url}
                      title={currentPrayer.title}
                      frameBorder="0"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                    />
                  ) : (
                    <>
                      <video
                        className="prayer-video w-full h-64 object-cover"
                        src={currentPrayer.video_url}
                        muted={isMuted}
                        controls
                        onPlay={() => setIsVideoPlaying(true)}
                        onPause={() => setIsVideoPlaying(false)}
                        onError={(e) => console.error('Video error:', e)}
                      />
                      <div className="absolute bottom-4 left-4 flex space-x-2">
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={toggleVideoPlay}
                          className="bg-black/50 text-white hover:bg-black/70"
                        >
                          {isVideoPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                        </Button>
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={toggleMute}
                          className="bg-black/50 text-white hover:bg-black/70"
                        >
                          {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                        </Button>
                      </div>
                    </>
                  )}
                </div>
              )}
            </div>

            {/* Prayer Actions */}
            <div className="flex items-center justify-between pt-6 border-t">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleLike(currentPrayer.id)}
                  className={currentPrayer.user_liked ? 'text-red-600' : 'text-gray-600'}
                >
                  <Heart className={`h-4 w-4 mr-1 ${currentPrayer.user_liked ? 'fill-current' : ''}`} />
                  {currentPrayer.likes_count}
                </Button>
                <Button variant="ghost" size="sm" className="text-gray-600">
                  <MessageSquare className="h-4 w-4 mr-1" />
                  {currentPrayer.comments_count}
                </Button>
                <Button variant="ghost" size="sm" className="text-gray-600">
                  <Share2 className="h-4 w-4 mr-1" />
                  Share
                </Button>
              </div>
              <Link to="/dashboard/prayers">
                <Button variant="outline" size="sm">
                  View All Prayers
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
