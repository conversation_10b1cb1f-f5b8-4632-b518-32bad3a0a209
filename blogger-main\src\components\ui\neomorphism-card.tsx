import React from 'react';
import { cn } from '@/lib/utils';

interface NeomorphismCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'pressed' | 'elevated' | 'flat';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  interactive?: boolean;
  onClick?: () => void;
}

export function NeomorphismCard({
  children,
  className,
  variant = 'default',
  size = 'md',
  interactive = false,
  onClick,
}: NeomorphismCardProps) {
  const baseClasses = 'relative transition-all duration-300 ease-in-out';
  
  const variantClasses = {
    default: 'bg-gray-100 shadow-[8px_8px_16px_#d1d9e6,-8px_-8px_16px_#ffffff] hover:shadow-[12px_12px_24px_#d1d9e6,-12px_-12px_24px_#ffffff]',
    pressed: 'bg-gray-100 shadow-[inset_8px_8px_16px_#d1d9e6,inset_-8px_-8px_16px_#ffffff]',
    elevated: 'bg-gray-50 shadow-[16px_16px_32px_#d1d9e6,-16px_-16px_32px_#ffffff] hover:shadow-[20px_20px_40px_#d1d9e6,-20px_-20px_40px_#ffffff]',
    flat: 'bg-gray-100 shadow-[4px_4px_8px_#d1d9e6,-4px_-4px_8px_#ffffff]',
  };

  const sizeClasses = {
    sm: 'p-3 rounded-lg',
    md: 'p-4 rounded-xl',
    lg: 'p-6 rounded-2xl',
    xl: 'p-8 rounded-3xl',
  };

  const interactiveClasses = interactive
    ? 'cursor-pointer transform hover:scale-[1.02] active:scale-[0.98] active:shadow-[inset_4px_4px_8px_#d1d9e6,inset_-4px_-4px_8px_#ffffff]'
    : '';

  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        interactiveClasses,
        className
      )}
      onClick={onClick}
    >
      {children}
    </div>
  );
}

interface NeomorphismButtonProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'primary' | 'secondary' | 'success' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  onClick?: () => void;
}

export function NeomorphismButton({
  children,
  className,
  variant = 'primary',
  size = 'md',
  disabled = false,
  onClick,
}: NeomorphismButtonProps) {
  const baseClasses = 'relative transition-all duration-200 ease-in-out font-medium border-none outline-none';
  
  const variantClasses = {
    primary: 'bg-blue-100 text-blue-800 shadow-[6px_6px_12px_#c5d4ed,-6px_-6px_12px_#ffffff] hover:shadow-[8px_8px_16px_#c5d4ed,-8px_-8px_16px_#ffffff] active:shadow-[inset_4px_4px_8px_#c5d4ed,inset_-4px_-4px_8px_#ffffff]',
    secondary: 'bg-gray-100 text-gray-700 shadow-[6px_6px_12px_#d1d9e6,-6px_-6px_12px_#ffffff] hover:shadow-[8px_8px_16px_#d1d9e6,-8px_-8px_16px_#ffffff] active:shadow-[inset_4px_4px_8px_#d1d9e6,inset_-4px_-4px_8px_#ffffff]',
    success: 'bg-green-100 text-green-800 shadow-[6px_6px_12px_#d1e7dd,-6px_-6px_12px_#ffffff] hover:shadow-[8px_8px_16px_#d1e7dd,-8px_-8px_16px_#ffffff] active:shadow-[inset_4px_4px_8px_#d1e7dd,inset_-4px_-4px_8px_#ffffff]',
    danger: 'bg-red-100 text-red-800 shadow-[6px_6px_12px_#f8d7da,-6px_-6px_12px_#ffffff] hover:shadow-[8px_8px_16px_#f8d7da,-8px_-8px_16px_#ffffff] active:shadow-[inset_4px_4px_8px_#f8d7da,inset_-4px_-4px_8px_#ffffff]',
  };

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm rounded-lg',
    md: 'px-4 py-2 text-base rounded-xl',
    lg: 'px-6 py-3 text-lg rounded-2xl',
  };

  const disabledClasses = disabled
    ? 'opacity-50 cursor-not-allowed shadow-[inset_2px_2px_4px_#d1d9e6,inset_-2px_-2px_4px_#ffffff]'
    : 'cursor-pointer';

  return (
    <button
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        disabledClasses,
        className
      )}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
}

interface NeomorphismInputProps {
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  className?: string;
  type?: string;
  disabled?: boolean;
}

export function NeomorphismInput({
  placeholder,
  value,
  onChange,
  className,
  type = 'text',
  disabled = false,
}: NeomorphismInputProps) {
  const baseClasses = 'w-full bg-gray-100 border-none outline-none transition-all duration-200 ease-in-out';
  const shadowClasses = 'shadow-[inset_6px_6px_12px_#d1d9e6,inset_-6px_-6px_12px_#ffffff] focus:shadow-[inset_8px_8px_16px_#d1d9e6,inset_-8px_-8px_16px_#ffffff]';
  const sizeClasses = 'px-4 py-3 rounded-xl text-gray-700 placeholder-gray-500';
  
  return (
    <input
      type={type}
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      disabled={disabled}
      className={cn(
        baseClasses,
        shadowClasses,
        sizeClasses,
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
    />
  );
}

interface NeomorphismBadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'success' | 'warning' | 'danger' | 'info';
  className?: string;
}

export function NeomorphismBadge({
  children,
  variant = 'default',
  className,
}: NeomorphismBadgeProps) {
  const baseClasses = 'inline-flex items-center px-3 py-1 text-xs font-medium rounded-full';
  
  const variantClasses = {
    default: 'bg-gray-100 text-gray-700 shadow-[4px_4px_8px_#d1d9e6,-4px_-4px_8px_#ffffff]',
    success: 'bg-green-100 text-green-800 shadow-[4px_4px_8px_#d1e7dd,-4px_-4px_8px_#ffffff]',
    warning: 'bg-yellow-100 text-yellow-800 shadow-[4px_4px_8px_#fef3cd,-4px_-4px_8px_#ffffff]',
    danger: 'bg-red-100 text-red-800 shadow-[4px_4px_8px_#f8d7da,-4px_-4px_8px_#ffffff]',
    info: 'bg-blue-100 text-blue-800 shadow-[4px_4px_8px_#c5d4ed,-4px_-4px_8px_#ffffff]',
  };

  return (
    <span className={cn(baseClasses, variantClasses[variant], className)}>
      {children}
    </span>
  );
}

interface NeomorphismToggleProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  className?: string;
  disabled?: boolean;
}

export function NeomorphismToggle({
  checked,
  onChange,
  className,
  disabled = false,
}: NeomorphismToggleProps) {
  return (
    <button
      type="button"
      className={cn(
        'relative inline-flex h-6 w-11 items-center rounded-full transition-all duration-200 ease-in-out',
        checked
          ? 'bg-blue-100 shadow-[inset_4px_4px_8px_#c5d4ed,inset_-4px_-4px_8px_#ffffff]'
          : 'bg-gray-100 shadow-[inset_4px_4px_8px_#d1d9e6,inset_-4px_-4px_8px_#ffffff]',
        disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer',
        className
      )}
      onClick={() => !disabled && onChange(!checked)}
      disabled={disabled}
    >
      <span
        className={cn(
          'inline-block h-4 w-4 transform rounded-full transition-all duration-200 ease-in-out',
          checked
            ? 'translate-x-6 bg-blue-600 shadow-[2px_2px_4px_#c5d4ed,-2px_-2px_4px_#ffffff]'
            : 'translate-x-1 bg-gray-400 shadow-[2px_2px_4px_#d1d9e6,-2px_-2px_4px_#ffffff]'
        )}
      />
    </button>
  );
}
