import { NextResponse } from 'next/server';
import { createClient } from '../../../../utils/supabase/server';
import { createServiceClient } from '../../../../utils/supabase/service';

export async function GET(request: Request) {
  try {
    // Use service client for public mentorship program data
    const supabase = createServiceClient();

    const { data: programs, error } = await supabase
      .from('mentorship_programs')
      .select('*')
      .order('duration_months', { ascending: true });

    if (error) {
      console.error('Error fetching mentorship programs:', error);
      return NextResponse.json(
        { error: 'Failed to fetch mentorship programs' },
        { status: 500 }
      );
    }

    return NextResponse.json(programs);
  } catch (error: any) {
    console.error('Error in mentorship programs API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || userData.role !== 'admin') {
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { name, description, duration_months, price_monthly, price_upfront, features } = body;

    // Validate required fields
    if (!name || !duration_months || !price_monthly) {
      return NextResponse.json(
        { error: 'Missing required fields: name, duration_months, price_monthly' },
        { status: 400 }
      );
    }

    const { data: program, error } = await supabase
      .from('mentorship_programs')
      .insert({
        name,
        description,
        duration_months,
        price_monthly,
        price_upfront,
        features
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating mentorship program:', error);
      return NextResponse.json(
        { error: 'Failed to create mentorship program' },
        { status: 500 }
      );
    }

    return NextResponse.json(program, { status: 201 });
  } catch (error: any) {
    console.error('Error in mentorship programs POST API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
