/**
 * Yoco Payment Gateway Integration
 * 
 * This file provides the configuration and utilities for integrating with Yoco payment gateway.
 * Yoco is a South African payment processor that allows businesses to accept card payments.
 * 
 * @see https://www.yoco.com/za/
 */

// Define the Yoco configuration interface
interface YocoConfig {
  publicKey: string;
  secretKey: string;
  apiVersion?: string;
  testMode?: boolean;
}

// Create the Yoco configuration
const yocoConfig: YocoConfig = {
  publicKey: process.env.YOCO_PUBLIC_KEY || '',
  secretKey: process.env.YOCO_SECRET_KEY || '',
  testMode: process.env.NODE_ENV !== 'production',
};

// Validate configuration
if (!yocoConfig.publicKey) {
  throw new Error('YOCO_PUBLIC_KEY is not set');
}

if (!yocoConfig.secretKey) {
  throw new Error('YOCO_SECRET_KEY is not set');
}

// Export the Yoco configuration
export default yocoConfig;

// Helper function to format amount for Yoco (in cents)
export const formatAmount = (amount: number): number => {
  return Math.round(amount * 100);
};

/**
 * Create a payment URL using Yoco's API
 * This function creates a payment link that redirects to Yoco's payment page
 */
export const createPaymentUrl = async (
  amount: number, 
  currency: string = 'ZAR', 
  description: string,
  metadata: Record<string, string> = {},
  successUrl: string,
  cancelUrl: string
): Promise<string> => {
  try {
    // Format metadata to ensure all values are strings
    const formattedMetadata: Record<string, string> = {};
    Object.keys(metadata).forEach(key => {
      formattedMetadata[key] = String(metadata[key] || '');
    });
    
    // Prepare the request payload for Yoco API
    const payload = {
      amount: formatAmount(amount),
      currency,
      name: description,
      description: description,
      metadata: formattedMetadata,
      callback_url: successUrl,
      callback_url_cancel: cancelUrl,
      // Set to true for test mode
      test: yocoConfig.testMode
    };
    
    // For development/testing, log the payload
    if (yocoConfig.testMode) {
      console.log('Yoco payment payload:', payload);
    }
    
    // Only use simulated payment URL in development/test mode
    if (yocoConfig.testMode && process.env.NODE_ENV !== 'production') {
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
      const simulatedPaymentUrl = `${baseUrl}/api/yoco-redirect?amount=${formatAmount(amount)}&currency=${currency}&description=${encodeURIComponent(description)}&successUrl=${encodeURIComponent(successUrl)}&cancelUrl=${encodeURIComponent(cancelUrl)}`;
      
      console.log('Using simulated Yoco payment URL:', simulatedPaymentUrl);
      return simulatedPaymentUrl;
    }
    
    // Make the API call to Yoco to create a payment link
    const response = await fetch('https://online.yoco.com/v1/checkout/', {
      method: 'POST',
      headers: {
        'X-Auth-Secret-Key': yocoConfig.secretKey,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });
    
    // Check if response is OK before trying to parse JSON
    if (!response.ok) {
      // Try to get the response text to see what's wrong
      const errorText = await response.text();
      console.error('Yoco API error response:', errorText);
      
      // Check if the response is HTML (likely an error page)
      if (errorText.includes('<!DOCTYPE') || errorText.includes('<html')) {
        throw new Error(`API returned HTML instead of JSON. Status: ${response.status}. Check your API keys and endpoint.`);
      }
      
      // Try to parse as JSON if possible
      try {
        const errorJson = JSON.parse(errorText);
        throw new Error(errorJson.message || `API error: ${response.status}`);
      } catch (e) {
        // If parsing fails, return a generic error with the status code
        throw new Error(`Failed to create payment. Status: ${response.status}`);
      }
    }
    
    // Parse the successful response
    const result = await response.json();
    
    // Return the payment URL
    return result.url;
  } catch (error: any) {
    console.error('Error creating Yoco payment URL:', error);
    throw new Error(`Failed to create payment link: ${error.message}`);
  }
};
