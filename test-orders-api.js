// Test script to verify orders API functionality
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://riypdqccaejgqwjfiumr.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJpeXBkcWNjYWVqZ3F3amZpdW1yIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjM0ODg4MiwiZXhwIjoyMDYxOTI0ODgyfQ.CMhTQ7DAmS-f1fv-xSEdUfWoO3ZI6VK5rUKD4Q2OBH0';

async function testOrdersQuery() {
  const supabase = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });

  console.log('Testing orders query...');

  try {
    // Test the exact query used in the API
    const { data: orders, error, count } = await supabase
      .from('orders')
      .select(`
        *,
        users!user_id (
          email,
          full_name
        )
      `, { count: 'exact' })
      .order('created_at', { ascending: false })
      .range(0, 9); // First 10 orders

    if (error) {
      console.error('Query error:', error);
      return;
    }

    console.log('Query successful!');
    console.log('Total orders:', count);
    console.log('Orders returned:', orders?.length || 0);
    
    if (orders && orders.length > 0) {
      console.log('Sample order:', {
        id: orders[0].id,
        user_id: orders[0].user_id,
        status: orders[0].status,
        payment_status: orders[0].payment_status,
        total_amount: orders[0].total_amount,
        customer_email: orders[0].users?.email,
        customer_name: orders[0].users?.full_name,
        created_at: orders[0].created_at
      });
    }

    // Test the stats function
    console.log('\nTesting stats function...');
    const { data: stats, error: statsError } = await supabase.rpc('get_order_stats');
    
    if (statsError) {
      console.error('Stats error:', statsError);
    } else {
      console.log('Stats successful:', stats);
    }

  } catch (error) {
    console.error('General error:', error);
  }
}

testOrdersQuery();
