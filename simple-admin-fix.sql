-- Simple Admin Fix - No Metadata Dependencies
-- Replace '<EMAIL>' with your actual admin email address

-- Step 1: Check current users in auth.users
SELECT 
    'Current auth users:' as info,
    id,
    email,
    created_at
FROM auth.users 
ORDER BY created_at DESC;

-- Step 2: Check current users in public.users  
SELECT 
    'Current public users:' as info,
    id,
    email,
    role,
    created_at
FROM public.users
ORDER BY created_at DESC;

-- Step 3: Add admin_role column if missing
DO $$
BEGIN
    -- Create admin_role enum if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'admin_role') THEN
        CREATE TYPE admin_role AS ENUM ('admin', 'senior_admin', 'junior_admin');
        RAISE NOTICE 'Created admin_role enum';
    END IF;
    
    -- Add admin_role column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'admin_role'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.users ADD COLUMN admin_role admin_role;
        RAISE NOTICE 'Added admin_role column';
    END IF;
END $$;

-- Step 4: Create or update your admin user
-- IMPORTANT: Replace '<EMAIL>' with your actual email!

-- Option A: If your user doesn't exist in public.users, create them
INSERT INTO public.users (
    id,
    email,
    full_name,
    name,
    role,
    admin_role,
    token_identifier,
    created_at,
    updated_at
)
SELECT 
    au.id,
    au.email,
    'Admin User',
    'Admin User',
    'admin',
    'admin',
    au.id::text,
    au.created_at,
    NOW()
FROM auth.users au
LEFT JOIN public.users pu ON au.id = pu.id
WHERE au.email = '<EMAIL>'  -- CHANGE THIS TO YOUR EMAIL
AND pu.id IS NULL;

-- Option B: If your user exists but isn't admin, update them
UPDATE public.users 
SET 
    role = 'admin',
    admin_role = 'admin',
    updated_at = NOW()
WHERE email = '<EMAIL>'  -- CHANGE THIS TO YOUR EMAIL
AND role != 'admin';

-- Step 5: Verify the fix
SELECT 
    'Admin users after fix:' as info,
    id,
    email,
    full_name,
    role,
    admin_role,
    created_at
FROM public.users
WHERE role = 'admin'
ORDER BY created_at DESC;

-- Step 6: Check RLS status
SELECT 
    'RLS Status:' as info,
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE tablename = 'users' 
AND schemaname = 'public';

-- Step 7: If still having issues, temporarily disable RLS for testing
-- UNCOMMENT THE NEXT LINE ONLY FOR TESTING:
-- ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;

-- Final instructions
SELECT 
    '=== NEXT STEPS ===' as instructions,
    '1. Replace <EMAIL> with your actual email in this script' as step1,
    '2. Run this script again after updating the email' as step2,
    '3. Clear browser cache and cookies' as step3,
    '4. Sign out and sign in again' as step4,
    '5. If still issues, uncomment the RLS disable line above' as step5;
