-- Sample Data for Specific User
-- Replace 'YOUR_USER_ID_HERE' with the actual user ID from auth.users

-- First, let's create a mentor profile for the user
INSERT INTO public.mentors (id, user_id, bio, specialties, experience_years, availability) VALUES
(
  '550e8400-e29b-41d4-a716-446655440003',
  'YOUR_USER_ID_HERE', -- Replace with actual user ID
  'Professional tennis coach with 15 years of experience. Former college player and certified instructor.',
  ARRAY['Serve technique', 'Backhand improvement', 'Mental game', 'Tournament preparation'],
  15,
  '{"monday": ["09:00", "17:00"], "tuesday": ["09:00", "17:00"], "wednesday": ["09:00", "17:00"], "thursday": ["09:00", "17:00"], "friday": ["09:00", "17:00"]}'::jsonb
)
ON CONFLICT (id) DO UPDATE SET
  user_id = EXCLUDED.user_id,
  bio = EXCLUDED.bio,
  specialties = EXCLUDED.specialties,
  experience_years = EXCLUDED.experience_years,
  availability = EXCLUDED.availability;

-- Create a student enrollment for the user
INSERT INTO public.student_enrollments (id, student_id, program_id, mentor_id, start_date, end_date, payment_type, status) VALUES
(
  '550e8400-e29b-41d4-a716-446655440008',
  'YOUR_USER_ID_HERE', -- Replace with actual user ID
  '550e8400-e29b-41d4-a716-446655440001', -- 6-Month Tennis Mastery program
  '550e8400-e29b-41d4-a716-446655440003', -- The mentor we just created
  '2025-01-01 00:00:00+00',
  '2025-07-01 00:00:00+00',
  'monthly',
  'active'
)
ON CONFLICT (id) DO UPDATE SET
  student_id = EXCLUDED.student_id,
  program_id = EXCLUDED.program_id,
  mentor_id = EXCLUDED.mentor_id,
  start_date = EXCLUDED.start_date,
  end_date = EXCLUDED.end_date,
  payment_type = EXCLUDED.payment_type,
  status = EXCLUDED.status;

-- Create sample mentorship sessions
INSERT INTO public.mentorship_sessions (id, enrollment_id, scheduled_at, duration_minutes, status, notes) VALUES
(
  '550e8400-e29b-41d4-a716-446655440009',
  '550e8400-e29b-41d4-a716-446655440008',
  '2025-01-25 10:00:00+00',
  60,
  'scheduled',
  'Focus on serve technique and footwork'
),
(
  '550e8400-e29b-41d4-a716-44665544000a',
  '550e8400-e29b-41d4-a716-446655440008',
  '2025-01-18 10:00:00+00',
  60,
  'completed',
  'Worked on backhand technique - great improvement shown'
),
(
  '550e8400-e29b-41d4-a716-44665544000b',
  '550e8400-e29b-41d4-a716-446655440008',
  '2025-02-01 10:00:00+00',
  60,
  'scheduled',
  'Progress review and goal setting for next month'
),
(
  '550e8400-e29b-41d4-a716-44665544000c',
  '550e8400-e29b-41d4-a716-446655440008',
  '2025-01-11 10:00:00+00',
  60,
  'completed',
  'Introduction session - assessed current skill level'
),
(
  '550e8400-e29b-41d4-a716-44665544000d',
  '550e8400-e29b-41d4-a716-446655440008',
  '2025-02-08 10:00:00+00',
  60,
  'scheduled',
  'Advanced serve techniques and power development'
)
ON CONFLICT (id) DO UPDATE SET
  enrollment_id = EXCLUDED.enrollment_id,
  scheduled_at = EXCLUDED.scheduled_at,
  duration_minutes = EXCLUDED.duration_minutes,
  status = EXCLUDED.status,
  notes = EXCLUDED.notes;

-- Update resources to have the user as creator
UPDATE public.resources 
SET created_by = 'YOUR_USER_ID_HERE' -- Replace with actual user ID
WHERE created_by IS NULL;

-- Verify the data was inserted correctly
SELECT 'Mentorship Programs' as table_name, count(*) as count FROM public.mentorship_programs
UNION ALL
SELECT 'Mentors' as table_name, count(*) as count FROM public.mentors
UNION ALL
SELECT 'Student Enrollments' as table_name, count(*) as count FROM public.student_enrollments
UNION ALL
SELECT 'Mentorship Sessions' as table_name, count(*) as count FROM public.mentorship_sessions
UNION ALL
SELECT 'Resources' as table_name, count(*) as count FROM public.resources;

-- Check specific user data
SELECT 
  'User Enrollments' as data_type,
  count(*) as count
FROM public.student_enrollments 
WHERE student_id = 'YOUR_USER_ID_HERE'
UNION ALL
SELECT 
  'User Sessions' as data_type,
  count(*) as count
FROM public.mentorship_sessions ms
JOIN public.student_enrollments se ON ms.enrollment_id = se.id
WHERE se.student_id = 'YOUR_USER_ID_HERE';
