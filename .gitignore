# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/
/.vscode/
# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env
.env.local.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Tempo

node_modules/
**/tempobook/dynamic/
**/tempobook/storyboards/

# remove git history
.git

.github/

# Windsurf rules
#.windsurfrules

