'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { createClient } from '@/utils/supabase/client';
import { AlertTriangle, CheckCircle, User, Database, Key } from 'lucide-react';

interface AuthDebugInfo {
  session: any;
  authUser: any;
  publicUser: any;
  errors: string[];
  checks: {
    hasSession: boolean;
    hasAuthUser: boolean;
    hasPublicUser: boolean;
    hasAdminRole: boolean;
    hasAdminRoleColumn: boolean;
  };
}

export default function AdminAuthDebugPage() {
  const [debugInfo, setDebugInfo] = useState<AuthDebugInfo | null>(null);
  const [loading, setLoading] = useState(true);

  const supabase = createClient();

  useEffect(() => {
    runDiagnostics();
  }, []);

  const runDiagnostics = async () => {
    setLoading(true);
    const errors: string[] = [];
    const checks = {
      hasSession: false,
      hasAuthUser: false,
      hasPublicUser: false,
      hasAdminRole: false,
      hasAdminRoleColumn: false,
    };

    try {
      // Check 1: Session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      if (sessionError) {
        errors.push(`Session error: ${sessionError.message}`);
      } else if (session) {
        checks.hasSession = true;
        checks.hasAuthUser = !!session.user;
      }

      let publicUser = null;
      if (session?.user) {
        // Check 2: Public user record
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('*')
          .eq('id', session.user.id)
          .single();

        if (userError) {
          errors.push(`Public user error: ${userError.message}`);
        } else if (userData) {
          checks.hasPublicUser = true;
          checks.hasAdminRole = userData.role === 'admin';
          checks.hasAdminRoleColumn = userData.admin_role !== undefined;
          publicUser = userData;
        }
      }

      setDebugInfo({
        session,
        authUser: session?.user || null,
        publicUser,
        errors,
        checks,
      });
    } catch (error) {
      errors.push(`Unexpected error: ${error}`);
      setDebugInfo({
        session: null,
        authUser: null,
        publicUser: null,
        errors,
        checks,
      });
    } finally {
      setLoading(false);
    }
  };

  const createMissingUser = async () => {
    if (!debugInfo?.authUser) return;

    try {
      const { error } = await supabase
        .from('users')
        .insert([
          {
            id: debugInfo.authUser.id,
            email: debugInfo.authUser.email,
            full_name: debugInfo.authUser.user_metadata?.full_name || 'Admin User',
            name: debugInfo.authUser.user_metadata?.full_name || 'Admin User',
            role: 'admin',
            admin_role: 'admin',
            token_identifier: debugInfo.authUser.id,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }
        ]);

      if (error) {
        alert(`Error creating user: ${error.message}`);
      } else {
        alert('User created successfully! Please refresh the page.');
        runDiagnostics();
      }
    } catch (error) {
      alert(`Error: ${error}`);
    }
  };

  const updateUserRole = async () => {
    if (!debugInfo?.authUser) return;

    try {
      const { error } = await supabase
        .from('users')
        .update({
          role: 'admin',
          admin_role: 'admin',
          updated_at: new Date().toISOString(),
        })
        .eq('id', debugInfo.authUser.id);

      if (error) {
        alert(`Error updating user: ${error.message}`);
      } else {
        alert('User role updated successfully! Please refresh the page.');
        runDiagnostics();
      }
    } catch (error) {
      alert(`Error: ${error}`);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Admin Auth Debug</h1>
          <p className="text-muted-foreground">
            Diagnose and fix admin authentication issues
          </p>
        </div>
        <Button onClick={runDiagnostics}>
          Refresh Diagnostics
        </Button>
      </div>

      {/* Status Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Key className="h-4 w-4" />
              Session
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Badge variant={debugInfo?.checks.hasSession ? "default" : "destructive"}>
              {debugInfo?.checks.hasSession ? "Active" : "Missing"}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <User className="h-4 w-4" />
              Auth User
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Badge variant={debugInfo?.checks.hasAuthUser ? "default" : "destructive"}>
              {debugInfo?.checks.hasAuthUser ? "Found" : "Missing"}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Database className="h-4 w-4" />
              Public User
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Badge variant={debugInfo?.checks.hasPublicUser ? "default" : "destructive"}>
              {debugInfo?.checks.hasPublicUser ? "Found" : "Missing"}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Admin Role</CardTitle>
          </CardHeader>
          <CardContent>
            <Badge variant={debugInfo?.checks.hasAdminRole ? "default" : "destructive"}>
              {debugInfo?.checks.hasAdminRole ? "Admin" : "Not Admin"}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Admin Role Column</CardTitle>
          </CardHeader>
          <CardContent>
            <Badge variant={debugInfo?.checks.hasAdminRoleColumn ? "default" : "secondary"}>
              {debugInfo?.checks.hasAdminRoleColumn ? "Present" : "Missing"}
            </Badge>
          </CardContent>
        </Card>
      </div>

      {/* Errors */}
      {debugInfo?.errors && debugInfo.errors.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <AlertTriangle className="h-5 w-5" />
              Errors Found
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {debugInfo.errors.map((error, index) => (
                <li key={index} className="text-sm text-destructive">
                  • {error}
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Session Info */}
      {debugInfo?.session && (
        <Card>
          <CardHeader>
            <CardTitle>Session Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p><strong>User ID:</strong> {debugInfo.session.user?.id}</p>
              <p><strong>Email:</strong> {debugInfo.session.user?.email}</p>
              <p><strong>Created:</strong> {new Date(debugInfo.session.user?.created_at).toLocaleString()}</p>
              <p><strong>Metadata Role:</strong> {debugInfo.session.user?.user_metadata?.role || 'Not set'}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Public User Info */}
      {debugInfo?.publicUser && (
        <Card>
          <CardHeader>
            <CardTitle>Public User Record</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p><strong>ID:</strong> {debugInfo.publicUser.id}</p>
              <p><strong>Email:</strong> {debugInfo.publicUser.email}</p>
              <p><strong>Full Name:</strong> {debugInfo.publicUser.full_name || 'Not set'}</p>
              <p><strong>Role:</strong> {debugInfo.publicUser.role}</p>
              <p><strong>Admin Role:</strong> {debugInfo.publicUser.admin_role || 'Not set'}</p>
              <p><strong>Created:</strong> {new Date(debugInfo.publicUser.created_at).toLocaleString()}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Fix Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Fix Actions</CardTitle>
          <CardDescription>
            Use these buttons to fix common admin authentication issues
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {debugInfo?.checks.hasAuthUser && !debugInfo?.checks.hasPublicUser && (
            <div>
              <p className="text-sm text-muted-foreground mb-2">
                You have an auth user but no public user record. Create one:
              </p>
              <Button onClick={createMissingUser}>
                Create Missing User Record
              </Button>
            </div>
          )}

          {debugInfo?.checks.hasPublicUser && !debugInfo?.checks.hasAdminRole && (
            <div>
              <p className="text-sm text-muted-foreground mb-2">
                You have a user record but no admin role. Update it:
              </p>
              <Button onClick={updateUserRole}>
                Update User to Admin Role
              </Button>
            </div>
          )}

          {debugInfo?.checks.hasAdminRole && (
            <div className="flex items-center gap-2 text-green-600">
              <CheckCircle className="h-4 w-4" />
              <span className="text-sm">Admin access should be working!</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Manual Fix Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-sm">
          <div>
            <h4 className="font-medium mb-2">1. Run the SQL Fix Script</h4>
            <p className="text-muted-foreground">
              Copy and run the content from <code>fix-admin-access-issue.sql</code> in your Supabase SQL Editor.
            </p>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">2. Clear Browser Data</h4>
            <p className="text-muted-foreground">
              Clear your browser cache, cookies, and local storage, then try signing in again.
            </p>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">3. Check Environment Variables</h4>
            <p className="text-muted-foreground">
              Ensure your <code>NEXT_PUBLIC_SUPABASE_URL</code> and <code>NEXT_PUBLIC_SUPABASE_ANON_KEY</code> are set correctly.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
