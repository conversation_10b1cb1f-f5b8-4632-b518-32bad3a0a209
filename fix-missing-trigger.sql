-- Fix Missing Trigger for Admin Authentication
-- This script specifically fixes the missing trigger issue

-- Step 1: Create or replace the user creation function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert new user profile with data from auth.users
  INSERT INTO public.users (id, email, full_name, name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'role', 'user')::user_role
  )
  ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = COALESCE(EXCLUDED.full_name, public.users.full_name),
    name = COALESCE(EXCLUDED.name, public.users.name),
    role = COALESCE(EXCLUDED.role, public.users.role),
    updated_at = NOW();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 2: Drop existing trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Step 3: Create the trigger
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Step 4: Grant necessary permissions
GRANT USAGE ON TYPE user_role TO authenticated;
GRANT USAGE ON TYPE user_role TO anon;
GRANT SELECT, INSERT, UPDATE ON public.users TO authenticated;
GRANT SELECT ON public.users TO anon;

-- Step 5: Verify the trigger was created
SELECT 
  'Trigger Verification' as test_name,
  trigger_name,
  event_manipulation,
  action_timing,
  CASE 
    WHEN trigger_name = 'on_auth_user_created' 
         AND event_manipulation = 'INSERT' 
         AND action_timing = 'AFTER' THEN '✅ TRIGGER CREATED SUCCESSFULLY'
    ELSE '❌ TRIGGER CREATION FAILED'
  END as status
FROM information_schema.triggers 
WHERE trigger_schema = 'public' 
AND trigger_name = 'on_auth_user_created';

-- Step 6: Verify the function exists
SELECT 
  'Function Verification' as test_name,
  routine_name,
  routine_type,
  CASE 
    WHEN routine_name = 'handle_new_user' AND routine_type = 'FUNCTION' THEN '✅ FUNCTION EXISTS'
    ELSE '❌ FUNCTION MISSING'
  END as status
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name = 'handle_new_user';

-- Step 7: Final status message
DO $$
BEGIN
    RAISE NOTICE '=== TRIGGER FIX COMPLETED ===';
    RAISE NOTICE 'The handle_new_user function has been created';
    RAISE NOTICE 'The on_auth_user_created trigger has been set up';
    RAISE NOTICE 'Admin sign-up should now work correctly';
    RAISE NOTICE 'Test at: /admin/sign-up with code TENNIS_ADMIN_2024';
END $$;
