import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, Smartphone, Key, CheckCircle, AlertTriangle } from 'lucide-react';
import { useAuth } from '../../../supabase/auth';
import { supabase } from '../../../supabase/client';

interface MFASetupProps {
  onComplete?: () => void;
  onSkip?: () => void;
}

export function MFASetup({ onComplete, onSkip }: MFASetupProps) {
  const { user } = useAuth();
  const [step, setStep] = useState<'setup' | 'verify' | 'complete'>('setup');
  const [qrCode, setQrCode] = useState<string>('');
  const [secret, setSecret] = useState<string>('');
  const [verificationCode, setVerificationCode] = useState<string>('');
  const [backupCodes, setBackupCodes] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    if (user) {
      generateMFASecret();
    }
  }, [user]);

  const generateMFASecret = async () => {
    try {
      setIsLoading(true);
      setError('');

      // Generate TOTP secret
      const { data, error } = await supabase.auth.mfa.enroll({
        factorType: 'totp',
        friendlyName: 'The Chronicle App'
      });

      if (error) throw error;

      if (data) {
        setSecret(data.totp.secret);
        setQrCode(data.totp.qr_code);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to generate MFA secret');
    } finally {
      setIsLoading(false);
    }
  };

  const verifyMFACode = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      setError('Please enter a valid 6-digit code');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // Verify the TOTP code
      const { data, error } = await supabase.auth.mfa.verify({
        factorId: secret,
        challengeId: '', // This would come from the enrollment
        code: verificationCode
      });

      if (error) throw error;

      // Generate backup codes
      const codes = generateBackupCodes();
      setBackupCodes(codes);
      setStep('complete');

      // Store MFA status in user metadata
      await supabase.auth.updateUser({
        data: { mfa_enabled: true }
      });

    } catch (err: any) {
      setError(err.message || 'Invalid verification code');
    } finally {
      setIsLoading(false);
    }
  };

  const generateBackupCodes = (): string[] => {
    const codes: string[] = [];
    for (let i = 0; i < 10; i++) {
      const code = Math.random().toString(36).substring(2, 10).toUpperCase();
      codes.push(code);
    }
    return codes;
  };

  const downloadBackupCodes = () => {
    const content = `The Chronicle - MFA Backup Codes\n\nGenerated: ${new Date().toISOString()}\nUser: ${user?.email}\n\nBackup Codes (use each code only once):\n${backupCodes.map((code, i) => `${i + 1}. ${code}`).join('\n')}\n\nKeep these codes in a safe place. You can use them to access your account if you lose your authenticator device.`;
    
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'chronicle-backup-codes.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (step === 'setup') {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
            <Shield className="w-6 h-6 text-primary" />
          </div>
          <CardTitle>Set Up Two-Factor Authentication</CardTitle>
          <CardDescription>
            Add an extra layer of security to your account
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="text-center">
            <div className="bg-white p-4 rounded-lg border inline-block">
              {qrCode ? (
                <div className="w-[200px] h-[200px] bg-gray-100 rounded flex items-center justify-center">
                  <div className="text-center">
                    <p className="text-sm font-medium mb-2">QR Code Available</p>
                    <p className="text-xs text-gray-600">Use manual entry key below</p>
                  </div>
                </div>
              ) : (
                <div className="w-[200px] h-[200px] bg-gray-100 rounded flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              )}
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <Smartphone className="w-5 h-5 text-primary mt-0.5" />
              <div>
                <p className="font-medium">1. Install an authenticator app</p>
                <p className="text-sm text-gray-600">
                  Download Google Authenticator, Authy, or similar app
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Key className="w-5 h-5 text-primary mt-0.5" />
              <div>
                <p className="font-medium">2. Scan the QR code</p>
                <p className="text-sm text-gray-600">
                  Use your authenticator app to scan the QR code above
                </p>
              </div>
            </div>
          </div>

          {secret && (
            <div className="bg-gray-50 p-3 rounded">
              <p className="text-sm font-medium mb-1">Manual entry key:</p>
              <code className="text-xs bg-white px-2 py-1 rounded border break-all">
                {secret}
              </code>
            </div>
          )}

          <div className="flex space-x-3">
            <Button
              onClick={() => setStep('verify')}
              disabled={!qrCode || isLoading}
              className="flex-1"
            >
              Continue
            </Button>
            {onSkip && (
              <Button variant="outline" onClick={onSkip}>
                Skip
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (step === 'verify') {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <CardTitle>Verify Your Setup</CardTitle>
          <CardDescription>
            Enter the 6-digit code from your authenticator app
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div>
            <Input
              type="text"
              placeholder="000000"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
              className="text-center text-2xl tracking-widest"
              maxLength={6}
            />
          </div>

          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={() => setStep('setup')}
              className="flex-1"
            >
              Back
            </Button>
            <Button
              onClick={verifyMFACode}
              disabled={verificationCode.length !== 6 || isLoading}
              className="flex-1"
            >
              {isLoading ? 'Verifying...' : 'Verify'}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
          <CheckCircle className="w-6 h-6 text-green-600" />
        </div>
        <CardTitle>MFA Setup Complete!</CardTitle>
        <CardDescription>
          Your account is now protected with two-factor authentication
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Alert>
          <Key className="h-4 w-4" />
          <AlertDescription>
            Save your backup codes in a secure location. You can use them to access your account if you lose your authenticator device.
          </AlertDescription>
        </Alert>

        <div className="bg-gray-50 p-4 rounded">
          <h4 className="font-medium mb-2">Backup Codes:</h4>
          <div className="grid grid-cols-2 gap-2 text-sm font-mono">
            {backupCodes.map((code, index) => (
              <div key={index} className="bg-white px-2 py-1 rounded border">
                {code}
              </div>
            ))}
          </div>
        </div>

        <div className="flex space-x-3">
          <Button onClick={downloadBackupCodes} variant="outline" className="flex-1">
            Download Codes
          </Button>
          <Button onClick={onComplete} className="flex-1">
            Complete Setup
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

export default MFASetup;
