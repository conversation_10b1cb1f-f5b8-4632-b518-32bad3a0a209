import supabase from './supabase-client';
import { Tables, InsertTables, UpdateTables } from './database.types';
import { v4 as uuidv4 } from 'uuid';

/**
 * Product management utilities for Tennis-Gear application
 * These functions handle product CRUD operations with Supabase
 */

/**
 * Fetch all products
 * @returns List of products or error
 */
export async function getProducts() {
  const { data, error } = await supabase
    .from('products')
    .select('*')
    .order('created_at', { ascending: false });
  
  return { data, error };
}

/**
 * Fetch products by category
 * @param category Product category
 * @returns List of products in the category or error
 */
export async function getProductsByCategory(category: string) {
  const { data, error } = await supabase
    .from('products')
    .select('*')
    .eq('category', category)
    .gt('stock', 0)
    .order('created_at', { ascending: false });
  
  return { data, error };
}

/**
 * Fetch products with advanced filtering
 * @param filters Object containing filter criteria
 * @returns Filtered list of products or error
 */
export async function getProductsWithFilters(filters: {
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  search?: string;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
}) {
  let query = supabase
    .from('products')
    .select('*');
  
  // Apply filters
  if (filters.category) {
    query = query.eq('category', filters.category);
  }
  
  if (filters.minPrice !== undefined) {
    query = query.gte('price', filters.minPrice);
  }
  
  if (filters.maxPrice !== undefined) {
    query = query.lte('price', filters.maxPrice);
  }
  
  if (filters.inStock) {
    query = query.gt('stock', 0);
  }
  
  if (filters.search) {
    query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
  }
  
  // Apply sorting
  const sortField = filters.sortBy || 'created_at';
  const sortDirection = filters.sortDirection || 'desc';
  query = query.order(sortField, { ascending: sortDirection === 'asc' });
  
  const { data, error } = await query;
  
  return { data, error };
}

/**
 * Get a single product by ID
 * @param id Product ID
 * @returns Product data or error
 */
export async function getProduct(id: string) {
  const { data, error } = await supabase
    .from('products')
    .select('*')
    .eq('id', id)
    .single();
  
  return { data, error };
}

/**
 * Create a new product
 * @param product Product data
 * @returns Created product or error
 */
export async function createProduct(product: {
  name: string;
  price: number;
  description?: string;
  image?: string;
  category: string;
  stock: number;
  status?: string;
}) {
  const productData: InsertTables<'products'> = { 
    ...product, 
    id: uuidv4(),
    status: product.status || 'In Stock',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  const { data, error } = await supabase
    .from('products')
    .insert(productData)
    .select()
    .single();
  
  return { data, error };
}

/**
 * Update an existing product
 * @param id Product ID
 * @param updates Product data to update
 * @returns Updated product or error
 */
export async function updateProduct(id: string, updates: {
  name?: string;
  price?: number;
  description?: string;
  image?: string;
  category?: string;
  stock?: number;
  status?: string;
}) {
  // Add updated_at timestamp
  const updatedProduct: UpdateTables<'products'> = {
    ...updates,
    updated_at: new Date().toISOString()
  };
  
  // Update stock status based on quantity if stock is being updated
  if (updates.stock !== undefined) {
    if (updates.stock === 0) {
      updatedProduct.status = 'Out of Stock';
    } else if (updates.stock < 10) {
      updatedProduct.status = 'Low Stock';
    } else {
      updatedProduct.status = 'In Stock';
    }
  }
  
  const { data, error } = await supabase
    .from('products')
    .update(updatedProduct)
    .eq('id', id)
    .select()
    .single();
  
  return { data, error };
}

/**
 * Update product stock
 * @param productId Product ID
 * @param newQuantity New stock quantity
 * @returns Updated product or error
 */
export async function updateProductStock(productId: string, newQuantity: number) {
  return updateProduct(productId, { stock: newQuantity });
}

/**
 * Delete a product
 * @param id Product ID
 * @returns Success or error
 */
export async function deleteProduct(id: string) {
  const { error } = await supabase
    .from('products')
    .delete()
    .eq('id', id);
  
  return { error };
}

/**
 * Get all product categories
 * @returns List of categories or error
 */
export async function getCategories() {
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .order('name', { ascending: true });
  
  return { data, error };
}

/**
 * Create a new category
 * @param category Category data
 * @returns Created category or error
 */
export async function createCategory(category: {
  name: string;
  image?: string;
}) {
  const categoryData: InsertTables<'categories'> = { 
    ...category, 
    id: uuidv4(),
    count: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  const { data, error } = await supabase
    .from('categories')
    .insert(categoryData)
    .select()
    .single();
  
  return { data, error };
}
