'use client';

import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { RefreshCw, CheckCircle, XCircle, AlertCircle, Database, User, ShoppingCart } from "lucide-react";

interface DebugInfo {
  timestamp: string;
  environment: {
    hasSupabaseUrl: boolean;
    hasAnonKey: boolean;
    hasServiceKey: boolean;
  };
  authentication: {
    hasUser: boolean;
    userId?: string;
    userEmail?: string;
    authError?: string;
    userData?: {
      found: boolean;
      role?: string;
      email?: string;
      fullName?: string;
      error?: string;
    };
  };
  database: {
    connected: boolean;
    ordersTableExists: boolean;
    totalOrders: number;
    error?: string;
  };
  orders: {
    fetchSuccessful: boolean;
    sampleCount: number;
    sampleOrders: Array<{
      id: string;
      status: string;
      paymentStatus: string;
      totalAmount: number;
      customerEmail?: string;
    }>;
    error?: string;
  };
  healthCheck: {
    authenticationWorking: boolean;
    databaseConnected: boolean;
    ordersAccessible: boolean;
    overallStatus: string;
  };
}

export default function AdminDebugPage() {
  const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const runDebugCheck = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/admin/debug');
      const data = await response.json();
      
      if (data.success) {
        setDebugInfo(data.debug);
      } else {
        setError(data.error || 'Debug check failed');
        setDebugInfo(data.debug || null);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to run debug check');
    } finally {
      setIsLoading(false);
    }
  };

  const createTestOrder = async () => {
    try {
      const response = await fetch('/api/admin/debug', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'create_test_order' }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        alert(`Test order created successfully! Order ID: ${data.orderId}`);
        // Refresh debug info
        runDebugCheck();
      } else {
        alert(`Failed to create test order: ${data.error}`);
      }
    } catch (err: any) {
      alert(`Error: ${err.message}`);
    }
  };

  const getStatusIcon = (status: boolean) => {
    return status ? (
      <CheckCircle className="h-5 w-5 text-green-500" />
    ) : (
      <XCircle className="h-5 w-5 text-red-500" />
    );
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, string> = {
      healthy: 'bg-green-100 text-green-800',
      authentication_required: 'bg-yellow-100 text-yellow-800',
      insufficient_permissions: 'bg-orange-100 text-orange-800',
      database_error: 'bg-red-100 text-red-800',
      partial_failure: 'bg-red-100 text-red-800',
      unknown: 'bg-gray-100 text-gray-800',
    };

    return (
      <Badge className={variants[status] || variants.unknown}>
        {status.replace(/_/g, ' ').toUpperCase()}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Admin Debug Console</h1>
          <p className="text-muted-foreground">
            Diagnose admin authentication and order management issues
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button onClick={createTestOrder} variant="outline">
            Create Test Order
          </Button>
          <Button onClick={runDebugCheck} disabled={isLoading}>
            {isLoading ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Run Debug Check
          </Button>
        </div>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-500" />
              <span className="text-red-700">{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {debugInfo && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Overall Health */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                System Health
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span>Overall Status</span>
                {getStatusBadge(debugInfo.healthCheck.overallStatus)}
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span>Authentication</span>
                  {getStatusIcon(debugInfo.healthCheck.authenticationWorking)}
                </div>
                <div className="flex items-center justify-between">
                  <span>Database Connection</span>
                  {getStatusIcon(debugInfo.healthCheck.databaseConnected)}
                </div>
                <div className="flex items-center justify-between">
                  <span>Orders Access</span>
                  {getStatusIcon(debugInfo.healthCheck.ordersAccessible)}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Authentication Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Authentication
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span>User Authenticated</span>
                {getStatusIcon(debugInfo.authentication.hasUser)}
              </div>
              
              {debugInfo.authentication.userId && (
                <div>
                  <span className="text-sm font-medium">User ID:</span>
                  <p className="text-xs font-mono bg-muted p-2 rounded mt-1">
                    {debugInfo.authentication.userId}
                  </p>
                </div>
              )}
              
              {debugInfo.authentication.userData && (
                <>
                  <div className="flex items-center justify-between">
                    <span>Role</span>
                    <Badge variant={debugInfo.authentication.userData.role === 'admin' ? 'default' : 'destructive'}>
                      {debugInfo.authentication.userData.role || 'unknown'}
                    </Badge>
                  </div>
                  
                  {debugInfo.authentication.userData.email && (
                    <div>
                      <span className="text-sm font-medium">Email:</span>
                      <p className="text-sm text-muted-foreground">
                        {debugInfo.authentication.userData.email}
                      </p>
                    </div>
                  )}
                </>
              )}
              
              {debugInfo.authentication.authError && (
                <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                  {debugInfo.authentication.authError}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Database Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Database
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span>Connection</span>
                {getStatusIcon(debugInfo.database.connected)}
              </div>
              
              <div className="flex items-center justify-between">
                <span>Orders Table</span>
                {getStatusIcon(debugInfo.database.ordersTableExists)}
              </div>
              
              <div className="flex items-center justify-between">
                <span>Total Orders</span>
                <Badge variant="outline">
                  {debugInfo.database.totalOrders}
                </Badge>
              </div>
              
              {debugInfo.database.error && (
                <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                  {debugInfo.database.error}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Orders Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShoppingCart className="h-5 w-5" />
                Orders
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span>Fetch Successful</span>
                {getStatusIcon(debugInfo.orders.fetchSuccessful)}
              </div>
              
              <div className="flex items-center justify-between">
                <span>Sample Orders</span>
                <Badge variant="outline">
                  {debugInfo.orders.sampleCount}
                </Badge>
              </div>
              
              {debugInfo.orders.sampleOrders.length > 0 && (
                <div>
                  <span className="text-sm font-medium">Recent Orders:</span>
                  <div className="mt-2 space-y-1">
                    {debugInfo.orders.sampleOrders.map((order) => (
                      <div key={order.id} className="text-xs bg-muted p-2 rounded">
                        <div className="flex justify-between">
                          <span className="font-mono">{order.id.slice(0, 8)}...</span>
                          <span>R {order.totalAmount}</span>
                        </div>
                        <div className="flex justify-between text-muted-foreground">
                          <span>{order.status}</span>
                          <span>{order.paymentStatus}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {debugInfo.orders.error && (
                <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                  {debugInfo.orders.error}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {!debugInfo && !isLoading && (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Debug Information</h3>
              <p className="text-muted-foreground mb-4">
                Click "Run Debug Check" to diagnose system status
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
