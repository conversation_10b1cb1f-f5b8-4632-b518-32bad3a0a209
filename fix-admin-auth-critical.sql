-- CRITICAL FIX: Admin Authentication Database Issues
-- This file fixes infinite recursion, duplicate key violations, and cipher errors
-- Run this in Supabase SQL Editor to fix all admin authentication issues

-- Step 1: Clean up all existing conflicting policies and triggers
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Ad<PERSON> can view all users" ON public.users;
DROP POLICY IF EXISTS "Ad<PERSON> can manage all users" ON public.users;
DROP POLICY IF EXISTS "Users can view own data" ON public.users;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.users;
DROP POLICY IF EXISTS "Allow trigger to insert user profiles" ON public.users;
DROP POLICY IF EXISTS "Authenticated users can view resources" ON public.resources;
DROP POLICY IF EXISTS "Mentors and admins can create resources" ON public.resources;

-- Drop existing triggers and functions
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- Step 2: Ensure user_role enum exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('admin', 'user', 'student');
        RAISE NOTICE 'Created user_role enum type';
    END IF;
END
$$;

-- Step 3: Ensure users table has correct structure
DO $$
BEGIN
    -- Check if users table exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'users'
    ) THEN
        -- Create users table with complete structure
        CREATE TABLE public.users (
            id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
            email TEXT UNIQUE NOT NULL,
            full_name TEXT,
            name TEXT,
            role user_role NOT NULL DEFAULT 'user',
            admin_role TEXT, -- For admin hierarchy (admin, junior_admin, senior_admin)
            token_identifier TEXT,
            avatar_url TEXT,
            user_id TEXT,
            image TEXT,
            shipping_details JSONB,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        RAISE NOTICE 'Created users table with complete structure';
    ELSE
        -- Add missing columns if they don't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = 'users'
            AND column_name = 'admin_role'
        ) THEN
            ALTER TABLE public.users ADD COLUMN admin_role TEXT;
            RAISE NOTICE 'Added admin_role column';
        END IF;

        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = 'users'
            AND column_name = 'avatar_url'
        ) THEN
            ALTER TABLE public.users ADD COLUMN avatar_url TEXT;
            RAISE NOTICE 'Added avatar_url column';
        END IF;

        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = 'users'
            AND column_name = 'shipping_details'
        ) THEN
            ALTER TABLE public.users ADD COLUMN shipping_details JSONB;
            RAISE NOTICE 'Added shipping_details column';
        END IF;

        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = 'users'
            AND column_name = 'name'
        ) THEN
            ALTER TABLE public.users ADD COLUMN name TEXT;
            RAISE NOTICE 'Added name column';
        END IF;

        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = 'users'
            AND column_name = 'user_id'
        ) THEN
            ALTER TABLE public.users ADD COLUMN user_id TEXT;
            RAISE NOTICE 'Added user_id column';
        END IF;

        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = 'users'
            AND column_name = 'image'
        ) THEN
            ALTER TABLE public.users ADD COLUMN image TEXT;
            RAISE NOTICE 'Added image column';
        END IF;
    END IF;

    -- Make token_identifier nullable to prevent constraint issues
    ALTER TABLE public.users ALTER COLUMN token_identifier DROP NOT NULL;
    RAISE NOTICE 'Made token_identifier nullable';
END
$$;

-- Step 4: Enable RLS and create clean, non-conflicting policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Policy 1: Users can view their own profile (no recursion)
CREATE POLICY "users_view_own_profile"
  ON public.users
  FOR SELECT
  USING (auth.uid() = id);

-- Policy 2: Users can update their own profile (no recursion)
CREATE POLICY "users_update_own_profile"
  ON public.users
  FOR UPDATE
  USING (auth.uid() = id);

-- Policy 3: Users can insert their own profile (for manual creation)
CREATE POLICY "users_insert_own_profile"
  ON public.users
  FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Policy 4: Service role can do everything (for triggers and admin operations)
CREATE POLICY "service_role_full_access"
  ON public.users
  FOR ALL
  USING (auth.role() = 'service_role');

-- Policy 5: Use JWT metadata for admin access (NO RECURSION)
CREATE POLICY "admins_can_access_all"
  ON public.users
  FOR ALL
  USING (
    auth.role() = 'service_role' OR
    (auth.jwt() ->> 'role' = 'admin')
  );

-- Step 5: Create robust trigger function with error handling
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Use INSERT with ON CONFLICT to handle duplicates gracefully
  INSERT INTO public.users (
    id,
    email,
    full_name,
    name,
    role,
    admin_role,
    token_identifier,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE((NEW.raw_user_meta_data->>'role')::user_role, 'user'),
    NEW.raw_user_meta_data->>'admin_role',
    NEW.id::text,
    NOW(),
    NOW()
  )
  ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = COALESCE(EXCLUDED.full_name, public.users.full_name),
    name = COALESCE(EXCLUDED.name, public.users.name),
    role = COALESCE(EXCLUDED.role, public.users.role),
    admin_role = COALESCE(EXCLUDED.admin_role, public.users.admin_role),
    updated_at = NOW();

  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log error but don't fail the auth.users insertion
    RAISE WARNING 'Failed to create user profile for %: % (SQLSTATE: %)', NEW.id, SQLERRM, SQLSTATE;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 6: Create trigger for automatic user profile creation
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Step 7: Grant necessary permissions
GRANT USAGE ON TYPE user_role TO authenticated;
GRANT USAGE ON TYPE user_role TO anon;
GRANT SELECT, INSERT, UPDATE ON public.users TO authenticated;
GRANT SELECT ON public.users TO anon;

-- Step 8: Clean up any orphaned or duplicate entries
DELETE FROM public.users
WHERE id NOT IN (SELECT id FROM auth.users);

-- Step 9: Final success message
DO $$
BEGIN
    RAISE NOTICE 'Admin authentication database fix completed successfully!';
END
$$;
