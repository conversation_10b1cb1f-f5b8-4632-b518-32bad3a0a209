-- Database Schema Diagnostic Script
-- Run this to identify the exact issue with your users table

-- 1. Check if users table exists
SELECT 
  'Table Existence Check' as test_category,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_name = 'users'
    ) THEN '✅ users table EXISTS'
    ELSE '❌ users table MISSING'
  END as result;

-- 2. Show current users table structure
SELECT 
  'Current Table Structure' as test_category,
  column_name,
  data_type,
  is_nullable,
  column_default,
  CASE 
    WHEN column_name = 'id' AND data_type = 'uuid' THEN '✅ CORRECT'
    WHEN column_name = 'email' AND data_type = 'text' THEN '✅ CORRECT'
    WHEN column_name = 'role' AND data_type = 'USER-DEFINED' THEN '✅ CORRECT (enum)'
    WHEN column_name = 'role' AND data_type = 'text' THEN '⚠️ TEXT (should be enum)'
    WHEN column_name = 'token_identifier' AND is_nullable = 'YES' THEN '✅ CORRECT (nullable)'
    WHEN column_name = 'token_identifier' AND is_nullable = 'NO' THEN '❌ NOT NULL (should be nullable)'
    WHEN column_name IN ('full_name', 'name') THEN '✅ CORRECT'
    ELSE '✅ OK'
  END as status
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'users'
ORDER BY ordinal_position;

-- 3. Check if user_role enum exists
SELECT 
  'Enum Type Check' as test_category,
  CASE 
    WHEN EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') 
    THEN '✅ user_role enum EXISTS'
    ELSE '❌ user_role enum MISSING'
  END as result;

-- 4. Show enum values if enum exists
SELECT 
  'Enum Values' as test_category,
  enumlabel as enum_value,
  CASE 
    WHEN enumlabel IN ('user', 'admin', 'student', 'mentor') THEN '✅ VALID'
    ELSE '⚠️ UNEXPECTED'
  END as status
FROM pg_enum 
WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')
ORDER BY enumsortorder;

-- 5. Check RLS status
SELECT 
  'RLS Status' as test_category,
  schemaname,
  tablename,
  CASE 
    WHEN rowsecurity = true THEN '✅ RLS ENABLED'
    ELSE '❌ RLS DISABLED'
  END as status
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename = 'users';

-- 6. Check trigger function
SELECT 
  'Trigger Function Check' as test_category,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM information_schema.routines 
      WHERE routine_schema = 'public' 
      AND routine_name = 'handle_new_user'
    ) THEN '✅ handle_new_user function EXISTS'
    ELSE '❌ handle_new_user function MISSING'
  END as result;

-- 7. Check trigger
SELECT 
  'Trigger Check' as test_category,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM information_schema.triggers 
      WHERE trigger_schema = 'public' 
      AND trigger_name = 'on_auth_user_created'
    ) THEN '✅ on_auth_user_created trigger EXISTS'
    ELSE '❌ on_auth_user_created trigger MISSING'
  END as result;

-- 8. Test insert permissions (this will show what the actual error might be)
DO $$
DECLARE
    test_result TEXT;
BEGIN
    -- Try to check if we can insert (without actually inserting)
    IF has_table_privilege('public.users', 'INSERT') THEN
        test_result := '✅ INSERT permission granted';
    ELSE
        test_result := '❌ INSERT permission denied';
    END IF;
    
    RAISE NOTICE 'Permission Test: %', test_result;
    
    -- Check specific column constraints that might cause issues
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'users' 
        AND column_name = 'token_identifier'
        AND is_nullable = 'NO'
    ) THEN
        RAISE NOTICE '❌ ISSUE FOUND: token_identifier is NOT NULL - this will cause insert failures';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'users' 
        AND column_name = 'role'
    ) THEN
        RAISE NOTICE '❌ ISSUE FOUND: role column is missing';
    END IF;
    
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'users' 
        AND column_name = 'role'
        AND data_type = 'text'
    ) THEN
        RAISE NOTICE '⚠️ POTENTIAL ISSUE: role column is TEXT instead of enum';
    END IF;

EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error during permission test: %', SQLERRM;
END $$;

-- 9. Summary and recommendations
DO $$
BEGIN
    RAISE NOTICE '=== DIAGNOSTIC SUMMARY ===';
    RAISE NOTICE 'Review the results above to identify issues';
    RAISE NOTICE '';
    RAISE NOTICE 'Common issues and solutions:';
    RAISE NOTICE '1. If users table is missing: Run database-setup-complete.sql';
    RAISE NOTICE '2. If token_identifier is NOT NULL: Run fix-admin-database-schema.sql';
    RAISE NOTICE '3. If user_role enum is missing: Run fix-admin-database-schema.sql';
    RAISE NOTICE '4. If role column is TEXT: Run fix-admin-database-schema.sql';
    RAISE NOTICE '5. If trigger function is missing: Run fix-admin-database-schema.sql';
    RAISE NOTICE '';
    RAISE NOTICE 'After fixing, test admin sign-up at: /admin/sign-up';
    RAISE NOTICE 'Admin access code: TENNIS_ADMIN_2024';
END $$;
