import { createClient } from './server';

// Define the user profile type
export type UserProfile = {
  id: string;
  user_id: string;
  full_name: string;
  email: string;
  phone?: string;
  address?: string;
  city?: string;
  postal_code?: string;
  country?: string;
  avatar_url?: string;
  created_at?: string;
  updated_at?: string;
};

// Get a user profile by user ID
export async function getUserProfile(userId: string) {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .from('user_profiles')
    .select('*')
    .eq('user_id', userId)
    .single();
  
  if (error) {
    // If the profile doesn't exist, return null instead of throwing an error
    if (error.code === 'PGRST116') {
      return null;
    }
    console.error(`Error fetching user profile for ${userId}:`, error);
    throw error;
  }
  
  return data as UserProfile;
}

// Create a new user profile
export async function createUserProfile(profile: Omit<UserProfile, 'id' | 'created_at' | 'updated_at'>) {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .from('user_profiles')
    .insert([profile])
    .select()
    .single();
  
  if (error) {
    console.error('Error creating user profile:', error);
    throw error;
  }
  
  return data as UserProfile;
}

// Update an existing user profile
export async function updateUserProfile(userId: string, updates: Partial<UserProfile>) {
  const supabase = await createClient();
  
  // Remove id and user_id from updates if present
  const { id, user_id, created_at, updated_at, ...validUpdates } = updates as any;
  
  const { data, error } = await supabase
    .from('user_profiles')
    .update(validUpdates)
    .eq('user_id', userId)
    .select()
    .single();
  
  if (error) {
    console.error(`Error updating user profile for ${userId}:`, error);
    throw error;
  }
  
  return data as UserProfile;
}

// Upload a profile avatar
export async function uploadProfileAvatar(userId: string, file: File) {
  const supabase = await createClient();
  
  // Generate a unique file name
  const fileExt = file.name.split('.').pop();
  const fileName = `${userId}-${Date.now()}.${fileExt}`;
  
  // Upload the file to Supabase Storage
  const { data: uploadData, error: uploadError } = await supabase
    .storage
    .from('user-profiles')
    .upload(fileName, file);
  
  if (uploadError) {
    console.error('Error uploading avatar:', uploadError);
    throw uploadError;
  }
  
  // Get the public URL
  const { data: urlData } = supabase
    .storage
    .from('user-profiles')
    .getPublicUrl(fileName);
  
  // Update the user's profile with the new avatar URL
  const avatarUrl = urlData.publicUrl;
  
  const { data: profileData, error: profileError } = await supabase
    .from('user_profiles')
    .update({ avatar_url: avatarUrl })
    .eq('user_id', userId)
    .select()
    .single();
  
  if (profileError) {
    console.error('Error updating profile with avatar URL:', profileError);
    throw profileError;
  }
  
  return profileData as UserProfile;
}
