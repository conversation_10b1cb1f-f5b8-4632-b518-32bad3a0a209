/**
 * Helper functions for product management
 * These functions ensure proper data formatting for Supabase
 */

import { createClient } from "./client";
import { v4 as uuidv4 } from "uuid";
import { Product } from "./products";

/**
 * Creates a new product with proper schema validation
 * This function ensures only valid fields are sent to Supabase
 */
export async function createProductWithValidation(productData: any): Promise<Product> {
  const supabase = createClient();
  
  // Extract only the fields that exist in the database schema
  const validatedProduct = {
    id: uuidv4(),
    name: productData.name,
    price: typeof productData.price === 'string' ? parseFloat(productData.price) : productData.price,
    description: productData.description || null,
    image: productData.image || null,
    category: productData.category,
    stock: typeof productData.stock === 'string' ? parseInt(productData.stock) : productData.stock,
    status: productData.status || 'In Stock'
  };
  
  // Insert the product with only valid fields
  const { data, error } = await supabase
    .from("products")
    .insert([validatedProduct])
    .select()
    .single();
  
  if (error) {
    console.error("Error creating product:", error);
    throw error;
  }
  
  return data as Product;
}

/**
 * Updates a product with proper schema validation
 */
export async function updateProductWithValidation(id: string, updates: any): Promise<Product> {
  const supabase = createClient();
  
  // Extract only the fields that exist in the database schema
  const validatedUpdates: any = {};
  
  if (updates.name !== undefined) validatedUpdates.name = updates.name;
  if (updates.price !== undefined) validatedUpdates.price = typeof updates.price === 'string' ? parseFloat(updates.price) : updates.price;
  if (updates.description !== undefined) validatedUpdates.description = updates.description;
  if (updates.image !== undefined) validatedUpdates.image = updates.image;
  if (updates.category !== undefined) validatedUpdates.category = updates.category;
  if (updates.stock !== undefined) validatedUpdates.stock = typeof updates.stock === 'string' ? parseInt(updates.stock) : updates.stock;
  if (updates.status !== undefined) validatedUpdates.status = updates.status;
  
  // Update the product with only valid fields
  const { data, error } = await supabase
    .from("products")
    .update(validatedUpdates)
    .eq("id", id)
    .select()
    .single();
  
  if (error) {
    console.error(`Error updating product ${id}:`, error);
    throw error;
  }
  
  return data as Product;
}
