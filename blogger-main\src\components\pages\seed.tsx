import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/components/ui/use-toast";
import { seedDatabase } from "@/lib/seed-database";
import { Loader2, Database, CheckCircle, AlertCircle } from "lucide-react";

export default function SeedPage() {
  const [isSeeding, setIsSeeding] = useState(false);
  const [seedResult, setSeedResult] = useState<{ success: boolean; message?: string; error?: string } | null>(null);
  const { toast } = useToast();

  const handleSeedDatabase = async () => {
    setIsSeeding(true);
    setSeedResult(null);

    try {
      const result = await seedDatabase();
      setSeedResult(result);
      
      if (result.success) {
        toast({
          title: "Success!",
          description: result.message,
          duration: 5000,
        });
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to seed database",
          variant: "destructive",
          duration: 5000,
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      setSeedResult({ success: false, error: errorMessage });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setIsSeeding(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Database Seeding</h1>
          <p className="text-lg text-gray-600">
            Initialize your database with sample data for The Chronicle platform
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Seed Database
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <p className="text-gray-600">
                This will populate your database with:
              </p>
              <ul className="list-disc list-inside space-y-2 text-sm text-gray-600">
                <li>5 article categories (Technology, Business, Health, Finance, Lifestyle)</li>
                <li>3 product categories (Digital Products, Subscriptions, Physical Products)</li>
                <li>3 featured products with proper pricing and descriptions</li>
                <li>4 sample articles with realistic content and engagement metrics</li>
                <li>Product and article tags for better organization</li>
              </ul>
            </div>

            {seedResult && (
              <Alert className={seedResult.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
                <div className="flex items-center gap-2">
                  {seedResult.success ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-600" />
                  )}
                  <AlertDescription className={seedResult.success ? "text-green-800" : "text-red-800"}>
                    {seedResult.success ? seedResult.message : seedResult.error}
                  </AlertDescription>
                </div>
              </Alert>
            )}

            <div className="flex justify-center">
              <Button
                onClick={handleSeedDatabase}
                disabled={isSeeding}
                size="lg"
                className="w-full sm:w-auto"
              >
                {isSeeding ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Seeding Database...
                  </>
                ) : (
                  <>
                    <Database className="mr-2 h-4 w-4" />
                    Seed Database
                  </>
                )}
              </Button>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-medium text-blue-900 mb-2">Important Notes:</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Make sure you're logged in before seeding</li>
                <li>• This operation is safe to run multiple times</li>
                <li>• Articles will be attributed to your user account</li>
                <li>• You can view the results on the homepage after seeding</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        <div className="mt-8 text-center">
          <Button variant="outline" asChild>
            <a href="/">
              ← Back to Homepage
            </a>
          </Button>
        </div>
      </div>
    </div>
  );
}
