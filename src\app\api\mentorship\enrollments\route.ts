import { NextResponse } from 'next/server';
import { createClient } from '../../../../utils/supabase/server';

export async function GET(request: Request) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const studentId = searchParams.get('student_id');
    const mentorId = searchParams.get('mentor_id');

    let query = supabase
      .from('student_enrollments')
      .select(`
        *,
        program:program_id (*),
        mentor:mentor_id (*),
        student:student_id (*)
      `);

    // Filter based on user role and parameters
    if (studentId) {
      query = query.eq('student_id', studentId);
    } else if (mentorId) {
      query = query.eq('mentor_id', mentorId);
    } else {
      // If no specific filter, show user's own enrollments
      query = query.eq('student_id', user.id);
    }

    query = query.order('created_at', { ascending: false });

    const { data: enrollments, error } = await query;

    if (error) {
      console.error('Error fetching enrollments:', error);
      return NextResponse.json(
        { error: 'Failed to fetch enrollments' },
        { status: 500 }
      );
    }

    return NextResponse.json(enrollments);
  } catch (error: any) {
    console.error('Error in enrollments API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { student_id, program_id, mentor_id, start_date, end_date, payment_type } = body;

    // Validate required fields
    if (!student_id || !program_id || !mentor_id || !start_date || !end_date || !payment_type) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate payment_type
    if (!['monthly', 'upfront'].includes(payment_type)) {
      return NextResponse.json(
        { error: 'Invalid payment_type. Must be "monthly" or "upfront"' },
        { status: 400 }
      );
    }

    const { data: enrollment, error } = await supabase
      .from('student_enrollments')
      .insert({
        student_id,
        program_id,
        mentor_id,
        start_date,
        end_date,
        payment_type,
        status: 'active'
      })
      .select(`
        *,
        program:program_id (*),
        mentor:mentor_id (*)
      `)
      .single();

    if (error) {
      console.error('Error creating enrollment:', error);
      return NextResponse.json(
        { error: 'Failed to create enrollment' },
        { status: 500 }
      );
    }

    return NextResponse.json(enrollment, { status: 201 });
  } catch (error: any) {
    console.error('Error in enrollments POST API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
