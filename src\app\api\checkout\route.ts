import { NextResponse } from 'next/server';
import yocoConfig, { formatAmount } from '@/lib/yoco';
import { createClient } from '@/utils/supabase/server';

// Define types for cart items
interface CartItem {
  id: number;
  name: string;
  price: number;
  image: string;
  quantity: number;
  category?: string;
}

// Simple function to generate a unique ID
function generateId(prefix = '') {
  return `${prefix}${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { items, shippingDetails, userId, notes, shippingMethod } = body;

    if (!items || items.length === 0) {
      return NextResponse.json(
        { error: 'No items provided' },
        { status: 400 }
      );
    }

    // Format line items for Yoco
    const lineItems = items.map((item: CartItem) => ({
      name: item.name,
      image: item.image,
      price: item.price,
      quantity: item.quantity,
      amount: Math.round(item.price * 100 * item.quantity), // Convert to cents
    }));

    // Calculate shipping cost if subtotal is less than R1000
    const subtotal = items.reduce((sum: number, item: CartItem) => sum + (item.price * item.quantity), 0);
    const shippingCost = subtotal < 1000 ? 99.99 : 0;
    
    // Add shipping as a line item if needed
    if (shippingCost > 0) {
      lineItems.push({
        name: 'Shipping',
        image: '',
        price: shippingCost,
        quantity: 1,
        amount: Math.round(shippingCost * 100), // Convert to cents
      });
    }

    // Generate a unique order ID
    const orderId = generateId('TEC-');

    // Create a simplified item summary to fit within Stripe's metadata limits
    const itemSummary = items.map((item: CartItem) => `${item.quantity}x ${item.name}`).join(', ');

    // Calculate the total amount
    const totalAmount = subtotal < 1000 ? subtotal + 99.99 : subtotal;

    // Store order in database if user is authenticated
    if (userId) {
      try {
        const supabase = await createClient();

        // Create order record in database
        const { error } = await supabase
          .from('orders')
          .insert({
            id: orderId,
            user_id: userId,
            items: items,
            shipping_details: shippingDetails,
            status: 'pending',
            payment_status: 'pending',
            total_amount: totalAmount,
            notes: notes || '',
            shipping_method: shippingMethod || 'standard'
          });

        if (error) {
          console.error('Error creating order in database:', error);
        }
      } catch (err) {
        console.error('Error storing order:', err);
      }
    }

    // Create Yoco payment data
    const origin = request.headers.get('origin') || process.env.NEXT_PUBLIC_BASE_URL || '';
    const paymentData = {
      amountInCents: formatAmount(totalAmount),
      currency: 'ZAR',
      name: 'Tennis Equipment Commerce',
      description: `Order ${orderId}: ${itemSummary.substring(0, 100)}${itemSummary.length > 100 ? '...' : ''}`,
      metadata: {
        order_id: orderId,
        user_id: userId || 'guest',
        item_count: items.length.toString(),
        shipping_method: shippingMethod || 'standard',
        notes: notes ? notes.substring(0, 100) : ''
      },
      successUrl: `${origin}/success?order_id=${orderId}`,
      cancelUrl: `${origin}/cart`,
      failureUrl: `${origin}/payment-failed?order_id=${orderId}`,
      shippingDetails: {
        allowedCountries: ['ZA'], // South Africa only
        cost: subtotal < 1000 ? formatAmount(99.99) : 0,
        method: subtotal < 1000 ? 'Standard Shipping' : 'Free Shipping',
        estimatedDays: {
          min: 3,
          max: 5
        }
      }
    };

    // In a real implementation, you would call Yoco's API here to create a payment
    // For now, we'll return a mock response with the data that would be used
    return NextResponse.json({
      orderId,
      paymentUrl: `${origin}/api/yoco-redirect?orderId=${orderId}&amount=${paymentData.amountInCents}`,
      paymentData
    });
  } catch (error: any) {
    console.error('Error creating checkout session:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
