import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { createServiceClient } from '@/utils/supabase/service';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import * as XLSX from 'xlsx';

declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions - only main admins can export reports
    const { data: adminData } = await supabase
      .from('users')
      .select('role, admin_role')
      .eq('id', session.user.id)
      .single();

    if (!adminData || adminData.role !== 'admin' || adminData.admin_role !== 'admin') {
      return NextResponse.json({ error: 'Main admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { 
      report_type, 
      file_format,
      filters = {}
    } = body;

    if (!report_type || !file_format) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Use service role client for database operations
    const serviceSupabase = createServiceClient();

    let reportData: any[] = [];
    let reportTitle = '';

    // Fetch data based on report type
    switch (report_type) {
      case 'activity_summary':
        reportTitle = 'Admin Activity Summary Report';
        // Get activities with admin details separately to avoid join issues
        const { data: activities, error: activitiesError } = await serviceSupabase
          .from('admin_activity_logs')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(1000);

        if (activitiesError) {
          console.error('Activities fetch error:', activitiesError);
          return NextResponse.json({ error: 'Failed to fetch activity data' }, { status: 500 });
        }

        // Fetch admin details for each activity
        const activitiesWithAdmins = await Promise.all(
          (activities || []).map(async (activity) => {
            const { data: adminData } = await serviceSupabase
              .from('users')
              .select('email, full_name, admin_role')
              .eq('id', activity.admin_id)
              .single();

            return {
              ...activity,
              admin: adminData || { email: 'Unknown', full_name: 'Unknown Admin', admin_role: 'unknown' }
            };
          })
        );

        reportData = activitiesWithAdmins;
        break;

      case 'performance_metrics':
        reportTitle = 'Admin Performance Metrics Report';
        // Get performance metrics manually if function fails
        try {
          const { data: metrics, error: metricsError } = await serviceSupabase
            .rpc('get_admin_performance_metrics');

          if (metricsError) {
            console.error('Metrics function error:', metricsError);
            // Fallback to manual calculation
            reportData = await calculatePerformanceMetricsManually(serviceSupabase);
          } else {
            reportData = metrics || [];
          }
        } catch (error) {
          console.error('Performance metrics error:', error);
          reportData = await calculatePerformanceMetricsManually(serviceSupabase);
        }
        break;

      case 'audit_trail':
        reportTitle = 'Admin Audit Trail Report';
        const { data: auditActivities, error: auditError } = await serviceSupabase
          .from('admin_activity_logs')
          .select('*')
          .eq('success', false)
          .order('created_at', { ascending: false })
          .limit(500);

        if (auditError) {
          console.error('Audit data fetch error:', auditError);
          return NextResponse.json({ error: 'Failed to fetch audit data' }, { status: 500 });
        }

        // Fetch admin details for audit activities
        const auditWithAdmins = await Promise.all(
          (auditActivities || []).map(async (activity) => {
            const { data: adminData } = await serviceSupabase
              .from('users')
              .select('email, full_name, admin_role')
              .eq('id', activity.admin_id)
              .single();

            return {
              ...activity,
              admin: adminData || { email: 'Unknown', full_name: 'Unknown Admin', admin_role: 'unknown' }
            };
          })
        );

        reportData = auditWithAdmins;
        break;

      default:
        return NextResponse.json({ error: 'Invalid report type' }, { status: 400 });
    }

    // Generate report based on format
    if (file_format === 'pdf') {
      const pdfBuffer = await generatePDFReport(reportData, reportTitle, report_type);
      
      return new NextResponse(pdfBuffer, {
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="${reportTitle.replace(/\s+/g, '_')}.pdf"`,
        },
      });
    } else if (file_format === 'excel') {
      const excelBuffer = await generateExcelReport(reportData, reportTitle, report_type);
      
      return new NextResponse(excelBuffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'Content-Disposition': `attachment; filename="${reportTitle.replace(/\s+/g, '_')}.xlsx"`,
        },
      });
    } else {
      return NextResponse.json({ error: 'Invalid file format' }, { status: 400 });
    }

  } catch (error) {
    console.error('Export error:', error);
    return NextResponse.json({ error: 'Failed to generate report' }, { status: 500 });
  }
}

async function generatePDFReport(data: any[], title: string, reportType: string): Promise<Buffer> {
  const doc = new jsPDF();
  
  // Add title
  doc.setFontSize(20);
  doc.text(title, 20, 20);
  
  // Add generation date
  doc.setFontSize(12);
  doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 35);
  
  // Prepare table data based on report type
  let headers: string[] = [];
  let rows: any[][] = [];

  switch (reportType) {
    case 'activity_summary':
      headers = ['Date', 'Admin', 'Action Type', 'Description', 'Status'];
      rows = data.map(item => [
        new Date(item.created_at).toLocaleDateString(),
        item.admin?.full_name || 'Unknown',
        item.action_type,
        item.action_description,
        item.success ? 'Success' : 'Failed'
      ]);
      break;

    case 'performance_metrics':
      headers = ['Admin', 'Total Actions', 'Success Rate', 'Last Activity'];
      rows = data.map(item => [
        item.admin_name,
        item.total_actions,
        `${item.success_rate}%`,
        new Date(item.last_activity).toLocaleDateString()
      ]);
      break;

    case 'audit_trail':
      headers = ['Date', 'Admin', 'Failed Action', 'Error Message'];
      rows = data.map(item => [
        new Date(item.created_at).toLocaleDateString(),
        item.admin?.full_name || 'Unknown',
        item.action_description,
        item.error_message || 'No error message'
      ]);
      break;
  }

  // Add table
  doc.autoTable({
    head: [headers],
    body: rows,
    startY: 50,
    styles: {
      fontSize: 8,
      cellPadding: 3,
    },
    headStyles: {
      fillColor: [41, 128, 185],
      textColor: 255,
    },
  });

  return Buffer.from(doc.output('arraybuffer'));
}

async function generateExcelReport(data: any[], title: string, reportType: string): Promise<Buffer> {
  const workbook = XLSX.utils.book_new();
  
  // Prepare worksheet data based on report type
  let worksheetData: any[] = [];

  switch (reportType) {
    case 'activity_summary':
      worksheetData = data.map(item => ({
        'Date': new Date(item.created_at).toLocaleDateString(),
        'Admin': item.admin?.full_name || 'Unknown',
        'Email': item.admin?.email || 'Unknown',
        'Admin Role': item.admin?.admin_role || 'Unknown',
        'Action Type': item.action_type,
        'Description': item.action_description,
        'Target Type': item.target_type || 'N/A',
        'Status': item.success ? 'Success' : 'Failed',
        'Error Message': item.error_message || 'N/A'
      }));
      break;

    case 'performance_metrics':
      worksheetData = data.map(item => ({
        'Admin Name': item.admin_name,
        'Total Actions': item.total_actions,
        'Successful Actions': item.successful_actions,
        'Failed Actions': item.failed_actions,
        'Success Rate': `${item.success_rate}%`,
        'Last Activity': new Date(item.last_activity).toLocaleDateString()
      }));
      break;

    case 'audit_trail':
      worksheetData = data.map(item => ({
        'Date': new Date(item.created_at).toLocaleDateString(),
        'Time': new Date(item.created_at).toLocaleTimeString(),
        'Admin': item.admin?.full_name || 'Unknown',
        'Email': item.admin?.email || 'Unknown',
        'Failed Action': item.action_description,
        'Action Type': item.action_type,
        'Target Type': item.target_type || 'N/A',
        'Error Message': item.error_message || 'No error message',
        'IP Address': item.ip_address || 'N/A',
        'User Agent': item.user_agent || 'N/A'
      }));
      break;
  }

  const worksheet = XLSX.utils.json_to_sheet(worksheetData);
  
  // Auto-size columns
  const colWidths = Object.keys(worksheetData[0] || {}).map(key => ({
    wch: Math.max(key.length, 15)
  }));
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, 'Report Data');
  
  // Add metadata sheet
  const metadataSheet = XLSX.utils.json_to_sheet([
    { Property: 'Report Title', Value: title },
    { Property: 'Report Type', Value: reportType },
    { Property: 'Generated Date', Value: new Date().toISOString() },
    { Property: 'Total Records', Value: data.length }
  ]);
  XLSX.utils.book_append_sheet(workbook, metadataSheet, 'Metadata');

  return Buffer.from(XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' }));
}

async function calculatePerformanceMetricsManually(serviceSupabase: any): Promise<any[]> {
  try {
    // Get all admin users
    const { data: admins } = await serviceSupabase
      .from('users')
      .select('id, email, full_name, admin_role')
      .eq('role', 'admin');

    if (!admins || admins.length === 0) {
      return [];
    }

    // Calculate metrics for each admin
    const metricsPromises = admins.map(async (admin) => {
      const { data: activities } = await serviceSupabase
        .from('admin_activity_logs')
        .select('id, success, created_at')
        .eq('admin_id', admin.id);

      const totalActions = activities?.length || 0;
      const successfulActions = activities?.filter(a => a.success).length || 0;
      const failedActions = totalActions - successfulActions;
      const successRate = totalActions > 0 ? (successfulActions / totalActions) * 100 : 0;
      const lastActivity = activities?.length > 0
        ? Math.max(...activities.map(a => new Date(a.created_at).getTime()))
        : null;

      return {
        admin_id: admin.id,
        admin_name: admin.full_name || admin.email,
        admin_email: admin.email,
        admin_role: admin.admin_role || 'admin',
        total_actions: totalActions,
        successful_actions: successfulActions,
        failed_actions: failedActions,
        success_rate: Math.round(successRate * 100) / 100,
        last_activity: lastActivity ? new Date(lastActivity).toISOString() : null
      };
    });

    const metrics = await Promise.all(metricsPromises);
    return metrics.sort((a, b) => b.total_actions - a.total_actions);
  } catch (error) {
    console.error('Manual metrics calculation error:', error);
    return [];
  }
}
