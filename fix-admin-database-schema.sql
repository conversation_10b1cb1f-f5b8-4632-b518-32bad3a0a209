-- Fix Admin Database Schema - Tennis Whisperer
-- This script will fix the conflicting database schemas and ensure admin authentication works

-- Step 1: Check current users table structure
DO $$
BEGIN
    RAISE NOTICE 'Checking current users table structure...';
END $$;

-- Display current table structure for debugging
SELECT 
    'Current users table structure' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'users'
ORDER BY ordinal_position;

-- Step 2: Create or update user_role enum
DO $$
BEGIN
    -- Check if user_role enum exists
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        -- Create user_role enum if it doesn't exist
        CREATE TYPE user_role AS ENUM ('user', 'admin', 'student', 'mentor');
        RAISE NOTICE 'Created user_role enum with values: user, admin, student, mentor';
    ELSE
        -- Add missing values to existing enum if needed
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum 
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role') 
            AND enumlabel = 'admin'
        ) THEN
            ALTER TYPE user_role ADD VALUE 'admin';
            RAISE NOTICE 'Added admin value to user_role enum';
        END IF;
        
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum 
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role') 
            AND enumlabel = 'user'
        ) THEN
            ALTER TYPE user_role ADD VALUE 'user';
            RAISE NOTICE 'Added user value to user_role enum';
        END IF;
    END IF;
END $$;

-- Step 3: Fix users table structure
DO $$
BEGIN
    -- Check if users table exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'users'
    ) THEN
        -- Create users table with correct structure
        CREATE TABLE public.users (
            id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
            email TEXT UNIQUE NOT NULL,
            full_name TEXT,
            name TEXT,
            role user_role NOT NULL DEFAULT 'user',
            token_identifier TEXT, -- Nullable
            avatar_url TEXT,
            user_id TEXT,
            image TEXT,
            shipping_details JSONB,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        RAISE NOTICE 'Created users table with proper structure';
    ELSE
        -- Table exists, let's fix the columns
        
        -- Make token_identifier nullable if it exists and is NOT NULL
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'users' 
            AND column_name = 'token_identifier'
            AND is_nullable = 'NO'
        ) THEN
            ALTER TABLE public.users ALTER COLUMN token_identifier DROP NOT NULL;
            RAISE NOTICE 'Made token_identifier nullable';
        END IF;
        
        -- Add missing columns if they don't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'users' 
            AND column_name = 'full_name'
        ) THEN
            ALTER TABLE public.users ADD COLUMN full_name TEXT;
            RAISE NOTICE 'Added full_name column';
        END IF;
        
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'users' 
            AND column_name = 'name'
        ) THEN
            ALTER TABLE public.users ADD COLUMN name TEXT;
            RAISE NOTICE 'Added name column';
        END IF;
        
        -- Fix role column - this is the tricky part
        -- First check if role column exists and what type it is
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'users' 
            AND column_name = 'role'
            AND data_type = 'text'
        ) THEN
            -- Role exists as TEXT, need to convert to enum
            -- First, add a temporary column
            ALTER TABLE public.users ADD COLUMN role_temp user_role DEFAULT 'user';
            
            -- Copy data, mapping text values to enum values
            UPDATE public.users SET role_temp = 
                CASE 
                    WHEN role = 'admin' THEN 'admin'::user_role
                    WHEN role = 'mentor' THEN 'mentor'::user_role
                    WHEN role = 'student' THEN 'user'::user_role
                    ELSE 'user'::user_role
                END;
            
            -- Drop old column and rename new one
            ALTER TABLE public.users DROP COLUMN role;
            ALTER TABLE public.users RENAME COLUMN role_temp TO role;
            ALTER TABLE public.users ALTER COLUMN role SET NOT NULL;
            
            RAISE NOTICE 'Converted role column from TEXT to user_role enum';
            
        ELSIF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'users' 
            AND column_name = 'role'
        ) THEN
            -- Role column doesn't exist, add it
            ALTER TABLE public.users ADD COLUMN role user_role NOT NULL DEFAULT 'user';
            RAISE NOTICE 'Added role column with user_role enum type';
        END IF;
        
    END IF;
END $$;

-- Step 4: Enable RLS and create policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Admins can view all users" ON public.users;
DROP POLICY IF EXISTS "Admins can manage all users" ON public.users;
DROP POLICY IF EXISTS "Users can view own data" ON public.users;

-- Create comprehensive RLS policies
CREATE POLICY "Users can view own profile" 
  ON public.users 
  FOR SELECT 
  USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" 
  ON public.users 
  FOR UPDATE 
  USING (auth.uid() = id);

CREATE POLICY "Admins can view all users" 
  ON public.users 
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Admins can manage all users" 
  ON public.users 
  FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Step 5: Create or replace the user creation function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, full_name, name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'role', 'user')::user_role
  )
  ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = COALESCE(EXCLUDED.full_name, public.users.full_name),
    name = COALESCE(EXCLUDED.name, public.users.name),
    role = COALESCE(EXCLUDED.role, public.users.role),
    updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 6: Create trigger for automatic user profile creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Step 7: Grant necessary permissions
GRANT USAGE ON TYPE user_role TO authenticated;
GRANT USAGE ON TYPE user_role TO anon;
GRANT SELECT, INSERT, UPDATE ON public.users TO authenticated;
GRANT SELECT ON public.users TO anon;

-- Step 8: Final verification
DO $$
BEGIN
    RAISE NOTICE '=== ADMIN DATABASE SCHEMA FIX COMPLETED ===';
    RAISE NOTICE 'Users table structure has been standardized';
    RAISE NOTICE 'Role column is now user_role enum type';
    RAISE NOTICE 'token_identifier is nullable';
    RAISE NOTICE 'RLS policies are properly configured';
    RAISE NOTICE 'Trigger function is set up for automatic profile creation';
    RAISE NOTICE 'Admin sign-up should now work correctly';
    RAISE NOTICE 'Test admin sign-up at: /admin/sign-up';
    RAISE NOTICE 'Admin access code: TENNIS_ADMIN_2024';
END $$;
