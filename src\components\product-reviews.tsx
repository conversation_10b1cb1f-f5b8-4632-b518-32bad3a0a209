"use client";

import { useState } from "react";
import { Star, ThumbsUp, Flag, User } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";

interface Review {
  id: number;
  user: string;
  rating: number;
  title: string;
  comment: string;
  date: string;
  helpful: number;
  isHelpful?: boolean;
}

interface ProductReviewsProps {
  productId: number;
  initialReviews: Review[];
}

export function ProductReviews({ productId, initialReviews }: ProductReviewsProps) {
  const [reviews, setReviews] = useState<Review[]>(initialReviews);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [newReview, setNewReview] = useState({
    rating: 5,
    title: "",
    comment: "",
    name: "",
    email: "",
  });
  const { toast } = useToast();

  const handleRatingChange = (rating: number) => {
    setNewReview({ ...newReview, rating });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewReview({ ...newReview, [name]: value });
  };

  const handleSubmitReview = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    if (!newReview.title.trim() || !newReview.comment.trim() || !newReview.name.trim() || !newReview.email.trim()) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive",
      });
      return;
    }
    
    // Create new review
    const review: Review = {
      id: Date.now(),
      user: newReview.name,
      rating: newReview.rating,
      title: newReview.title,
      comment: newReview.comment,
      date: new Date().toLocaleDateString("en-ZA", {
        year: "numeric",
        month: "long",
        day: "numeric",
      }),
      helpful: 0,
    };
    
    // Add to reviews
    setReviews([review, ...reviews]);
    
    // Reset form
    setNewReview({
      rating: 5,
      title: "",
      comment: "",
      name: "",
      email: "",
    });
    
    // Hide form
    setShowReviewForm(false);
    
    // Show success message
    toast({
      title: "Review Submitted",
      description: "Thank you for your review!",
    });
  };

  const markHelpful = (reviewId: number) => {
    setReviews(
      reviews.map((review) => {
        if (review.id === reviewId) {
          return {
            ...review,
            helpful: review.helpful + 1,
            isHelpful: true,
          };
        }
        return review;
      })
    );
  };

  const calculateAverageRating = () => {
    if (reviews.length === 0) return 0;
    const sum = reviews.reduce((total, review) => total + review.rating, 0);
    return sum / reviews.length;
  };

  const getRatingPercentage = (rating: number) => {
    if (reviews.length === 0) return 0;
    const count = reviews.filter((review) => review.rating === rating).length;
    return Math.round((count / reviews.length) * 100);
  };

  return (
    <div className="mt-12">
      <h2 className="text-2xl font-bold mb-6">Customer Reviews</h2>

      {/* Reviews Summary */}
      <div className="bg-card border border-border rounded-lg p-6 mb-8">
        <div className="flex flex-col md:flex-row gap-8">
          <div className="md:w-1/3 text-center">
            <div className="text-4xl font-bold text-foreground mb-2">
              {calculateAverageRating().toFixed(1)}
            </div>
            <div className="flex justify-center mb-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  className={`h-5 w-5 ${
                    star <= Math.round(calculateAverageRating())
                      ? "text-yellow-400 fill-yellow-400"
                      : "text-gray-300"
                  }`}
                />
              ))}
            </div>
            <p className="text-sm text-muted-foreground">
              Based on {reviews.length} {reviews.length === 1 ? "review" : "reviews"}
            </p>
          </div>

          <div className="md:w-2/3">
            <div className="space-y-2">
              {[5, 4, 3, 2, 1].map((rating) => (
                <div key={rating} className="flex items-center">
                  <div className="w-12 text-sm text-muted-foreground">{rating} star</div>
                  <div className="flex-1 mx-3 h-4 rounded-full bg-muted overflow-hidden">
                    <div
                      className="h-full bg-yellow-400"
                      style={{ width: `${getRatingPercentage(rating)}%` }}
                    ></div>
                  </div>
                  <div className="w-12 text-sm text-right text-muted-foreground">
                    {getRatingPercentage(rating)}%
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="mt-6 pt-6 border-t border-border text-center">
          <p className="mb-4 text-muted-foreground">Share your thoughts with other customers</p>
          <Button onClick={() => setShowReviewForm(true)}>Write a Review</Button>
        </div>
      </div>

      {/* Review Form */}
      {showReviewForm && (
        <div className="bg-card border border-border rounded-lg p-6 mb-8">
          <h3 className="text-xl font-semibold mb-4">Write a Review</h3>
          <form onSubmit={handleSubmitReview}>
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">Rating</label>
              <div className="flex gap-1">
                {[1, 2, 3, 4, 5].map((rating) => (
                  <button
                    key={rating}
                    type="button"
                    onClick={() => handleRatingChange(rating)}
                    className="focus:outline-none"
                  >
                    <Star
                      className={`h-8 w-8 ${
                        rating <= newReview.rating
                          ? "text-yellow-400 fill-yellow-400"
                          : "text-gray-300"
                      }`}
                    />
                  </button>
                ))}
              </div>
            </div>

            <div className="mb-4">
              <label htmlFor="title" className="block text-sm font-medium mb-2">
                Review Title
              </label>
              <Input
                id="title"
                name="title"
                placeholder="Summarize your experience"
                value={newReview.title}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="comment" className="block text-sm font-medium mb-2">
                Review
              </label>
              <Textarea
                id="comment"
                name="comment"
                placeholder="What did you like or dislike about this product?"
                rows={4}
                value={newReview.comment}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium mb-2">
                  Your Name
                </label>
                <Input
                  id="name"
                  name="name"
                  placeholder="Enter your name"
                  value={newReview.name}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div>
                <label htmlFor="email" className="block text-sm font-medium mb-2">
                  Email Address
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Enter your email"
                  value={newReview.email}
                  onChange={handleInputChange}
                  required
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Your email will not be published
                </p>
              </div>
            </div>

            <div className="flex gap-3">
              <Button type="submit">Submit Review</Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowReviewForm(false)}
              >
                Cancel
              </Button>
            </div>
          </form>
        </div>
      )}

      {/* Reviews List */}
      {reviews.length > 0 ? (
        <div className="space-y-6">
          {reviews.map((review) => (
            <div key={review.id} className="border-b border-border pb-6">
              <div className="flex items-start justify-between">
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <div className="flex">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`h-4 w-4 ${
                            star <= review.rating
                              ? "text-yellow-400 fill-yellow-400"
                              : "text-gray-300"
                          }`}
                        />
                      ))}
                    </div>
                    <h4 className="font-semibold">{review.title}</h4>
                  </div>
                  <p className="text-sm text-muted-foreground mb-3">
                    <span className="flex items-center gap-1">
                      <User className="h-3 w-3" />
                      {review.user}
                    </span>
                    <span className="text-xs"> • {review.date}</span>
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-xs"
                    onClick={() => markHelpful(review.id)}
                    disabled={review.isHelpful}
                  >
                    <ThumbsUp className="h-3 w-3 mr-1" />
                    Helpful ({review.helpful})
                  </Button>
                  <Button variant="ghost" size="sm" className="text-xs">
                    <Flag className="h-3 w-3 mr-1" />
                    Report
                  </Button>
                </div>
              </div>
              <p className="text-foreground mt-2">{review.comment}</p>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 bg-card border border-border rounded-lg">
          <h3 className="text-lg font-medium mb-2">No reviews yet</h3>
          <p className="text-muted-foreground mb-6">Be the first to review this product</p>
          <Button onClick={() => setShowReviewForm(true)}>Write a Review</Button>
        </div>
      )}
    </div>
  );
}
