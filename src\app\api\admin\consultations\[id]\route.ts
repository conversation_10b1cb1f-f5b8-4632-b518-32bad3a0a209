import { NextRequest, NextResponse } from 'next/server';
import { createClient, createServiceRoleClient } from '@/utils/supabase/server';
import { consultationStatusUpdateSchema } from '@/types/consultations';
import { SystemActivityLogger } from '@/utils/admin-activity-logger';

/**
 * GET /api/admin/consultations/[id]
 * Fetch a specific consultation by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`GET /api/admin/consultations/${params.id} - Starting request`);

    // Verify admin authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const { data: userData } = await supabase
      .from('users')
      .select('role, admin_role')
      .eq('id', session.user.id)
      .single();

    if (!userData || userData.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Use service role client for database operations
    const serviceSupabase = createServiceRoleClient();

    const { data: consultation, error } = await serviceSupabase
      .from('consultations')
      .select('*')
      .eq('id', params.id)
      .single();

    if (error) {
      console.error(`GET /api/admin/consultations/${params.id} - Database error:`, error);
      return NextResponse.json({ error: 'Consultation not found' }, { status: 404 });
    }

    return NextResponse.json(consultation);

  } catch (error: any) {
    console.error(`GET /api/admin/consultations/${params.id} - Error:`, error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/consultations/[id]
 * Update consultation status and details
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`PATCH /api/admin/consultations/${params.id} - Starting request`);

    // Verify admin authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const { data: userData } = await supabase
      .from('users')
      .select('role, admin_role')
      .eq('id', session.user.id)
      .single();

    if (!userData || userData.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    
    // Validate the update data
    const validationResult = consultationStatusUpdateSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid update data', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const updateData = validationResult.data;

    // Use service role client for database operations
    const serviceSupabase = createServiceRoleClient();

    // Get the current consultation for logging
    const { data: currentConsultation } = await serviceSupabase
      .from('consultations')
      .select('*')
      .eq('id', params.id)
      .single();

    if (!currentConsultation) {
      return NextResponse.json({ error: 'Consultation not found' }, { status: 404 });
    }

    // Prepare update object
    const updates: any = {
      status: updateData.status,
      updated_at: new Date().toISOString(),
    };

    if (updateData.admin_notes) {
      updates.admin_notes = updateData.admin_notes;
    }

    if (updateData.cancellation_reason) {
      updates.cancellation_reason = updateData.cancellation_reason;
    }

    // Handle rescheduling
    if (updateData.status === 'rescheduled' && updateData.new_date && updateData.new_time) {
      updates.rescheduled_from = `${currentConsultation.scheduled_date} ${currentConsultation.scheduled_time}`;
      updates.scheduled_date = updateData.new_date;
      updates.scheduled_time = updateData.new_time;
    }

    // Update the consultation
    const { data: updatedConsultation, error } = await serviceSupabase
      .from('consultations')
      .update(updates)
      .eq('id', params.id)
      .select()
      .single();

    if (error) {
      console.error(`PATCH /api/admin/consultations/${params.id} - Database error:`, error);
      return NextResponse.json({ error: 'Failed to update consultation' }, { status: 500 });
    }

    // Log the activity
    try {
      await SystemActivityLogger.consultationUpdated(
        params.id,
        `Status changed from ${currentConsultation.status} to ${updateData.status}`,
        session.user.id
      );
    } catch (logError) {
      console.error('Failed to log activity:', logError);
      // Don't fail the request if logging fails
    }

    console.log(`PATCH /api/admin/consultations/${params.id} - Consultation updated successfully`);

    return NextResponse.json({
      success: true,
      consultation: updatedConsultation,
    });

  } catch (error: any) {
    console.error(`PATCH /api/admin/consultations/${params.id} - Error:`, error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/consultations/[id]
 * Delete a consultation (admin only)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`DELETE /api/admin/consultations/${params.id} - Starting request`);

    // Verify admin authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const { data: userData } = await supabase
      .from('users')
      .select('role, admin_role')
      .eq('id', session.user.id)
      .single();

    if (!userData || userData.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Use service role client for database operations
    const serviceSupabase = createServiceRoleClient();

    // Get consultation details for logging
    const { data: consultation } = await serviceSupabase
      .from('consultations')
      .select('first_name, last_name, email')
      .eq('id', params.id)
      .single();

    // Delete the consultation
    const { error } = await serviceSupabase
      .from('consultations')
      .delete()
      .eq('id', params.id);

    if (error) {
      console.error(`DELETE /api/admin/consultations/${params.id} - Database error:`, error);
      return NextResponse.json({ error: 'Failed to delete consultation' }, { status: 500 });
    }

    // Log the activity
    try {
      const customerName = consultation 
        ? `${consultation.first_name} ${consultation.last_name} (${consultation.email})`
        : 'Unknown customer';
      
      await SystemActivityLogger.consultationDeleted(
        params.id,
        `Deleted consultation for ${customerName}`,
        session.user.id
      );
    } catch (logError) {
      console.error('Failed to log activity:', logError);
      // Don't fail the request if logging fails
    }

    console.log(`DELETE /api/admin/consultations/${params.id} - Consultation deleted successfully`);

    return NextResponse.json({
      success: true,
      message: 'Consultation deleted successfully',
    });

  } catch (error: any) {
    console.error(`DELETE /api/admin/consultations/${params.id} - Error:`, error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
