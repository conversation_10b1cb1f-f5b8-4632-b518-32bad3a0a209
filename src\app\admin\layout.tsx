'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { AdminSidebar } from "@/components/admin/admin-sidebar";
import { AdminHeader } from "@/components/admin/admin-header";
import { createClient } from '@/utils/supabase/client';
import dynamic from 'next/dynamic';

// Dynamically import Silk component to avoid SSR issues with Three.js
const Silk = dynamic(() => import('@/components/ui/silk'), {
  ssr: false,
  loading: () => <div className="absolute inset-0 bg-gradient-to-br from-background via-muted/20 to-background" />
});

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const supabase = createClient();
        const { data: { session } } = await supabase.auth.getSession();

        if (!session) {
          router.push('/admin/sign-in');
          return;
        }

        // Check if user has admin role
        const { data: userData } = await supabase
          .from('users')
          .select('role, admin_role')
          .eq('id', session.user.id)
          .single();

        if (!userData || userData.role !== 'admin') {
          router.push('/admin/sign-in?error=admin_access_required');
          return;
        }

        setIsAuthenticated(true);
      } catch (error) {
        console.error('Auth check error:', error);
        router.push('/admin/sign-in?error=auth_error');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect via useEffect
  }

  return (
    <div className="min-h-screen flex relative overflow-hidden">
      {/* Silk Background */}
      <div className="absolute inset-0 -z-10">
        <Silk
          speed={2}
          scale={1.5}
          color="#0f0f23"
          noiseIntensity={0.5}
          rotation={0.05}
        />
        {/* Overlay for better contrast and readability */}
        <div className="absolute inset-0 bg-background/70 backdrop-blur-[2px]" />
      </div>

      <AdminSidebar />
      <div className="flex-1 flex flex-col relative z-10">
        <AdminHeader />
        <main className="flex-1 p-6 overflow-auto">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
