import { FormMessage, Message } from "@/components/form-message";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { adminSignInAction } from "./actions";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ock, FaS<PERSON>eldAlt, <PERSON>a<PERSON><PERSON> } from "react-icons/fa";
import Image from "next/image";

interface AdminLoginProps {
  searchParams: {
    message?: string;
    error?: string;
    redirect_to?: string;
    [key: string]: string | undefined;
  };
}

export default async function AdminSignInPage({ searchParams }: AdminLoginProps) {
  const message = searchParams as unknown as Message;
  const redirectTo = searchParams.redirect_to || '/admin';

  if ("message" in message) {
    return (
      <div className="flex h-screen w-full flex-1 items-center justify-center p-4 sm:max-w-md">
        <FormMessage message={message} />
      </div>
    );
  }

  return (
    <>
      {/* Desktop Navbar */}
      <div className="hidden md:block">
        {/* <Navbar /> */}
      </div>

      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background via-muted/20 to-background p-4">
        {/* Background decoration */}
        <div className="absolute inset-0 overflow-hidden -z-10">
          <div className="absolute h-[500px] w-[500px] rounded-full gradient-blue top-[-250px] left-[-250px] blur-[120px] opacity-15 animate-pulse"></div>
          <div className="absolute h-[400px] w-[400px] rounded-full gradient-purple bottom-[-200px] right-[-200px] blur-[100px] opacity-20 animate-pulse"></div>
        </div>

        <div className="w-full max-w-md">
           {/* Logo Avatar */}
                    <div className="text-center mb-8">
                      <div className="relative mx-auto w-28 h-28 mb-6">
                        <div className="w-full h-full rounded-full p-1 neo-shadow">
                          <div className="w-full h-full rounded-full bg-blur flex items-center justify-center">
                            <Link
                              href="/"
                              className="text-center"
                            >
                            <Image
                              src="/logo.svg"
                              alt="Logo"
                              width={70}
                              height={70}
                              className=" object-contain"
                              />
                              </Link>
                          </div>
                        </div>
                      </div>
            <h1 className="text-2xl font-bold tracking-tight text-foreground mb-2">ADMIN ACCESS</h1>
            <p className="text-muted-foreground text-sm">
              Sign in to Tennis Whisperer Admin Dashboard
            </p>
          </div>

          <div className="glass-effect-dark rounded-3xl p-8 neo-shadow">
            <form className="flex flex-col space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="relative">
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="admin email"
                      required
                      className="w-full h-14 pl-12 rounded-2xl bg-muted/30 border-0 neo-shadow-inset placeholder:text-muted-foreground/70"
                    />
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                        <span className="text-sm"><FaRegUser /></span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="relative">
                    <Input
                      id="password"
                      type="password"
                      name="password"
                      placeholder="password"
                      required
                      className="w-full h-14 pl-12 rounded-2xl bg-muted/30 border-0 neo-shadow-inset placeholder:text-muted-foreground/70"
                    />
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                        <span className="text-sm"><FaLock /></span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="relative">
                    <Input
                      id="admin_code"
                      name="admin_code"
                      type="password"
                      placeholder="admin access code"
                      required
                      className="w-full h-14 pl-12 rounded-2xl bg-muted/30 border-0 neo-shadow-inset placeholder:text-muted-foreground/70"
                    />
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                        <span className="text-sm"><FaKey /></span>
                    </div>
                  </div>
                </div>
              </div>

              <SubmitButton
                className="w-full h-14 rounded-2xl gradient-blue text-white font-semibold text-lg neo-shadow hover:neo-shadow-light transition-neo border-0"
                pendingText="Signing in..."
                formAction={async (formData: FormData) => {
                  "use server";
                  return adminSignInAction(formData, redirectTo);
                }}
              >
                <div className="flex items-center justify-center gap-2">
                  <FaShieldAlt className="text-sm" />
                  Admin Login
                </div>
              </SubmitButton>

              <FormMessage message={message} />

              <div className="text-center space-y-2">
                <p className="text-sm text-muted-foreground">
                  Need admin access?{" "}
                  <Link
                    className="text-primary font-medium hover:underline"
                    href="/admin/sign-up"
                  >
                    Request admin account
                  </Link>
                </p>
                <p className="text-xs text-muted-foreground">
                  <Link
                    className="text-primary hover:underline"
                    href="/sign-in"
                  >
                    Regular user login
                  </Link>
                </p>
              </div>

              {/* Security Notice */}
              <div className="mt-6 p-4 rounded-xl bg-muted/20 border border-border/50">
                <div className="flex items-start gap-3">
                  <FaShieldAlt className="text-primary mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="text-sm font-semibold text-foreground mb-1">Security Notice</h4>
                    <p className="text-xs text-muted-foreground leading-relaxed">
                      This is a secure admin portal. All access attempts are logged and monitored. 
                      Only authorized personnel should access this area.
                    </p>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* <Footer /> */}
    </>
  );
}
