"use client";

import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

export interface CardData {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  image?: string;
  badge?: string;
  badgeVariant?: "default" | "secondary" | "destructive" | "outline";
  ctaText: string;
  ctaLink: string;
  ctaVariant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  backgroundColor?: string;
  textColor?: string;
}

export interface CardSwapProps {
  cards: CardData[];
  cardDistance?: number;
  verticalDistance?: number;
  delay?: number;
  pauseOnHover?: boolean;
  className?: string;
  autoPlay?: boolean;
}

export function CardSwap({
  cards,
  cardDistance = 60,
  verticalDistance = 70,
  delay = 5000,
  pauseOnHover = false,
  className,
  autoPlay = true,
}: CardSwapProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Auto-advance cards
  useEffect(() => {
    if (!autoPlay || cards.length <= 1) return;

    const shouldPause = pauseOnHover && isHovered;
    
    if (!shouldPause) {
      intervalRef.current = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % cards.length);
      }, delay);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [autoPlay, cards.length, delay, isHovered, pauseOnHover]);

  // Handle mouse events
  const handleMouseEnter = () => {
    if (pauseOnHover) {
      setIsHovered(true);
    }
  };

  const handleMouseLeave = () => {
    if (pauseOnHover) {
      setIsHovered(false);
    }
  };

  if (!cards || cards.length === 0) {
    return null;
  }

  const currentCard = cards[currentIndex];

  return (
    <div
      className={cn("relative w-full max-w-md mx-auto", className)}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Card Stack Background */}
      <div className="relative">
        {/* Background cards for depth effect */}
        {cards.length > 1 && (
          <>
            {/* Third card (furthest back) */}
            <div
              className="absolute inset-0 rounded-2xl bg-white/40 backdrop-blur-sm border border-white/20 neo-shadow-light"
              style={{
                transform: `translateX(${cardDistance * 2}px) translateY(${verticalDistance * 2}px) scale(0.9)`,
                zIndex: 1,
              }}
            />
            
            {/* Second card (middle) */}
            <div
              className="absolute inset-0 rounded-2xl bg-white/60 backdrop-blur-sm border border-white/30 neo-shadow"
              style={{
                transform: `translateX(${cardDistance}px) translateY(${verticalDistance}px) scale(0.95)`,
                zIndex: 2,
              }}
            />
          </>
        )}

        {/* Main animated card */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentCard.id}
            initial={{ 
              opacity: 0, 
              scale: 0.8,
              rotateY: -15,
              z: -100
            }}
            animate={{ 
              opacity: 1, 
              scale: 1,
              rotateY: 0,
              z: 0
            }}
            exit={{ 
              opacity: 0, 
              scale: 0.8,
              rotateY: 15,
              z: -100
            }}
            transition={{
              duration: 0.6,
              ease: [0.25, 0.46, 0.45, 0.94],
            }}
            className="relative z-10"
            style={{ perspective: "1000px" }}
          >
            <Card 
              className={cn(
                "overflow-hidden border-0 neo-shadow-hover transition-all duration-300",
                "bg-white/90 backdrop-blur-sm",
                currentCard.backgroundColor
              )}
            >
              {/* Card Image */}
              {currentCard.image && (
                <div className="aspect-video overflow-hidden rounded-t-2xl">
                  <img
                    src={currentCard.image}
                    alt={currentCard.title}
                    className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                  />
                </div>
              )}

              <CardHeader className="pb-4">
                <div className="flex items-start justify-between gap-4">
                  <div className="flex-1">
                    <CardTitle className={cn(
                      "text-xl font-bold mb-2 leading-tight",
                      currentCard.textColor || "text-slate-900"
                    )}>
                      {currentCard.title}
                    </CardTitle>
                    <p className={cn(
                      "text-sm font-medium",
                      currentCard.textColor || "text-slate-600"
                    )}>
                      {currentCard.subtitle}
                    </p>
                  </div>
                  
                  {currentCard.badge && (
                    <Badge variant={currentCard.badgeVariant || "default"}>
                      {currentCard.badge}
                    </Badge>
                  )}
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                <p className={cn(
                  "text-sm leading-relaxed mb-6",
                  currentCard.textColor || "text-slate-700"
                )}>
                  {currentCard.description}
                </p>

                <Button
                  asChild
                  variant={currentCard.ctaVariant || "default"}
                  className="w-full min-h-[44px] rounded-xl neo-shadow hover:neo-shadow-hover transition-all duration-300"
                >
                  <a href={currentCard.ctaLink}>
                    {currentCard.ctaText}
                  </a>
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Card indicators */}
      {cards.length > 1 && (
        <div className="flex justify-center gap-2 mt-6">
          {cards.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={cn(
                "w-2 h-2 rounded-full transition-all duration-300 focus-ring",
                index === currentIndex
                  ? "bg-green-600 w-6"
                  : "bg-slate-300 hover:bg-slate-400"
              )}
              aria-label={`Go to card ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  );
}
