"use client";

import { useEffect, useState, useRef } from 'react';
import { usePathname } from 'next/navigation';
import Image from 'next/image';
import { cn } from '@/lib/utils';

export default function RoutePreloader() {
  const [isLoading, setIsLoading] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);
  const pathname = usePathname();
  const timeoutsRef = useRef<NodeJS.Timeout[]>([]);
  const previousPathnameRef = useRef<string>(pathname);

  // Helper function to add timeout to ref for cleanup
  const addTimeout = (timeout: NodeJS.Timeout) => {
    timeoutsRef.current.push(timeout);
    return timeout;
  };

  // Helper function to clear all timeouts
  const clearAllTimeouts = () => {
    timeoutsRef.current.forEach(clearTimeout);
    timeoutsRef.current = [];
  };

  // Handle navigation detection through pathname changes
  useEffect(() => {
    // Skip on initial load
    if (previousPathnameRef.current === pathname) {
      previousPathnameRef.current = pathname;
      return;
    }

    // Show preloader for navigation
    setIsNavigating(true);
    setIsLoading(true);
    setIsVisible(true);

    // Hide preloader after navigation completes
    const hidePreloader = () => {
      setIsLoading(false);
      addTimeout(setTimeout(() => {
        setIsVisible(false);
        setIsNavigating(false);
      }, 500));
    };

    // Simulate loading time for smooth UX
    addTimeout(setTimeout(hidePreloader, 600));

    // Update previous pathname
    previousPathnameRef.current = pathname;

    return () => {
      clearAllTimeouts();
    };
  }, [pathname]);

  // Handle link clicks for immediate feedback
  useEffect(() => {
    const handleLinkClick = (e: Event) => {
      const target = e.target as HTMLElement;
      const link = target.closest('a[href]') as HTMLAnchorElement;

      if (link && link.href && !link.href.startsWith('#') && !link.href.includes('mailto:') && !link.href.includes('tel:')) {
        try {
          const url = new URL(link.href);
          const currentUrl = new URL(window.location.href);

          // Only show preloader for internal navigation to different pages
          if (url.origin === currentUrl.origin && url.pathname !== currentUrl.pathname) {
            setIsNavigating(true);
            setIsLoading(true);
            setIsVisible(true);
          }
        } catch (error) {
          // Handle invalid URLs gracefully
          console.warn('Invalid URL:', link.href);
        }
      }
    };

    // Add event listener for link clicks
    document.addEventListener('click', handleLinkClick);

    // Cleanup
    return () => {
      document.removeEventListener('click', handleLinkClick);
      clearAllTimeouts();
    };
  }, []);

  // Handle reduced motion preference
  const prefersReducedMotion = typeof window !== 'undefined' 
    ? window.matchMedia('(prefers-reduced-motion: reduce)').matches 
    : false;

  if (!isVisible) return null;

  return (
    <div
      className={cn(
        "fixed inset-0 z-[9998] flex items-center justify-center",
        "bg-background/90",
        "transition-all duration-500 ease-out",
        isLoading ? "opacity-100 scale-100" : "opacity-0 scale-105"
      )}
      role="progressbar"
      aria-label="Loading page"
      aria-live="polite"
    >
      {/* VirTra-style compact logo for route changes */}
      <div className="relative flex flex-col items-center justify-center">
        <div
          className={cn(
            "relative transition-all duration-500 ease-out",
            isLoading ? "opacity-100 scale-100" : "opacity-0 scale-95",
            !prefersReducedMotion && "animate-virtra-logo"
          )}
        >
          <div className="relative w-20 h-20">
            <Image
              src="/logo.svg"
              alt="Tennis Whisperer"
              fill
              className="object-contain virtra-logo-glow"
              priority
              sizes="80px"
            />
          </div>
        </div>

        {/* VirTra-style loading indicator */}
        <div className="mt-6 flex space-x-2">
          {!prefersReducedMotion ? (
            <>
              <div className="w-2 h-2 bg-primary rounded-full animate-virtra-dot"></div>
              <div className="w-2 h-2 bg-primary rounded-full animate-virtra-dot" style={{ animationDelay: '0.2s' }}></div>
              <div className="w-2 h-2 bg-primary rounded-full animate-virtra-dot" style={{ animationDelay: '0.4s' }}></div>
            </>
          ) : (
            <>
              <div className="w-2 h-2 bg-primary rounded-full"></div>
              <div className="w-2 h-2 bg-primary rounded-full"></div>
              <div className="w-2 h-2 bg-primary rounded-full"></div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
