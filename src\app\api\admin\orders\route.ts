import { NextRequest, NextResponse } from 'next/server';
import { createClient, createServiceRoleClient } from '@/utils/supabase/server';
import { OrderFilters, OrderStats } from '@/types/orders';

/**
 * GET /api/admin/orders
 * Fetch paginated orders with filtering and search
 */
export async function GET(request: NextRequest) {
  try {
    console.log('GET /api/admin/orders - Request received');

    // Check authentication
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (!user) {
      console.log('GET /api/admin/orders - No authenticated user');
      return NextResponse.json(
        { error: 'Unauthorized', details: authError?.message },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || userData.role !== 'admin') {
      console.log('GET /api/admin/orders - User is not admin:', userData?.role);
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }

    console.log('GET /api/admin/orders - Admin access confirmed');

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const payment_status = searchParams.get('payment_status');
    const search = searchParams.get('search');
    const date_from = searchParams.get('date_from');
    const date_to = searchParams.get('date_to');

    const offset = (page - 1) * limit;

    // Use service role client for database operations
    const serviceSupabase = createServiceRoleClient();

    // Build query
    let query = serviceSupabase
      .from('orders')
      .select(`
        *,
        users!user_id (
          email,
          full_name
        )
      `, { count: 'exact' });

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }
    if (payment_status) {
      query = query.eq('payment_status', payment_status);
    }
    if (date_from) {
      query = query.gte('created_at', date_from);
    }
    if (date_to) {
      query = query.lte('created_at', date_to);
    }
    if (search) {
      // Search in order ID, customer name, or email
      query = query.or(`id.ilike.%${search}%,users.full_name.ilike.%${search}%,users.email.ilike.%${search}%`);
    }

    // Apply pagination and ordering
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: orders, error, count } = await query;

    if (error) {
      console.error('GET /api/admin/orders - Database error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch orders', details: error.message },
        { status: 500 }
      );
    }

    // Transform data to match our interface
    const transformedOrders = orders?.map(order => ({
      ...order,
      customer_email: order.users?.email,
      customer_name: order.users?.full_name,
      users: undefined, // Remove the nested users object
    })) || [];

    const totalPages = Math.ceil((count || 0) / limit);

    console.log('GET /api/admin/orders - Success:', {
      ordersCount: transformedOrders.length,
      total: count,
      page,
      totalPages
    });

    return NextResponse.json({
      success: true,
      data: {
        orders: transformedOrders,
        total: count || 0,
        page,
        limit,
        total_pages: totalPages,
      }
    });

  } catch (error: any) {
    console.error('GET /api/admin/orders - General error:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/orders/stats
 * Get order statistics for dashboard
 */
export async function POST(request: NextRequest) {
  try {
    console.log('POST /api/admin/orders - Stats request received');

    // Check authentication (same as GET)
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || userData.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 });
    }

    const serviceSupabase = createServiceRoleClient();

    // Get order statistics
    const { data: stats, error } = await serviceSupabase.rpc('get_order_stats');

    if (error) {
      console.error('POST /api/admin/orders - Stats error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch order statistics', details: error.message },
        { status: 500 }
      );
    }

    // Extract the stats from the returned object structure
    const statsData = stats?.[0] || stats || {
      total_orders: 0,
      total_revenue: 0,
      pending_orders: 0,
      processing_orders: 0,
      completed_orders: 0,
      monthly_growth: 0
    };

    console.log('POST /api/admin/orders - Stats data:', statsData);

    return NextResponse.json({
      success: true,
      data: statsData
    });

  } catch (error: any) {
    console.error('POST /api/admin/orders - Stats general error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
