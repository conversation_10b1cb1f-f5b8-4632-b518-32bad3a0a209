"use client";

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Search, 
  Calendar, 
  User, 
  Eye, 
  Heart, 
  MessageSquare,
  Clock,
  ArrowRight,
  ArrowLeft,
  FolderOpen
} from "lucide-react";
import Link from 'next/link';
import { createClient } from '@/utils/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  featured_image: string | null;
  is_featured: boolean;
  views: number;
  likes: number;
  comments_count: number;
  read_time: number;
  tags: string[];
  published_at: string;
  users?: {
    full_name: string;
  };
}

interface Category {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  color: string;
  post_count: number;
}

export default function BlogCategoryPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [category, setCategory] = useState<Category | null>(null);
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const supabase = createClient();

  const categorySlug = params.slug as string;

  useEffect(() => {
    if (categorySlug) {
      fetchCategory();
      fetchPosts();
    }
  }, [categorySlug, searchTerm, currentPage]);

  const fetchCategory = async () => {
    try {
      const { data, error } = await supabase
        .from('blog_categories')
        .select('id, name, slug, description, color, post_count')
        .eq('slug', categorySlug)
        .eq('is_active', true)
        .single();

      if (error || !data) {
        throw new Error('Category not found');
      }

      setCategory(data);
    } catch (error) {
      console.error('Error fetching category:', error);
      toast({
        title: "Error",
        description: "Category not found",
        variant: "destructive",
      });
      router.push('/blog');
    }
  };

  const fetchPosts = async () => {
    try {
      setIsLoading(true);
      
      let query = supabase
        .from('blog_articles')
        .select(`
          id, title, slug, excerpt, featured_image, is_featured, views, 
          likes, comments_count, read_time, tags, published_at,
          users (full_name),
          blog_categories!inner (slug)
        `, { count: 'exact' })
        .eq('is_published', true)
        .eq('blog_categories.slug', categorySlug);

      // Apply search filter
      if (searchTerm) {
        query = query.or(`title.ilike.%${searchTerm}%,excerpt.ilike.%${searchTerm}%`);
      }

      // Pagination
      const limit = 12;
      const from = (currentPage - 1) * limit;
      const to = from + limit - 1;

      const { data, error, count } = await query
        .order('published_at', { ascending: false })
        .range(from, to);

      if (error) throw error;
      
      setPosts(data || []);
      setTotalPages(Math.ceil((count || 0) / limit));
    } catch (error) {
      console.error('Error fetching posts:', error);
      toast({
        title: "Error",
        description: "Failed to load articles",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  if (!category && !isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
        <div className="container mx-auto px-4 py-12">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-2xl font-bold text-foreground mb-4">Category not found</h1>
            <p className="text-muted-foreground mb-6">The category you're looking for doesn't exist.</p>
            <Button asChild className="bg-primary hover:bg-primary/90 text-primary-foreground min-h-[44px]">
              <Link href="/blog">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Blog
              </Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      <div className="container mx-auto px-4 py-12">
        {/* Back Button */}
        <Button 
          asChild 
          variant="ghost" 
          className="mb-6 glass-effect border border-white/20 hover:glass-effect-subtle min-h-[44px]"
        >
          <Link href="/blog">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Blog
          </Link>
        </Button>

        {/* Category Header */}
        {category && (
          <Card className="glass-effect border border-white/10 neo-shadow mb-8">
            <CardContent className="p-8">
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div 
                    className="w-6 h-6 rounded-full border border-white/20"
                    style={{ backgroundColor: category.color }}
                  ></div>
                  <Badge 
                    className="text-lg px-4 py-2"
                    style={{ 
                      backgroundColor: category.color + '20',
                      color: category.color,
                      borderColor: category.color + '50'
                    }}
                  >
                    <FolderOpen className="h-4 w-4 mr-2" />
                    {category.name}
                  </Badge>
                </div>
                
                <h1 className="text-3xl md:text-4xl font-bold text-foreground">
                  {category.name} Articles
                </h1>
                
                {category.description && (
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    {category.description}
                  </p>
                )}
                
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span>{category.post_count} {category.post_count === 1 ? 'article' : 'articles'}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Search */}
        <Card className="glass-effect border border-white/10 neo-shadow mb-8">
          <CardContent className="p-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={`Search ${category?.name || 'category'} articles...`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 glass-effect border-white/20 text-foreground"
              />
            </div>
          </CardContent>
        </Card>

        {/* Posts Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(9)].map((_, i) => (
              <Card key={i} className="glass-effect border border-white/10 neo-shadow animate-pulse">
                <div className="h-48 bg-white/20 rounded-t-2xl"></div>
                <CardContent className="p-6">
                  <div className="space-y-3">
                    <div className="h-4 bg-white/20 rounded"></div>
                    <div className="h-3 bg-white/10 rounded w-3/4"></div>
                    <div className="h-8 bg-white/10 rounded"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : posts.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {posts.map((post) => (
                <Card key={post.id} className="glass-effect border border-white/10 neo-shadow hover:neo-shadow-light transition-all duration-300 group">
                  <div className="relative">
                    <img
                      src={post.featured_image || '/api/placeholder/400/200'}
                      alt={post.title}
                      className="w-full h-48 object-cover rounded-t-2xl"
                    />
                    {post.is_featured && (
                      <Badge className="absolute top-3 left-3 bg-primary/20 text-primary border-primary/30">
                        Featured
                      </Badge>
                    )}
                    {category && (
                      <Badge 
                        className="absolute top-3 right-3"
                        style={{ 
                          backgroundColor: category.color + '20',
                          color: category.color,
                          borderColor: category.color + '50'
                        }}
                      >
                        {category.name}
                      </Badge>
                    )}
                  </div>
                  
                  <CardContent className="p-6">
                    <div className="space-y-3">
                      <div>
                        <h3 className="text-lg font-semibold text-foreground group-hover:text-primary transition-colors line-clamp-2">
                          {post.title}
                        </h3>
                        <p className="text-sm text-muted-foreground mt-2 line-clamp-2">
                          {post.excerpt}
                        </p>
                      </div>
                      
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {post.users?.full_name || 'Tennis Whisperer'}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {formatDate(post.published_at)}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          {formatNumber(post.views)}
                        </div>
                        <div className="flex items-center gap-1">
                          <Heart className="h-3 w-3" />
                          {post.likes}
                        </div>
                        <div className="flex items-center gap-1">
                          <MessageSquare className="h-3 w-3" />
                          {post.comments_count}
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {post.read_time} min
                        </div>
                      </div>
                      
                      <Button asChild variant="outline" className="w-full glass-effect border-white/20 hover:glass-effect-subtle min-h-[44px]">
                        <Link href={`/blog/${post.slug}`}>
                          Read Article
                          <ArrowRight className="h-4 w-4 ml-2" />
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center gap-2">
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="glass-effect border-white/20 hover:glass-effect-subtle min-h-[44px]"
                >
                  Previous
                </Button>
                
                {[...Array(totalPages)].map((_, i) => (
                  <Button
                    key={i + 1}
                    variant={currentPage === i + 1 ? 'default' : 'outline'}
                    onClick={() => setCurrentPage(i + 1)}
                    className="min-h-[44px] min-w-[44px]"
                  >
                    {i + 1}
                  </Button>
                ))}
                
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="glass-effect border-white/20 hover:glass-effect-subtle min-h-[44px]"
                >
                  Next
                </Button>
              </div>
            )}
          </>
        ) : (
          <Card className="glass-effect border border-white/10 neo-shadow">
            <CardContent className="p-12 text-center">
              <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-foreground mb-2">No articles found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm 
                  ? `No articles found matching "${searchTerm}" in ${category?.name || 'this category'}`
                  : `No articles available in ${category?.name || 'this category'} yet`
                }
              </p>
              {searchTerm && (
                <Button 
                  onClick={() => setSearchTerm('')}
                  variant="outline"
                  className="glass-effect border-white/20 hover:glass-effect-subtle min-h-[44px]"
                >
                  Clear Search
                </Button>
              )}
            </CardContent>
          </Card>
        )}

        {/* Category Navigation */}
        <Card className="glass-effect border border-white/10 neo-shadow mt-8">
          <CardContent className="p-6 text-center">
            <h3 className="text-lg font-semibold text-foreground mb-4">Explore More Categories</h3>
            <Button asChild className="bg-primary hover:bg-primary/90 text-primary-foreground min-h-[44px]">
              <Link href="/blog">
                <FolderOpen className="h-4 w-4 mr-2" />
                View All Categories
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
