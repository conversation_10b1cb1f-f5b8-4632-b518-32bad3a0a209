"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Clock,
  Users,
  Plus,
  Loader2,
  AlertCircle,
  Calendar as CalendarIcon,
  Video,
  Edit,
  Trash2
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { createBrowserClient } from '@supabase/ssr';
import { redirect } from "next/navigation";
import {
  getStudentUpcomingSessions,
  getStudentEnrollments,
  requestSession
} from "@/utils/supabase/mentorship-utils";

export default function SchedulePage() {
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [isNewSessionDialogOpen, setIsNewSessionDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sessions, setSessions] = useState<any[]>([]);
  const [enrollments, setEnrollments] = useState<any[]>([]);
  const [submitting, setSubmitting] = useState(false);

  // Form state
  const [sessionType, setSessionType] = useState("");
  const [preferredDate, setPreferredDate] = useState("");
  const [preferredTime, setPreferredTime] = useState("");
  const [selectedEnrollment, setSelectedEnrollment] = useState("");
  const [notes, setNotes] = useState("");

  const { toast } = useToast();

  useEffect(() => {
    loadScheduleData();
  }, []);

  const loadScheduleData = async () => {
    try {
      setLoading(true);
      setError(null);

      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        redirect("/auth/sign-in");
        return;
      }

      // Load sessions and enrollments
      const [sessionsResult, enrollmentsResult] = await Promise.all([
        getStudentUpcomingSessions(user.id),
        getStudentEnrollments(user.id)
      ]);

      if (sessionsResult.error) {
        throw new Error('Failed to load sessions');
      }

      if (enrollmentsResult.error) {
        throw new Error('Failed to load enrollments');
      }

      setSessions(sessionsResult.data || []);
      setEnrollments(enrollmentsResult.data || []);
    } catch (error: any) {
      console.error('Error loading schedule data:', error);
      setError(error.message || 'Failed to load schedule data');
    } finally {
      setLoading(false);
    }
  };

  const handleSessionRequest = async () => {
    if (!sessionType || !preferredDate || !preferredTime || !selectedEnrollment) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      setSubmitting(true);

      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        redirect("/auth/sign-in");
        return;
      }

      const { data, error } = await requestSession({
        student_id: user.id,
        enrollment_id: selectedEnrollment,
        preferred_date: preferredDate,
        preferred_time: preferredTime,
        session_type: sessionType,
        notes: notes
      });

      if (error) {
        throw new Error('Failed to request session');
      }

      toast({
        title: "Session Requested",
        description: "Your session request has been submitted successfully",
      });

      // Reset form and close dialog
      setSessionType("");
      setPreferredDate("");
      setPreferredTime("");
      setSelectedEnrollment("");
      setNotes("");
      setIsNewSessionDialogOpen(false);

      // Reload data
      loadScheduleData();
    } catch (error: any) {
      console.error('Error requesting session:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to request session",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading your schedule...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Schedule</h1>
        <Dialog open={isNewSessionDialogOpen} onOpenChange={setIsNewSessionDialogOpen}>
          <DialogTrigger asChild>
            <Button disabled={enrollments.length === 0}>
              <Plus className="h-4 w-4 mr-2" />
              New Session
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-card max-w-md">
            <DialogHeader>
              <DialogTitle>Request New Session</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-4">
              {enrollments.length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">
                  <CalendarIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No active enrollments found</p>
                  <p className="text-sm">You need to be enrolled in a program to schedule sessions</p>
                </div>
              ) : (
                <>
                  <div className="space-y-2">
                    <Label>Program/Mentor</Label>
                    <Select value={selectedEnrollment} onValueChange={setSelectedEnrollment}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select program" />
                      </SelectTrigger>
                      <SelectContent>
                        {enrollments.map((enrollment: any) => (
                          <SelectItem key={enrollment.id} value={enrollment.id}>
                            {enrollment.program?.name || 'Mentorship Program'} - {enrollment.mentor?.user?.full_name || 'Mentor'}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Session Type</Label>
                    <Select value={sessionType} onValueChange={setSessionType}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="training">Training Session</SelectItem>
                        <SelectItem value="review">Progress Review</SelectItem>
                        <SelectItem value="consultation">Consultation</SelectItem>
                        <SelectItem value="practice">Practice Session</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Preferred Date</Label>
                    <Input
                      type="date"
                      value={preferredDate}
                      onChange={(e) => setPreferredDate(e.target.value)}
                      min={new Date().toISOString().split('T')[0]}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Preferred Time</Label>
                    <Input
                      type="time"
                      value={preferredTime}
                      onChange={(e) => setPreferredTime(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Notes (Optional)</Label>
                    <Textarea
                      placeholder="Any specific focus areas or requests"
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      rows={3}
                    />
                  </div>

                  <Button
                    className="w-full"
                    onClick={handleSessionRequest}
                    disabled={submitting}
                  >
                    {submitting ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Requesting...
                      </>
                    ) : (
                      'Request Session'
                    )}
                  </Button>
                </>
              )}
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6">
          <Calendar
            mode="single"
            selected={date}
            onSelect={setDate}
            className="rounded-md border bg-card"
          />
        </Card>

        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Upcoming Sessions</h2>
          <ScrollArea className="h-[400px] pr-4">
            <div className="space-y-4">
              {sessions.length > 0 ? (
                sessions.map((session: any) => (
                  <Card key={session.id} className="p-4 bg-card hover:bg-accent transition-colors">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-semibold">
                            {session.notes || 'Mentorship Session'}
                          </h4>
                          <Badge
                            variant={
                              session.status === 'scheduled' ? 'default' :
                              session.status === 'completed' ? 'secondary' :
                              'destructive'
                            }
                          >
                            {session.status}
                          </Badge>
                        </div>
                        <div className="text-sm text-muted-foreground space-y-1">
                          <p className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            {new Date(session.scheduled_at).toLocaleDateString()} at{' '}
                            {new Date(session.scheduled_at).toLocaleTimeString([], {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </p>
                          <p className="flex items-center">
                            <Users className="h-4 w-4 mr-1" />
                            {session.enrollment?.mentor?.user?.full_name || 'Mentor'}
                          </p>
                          <p className="flex items-center">
                            <CalendarIcon className="h-4 w-4 mr-1" />
                            {session.enrollment?.program?.name || 'Mentorship Program'}
                          </p>
                        </div>
                      </div>
                      <div className="flex flex-col gap-2 ml-4">
                        {session.status === 'scheduled' && (
                          <>
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4 mr-1" />
                              Reschedule
                            </Button>
                            <Button variant="default" size="sm">
                              <Video className="h-4 w-4 mr-1" />
                              Join
                            </Button>
                          </>
                        )}
                        {session.status === 'completed' && (
                          <Button variant="outline" size="sm" disabled>
                            Completed
                          </Button>
                        )}
                      </div>
                    </div>
                  </Card>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <CalendarIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No upcoming sessions</p>
                  <p className="text-sm">Request a session with your mentor to get started</p>
                </div>
              )}
            </div>
          </ScrollArea>
        </Card>
      </div>
    </div>
  );
}
