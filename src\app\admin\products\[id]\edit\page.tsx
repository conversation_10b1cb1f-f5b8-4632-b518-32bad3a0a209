'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FileUpload } from "@/components/ui/file-upload";
import { FeatureInput } from "@/components/ui/feature-input";
import { useProduct, useUpdateProduct } from "@/hooks/useProducts";
import { useCategories } from "@/hooks/useCategories";
import { uploadMultipleFilesClient as uploadMultipleFiles } from "@/utils/supabase/storage-client";
import { createClient } from "@/utils/supabase/client";
import { redirect, useRouter } from "next/navigation";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { useState, useEffect } from "react";

export default function EditProductPage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [urlImages, setUrlImages] = useState<string[]>([]);
  const [existingImages, setExistingImages] = useState<string[]>([]);
  const [removedImageIndices, setRemovedImageIndices] = useState<number[]>([]);
  const [replaceAllImages, setReplaceAllImages] = useState(false);
  const [features, setFeatures] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Use React Query hooks
  const { data: product, isLoading: productLoading, error: productError } = useProduct(params.id);
  const { data: categories = [], isLoading: categoriesLoading } = useCategories();
  const updateProductMutation = useUpdateProduct();
  
  // Form data
  const [formData, setFormData] = useState({
    name: '',
    price: '',
    description: '',
    category: '',
    stock: '10'
  });

  // Update form data when product loads
  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name,
        price: product.price.toString(),
        description: product.description || '',
        category: product.category,
        stock: product.stock.toString()
      });

      // Set existing images if available
      if (product.images && product.images.length > 0) {
        setExistingImages(product.images);
      } else if (product.image) {
        // For backward compatibility
        setExistingImages([product.image]);
      }

      // Set features if available
      if (product.features && product.features.length > 0) {
        setFeatures(product.features);
      }
    }
  }, [product]);

  // Handle product loading error
  useEffect(() => {
    if (productError) {
      setError('Failed to load product data');
    }
  }, [productError]);
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle file selection
  const handleFilesSelected = (files: File[]) => {
    setUploadedFiles(prev => [...prev, ...files]);
  };

  // Handle URL addition
  const handleUrlsAdded = (urls: string[]) => {
    setUrlImages(prev => [...prev, ...urls]);
  };

  // Handle file removal (new files)
  const handleFileRemoved = (index: number) => {
    setUploadedFiles(prev => {
      const newFiles = [...prev];
      newFiles.splice(index, 1);
      return newFiles;
    });
  };
  
  // Handle existing image removal
  const handleExistingImageRemoved = (index: number) => {
    setRemovedImageIndices(prev => [...prev, index]);
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    try {
      const { name, price, description, category, stock } = formData;

      // Validate required fields
      if (!name || !price || !category || !stock) {
        throw new Error('Please fill in all required fields');
      }

      // Determine status based on stock
      const stockNum = parseInt(stock);
      let status = "In Stock";
      if (stockNum === 0) {
        status = "Out of Stock";
      } else if (stockNum < 10) {
        status = "Low Stock";
      }

      // Upload new images if any
      let newImageUrls: string[] = [];
      if (uploadedFiles.length > 0) {
        newImageUrls = await uploadMultipleFiles(uploadedFiles);
      }

      // Handle image replacement logic
      let allImages: string[] = [];

      if (replaceAllImages && (newImageUrls.length > 0 || urlImages.length > 0)) {
        // Replace all existing images with new ones and URLs
        allImages = [...newImageUrls, ...urlImages];
      } else {
        // Filter out removed existing images and combine with new images and URLs
        const remainingExistingImages = existingImages.filter((_, index) =>
          !removedImageIndices.includes(index)
        );

        // Combine all images: existing (remaining) + new files + URLs
        allImages = [...remainingExistingImages, ...newImageUrls, ...urlImages];
      }

      // Update product using React Query mutation
      await updateProductMutation.mutateAsync({
        id: params.id,
        name,
        price: parseFloat(price),
        description,
        image: allImages[0] || '', // Set first image as main image for backward compatibility
        images: allImages,
        category,
        stock: stockNum,
        status
        // features field removed as it doesn't exist in the database schema
      });

      // Redirect to products page on success
      router.push("/admin/products");
    } catch (error: any) {
      console.error('Error updating product:', error);
      setError(error.message || 'Failed to update product');
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Link href="/admin/products">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-3xl font-bold tracking-tight">Edit Product</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Product Details</CardTitle>
          <CardDescription>
            {product ? `Update the details for ${product.name}.` : 'Loading product details...'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {productLoading ? (
            <div className="flex justify-center items-center py-12">
              <p className="text-muted-foreground">Loading product data...</p>
            </div>
          ) : error || productError ? (
            <div className="bg-destructive/15 text-destructive p-3 rounded-md mb-6">
              {error || productError?.message || 'Failed to load product'}
            </div>
          ) : product && (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Product Name</Label>
                  <Input 
                    id="name" 
                    name="name" 
                    value={formData.name}
                    onChange={handleInputChange}
                    required 
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="price">Price (R)</Label>
                  <Input 
                    id="price" 
                    name="price" 
                    type="number" 
                    step="0.01" 
                    value={formData.price}
                    onChange={handleInputChange}
                    required 
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  {categories.length > 0 ? (
                    <Select 
                      name="category" 
                      value={formData.category}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.id} value={category.name}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <Input 
                      id="category" 
                      name="category" 
                      value={formData.category}
                      onChange={handleInputChange}
                      required 
                    />
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="stock">Stock Quantity</Label>
                  <Input 
                    id="stock" 
                    name="stock" 
                    type="number" 
                    value={formData.stock}
                    onChange={handleInputChange}
                    required 
                  />
                </div>
                
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea 
                    id="description" 
                    name="description" 
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={5}
                  />
                </div>
                
                <div className="space-y-2 md:col-span-2">
                  <Label>Product Images</Label>

                  {existingImages.length > 0 && (
                    <div className="flex items-center space-x-2 mb-3">
                      <Checkbox
                        id="replace-all-images"
                        checked={replaceAllImages}
                        onCheckedChange={(checked) => setReplaceAllImages(checked as boolean)}
                      />
                      <Label htmlFor="replace-all-images" className="text-sm font-normal">
                        Replace all existing images with new uploads
                      </Label>
                    </div>
                  )}

                  <FileUpload
                    onFilesSelected={handleFilesSelected}
                    onUrlsAdded={handleUrlsAdded}
                    onFileRemoved={handleFileRemoved}
                    onExistingImageRemoved={handleExistingImageRemoved}
                    previewUrls={replaceAllImages ? [] : existingImages.filter((_, index) => !removedImageIndices.includes(index))}
                    maxFiles={5}
                    label="Upload product images"
                    allowUrls={true}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Upload up to 5 images. The first image will be used as the main product image.
                    {existingImages.length > 0 && !replaceAllImages && (
                      <span className="block mt-1">
                        Tip: Check "Replace all existing images" to completely replace current images, or remove individual images by clicking the X button.
                      </span>
                    )}
                  </p>
                </div>
                
                <div className="space-y-2 md:col-span-2">
                  <Label>Product Features</Label>
                  <FeatureInput 
                    features={features}
                    onChange={setFeatures}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Add key features of the product to highlight its selling points.
                  </p>
                </div>
              </div>
              
              <div className="flex justify-end gap-4">
                <Link href="/admin/products">
                  <Button type="button" variant="outline">Cancel</Button>
                </Link>
                <Button
                  type="submit"
                  disabled={updateProductMutation.isPending}
                  className={updateProductMutation.isPending ? 'opacity-70' : ''}
                >
                  {updateProductMutation.isPending ? 'Updating...' : 'Update Product'}
                </Button>
              </div>
            </form>
          )}
        
        </CardContent>
      </Card>
    </div>
  );
}