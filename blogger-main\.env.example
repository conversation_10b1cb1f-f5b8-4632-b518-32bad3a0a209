# 🔑 THABO BESTER PROJECT - ENVIRONMENT VARIABLES
# Copy this file to .env and fill in your actual values

# ===== DATABASE CONFIGURATION =====
VITE_SUPABASE_URL=https://nugwxsejlxsyppzckcrw.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.yipbiDY3z7HcClchaOolPvG9_pcgXaTcZpKB_LM64Z0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.I2OLSV1JrIS4VWkQR2IV-8sdUaN6j7Jk2JaLH24Wsiw

# ===== SOCIAL AUTHENTICATION =====
# Configure these in your Supabase Dashboard under Authentication > Providers

# Google OAuth
# 1. Go to Google Cloud Console (https://console.cloud.google.com/)
# 2. Create OAuth 2.0 credentials
# 3. Add authorized redirect URI: https://nugwxsejlxsyppzckcrw.supabase.co/auth/v1/callback
# 4. Add the Client ID and Secret in Supabase Dashboard
VITE_GOOGLE_CLIENT_ID=your_google_client_id

# GitHub OAuth
# 1. Go to GitHub Settings > Developer settings > OAuth Apps
# 2. Create a new OAuth App
# 3. Set Authorization callback URL: https://nugwxsejlxsyppzckcrw.supabase.co/auth/v1/callback
# 4. Add the Client ID and Secret in Supabase Dashboard
VITE_GITHUB_CLIENT_ID=your_github_client_id

# Facebook OAuth
# 1. Go to Facebook Developers (https://developers.facebook.com/)
# 2. Create a new app
# 3. Add Facebook Login product
# 4. Set Valid OAuth Redirect URIs: https://nugwxsejlxsyppzckcrw.supabase.co/auth/v1/callback
# 5. Add the App ID and Secret in Supabase Dashboard
VITE_FACEBOOK_APP_ID=your_facebook_app_id

# Twitter OAuth
# 1. Go to Twitter Developer Portal (https://developer.twitter.com/)
# 2. Create a new app
# 3. Set Callback URL: https://nugwxsejlxsyppzckcrw.supabase.co/auth/v1/callback
# 4. Add the API Key and Secret in Supabase Dashboard
VITE_TWITTER_API_KEY=your_twitter_api_key

# ===== ERROR TRACKING & MONITORING =====
VITE_SENTRY_DSN=your_sentry_dsn_here
SENTRY_AUTH_TOKEN=your_sentry_auth_token_here
VITE_ENABLE_ERROR_TRACKING=true

# ===== PAYMENT PROCESSING (YOCO - SOUTH AFRICAN) =====
VITE_YOCO_PUBLIC_KEY=your_yoco_public_key_here
YOCO_SECRET_KEY=your_yoco_secret_key_here
VITE_YOCO_WEBHOOK_SECRET=your_yoco_webhook_secret_here
VITE_PAYMENT_CURRENCY=ZAR
VITE_PAYMENT_PROVIDER=yoco

# ===== ANALYTICS & TRACKING =====
VITE_GOOGLE_ANALYTICS_ID=your_ga_id_here
VITE_ENABLE_ANALYTICS=true

# ===== APPLICATION URLS =====
VITE_APP_URL=http://localhost:5173
VITE_API_URL=http://localhost:5173/api
VITE_PRODUCTION_URL=https://your-domain.com

# ===== REAL-TIME FEATURES =====
VITE_ENABLE_REALTIME=true
VITE_REALTIME_CHANNELS=articles,comments,likes,notifications

# ===== PERFORMANCE & CACHING =====
VITE_ENABLE_CACHING=true
VITE_CACHE_DURATION=300000
VITE_ENABLE_OPTIMISTIC_UI=true

# ===== SECURITY =====
VITE_ENABLE_RATE_LIMITING=true
VITE_MAX_REQUESTS_PER_MINUTE=100

# ===== DEVELOPMENT =====
VITE_NODE_ENV=development
VITE_DEBUG_MODE=true
VITE_LOG_LEVEL=info

# ===== MEDIA & STORAGE =====
VITE_MAX_FILE_SIZE=52428800
VITE_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp

# ===== THEME CONFIGURATION =====
VITE_THEME_PRIMARY_COLOR=#FFD700
VITE_THEME_SECONDARY_COLOR=#000000
VITE_THEME_ACCENT_COLOR=#B8860B
