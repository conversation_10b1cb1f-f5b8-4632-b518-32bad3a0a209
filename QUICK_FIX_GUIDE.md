# 🚨 QUICK FIX: "Database error saving new user"

## The Problem
You're getting "Database error saving new user" when trying to sign up as admin because your database schema doesn't match what the admin sign-up code expects.

## The Solution (3 Steps)

### Step 1: Diagnose the Issue
1. Go to **Supabase Dashboard** → **SQL Editor**
2. Copy and paste the contents of `diagnose-database-schema.sql`
3. **Run the script**
4. Look for any ❌ or ⚠️ symbols in the results

### Step 2: Fix the Schema
1. In the same **SQL Editor**
2. Copy and paste the contents of `fix-admin-database-schema.sql`
3. **Run the script**
4. Wait for it to complete (should show success messages)

### Step 3: Test Admin Sign-Up
1. Go to `/admin/sign-up` in your app
2. Fill in the form:
   - **Access Code**: `TENNIS_ADMIN_2024`
   - Use any valid email and password
3. Submit the form
4. **Expected Result**: Should work without "Database error saving new user"

## What the Fix Does

The `fix-admin-database-schema.sql` script will:
- ✅ Create the correct `user_role` enum with values: `user`, `admin`, `student`, `mentor`
- ✅ Fix the `users` table structure to match what the code expects
- ✅ Make `token_identifier` nullable (this was causing the insert to fail)
- ✅ Set up proper Row Level Security (RLS) policies
- ✅ Create the trigger function for automatic user profile creation
- ✅ Grant necessary permissions

## If It Still Doesn't Work

1. **Check the browser console** for detailed error messages
2. **Check Supabase logs** in your dashboard
3. **Run `verify-admin-auth-setup.sql`** to confirm everything is set up correctly
4. **Look at the enhanced error logging** in the admin sign-up action

## Files You Need

1. **`diagnose-database-schema.sql`** - Run this first to see what's wrong
2. **`fix-admin-database-schema.sql`** - Run this to fix the issues
3. **`verify-admin-auth-setup.sql`** - Run this to verify the fix worked

## Why This Happened

Your codebase has multiple conflicting database schema definitions:
- `admin-auth-setup.sql` expects one structure
- `database-setup-complete.sql` has a different structure  
- `initial-setup.sql` has yet another structure

The fix script creates a unified schema that works with all the authentication code.

## After the Fix

Once this is working:
- ✅ Admin sign-up will work correctly
- ✅ Admin users will be redirected to `/admin` dashboard
- ✅ Regular users will still work normally
- ✅ All authentication flows will be consistent

## Quick Test Checklist

- [ ] Run `diagnose-database-schema.sql`
- [ ] Run `fix-admin-database-schema.sql`  
- [ ] Test admin sign-up at `/admin/sign-up`
- [ ] Verify admin gets redirected to `/admin` after email verification
- [ ] Test regular user sign-in still works

**Access Code**: `TENNIS_ADMIN_2024`
