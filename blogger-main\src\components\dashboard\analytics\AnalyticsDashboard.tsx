import React from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import {
  BarChart3,
  TrendingUp,
  Users,
  ShoppingCart,
  FileText,
  DollarSign,
  ArrowUpRight,
  ArrowDownRight,
} from "lucide-react";
import { formatCurrency } from "@/utils/currency";

const AnalyticsDashboard = () => {
  return (
    <div className="space-y-6">
      <Card className="bg-white/90 backdrop-blur-sm border border-gray-100 rounded-2xl shadow-sm overflow-hidden">
        <CardHeader className="pb-3">
          <CardTitle className="text-xl font-semibold text-gray-900">
            Analytics Dashboard
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-6">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="content">Content Performance</TabsTrigger>
              <TabsTrigger value="sales">Sales Reports</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <StatCard
                  title="Total Revenue"
                  value="R312,426.80"
                  change={12.5}
                  icon={<DollarSign className="h-5 w-5" />}
                />
                <StatCard
                  title="Total Orders"
                  value="384"
                  change={8.2}
                  icon={<ShoppingCart className="h-5 w-5" />}
                />
                <StatCard
                  title="Total Users"
                  value="2,841"
                  change={24.5}
                  icon={<Users className="h-5 w-5" />}
                />
                <StatCard
                  title="Content Views"
                  value="28.4k"
                  change={-3.2}
                  icon={<FileText className="h-5 w-5" />}
                />
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base font-medium">
                      Revenue Over Time
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="h-[240px] flex items-center justify-center bg-gray-50 rounded-md">
                      <BarChart3 className="h-16 w-16 text-gray-300" />
                      <span className="ml-2 text-gray-400">Revenue Chart</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base font-medium">
                      User Growth
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="h-[240px] flex items-center justify-center bg-gray-50 rounded-md">
                      <TrendingUp className="h-16 w-16 text-gray-300" />
                      <span className="ml-2 text-gray-400">
                        User Growth Chart
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="content" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <StatCard
                  title="Total Articles"
                  value="124"
                  change={5.3}
                  icon={<FileText className="h-5 w-5" />}
                />
                <StatCard
                  title="Premium Content"
                  value="48"
                  change={12.7}
                  icon={<FileText className="h-5 w-5" />}
                />
                <StatCard
                  title="Avg. Read Time"
                  value="4:32"
                  change={1.2}
                  icon={<FileText className="h-5 w-5" />}
                />
              </div>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base font-medium">
                    Top Performing Content
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-4">
                    <ContentPerformanceRow
                      title="10 Tips for Better Web Design"
                      views={12453}
                      engagement={8.7}
                      isPremium={true}
                    />
                    <ContentPerformanceRow
                      title="Introduction to React Hooks"
                      views={8932}
                      engagement={7.2}
                      isPremium={false}
                    />
                    <ContentPerformanceRow
                      title="Advanced CSS Techniques"
                      views={7245}
                      engagement={6.5}
                      isPremium={true}
                    />
                    <ContentPerformanceRow
                      title="JavaScript Performance Optimization"
                      views={6821}
                      engagement={9.1}
                      isPremium={false}
                    />
                    <ContentPerformanceRow
                      title="Building Responsive Layouts"
                      views={5932}
                      engagement={7.8}
                      isPremium={false}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="sales" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <StatCard
                  title="Monthly Revenue"
                  value="R104,285.90"
                  change={15.3}
                  icon={<DollarSign className="h-5 w-5" />}
                />
                <StatCard
                  title="Avg. Order Value"
                  value="R832.48"
                  change={3.7}
                  icon={<ShoppingCart className="h-5 w-5" />}
                />
                <StatCard
                  title="Conversion Rate"
                  value="3.2%"
                  change={0.5}
                  icon={<TrendingUp className="h-5 w-5" />}
                />
                <StatCard
                  title="Subscriptions"
                  value="284"
                  change={18.2}
                  icon={<Users className="h-5 w-5" />}
                />
              </div>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base font-medium">
                    Top Selling Products
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-4">
                    <ProductPerformanceRow
                      name="Web Development Course"
                      price={4999.99}
                      sales={48}
                      type="digital"
                    />
                    <ProductPerformanceRow
                      name="Premium Article Bundle"
                      price={749.99}
                      sales={124}
                      type="digital"
                    />
                    <ProductPerformanceRow
                      name="Branded Notebook"
                      price={624.99}
                      sales={87}
                      type="physical"
                    />
                    <ProductPerformanceRow
                      name="Developer T-Shirt"
                      price={499.99}
                      sales={65}
                      type="physical"
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

interface StatCardProps {
  title: string;
  value: string;
  change: number;
  icon: React.ReactNode;
}

const StatCard = ({ title, value, change, icon }: StatCardProps) => {
  const isPositive = change >= 0;

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="bg-blue-50 p-2 rounded-full">
            <div className="text-blue-600">{icon}</div>
          </div>
          <div
            className={`flex items-center ${isPositive ? "text-green-600" : "text-red-600"} text-sm font-medium`}
          >
            {isPositive ? (
              <ArrowUpRight className="h-4 w-4 mr-1" />
            ) : (
              <ArrowDownRight className="h-4 w-4 mr-1" />
            )}
            {Math.abs(change)}%
          </div>
        </div>
        <div className="mt-4">
          <p className="text-sm font-medium text-gray-500">{title}</p>
          <p className="text-2xl font-bold mt-1">{value}</p>
        </div>
      </CardContent>
    </Card>
  );
};

interface ContentPerformanceRowProps {
  title: string;
  views: number;
  engagement: number;
  isPremium: boolean;
}

const ContentPerformanceRow = ({
  title,
  views,
  engagement,
  isPremium,
}: ContentPerformanceRowProps) => {
  return (
    <div className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0">
      <div className="flex-1">
        <div className="flex items-center">
          <p className="font-medium text-gray-900">{title}</p>
          {isPremium && (
            <span className="ml-2 px-2 py-0.5 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">
              Premium
            </span>
          )}
        </div>
      </div>
      <div className="flex items-center gap-8">
        <div className="text-right">
          <p className="text-sm text-gray-500">Views</p>
          <p className="font-medium">{views.toLocaleString()}</p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-500">Engagement</p>
          <p className="font-medium">{engagement}/10</p>
        </div>
      </div>
    </div>
  );
};

interface ProductPerformanceRowProps {
  name: string;
  price: number;
  sales: number;
  type: "physical" | "digital";
}

const ProductPerformanceRow = ({
  name,
  price,
  sales,
  type,
}: ProductPerformanceRowProps) => {
  const formattedPrice = formatCurrency(price);

  const revenue = price * sales;
  const formattedRevenue = formatCurrency(revenue);

  return (
    <div className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0">
      <div className="flex-1">
        <div className="flex items-center">
          <p className="font-medium text-gray-900">{name}</p>
          <span
            className={`ml-2 px-2 py-0.5 text-xs font-medium rounded-full ${type === "digital" ? "bg-blue-100 text-blue-800" : "bg-amber-100 text-amber-800"}`}
          >
            {type}
          </span>
        </div>
        <p className="text-sm text-gray-500">{formattedPrice}</p>
      </div>
      <div className="flex items-center gap-8">
        <div className="text-right">
          <p className="text-sm text-gray-500">Units Sold</p>
          <p className="font-medium">{sales}</p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-500">Revenue</p>
          <p className="font-medium">{formattedRevenue}</p>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
