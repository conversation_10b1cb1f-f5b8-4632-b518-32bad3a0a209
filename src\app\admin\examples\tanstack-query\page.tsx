'use client';

import { ProductListWithQuery } from '@/components/examples/product-list-with-query';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

/**
 * TanStack Query Example Page
 * 
 * This page demonstrates the integration of TanStack Query with Supabase and Zod.
 * It showcases how to use these tools for data fetching, mutations, and validation.
 */
export default function TanStackQueryExamplePage() {
  return (
    <div className="container py-10 space-y-8">
      <div className="flex items-center gap-2">
        <Link href="/admin">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-3xl font-bold tracking-tight">TanStack Query with Supabase & Zod</h1>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Implementation Overview</CardTitle>
          <CardDescription>
            This page demonstrates the implementation of TanStack Query, Supabase, and Zod for data fetching and validation.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">TanStack Query</h3>
              <p className="text-sm text-muted-foreground">
                Provides powerful data fetching, caching, synchronization, and update capabilities.
                Reduces boilerplate code and improves user experience with loading states and error handling.
              </p>
            </div>
            
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Zod</h3>
              <p className="text-sm text-muted-foreground">
                TypeScript-first schema validation with static type inference.
                Ensures data integrity and provides runtime type checking.
              </p>
            </div>
            
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Supabase</h3>
              <p className="text-sm text-muted-foreground">
                Open-source Firebase alternative with PostgreSQL database.
                Provides authentication, real-time subscriptions, and storage.
              </p>
            </div>
          </div>
          
          <div className="pt-4 border-t">
            <h3 className="text-lg font-semibold mb-2">Key Files</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <strong className="font-medium">/src/lib/zod-schemas.ts</strong> - Zod schemas for database tables
              </li>
              <li>
                <strong className="font-medium">/src/lib/tanstack-query.ts</strong> - Custom hooks for TanStack Query with Supabase
              </li>
              <li>
                <strong className="font-medium">/src/providers/query-provider.tsx</strong> - QueryClientProvider for the application
              </li>
              <li>
                <strong className="font-medium">/src/components/examples/product-list-with-query.tsx</strong> - Example component using these tools
              </li>
            </ul>
          </div>
        </CardContent>
      </Card>
      
      <Tabs defaultValue="demo" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="demo">Live Demo</TabsTrigger>
          <TabsTrigger value="usage">Usage Guide</TabsTrigger>
        </TabsList>
        
        <TabsContent value="demo" className="p-4 border rounded-md mt-2">
          <ProductListWithQuery />
        </TabsContent>
        
        <TabsContent value="usage" className="p-4 border rounded-md mt-2 space-y-6">
          <div>
            <h3 className="text-xl font-semibold mb-2">How to Use</h3>
            
            <div className="space-y-4">
              <div>
                <h4 className="text-lg font-medium">1. Define Zod Schemas</h4>
                <pre className="p-4 bg-muted rounded-md overflow-x-auto text-sm mt-2">
                  {`// In /src/lib/zod-schemas.ts
import { z } from 'zod';

export const productSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1, "Product name is required"),
  price: z.number().positive("Price must be positive"),
  // ... other fields
});

export type Product = z.infer<typeof productSchema>;

// For input validation (creating/updating)
export const productInputSchema = productSchema.omit({ 
  id: true, 
  created_at: true, 
  updated_at: true 
});`}
                </pre>
              </div>
              
              <div>
                <h4 className="text-lg font-medium">2. Use TanStack Query Hooks</h4>
                <pre className="p-4 bg-muted rounded-md overflow-x-auto text-sm mt-2">
                  {`// In your component
import { useRecords, useCreateRecord } from '@/lib/tanstack-query';
import { productSchema, productInputSchema } from '@/lib/zod-schemas';

// Fetch products
const { data: products, isLoading, isError } = useRecords(
  'products', 
  productSchema
);

// Create product mutation
const createProduct = useCreateRecord('products', productInputSchema);

// Use the mutation
const handleSubmit = async (data) => {
  try {
    await createProduct.mutateAsync(data);
    // Success handling
  } catch (error) {
    // Error handling
  }
};`}
                </pre>
              </div>
              
              <div>
                <h4 className="text-lg font-medium">3. Set Up the Query Provider</h4>
                <pre className="p-4 bg-muted rounded-md overflow-x-auto text-sm mt-2">
                  {`// In your root layout
import { QueryProvider } from '@/providers/query-provider';

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body>
        <QueryProvider>
          {children}
        </QueryProvider>
      </body>
    </html>
  );
}`}
                </pre>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
