/**
 * TanStack Query utilities for Supabase
 * 
 * This file provides hooks and utilities for using TanStack Query with Supabase.
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { createClient } from '@/utils/supabase/client';
import { z } from 'zod';

/**
 * Custom hook for fetching data from Supabase with TanStack Query
 * 
 * @param key The query key
 * @param fetcher The data fetching function
 * @param options Additional options for the query
 * @returns The query result
 */
export function useSupabaseQuery<T>(
  key: string | string[],
  fetcher: () => Promise<T>,
  options: {
    enabled?: boolean;
    staleTime?: number;
    refetchOnWindowFocus?: boolean;
    retry?: boolean | number;
  } = {}
) {
  const queryKey = Array.isArray(key) ? key : [key];
  
  return useQuery({
    queryKey,
    queryFn: fetcher,
    ...options,
  });
}

/**
 * Custom hook for fetching a single record by ID
 * 
 * @param table The table name
 * @param id The record ID
 * @param schema The Zod schema to validate the data
 * @param options Additional options for the query
 * @returns The query result
 */
export function useRecord<T extends z.ZodType>(
  table: string,
  id: string | undefined,
  schema: T,
  options: {
    enabled?: boolean;
    select?: string;
  } = {}
) {
  const { select = '*', enabled = !!id } = options;
  const supabase = createClient();
  
  return useQuery({
    queryKey: [table, id],
    queryFn: async () => {
      if (!id) throw new Error('ID is required');
      
      const { data, error } = await supabase
        .from(table)
        .select(select)
        .eq('id', id)
        .single();
      
      if (error) throw error;
      
      // Validate with Zod schema
      return schema.parse(data);
    },
    enabled,
  });
}

/**
 * Custom hook for fetching multiple records
 * 
 * @param table The table name
 * @param schema The Zod schema to validate the data
 * @param options Additional options for the query
 * @returns The query result
 */
export function useRecords<T extends z.ZodType>(
  table: string,
  schema: T,
  options: {
    enabled?: boolean;
    select?: string;
    filter?: (query: any) => any;
  } = {}
) {
  const { select = '*', filter, enabled = true } = options;
  const supabase = createClient();
  
  return useQuery({
    queryKey: [table, select, filter ? 'filtered' : 'all'],
    queryFn: async () => {
      let query = supabase.from(table).select(select);
      
      if (filter) {
        query = filter(query);
      }
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      // Validate with Zod schema
      return z.array(schema).parse(data);
    },
    enabled,
  });
}

/**
 * Custom hook for creating a record
 * 
 * @param table The table name
 * @param schema The Zod schema to validate the input data
 * @returns The mutation function and result
 */
export function useCreateRecord<T extends z.ZodType>(
  table: string,
  schema: T
) {
  const queryClient = useQueryClient();
  const supabase = createClient();
  
  return useMutation({
    mutationFn: async (data: z.infer<T>) => {
      // Validate with Zod schema
      const validData = schema.parse(data);
      
      const { data: result, error } = await supabase
        .from(table)
        .insert(validData)
        .select()
        .single();
      
      if (error) throw error;
      
      return result;
    },
    onSuccess: () => {
      // Invalidate queries for this table
      queryClient.invalidateQueries({ queryKey: [table] });
    },
  });
}

/**
 * Custom hook for updating a record
 * 
 * @param table The table name
 * @param schema The Zod schema to validate the input data
 * @returns The mutation function and result
 */
export function useUpdateRecord<T extends z.ZodObject<any>>(
  table: string,
  schema: T
) {
  const queryClient = useQueryClient();
  const supabase = createClient();
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<z.infer<T>> }) => {
      // Create a partial schema for validation
      const partialSchema = schema.partial();
      const validData = partialSchema.parse(data);
      
      const { data: result, error } = await supabase
        .from(table)
        .update(validData)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      
      return result;
    },
    onSuccess: (_, variables) => {
      // Invalidate queries for this table and specific record
      queryClient.invalidateQueries({ queryKey: [table] });
      queryClient.invalidateQueries({ queryKey: [table, variables.id] });
    },
  });
}

/**
 * Custom hook for deleting a record
 * 
 * @param table The table name
 * @returns The mutation function and result
 */
export function useDeleteRecord(table: string) {
  const queryClient = useQueryClient();
  const supabase = createClient();
  
  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from(table)
        .delete()
        .eq('id', id);
      
      if (error) throw error;
      
      return true;
    },
    onSuccess: (_, id) => {
      // Invalidate queries for this table and specific record
      queryClient.invalidateQueries({ queryKey: [table] });
      queryClient.invalidateQueries({ queryKey: [table, id] });
    },
  });
}
