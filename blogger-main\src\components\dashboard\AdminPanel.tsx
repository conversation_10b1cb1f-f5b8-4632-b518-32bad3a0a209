import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import UserManagement from "./user/UserManagement";
import ContentManagement from "./content/ContentManagement";
import ProductManagement from "./ecommerce/ProductManagement";
import OrderManagement from "./ecommerce/OrderManagement";
import AnalyticsDashboard from "./analytics/AnalyticsDashboard";

import SystemMonitoringDashboard from "../monitoring/SystemMonitoringDashboard";
import PerformanceMonitor from "../performance/PerformanceMonitor";
import ConsentManager from "../privacy/ConsentManager";
import { useAuth } from "../../../supabase/auth";

export default function AdminPanel() {
  const [activeTab, setActiveTab] = useState("analytics");
  const { isAdmin } = useAuth();

  if (!isAdmin) {
    return (
      <Card className="bg-white/90 backdrop-blur-sm border border-gray-100 rounded-2xl shadow-sm overflow-hidden">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-gray-900">
            Access Denied
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">
            You don't have permission to access the admin panel.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="bg-white/90 backdrop-blur-sm border border-gray-100 rounded-2xl shadow-sm overflow-hidden">
        <CardHeader className="pb-3">
          <CardTitle className="text-xl font-semibold text-gray-900">
            Admin Dashboard
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs
            defaultValue="analytics"
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <div className="overflow-x-auto mb-6">
              <TabsList className="grid w-full grid-cols-8 min-w-max">
                <TabsTrigger value="analytics">Analytics</TabsTrigger>
                <TabsTrigger value="content">Content</TabsTrigger>
                <TabsTrigger value="products">Products</TabsTrigger>
                <TabsTrigger value="orders">Orders</TabsTrigger>
                <TabsTrigger value="users">Users</TabsTrigger>
                <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
                <TabsTrigger value="performance">Performance</TabsTrigger>
                <TabsTrigger value="privacy">Privacy</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="analytics">
              <AnalyticsDashboard />
            </TabsContent>

            <TabsContent value="content">
              <ContentManagement />
            </TabsContent>

            <TabsContent value="products">
              <ProductManagement />
            </TabsContent>

            <TabsContent value="orders">
              <OrderManagement />
            </TabsContent>

            <TabsContent value="users">
              <UserManagement />
            </TabsContent>

            <TabsContent value="monitoring">
              <SystemMonitoringDashboard />
            </TabsContent>

            <TabsContent value="performance">
              <PerformanceMonitor />
            </TabsContent>

            <TabsContent value="privacy">
              <div className="space-y-6">
                <ConsentManager isOpen={true} onClose={() => {}} mode="full" />
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
