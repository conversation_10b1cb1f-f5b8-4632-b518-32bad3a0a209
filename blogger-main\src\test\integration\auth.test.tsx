import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '../utils/test-utils';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import Login from '../../components/auth/Login';
import Register from '../../components/auth/Register';

// Mock Supabase auth
const mockSignInWithPassword = vi.fn();
const mockSignUp = vi.fn();
const mockSignOut = vi.fn();

vi.mock('../../../supabase/client', () => ({
  supabase: {
    auth: {
      signInWithPassword: mockSignInWithPassword,
      signUp: mockSignUp,
      signOut: mockSignOut,
      getSession: vi.fn(() => Promise.resolve({ data: { session: null }, error: null })),
      onAuthStateChange: vi.fn(() => ({ data: { subscription: { unsubscribe: vi.fn() } } })),
    },
  },
}));

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('Authentication Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Login Flow', () => {
    it('successfully logs in a user', async () => {
      const user = userEvent.setup();
      const mockUser = {
        id: 'test-user-id',
        email: '<EMAIL>',
        user_metadata: { firstName: 'Test', lastName: 'User' },
      };

      mockSignInWithPassword.mockResolvedValue({
        data: {
          user: mockUser,
          session: { access_token: 'token', user: mockUser },
        },
        error: null,
      });

      renderWithRouter(<Login />);

      // Fill in login form
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);

      // Verify API call
      await waitFor(() => {
        expect(mockSignInWithPassword).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
        });
      });
    });

    it('shows error for invalid credentials', async () => {
      const user = userEvent.setup();
      
      mockSignInWithPassword.mockResolvedValue({
        data: { user: null, session: null },
        error: { message: 'Invalid login credentials' },
      });

      renderWithRouter(<Login />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'wrongpassword');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/invalid login credentials/i)).toBeInTheDocument();
      });
    });

    it('validates email format', async () => {
      const user = userEvent.setup();
      renderWithRouter(<Login />);

      const emailInput = screen.getByLabelText(/email/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, 'invalid-email');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/please enter a valid email/i)).toBeInTheDocument();
      });

      expect(mockSignInWithPassword).not.toHaveBeenCalled();
    });

    it('requires password field', async () => {
      const user = userEvent.setup();
      renderWithRouter(<Login />);

      const emailInput = screen.getByLabelText(/email/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/password is required/i)).toBeInTheDocument();
      });

      expect(mockSignInWithPassword).not.toHaveBeenCalled();
    });

    it('handles loading state during login', async () => {
      const user = userEvent.setup();
      
      // Mock a delayed response
      mockSignInWithPassword.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve({
          data: { user: null, session: null },
          error: null,
        }), 100))
      );

      renderWithRouter(<Login />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);

      // Check loading state
      expect(screen.getByText(/signing in/i)).toBeInTheDocument();
      expect(submitButton).toBeDisabled();

      // Wait for completion
      await waitFor(() => {
        expect(screen.queryByText(/signing in/i)).not.toBeInTheDocument();
      });
    });
  });

  describe('Registration Flow', () => {
    it('successfully registers a new user', async () => {
      const user = userEvent.setup();
      const mockUser = {
        id: 'new-user-id',
        email: '<EMAIL>',
        user_metadata: { firstName: 'New', lastName: 'User' },
      };

      mockSignUp.mockResolvedValue({
        data: {
          user: mockUser,
          session: { access_token: 'token', user: mockUser },
        },
        error: null,
      });

      renderWithRouter(<Register />);

      // Fill in registration form
      const firstNameInput = screen.getByLabelText(/first name/i);
      const lastNameInput = screen.getByLabelText(/last name/i);
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const termsCheckbox = screen.getByLabelText(/terms/i);
      const submitButton = screen.getByRole('button', { name: /create account/i });

      await user.type(firstNameInput, 'New');
      await user.type(lastNameInput, 'User');
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'StrongPass123!');
      await user.type(confirmPasswordInput, 'StrongPass123!');
      await user.click(termsCheckbox);
      await user.click(submitButton);

      // Verify API call
      await waitFor(() => {
        expect(mockSignUp).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'StrongPass123!',
          options: {
            data: {
              firstName: 'New',
              lastName: 'User',
            },
          },
        });
      });
    });

    it('validates password strength', async () => {
      const user = userEvent.setup();
      renderWithRouter(<Register />);

      const passwordInput = screen.getByLabelText(/^password/i);
      const submitButton = screen.getByRole('button', { name: /create account/i });

      await user.type(passwordInput, 'weak');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/password must be at least 8 characters/i)).toBeInTheDocument();
      });

      expect(mockSignUp).not.toHaveBeenCalled();
    });

    it('validates password confirmation', async () => {
      const user = userEvent.setup();
      renderWithRouter(<Register />);

      const passwordInput = screen.getByLabelText(/^password/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /create account/i });

      await user.type(passwordInput, 'StrongPass123!');
      await user.type(confirmPasswordInput, 'DifferentPass123!');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument();
      });

      expect(mockSignUp).not.toHaveBeenCalled();
    });

    it('requires terms acceptance', async () => {
      const user = userEvent.setup();
      renderWithRouter(<Register />);

      const firstNameInput = screen.getByLabelText(/first name/i);
      const lastNameInput = screen.getByLabelText(/last name/i);
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /create account/i });

      await user.type(firstNameInput, 'New');
      await user.type(lastNameInput, 'User');
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'StrongPass123!');
      await user.type(confirmPasswordInput, 'StrongPass123!');
      // Don't check terms checkbox
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/you must accept the terms/i)).toBeInTheDocument();
      });

      expect(mockSignUp).not.toHaveBeenCalled();
    });

    it('shows error for existing email', async () => {
      const user = userEvent.setup();
      
      mockSignUp.mockResolvedValue({
        data: { user: null, session: null },
        error: { message: 'User already registered' },
      });

      renderWithRouter(<Register />);

      const firstNameInput = screen.getByLabelText(/first name/i);
      const lastNameInput = screen.getByLabelText(/last name/i);
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const termsCheckbox = screen.getByLabelText(/terms/i);
      const submitButton = screen.getByRole('button', { name: /create account/i });

      await user.type(firstNameInput, 'Existing');
      await user.type(lastNameInput, 'User');
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'StrongPass123!');
      await user.type(confirmPasswordInput, 'StrongPass123!');
      await user.click(termsCheckbox);
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/user already registered/i)).toBeInTheDocument();
      });
    });

    it('sanitizes input fields', async () => {
      const user = userEvent.setup();
      
      mockSignUp.mockResolvedValue({
        data: {
          user: { id: 'test', email: '<EMAIL>' },
          session: null,
        },
        error: null,
      });

      renderWithRouter(<Register />);

      const firstNameInput = screen.getByLabelText(/first name/i);
      const lastNameInput = screen.getByLabelText(/last name/i);
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const termsCheckbox = screen.getByLabelText(/terms/i);
      const submitButton = screen.getByRole('button', { name: /create account/i });

      // Input with potential XSS
      await user.type(firstNameInput, '<script>alert("xss")</script>John');
      await user.type(lastNameInput, '  Doe  ');
      await user.type(emailInput, '  <EMAIL>  ');
      await user.type(passwordInput, 'StrongPass123!');
      await user.type(confirmPasswordInput, 'StrongPass123!');
      await user.click(termsCheckbox);
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockSignUp).toHaveBeenCalledWith({
          email: '<EMAIL>', // Should be lowercase and trimmed
          password: 'StrongPass123!',
          options: {
            data: {
              firstName: 'John', // Should be sanitized
              lastName: 'Doe', // Should be trimmed
            },
          },
        });
      });
    });
  });

  describe('Form Validation Integration', () => {
    it('shows real-time validation feedback', async () => {
      const user = userEvent.setup();
      renderWithRouter(<Register />);

      const emailInput = screen.getByLabelText(/email/i);
      
      // Type invalid email
      await user.type(emailInput, 'invalid');
      await user.tab(); // Trigger blur event

      await waitFor(() => {
        expect(screen.getByText(/please enter a valid email/i)).toBeInTheDocument();
      });

      // Fix email
      await user.clear(emailInput);
      await user.type(emailInput, '<EMAIL>');
      await user.tab();

      await waitFor(() => {
        expect(screen.queryByText(/please enter a valid email/i)).not.toBeInTheDocument();
      });
    });

    it('prevents submission with validation errors', async () => {
      const user = userEvent.setup();
      renderWithRouter(<Login />);

      const submitButton = screen.getByRole('button', { name: /sign in/i });
      
      // Try to submit empty form
      await user.click(submitButton);

      // Should show validation errors
      await waitFor(() => {
        expect(screen.getByText(/email is required/i)).toBeInTheDocument();
        expect(screen.getByText(/password is required/i)).toBeInTheDocument();
      });

      // Should not call API
      expect(mockSignInWithPassword).not.toHaveBeenCalled();
    });
  });
});
