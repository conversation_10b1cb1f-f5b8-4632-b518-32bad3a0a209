import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../../../supabase/auth";
import { useCart } from "@/contexts/CartContext";
import { useSEO } from "@/hooks/useSEO";
import { Navbar } from "@/components/layout/Navbar";
import HeroSection from "../blog/HeroSection";
import ArticleCard from "../blog/ArticleCard";
import ProductShowcase from "../ecommerce/ProductShowcase";
import { TrendingContent } from "../blog/TrendingContent";
import { Article } from "@/types/blog";
import { Product } from "@/types/ecommerce";
import { blogService, ecommerceService } from "@/lib/supabase-services";
import { Footer } from "@/components/layout/Footer";
import { LoadingScreen } from "@/components/ui/loading-spinner";
import { ScrollToTop } from "@/components/ui/scroll-to-top";
import { PrayerBanner } from "@/components/home/<USER>";
import {
  Search,
  Menu,
  TrendingUp,
  ArrowRight,
} from "lucide-react";

// All data now comes from the database - no mock data





export default function LandingPage() {
  const { addItem } = useCart();
  const [wishlistItems, setWishlistItems] = useState<string[]>([]);
  const [featuredArticle, setFeaturedArticle] = useState<Article | null>(null);
  const [trendingArticles, setTrendingArticles] = useState<Article[]>([]);
  const [latestArticles, setLatestArticles] = useState<Article[]>([]);
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // SEO for homepage
  useSEO({
    title: 'Home',
    description: 'Welcome to Thabo Bester - your premier destination for quality journalism and curated premium products. Discover the latest news, insights, and exclusive shopping experiences.',
    keywords: ['news', 'journalism', 'premium products', 'ecommerce', 'technology', 'business', 'lifestyle', 'shopping', 'articles', 'marketplace'],
    type: 'website',
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);

      // Load blog data
      const [featured, trending, latest, products] = await Promise.all([
        blogService.getFeaturedArticle(),
        blogService.getTrendingArticles(3),
        blogService.getArticles(1, 3),
        ecommerceService.getFeaturedProducts(3)
      ]);

      setFeaturedArticle(featured);
      setTrendingArticles(trending);
      setLatestArticles(latest.articles);
      setFeaturedProducts(products);
    } catch (error) {
      console.error('Error loading data:', error);
      // Show empty state instead of mock data
      setFeaturedArticle(null);
      setTrendingArticles([]);
      setLatestArticles([]);
      setFeaturedProducts([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddToCart = (productId: string) => {
    const product = featuredProducts.find(p => p.id === productId);
    if (product) {
      addItem(product);
    }
  };

  const handleToggleWishlist = (productId: string) => {
    setWishlistItems(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  if (isLoading) {
    return <LoadingScreen text="Loading Thabo Bester..." />;
  }

  return (
    <div className="min-h-screen bg-white text-black">
      <Navbar />

      <main>
        {/* Hero Section with Featured Article */}
        {featuredArticle && (
          <HeroSection
            featuredArticle={featuredArticle}
            trendingArticles={trendingArticles.slice(0, 3)}
          />
        )}

        {/* Daily Prayer Banner */}
        <PrayerBanner />

        {/* Latest Articles Grid */}
        <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="flex justify-between items-center mb-12">
            <div className="flex items-center gap-3">
              <TrendingUp className="h-6 w-6 text-red-500" />
              <h2 className="text-3xl font-bold text-gray-900">Latest Articles</h2>
            </div>
            <Link
              to="/articles"
              className="flex items-center text-blue-600 hover:text-blue-700 font-medium transition-colors"
            >
              View all articles <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {latestArticles.map((article, index) => (
              <ArticleCard
                key={article.id}
                article={article}
                variant={index === 0 ? "featured" : "default"}
              />
            ))}
          </div>
        </section>

        {/* Featured Products Showcase */}
        <ProductShowcase
          title="Featured Products"
          subtitle="Discover our curated selection of premium digital and physical products"
          products={featuredProducts.slice(0, 3)} // Ensure exactly 3 products
          onAddToCart={handleAddToCart}
          onToggleWishlist={handleToggleWishlist}
          wishlistItems={wishlistItems}
        />

        {/* Trending Content - Full Width */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-full px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto">
              <TrendingContent />
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <Footer />

      <ScrollToTop />
    </div>
  );
}
