// 🎨 UNIVERSAL GOLD THEME CONFIGURATION
// Thabo Bester Project - Gold & Black Accent Theme

export const theme = {
  // Primary Colors - Gold Palette
  colors: {
    primary: {
      50: '#FFFEF7',
      100: '#FFFACD',
      200: '#FFF8DC',
      300: '#FFE135',
      400: '#FFD700', // Main Gold
      500: '#F4C430', // Rich Gold
      600: '#DAA520', // Goldenrod
      700: '#B8860B', // Dark Goldenrod
      800: '#996515',
      900: '#7A4F0A',
    },
    
    // Secondary Colors - Black Palette
    secondary: {
      50: '#F8F8F8',
      100: '#E5E5E5',
      200: '#CCCCCC',
      300: '#999999',
      400: '#666666',
      500: '#333333',
      600: '#1A1A1A',
      700: '#0D0D0D',
      800: '#000000', // Pure Black
      900: '#000000',
    },
    
    // Accent Colors
    accent: {
      gold: '#FFD700',
      darkGold: '#B8860B',
      lightGold: '#FFF8DC',
      bronze: '#CD7F32',
      copper: '#B87333',
    },
    
    // Status Colors with Gold Tints
    status: {
      success: '#22C55E',
      warning: '#F59E0B', // Gold-tinted warning
      error: '#EF4444',
      info: '#3B82F6',
    },
    
    // Background Colors
    background: {
      primary: '#FFFFFF',
      secondary: '#FAFAFA',
      tertiary: '#F5F5F5',
      dark: '#1A1A1A',
      gold: '#FFFEF7',
    },
    
    // Text Colors
    text: {
      primary: '#1A1A1A',
      secondary: '#666666',
      tertiary: '#999999',
      inverse: '#FFFFFF',
      gold: '#B8860B',
    },
    
    // Border Colors
    border: {
      light: '#E5E5E5',
      medium: '#CCCCCC',
      dark: '#999999',
      gold: '#FFD700',
    },
  },
  
  // Typography
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      serif: ['Georgia', 'serif'],
      mono: ['JetBrains Mono', 'monospace'],
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
    },
    fontWeight: {
      light: '300',
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800',
    },
  },
  
  // Spacing
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem',
  },
  
  // Border Radius
  borderRadius: {
    none: '0',
    sm: '0.125rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    '2xl': '1rem',
    full: '9999px',
  },
  
  // Shadows with Gold Tints
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    gold: '0 4px 14px 0 rgba(255, 215, 0, 0.39)',
    goldLg: '0 10px 25px -3px rgba(255, 215, 0, 0.3), 0 4px 6px -2px rgba(255, 215, 0, 0.15)',
  },
  
  // Animations
  animations: {
    duration: {
      fast: '150ms',
      normal: '300ms',
      slow: '500ms',
    },
    easing: {
      ease: 'ease',
      easeIn: 'ease-in',
      easeOut: 'ease-out',
      easeInOut: 'ease-in-out',
    },
  },
  
  // Breakpoints
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },
  
  // Component Variants
  components: {
    button: {
      primary: {
        background: '#FFD700',
        color: '#1A1A1A',
        border: '#FFD700',
        hover: {
          background: '#F4C430',
          border: '#F4C430',
        },
      },
      secondary: {
        background: '#1A1A1A',
        color: '#FFFFFF',
        border: '#1A1A1A',
        hover: {
          background: '#333333',
          border: '#333333',
        },
      },
      outline: {
        background: 'transparent',
        color: '#FFD700',
        border: '#FFD700',
        hover: {
          background: '#FFD700',
          color: '#1A1A1A',
        },
      },
    },
    
    card: {
      default: {
        background: '#FFFFFF',
        border: '#E5E5E5',
        shadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      },
      gold: {
        background: '#FFFEF7',
        border: '#FFD700',
        shadow: '0 4px 14px 0 rgba(255, 215, 0, 0.39)',
      },
    },
    
    input: {
      default: {
        background: '#FFFFFF',
        border: '#E5E5E5',
        focus: {
          border: '#FFD700',
          shadow: '0 0 0 3px rgba(255, 215, 0, 0.1)',
        },
      },
    },
  },
} as const;

// CSS Custom Properties for dynamic theming
export const cssVariables = {
  '--color-primary': theme.colors.primary[400],
  '--color-primary-dark': theme.colors.primary[700],
  '--color-secondary': theme.colors.secondary[800],
  '--color-accent': theme.colors.accent.gold,
  '--color-background': theme.colors.background.primary,
  '--color-text': theme.colors.text.primary,
  '--shadow-gold': theme.shadows.gold,
  '--border-radius': theme.borderRadius.md,
  '--font-family': theme.typography.fontFamily.sans.join(', '),
} as const;

// Utility functions
export const getColor = (path: string) => {
  const keys = path.split('.');
  let value: any = theme.colors;
  
  for (const key of keys) {
    value = value?.[key];
  }
  
  return value || '#000000';
};

export const getSpacing = (size: keyof typeof theme.spacing) => {
  return theme.spacing[size];
};

export const getShadow = (size: keyof typeof theme.shadows) => {
  return theme.shadows[size];
};

// Export theme as default
export default theme;
