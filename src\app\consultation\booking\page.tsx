'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { CalendarIcon, Clock, Loader2, User, Mail, Phone, MapPin } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

// UI Components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

// Schema and server action
import { consultationFormSchema, type ConsultationFormValues } from '@/schemas/consultation';
import { createConsultation } from '@/app/actions/createConsultation';

/**
 * Generate time slots from 08:00 to 18:00 in 30-minute intervals
 */
function generateTimeSlots() {
  const slots: string[] = [];
  for (let hour = 8; hour <= 18; hour++) {
    const hourStr = hour.toString().padStart(2, '0');
    slots.push(`${hourStr}:00`);
    if (hour < 18) {
      slots.push(`${hourStr}:30`);
    }
  }
  return slots;
}

export default function ConsultationBookingPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const router = useRouter();

  // Initialize form with React Hook Form + Zod validation
  const form = useForm<ConsultationFormValues>({
    resolver: zodResolver(consultationFormSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      location: '',
      reason: '',
      date: undefined,
      time: '',
    },
  });

  // Generate time slots
  const timeSlots = generateTimeSlots();

  // Handle form submission
  async function onSubmit(data: ConsultationFormValues) {
    try {
      setIsSubmitting(true);

      // Create consultation record in database
      const result = await createConsultation(data);
      
      // Validate result
      if (!result || !result.success) {
        throw new Error(result?.error || 'Failed to create consultation');
      }
      
      if (!result.consultation) {
        throw new Error('Consultation created but no data returned');
      }

      const consultationPrice = 300; // R3.00 in cents (300 cents = R3.00)

      const paymentData = {
        consultationId: result.consultation.id,
        userId: result.consultation.user_id || 'guest-' + Date.now(),
        items: [{
          name: 'Tennis Consultation Session',
          price: consultationPrice,
          quantity: 1,
        }],
        notes: data.reason,
        customerDetails: {
          name: `${data.firstName} ${data.lastName}`,
          email: data.email,
          phone: data.phoneNumber,
        }
      };

      // Process payment through our API
      let paymentResult;
      try {
        const paymentResponse = await fetch('/api/consultation-payment', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(paymentData),
        });

        paymentResult = await paymentResponse.json();
        
        // Log payment result for debugging
        console.log('Payment result:', paymentResult);

        if (!paymentResponse.ok || !paymentResult?.paymentUrl) {
          throw new Error(paymentResult?.error || 'Failed to process payment request');
        }
      } catch (paymentError: any) {
        console.error('Payment processing error:', paymentError);
        throw new Error(`Payment processing failed: ${paymentError.message}`);
      }

      // Generate a reference if one doesn't exist
      const consultationReference = 
        (result?.consultation?.payment_reference) || 
        (paymentResult?.paymentReference) || 
        `CONS-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
      
      console.log('Using consultation reference:', consultationReference);
      
      // Store the consultation data in localStorage for the success page
      const consultationDataForStorage = {
        id: result?.consultation?.id || `temp-${Date.now()}`,
        first_name: data.firstName,
        last_name: data.lastName,
        email: data.email,
        phone_number: data.phoneNumber,
        location: data.location,
        scheduled_date: data.date.toISOString().split('T')[0], // Store as YYYY-MM-DD format
        scheduled_time: data.time,
        reason: data.reason,
        payment_reference: consultationReference,
        payment_amount: paymentResult?.amount || 3, // R3.00 as whole number
        status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      console.log('Storing consultation data in localStorage:', consultationDataForStorage);
      localStorage.setItem('consultation_data', JSON.stringify(consultationDataForStorage));
      
      try {
        // Make sure the success URL has the reference parameter
        if (!paymentResult?.paymentUrl) {
          throw new Error('No payment URL returned from payment processor');
        }
        
        const paymentUrl = new URL(paymentResult.paymentUrl);
        console.log('Original payment URL:', paymentUrl.toString());
        
        const successUrlParam = paymentUrl.searchParams.get('successUrl');
        let finalSuccessUrl;
        
        if (successUrlParam) {
          // If successUrl is in the payment URL params, update it to include the reference
          try {
            const successUrl = new URL(successUrlParam);
            successUrl.searchParams.set('reference', consultationReference);
            finalSuccessUrl = successUrl.toString();
          } catch (urlError) {
            console.error('Error parsing success URL:', urlError);
            // Fallback to creating a new success URL
            const baseUrl = window.location.origin;
            const successUrl = new URL(`${baseUrl}/consultation/success`);
            successUrl.searchParams.set('reference', consultationReference);
            finalSuccessUrl = successUrl.toString();
          }
        } else {
          // If no successUrl in params, create one
          const baseUrl = window.location.origin;
          const successUrl = new URL(`${baseUrl}/consultation/success`);
          successUrl.searchParams.set('reference', consultationReference);
          finalSuccessUrl = successUrl.toString();
        }
        
        // Set the success URL in the payment URL
        paymentUrl.searchParams.set('successUrl', finalSuccessUrl);
        console.log('Modified payment URL:', paymentUrl.toString());
        console.log('Success URL with reference:', finalSuccessUrl);
        
        // Redirect to the modified payment URL
        window.location.href = paymentUrl.toString();
      } catch (urlError) {
        console.error('Error handling payment URL:', urlError);
        toast({
          title: 'URL Error',
          description: 'There was a problem with the payment URL. Please try again.',
          variant: 'destructive',
        });
        setIsSubmitting(false);
      }

    } catch (error: any) {
      // Log detailed error information
      console.error('Error during consultation booking:', error);
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
        code: error.code,
        cause: error.cause,
        toString: error.toString()
      });
      
      // Show appropriate error message based on error type
      let errorMessage = 'An unexpected error occurred';
      
      if (error.message?.includes('<!DOCTYPE') || error.message?.includes('<html')) {
        errorMessage = 'Payment processing error: Unable to connect to payment provider.';
      } else if (error.message?.includes('API returned HTML')) {
        errorMessage = 'Payment processing error: Unable to authenticate with payment provider.';
      } else if (error.message?.includes('Failed to create payment link')) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      toast({
        title: 'Booking Error',
        description: errorMessage,
        variant: 'destructive',
      });

      setIsSubmitting(false);
    }
  }
  
  return (
    <div className="min-h-screen bg-background py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">Book Your Consultation</h1>
          <p className="text-lg text-muted-foreground">
            Schedule a one-hour personal consultation with our tennis experts
          </p>
          <div className="mt-4 text-2xl font-bold text-primary">R3.00</div>
          <p className="text-sm text-muted-foreground">One-time consultation fee</p>
        </div>

        <div className="glass-effect-dark rounded-3xl p-8 neo-shadow">
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Personal Information */}
              <div className="space-y-4">
                <h3 className="text-xl font-medium text-center mb-6">Personal Information</h3>

                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  {/* First Name */}
                  <div className="space-y-2">
                    <div className="relative">
                      <Input
                        id="firstName"
                        placeholder="First Name"
                        {...form.register('firstName')}
                        className={cn(
                          "w-full h-14 pl-12 rounded-2xl bg-muted/30 border-0 neo-shadow-inset placeholder:text-muted-foreground/70",
                          form.formState.errors.firstName && 'border-red-500'
                        )}
                      />
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                        <User className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </div>
                    {form.formState.errors.firstName && (
                      <p className="text-sm text-red-500 ml-2">
                        {form.formState.errors.firstName.message}
                      </p>
                    )}
                  </div>

                  {/* Last Name */}
                  <div className="space-y-2">
                    <div className="relative">
                      <Input
                        id="lastName"
                        placeholder="Last Name"
                        {...form.register('lastName')}
                        className={cn(
                          "w-full h-14 pl-12 rounded-2xl bg-muted/30 border-0 neo-shadow-inset placeholder:text-muted-foreground/70",
                          form.formState.errors.lastName && 'border-red-500'
                        )}
                      />
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                        <User className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </div>
                    {form.formState.errors.lastName && (
                      <p className="text-sm text-red-500 ml-2">
                        {form.formState.errors.lastName.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  {/* Email */}
                  <div className="space-y-2">
                    <div className="relative">
                      <Input
                        id="email"
                        type="email"
                        placeholder="Email Address"
                        {...form.register('email')}
                        className={cn(
                          "w-full h-14 pl-12 rounded-2xl bg-muted/30 border-0 neo-shadow-inset placeholder:text-muted-foreground/70",
                          form.formState.errors.email && 'border-red-500'
                        )}
                      />
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </div>
                    {form.formState.errors.email && (
                      <p className="text-sm text-red-500 ml-2">
                        {form.formState.errors.email.message}
                      </p>
                    )}
                  </div>

                  {/* Phone Number */}
                  <div className="space-y-2">
                    <div className="relative">
                      <Input
                        id="phoneNumber"
                        type="tel"
                        placeholder="Phone Number"
                        {...form.register('phoneNumber')}
                        className={cn(
                          "w-full h-14 pl-12 rounded-2xl bg-muted/30 border-0 neo-shadow-inset placeholder:text-muted-foreground/70",
                          form.formState.errors.phoneNumber && 'border-red-500'
                        )}
                      />
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </div>
                    {form.formState.errors.phoneNumber && (
                      <p className="text-sm text-red-500 ml-2">
                        {form.formState.errors.phoneNumber.message}
                      </p>
                    )}
                  </div>
                </div>

                {/* Location */}
                <div className="space-y-2">
                  <div className="relative">
                    <Input
                      id="location"
                      placeholder="Consultation Location (e.g., Your home, Tennis club, etc.)"
                      {...form.register('location')}
                      className={cn(
                        "w-full h-14 pl-12 rounded-2xl bg-muted/30 border-0 neo-shadow-inset placeholder:text-muted-foreground/70",
                        form.formState.errors.location && 'border-red-500'
                      )}
                    />
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                    </div>
                  </div>
                  {form.formState.errors.location && (
                    <p className="text-sm text-red-500 ml-2">
                      {form.formState.errors.location.message}
                    </p>
                  )}
                </div>
              </div>
              
              {/* Schedule Selection */}
              <div className="space-y-4">
                <h3 className="text-xl font-medium text-center mb-6">Select Date & Time</h3>

                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  {/* Date Picker */}
                  <div className="space-y-2">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            'w-full h-14 justify-start text-left font-normal rounded-2xl bg-muted/30 border-0 neo-shadow-inset',
                            !form.watch('date') && 'text-muted-foreground',
                            form.formState.errors.date && 'border-red-500'
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {form.watch('date') ? (
                            format(form.watch('date'), 'PPP')
                          ) : (
                            <span>Select a date</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={form.watch('date')}
                          onSelect={(date) => {
                            if (date) {
                              form.setValue('date', date);
                              // Close the popover
                              document.body.click();
                            }
                          }}
                          disabled={(date) => date < new Date()}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    {form.formState.errors.date && (
                      <p className="text-sm text-red-500 ml-2">
                        {form.formState.errors.date.message}
                      </p>
                    )}
                  </div>

                  {/* Time Picker */}
                  <div className="space-y-2">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            'w-full h-14 justify-start text-left font-normal rounded-2xl bg-muted/30 border-0 neo-shadow-inset',
                            !form.watch('time') && 'text-muted-foreground',
                            form.formState.errors.time && 'border-red-500'
                          )}
                        >
                          <Clock className="mr-2 h-4 w-4" />
                          {form.watch('time') ? (
                            form.watch('time')
                          ) : (
                            <span>Select a time</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-48 p-0" align="start">
                        <div className="max-h-72 overflow-y-auto p-2">
                          {timeSlots.map((timeSlot) => (
                            <Button
                              key={timeSlot}
                              variant="ghost"
                              className="w-full justify-start font-normal"
                              onClick={() => {
                                form.setValue('time', timeSlot);
                                // Close the popover
                                document.body.click();
                              }}
                            >
                              {timeSlot}
                            </Button>
                          ))}
                        </div>
                      </PopoverContent>
                    </Popover>
                    {form.formState.errors.time && (
                      <p className="text-sm text-red-500 ml-2">
                        {form.formState.errors.time.message}
                      </p>
                    )}
                  </div>
                </div>
              </div>
              
              {/* Reason for Appointment */}
              <div className="space-y-4">
                <h3 className="text-xl font-medium text-center mb-6">Consultation Details</h3>
                <div className="space-y-2">
                  <Textarea
                    id="reason"
                    placeholder="Please describe your tennis goals and what you'd like to discuss during the consultation..."
                    rows={4}
                    {...form.register('reason')}
                    className={cn(
                      "w-full rounded-2xl bg-muted/30 border-0 neo-shadow-inset placeholder:text-muted-foreground/70 p-4",
                      form.formState.errors.reason && 'border-red-500'
                    )}
                  />
                  {form.formState.errors.reason ? (
                    <p className="text-sm text-red-500 ml-2">
                      {form.formState.errors.reason.message}
                    </p>
                  ) : (
                    <p className="text-sm text-muted-foreground ml-2">
                      Please provide at least 10 characters describing your goals.
                    </p>
                  )}
                </div>
              </div>

              <Button
                type="submit"
                className="w-full h-14 rounded-2xl gradient-blue text-white font-semibold text-lg neo-shadow hover:neo-shadow-light transition-neo border-0"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  'Book Consultation - R3.00'
                )}
              </Button>
            </form>

            <div className="text-center text-sm text-muted-foreground mt-6 pt-6 border-t border-border/20">
              <p>Your consultation will be confirmed after payment is completed.</p>
              <p className="mt-2">Secure payment powered by Yoco</p>
            </div>
          </div>
        </div>
      </div>
    );
}
