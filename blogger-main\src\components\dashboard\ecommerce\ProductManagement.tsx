import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON>bsContent } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { InputFile } from "@/components/ui/input-file";
import { DataTable } from "@/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Edit, Trash2, Eye, Plus, Image, Package } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

type Product = {
  id: string;
  name: string;
  price: number;
  type: "physical" | "digital";
  stock: number;
  status: "active" | "draft" | "out-of-stock";
  createdAt: Date;
};

const products: Product[] = [
  {
    id: "1",
    name: "Premium Article Bundle",
    price: 29.99,
    type: "digital",
    stock: 999,
    status: "active",
    createdAt: new Date(2023, 5, 15),
  },
  {
    id: "2",
    name: "Web Development Course",
    price: 199.99,
    type: "digital",
    stock: 999,
    status: "active",
    createdAt: new Date(2023, 5, 10),
  },
  {
    id: "3",
    name: "Branded Notebook",
    price: 24.99,
    type: "physical",
    stock: 45,
    status: "active",
    createdAt: new Date(2023, 5, 5),
  },
  {
    id: "4",
    name: "Developer T-Shirt",
    price: 19.99,
    type: "physical",
    stock: 0,
    status: "out-of-stock",
    createdAt: new Date(2023, 4, 28),
  },
];

export default function ProductManagement() {
  const [activeTab, setActiveTab] = useState("products");
  const [isDigital, setIsDigital] = useState(false);

  const columns: ColumnDef<Product>[] = [
    {
      accessorKey: "name",
      header: "Product Name",
    },
    {
      accessorKey: "price",
      header: "Price",
      cell: ({ row }) => {
        const price = parseFloat(row.getValue("price"));
        const formatted = new Intl.NumberFormat("en-ZA", {
          style: "currency",
          currency: "ZAR",
        }).format(price);
        return <span>{formatted}</span>;
      },
    },
    {
      accessorKey: "type",
      header: "Type",
      cell: ({ row }) => {
        const type = row.getValue("type") as string;
        return (
          <Badge
            className={`${type === "digital" ? "bg-blue-100 text-blue-800" : "bg-amber-100 text-amber-800"}`}
          >
            {type.charAt(0).toUpperCase() + type.slice(1)}
          </Badge>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        let badgeClass = "";

        switch (status) {
          case "active":
            badgeClass = "bg-green-100 text-green-800";
            break;
          case "draft":
            badgeClass = "bg-yellow-100 text-yellow-800";
            break;
          case "out-of-stock":
            badgeClass = "bg-red-100 text-red-800";
            break;
        }

        return (
          <Badge className={badgeClass}>
            {status
              .split("-")
              .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
              .join(" ")}
          </Badge>
        );
      },
    },
    {
      accessorKey: "stock",
      header: "Stock",
      cell: ({ row }) => {
        const stock = parseInt(row.getValue("stock"));
        const type = row.getValue("type") as string;

        if (type === "digital") return <span>∞</span>;
        return <span>{stock}</span>;
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Eye className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Edit className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
    },
  ];

  return (
    <div className="space-y-6">
      <Card className="bg-white/90 backdrop-blur-sm border border-gray-100 rounded-2xl shadow-sm overflow-hidden">
        <CardHeader className="pb-3">
          <CardTitle className="text-xl font-semibold text-gray-900">
            Product Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs
            defaultValue="products"
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="products">Products</TabsTrigger>
              <TabsTrigger value="create">Add Product</TabsTrigger>
            </TabsList>

            <TabsContent value="products" className="space-y-4">
              <DataTable
                columns={columns}
                data={products}
                searchKey="name"
                searchPlaceholder="Search products..."
              />
            </TabsContent>

            <TabsContent value="create" className="space-y-6">
              <div className="grid gap-6">
                <div className="grid gap-3">
                  <Label htmlFor="name">Product Name</Label>
                  <Input id="name" placeholder="Enter product name" />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-3">
                    <Label htmlFor="price">Price ($)</Label>
                    <Input
                      id="price"
                      type="number"
                      min="0"
                      step="0.01"
                      placeholder="0.00"
                    />
                  </div>

                  <div className="grid gap-3">
                    <Label htmlFor="type">Product Type</Label>
                    <Select
                      onValueChange={(value) =>
                        setIsDigital(value === "digital")
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="physical">Physical</SelectItem>
                        <SelectItem value="digital">Digital</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {!isDigital && (
                  <div className="grid gap-3">
                    <Label htmlFor="stock">Stock Quantity</Label>
                    <Input id="stock" type="number" min="0" placeholder="0" />
                  </div>
                )}

                <div className="grid gap-3">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Describe your product..."
                    className="min-h-[120px]"
                  />
                </div>

                <div className="grid gap-3">
                  <Label>Product Image</Label>
                  <InputFile
                    accept="image/*"
                    icon={<Image className="h-4 w-4" />}
                  />
                </div>

                {isDigital && (
                  <div className="grid gap-3">
                    <Label>Digital File</Label>
                    <InputFile icon={<Package className="h-4 w-4" />} />
                  </div>
                )}

                <div className="flex justify-end gap-3">
                  <Button variant="outline">Save as Draft</Button>
                  <Button>Publish Product</Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
