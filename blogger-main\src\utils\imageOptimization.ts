// Advanced Image Optimization for The Chronicle
// Lazy loading, WebP conversion, responsive images, and performance optimization

export interface ImageOptimizationConfig {
  quality?: number;
  format?: 'webp' | 'avif' | 'jpeg' | 'png' | 'auto';
  sizes?: string[];
  lazy?: boolean;
  placeholder?: 'blur' | 'empty' | 'data-url';
  priority?: boolean;
  aspectRatio?: string;
  objectFit?: 'cover' | 'contain' | 'fill' | 'scale-down' | 'none';
}

export interface ResponsiveImageSet {
  src: string;
  srcSet: string;
  sizes: string;
  width: number;
  height: number;
  aspectRatio: number;
  placeholder?: string;
}

class ImageOptimizer {
  private canvas: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;
  private observer: IntersectionObserver | null = null;
  private loadedImages = new Set<string>();
  private imageCache = new Map<string, HTMLImageElement>();

  constructor() {
    this.initializeCanvas();
    this.setupLazyLoading();
  }

  /**
   * Generate optimized image with multiple formats and sizes
   */
  generateResponsiveImage(
    src: string,
    config: ImageOptimizationConfig = {}
  ): ResponsiveImageSet {
    const {
      quality = 80,
      format = 'auto',
      sizes = ['320w', '640w', '1024w', '1920w'],
      aspectRatio = '16/9',
    } = config;

    // Calculate dimensions based on aspect ratio
    const ratio = this.parseAspectRatio(aspectRatio);
    const baseWidth = 1920;
    const baseHeight = Math.round(baseWidth / ratio);

    // Generate srcSet for different sizes
    const srcSet = sizes.map(size => {
      const width = parseInt(size.replace('w', ''));
      const height = Math.round(width / ratio);
      const optimizedSrc = this.generateOptimizedUrl(src, {
        width,
        height,
        quality,
        format,
      });
      return `${optimizedSrc} ${width}w`;
    }).join(', ');

    // Generate sizes attribute
    const sizesAttr = this.generateSizesAttribute(sizes);

    // Generate placeholder
    const placeholder = config.placeholder ? this.generatePlaceholder(src, config.placeholder) : undefined;

    return {
      src: this.generateOptimizedUrl(src, { width: baseWidth, height: baseHeight, quality, format }),
      srcSet,
      sizes: sizesAttr,
      width: baseWidth,
      height: baseHeight,
      aspectRatio: ratio,
      placeholder,
    };
  }

  /**
   * Lazy load images with intersection observer
   */
  lazyLoadImage(
    element: HTMLImageElement,
    src: string,
    config: ImageOptimizationConfig = {}
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.loadedImages.has(src)) {
        element.src = src;
        resolve();
        return;
      }

      if (config.lazy && this.observer) {
        element.dataset.src = src;
        element.classList.add('lazy-image');
        
        // Add placeholder
        if (config.placeholder) {
          element.src = this.generatePlaceholder(src, config.placeholder);
        }

        this.observer.observe(element);
        
        element.addEventListener('load', () => {
          this.loadedImages.add(src);
          resolve();
        }, { once: true });

        element.addEventListener('error', reject, { once: true });
      } else {
        this.loadImageDirectly(element, src).then(resolve).catch(reject);
      }
    });
  }

  /**
   * Preload critical images
   */
  preloadImage(src: string, config: ImageOptimizationConfig = {}): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      if (this.imageCache.has(src)) {
        resolve(this.imageCache.get(src)!);
        return;
      }

      const img = new Image();
      
      // Set crossorigin for CORS
      img.crossOrigin = 'anonymous';
      
      // Handle high priority images
      if (config.priority) {
        img.loading = 'eager';
        img.fetchPriority = 'high';
      }

      img.onload = () => {
        this.imageCache.set(src, img);
        this.loadedImages.add(src);
        resolve(img);
      };

      img.onerror = reject;
      img.src = src;
    });
  }

  /**
   * Convert image to WebP format
   */
  async convertToWebP(
    file: File,
    quality: number = 80
  ): Promise<Blob> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        if (!this.canvas || !this.ctx) {
          reject(new Error('Canvas not available'));
          return;
        }

        this.canvas.width = img.width;
        this.canvas.height = img.height;
        
        this.ctx.drawImage(img, 0, 0);
        
        this.canvas.toBlob(
          (blob) => {
            if (blob) {
              resolve(blob);
            } else {
              reject(new Error('Failed to convert image'));
            }
          },
          'image/webp',
          quality / 100
        );
      };

      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Resize image while maintaining aspect ratio
   */
  async resizeImage(
    file: File,
    maxWidth: number,
    maxHeight: number,
    quality: number = 80
  ): Promise<Blob> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        if (!this.canvas || !this.ctx) {
          reject(new Error('Canvas not available'));
          return;
        }

        // Calculate new dimensions
        const { width, height } = this.calculateDimensions(
          img.width,
          img.height,
          maxWidth,
          maxHeight
        );

        this.canvas.width = width;
        this.canvas.height = height;
        
        // Use high-quality scaling
        this.ctx.imageSmoothingEnabled = true;
        this.ctx.imageSmoothingQuality = 'high';
        
        this.ctx.drawImage(img, 0, 0, width, height);
        
        this.canvas.toBlob(
          (blob) => {
            if (blob) {
              resolve(blob);
            } else {
              reject(new Error('Failed to resize image'));
            }
          },
          'image/jpeg',
          quality / 100
        );
      };

      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Generate blur placeholder
   */
  generateBlurPlaceholder(
    src: string,
    width: number = 40,
    height: number = 40
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      
      img.onload = () => {
        if (!this.canvas || !this.ctx) {
          reject(new Error('Canvas not available'));
          return;
        }

        this.canvas.width = width;
        this.canvas.height = height;
        
        // Apply blur filter
        this.ctx.filter = 'blur(4px)';
        this.ctx.drawImage(img, 0, 0, width, height);
        
        const dataUrl = this.canvas.toDataURL('image/jpeg', 0.1);
        resolve(dataUrl);
      };

      img.onerror = reject;
      img.src = src;
    });
  }

  /**
   * Check if WebP is supported
   */
  supportsWebP(): Promise<boolean> {
    return new Promise((resolve) => {
      const webP = new Image();
      webP.onload = webP.onerror = () => {
        resolve(webP.height === 2);
      };
      webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
    });
  }

  /**
   * Check if AVIF is supported
   */
  supportsAVIF(): Promise<boolean> {
    return new Promise((resolve) => {
      const avif = new Image();
      avif.onload = avif.onerror = () => {
        resolve(avif.height === 2);
      };
      avif.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=';
    });
  }

  /**
   * Get optimal image format based on browser support
   */
  async getOptimalFormat(): Promise<'avif' | 'webp' | 'jpeg'> {
    if (await this.supportsAVIF()) {
      return 'avif';
    } else if (await this.supportsWebP()) {
      return 'webp';
    } else {
      return 'jpeg';
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.observer) {
      this.observer.disconnect();
    }
    
    this.imageCache.clear();
    this.loadedImages.clear();
    
    if (this.canvas) {
      this.canvas.remove();
    }
  }

  // Private methods

  private initializeCanvas(): void {
    if (typeof window !== 'undefined') {
      this.canvas = document.createElement('canvas');
      this.ctx = this.canvas.getContext('2d');
    }
  }

  private setupLazyLoading(): void {
    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement;
              const src = img.dataset.src;
              
              if (src) {
                this.loadImageDirectly(img, src).then(() => {
                  img.classList.remove('lazy-image');
                  img.classList.add('lazy-loaded');
                  this.observer?.unobserve(img);
                });
              }
            }
          });
        },
        {
          rootMargin: '50px 0px',
          threshold: 0.01,
        }
      );
    }
  }

  private async loadImageDirectly(element: HTMLImageElement, src: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        element.src = src;
        this.loadedImages.add(src);
        resolve();
      };

      img.onerror = reject;
      img.src = src;
    });
  }

  private generateOptimizedUrl(
    src: string,
    params: {
      width?: number;
      height?: number;
      quality?: number;
      format?: string;
    }
  ): string {
    // This would integrate with your image optimization service
    // For now, return the original src
    const url = new URL(src, window.location.origin);
    
    if (params.width) url.searchParams.set('w', params.width.toString());
    if (params.height) url.searchParams.set('h', params.height.toString());
    if (params.quality) url.searchParams.set('q', params.quality.toString());
    if (params.format && params.format !== 'auto') url.searchParams.set('f', params.format);
    
    return url.toString();
  }

  private generatePlaceholder(src: string, type: string): string {
    switch (type) {
      case 'blur':
        // Return a low-quality blur placeholder
        return this.generateOptimizedUrl(src, { width: 40, height: 40, quality: 10 });
      case 'data-url':
        // Return a minimal SVG placeholder
        return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PC9zdmc+';
      default:
        return '';
    }
  }

  private parseAspectRatio(aspectRatio: string): number {
    const [width, height] = aspectRatio.split('/').map(Number);
    return width / height;
  }

  private generateSizesAttribute(sizes: string[]): string {
    // Generate responsive sizes attribute
    return '(max-width: 320px) 320px, (max-width: 640px) 640px, (max-width: 1024px) 1024px, 1920px';
  }

  private calculateDimensions(
    originalWidth: number,
    originalHeight: number,
    maxWidth: number,
    maxHeight: number
  ): { width: number; height: number } {
    const aspectRatio = originalWidth / originalHeight;
    
    let width = originalWidth;
    let height = originalHeight;
    
    if (width > maxWidth) {
      width = maxWidth;
      height = width / aspectRatio;
    }
    
    if (height > maxHeight) {
      height = maxHeight;
      width = height * aspectRatio;
    }
    
    return { width: Math.round(width), height: Math.round(height) };
  }
}

// Global image optimizer instance
export const imageOptimizer = new ImageOptimizer();

// React hook for image optimization
export function useImageOptimization() {
  return {
    generateResponsiveImage: imageOptimizer.generateResponsiveImage.bind(imageOptimizer),
    lazyLoadImage: imageOptimizer.lazyLoadImage.bind(imageOptimizer),
    preloadImage: imageOptimizer.preloadImage.bind(imageOptimizer),
    convertToWebP: imageOptimizer.convertToWebP.bind(imageOptimizer),
    resizeImage: imageOptimizer.resizeImage.bind(imageOptimizer),
    supportsWebP: imageOptimizer.supportsWebP.bind(imageOptimizer),
    supportsAVIF: imageOptimizer.supportsAVIF.bind(imageOptimizer),
    getOptimalFormat: imageOptimizer.getOptimalFormat.bind(imageOptimizer),
  };
}

// Utility functions
export async function optimizeImageUpload(file: File): Promise<File> {
  const maxWidth = 1920;
  const maxHeight = 1080;
  const quality = 85;

  try {
    // Check if image needs optimization
    if (file.size < 100 * 1024) { // Less than 100KB
      return file;
    }

    // Resize if too large
    const resizedBlob = await imageOptimizer.resizeImage(file, maxWidth, maxHeight, quality);
    
    // Convert to WebP if supported and beneficial
    if (await imageOptimizer.supportsWebP() && file.type !== 'image/webp') {
      const webpBlob = await imageOptimizer.convertToWebP(new File([resizedBlob], file.name), quality);
      
      // Use WebP if it's smaller
      if (webpBlob.size < resizedBlob.size) {
        return new File([webpBlob], file.name.replace(/\.[^/.]+$/, '.webp'), {
          type: 'image/webp',
        });
      }
    }

    return new File([resizedBlob], file.name, {
      type: file.type,
    });
  } catch (error) {
    console.error('Image optimization failed:', error);
    return file;
  }
}

export default imageOptimizer;
