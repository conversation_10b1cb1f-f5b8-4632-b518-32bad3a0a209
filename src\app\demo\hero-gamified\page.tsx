"use client";

import { Suspense } from "react";
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import HeroGameified from "@/components/demo/hero-gamified";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft, Gamepad2, Trophy, Target } from "lucide-react";

export default function HeroGameifiedDemo() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-green-50/30">
      <Navbar />
      
      {/* Demo Header */}
      <div className="pt-24 pb-8">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <Button asChild variant="outline" size="sm">
                <Link href="/">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Home
                </Link>
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-slate-900">Gamified Hero Demo</h1>
                <p className="text-slate-600">Tennis ball hitting mini-game with rewards</p>
              </div>
            </div>
            
            {/* Demo Navigation */}
            <div className="flex gap-2">
              <Button asChild variant="outline" size="sm">
                <Link href="/demo/hero-3d">3D Demo</Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href="/demo/hero-ballpit">Ballpit Demo</Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href="/demo/hero-ballpit-cards">Ballpit + Cards</Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href="/demo/hero-video">Video Demo</Link>
              </Button>
            </div>
          </div>

          {/* Demo Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-green-100">
              <div className="flex items-center gap-2 mb-2">
                <Gamepad2 className="w-4 h-4 text-green-600" />
                <span className="text-sm font-semibold">Mini-Game</span>
              </div>
              <p className="text-xs text-slate-600">Click/tap to hit moving tennis balls and score points</p>
            </div>
            
            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-green-100">
              <div className="flex items-center gap-2 mb-2">
                <Trophy className="w-4 h-4 text-green-600" />
                <span className="text-sm font-semibold">Rewards System</span>
              </div>
              <p className="text-xs text-slate-600">Unlock discount codes at milestone scores</p>
            </div>
            
            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-green-100">
              <div className="flex items-center gap-2 mb-2">
                <Target className="w-4 h-4 text-green-600" />
                <span className="text-sm font-semibold">Leaderboard</span>
              </div>
              <p className="text-xs text-slate-600">Compete with other players for high scores</p>
            </div>
          </div>
        </div>
      </div>

      {/* Gamified Hero Section */}
      <Suspense fallback={
        <div className="h-[600px] flex items-center justify-center bg-slate-100">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-green-600 rounded-full flex items-center justify-center animate-pulse">
              <Gamepad2 className="w-8 h-8 text-white" />
            </div>
            <p className="text-slate-600">Loading Game Experience...</p>
          </div>
        </div>
      }>
        <HeroGameified />
      </Suspense>

      {/* Demo Info Section */}
      <section className="py-16 bg-white/50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-8">Gamified Hero Features</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-slate-900">Technical Implementation</h3>
                <ul className="space-y-2 text-slate-600">
                  <li>• HTML5 Canvas with optimized game loop</li>
                  <li>• Physics engine for realistic ball movement</li>
                  <li>• Local storage for score persistence</li>
                  <li>• Sound effects with user controls</li>
                  <li>• Battery-conscious frame rate adjustment</li>
                </ul>
              </div>
              
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-slate-900">User Experience</h3>
                <ul className="space-y-2 text-slate-600">
                  <li>• Touch-optimized controls with haptic feedback</li>
                  <li>• Unlockable discount codes at milestones</li>
                  <li>• Product showcases between game rounds</li>
                  <li>• Portrait and landscape orientation support</li>
                  <li>• Smooth transition to shopping experience</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
