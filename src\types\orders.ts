/**
 * TypeScript interfaces for Orders Management
 * Based on the actual database schema and real order data
 */

export interface OrderItem {
  id?: string;
  name: string;
  image: string;
  price: number;
  quantity: number;
  product_id?: string;
}

export interface ShippingDetails {
  name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  postal_code: string;
  country: string;
  alternative_phone?: string;
}

export interface Order {
  id: string;
  user_id: string;
  items: OrderItem[];
  shipping_details: ShippingDetails;
  status: OrderStatus;
  payment_status: PaymentStatus;
  total_amount: number;
  stripe_session_id?: string | null;
  stripe_payment_intent?: string | null;
  yoco_payment_id?: string | null;
  notes?: string | null;
  shipping_method?: string | null;
  created_at: string;
  updated_at: string;
  // Joined fields from users table and shipping details
  customer_email?: string;
  customer_name?: string;
  customer_phone?: string;
}

export type OrderStatus = 
  | 'pending'
  | 'confirmed'
  | 'processing'
  | 'shipped'
  | 'delivered'
  | 'cancelled'
  | 'refunded';

export type PaymentStatus = 
  | 'pending'
  | 'paid'
  | 'failed'
  | 'refunded'
  | 'partially_refunded';

export interface OrderFilters {
  status?: OrderStatus;
  payment_status?: PaymentStatus;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface OrderStats {
  total_orders: number;
  pending_orders: number;
  processing_orders: number;
  delivered_orders: number;
  total_revenue: number;
  monthly_growth: number;
}

export interface OrderUpdateData {
  status?: OrderStatus;
  payment_status?: PaymentStatus;
  notes?: string;
  shipping_method?: string;
}

export interface PaginatedOrders {
  orders: Order[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

// API Response types
export interface OrdersResponse {
  success: boolean;
  data: PaginatedOrders;
  error?: string;
}

export interface OrderResponse {
  success: boolean;
  data: Order;
  error?: string;
}

export interface OrderStatsResponse {
  success: boolean;
  data: OrderStats;
  error?: string;
}

// Form types for order management
export interface OrderStatusUpdateForm {
  status: OrderStatus;
  notes?: string;
}

export interface OrderShippingUpdateForm {
  shipping_method: string;
  tracking_number?: string;
  notes?: string;
}

// Export utility functions
export const ORDER_STATUS_COLORS: Record<OrderStatus, string> = {
  pending: 'bg-yellow-100 text-yellow-800',
  confirmed: 'bg-blue-100 text-blue-800',
  processing: 'bg-purple-100 text-purple-800',
  shipped: 'bg-indigo-100 text-indigo-800',
  delivered: 'bg-green-100 text-green-800',
  cancelled: 'bg-red-100 text-red-800',
  refunded: 'bg-gray-100 text-gray-800',
};

export const PAYMENT_STATUS_COLORS: Record<PaymentStatus, string> = {
  pending: 'bg-yellow-100 text-yellow-800',
  paid: 'bg-green-100 text-green-800',
  failed: 'bg-red-100 text-red-800',
  refunded: 'bg-gray-100 text-gray-800',
  partially_refunded: 'bg-orange-100 text-orange-800',
};

export const ORDER_STATUS_OPTIONS: { value: OrderStatus; label: string }[] = [
  { value: 'pending', label: 'Pending' },
  { value: 'confirmed', label: 'Confirmed' },
  { value: 'processing', label: 'Processing' },
  { value: 'shipped', label: 'Shipped' },
  { value: 'delivered', label: 'Delivered' },
  { value: 'cancelled', label: 'Cancelled' },
  { value: 'refunded', label: 'Refunded' },
];

export const PAYMENT_STATUS_OPTIONS: { value: PaymentStatus; label: string }[] = [
  { value: 'pending', label: 'Pending' },
  { value: 'paid', label: 'Paid' },
  { value: 'failed', label: 'Failed' },
  { value: 'refunded', label: 'Refunded' },
  { value: 'partially_refunded', label: 'Partially Refunded' },
];
