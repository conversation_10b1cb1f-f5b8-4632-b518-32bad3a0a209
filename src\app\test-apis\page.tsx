"use client";

import { useState } from 'react';
import { useProducts, useCategories, useMentorshipPrograms, useStudentEnrollments } from '../../hooks';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Loader2, CheckCircle, XCircle, ShoppingCart, Users, GraduationCap } from 'lucide-react';

export default function TestAPIsPage() {
  const [testResults, setTestResults] = useState<Record<string, 'pending' | 'success' | 'error'>>({});

  // API Hooks
  const { data: products, isLoading: productsLoading, error: productsError } = useProducts();
  const { data: categories, isLoading: categoriesLoading, error: categoriesError } = useCategories();
  const { data: programs, isLoading: programsLoading, error: programsError } = useMentorshipPrograms();
  const { data: enrollments, isLoading: enrollmentsLoading, error: enrollmentsError } = useStudentEnrollments();

  const runAPITest = async (testName: string, testFn: () => Promise<void>) => {
    setTestResults(prev => ({ ...prev, [testName]: 'pending' }));
    try {
      await testFn();
      setTestResults(prev => ({ ...prev, [testName]: 'success' }));
    } catch (error) {
      console.error(`Test ${testName} failed:`, error);
      setTestResults(prev => ({ ...prev, [testName]: 'error' }));
    }
  };

  const testProductsAPI = async () => {
    const response = await fetch('/api/products');
    if (!response.ok) throw new Error('Products API failed');
    const data = await response.json();
    if (!Array.isArray(data)) throw new Error('Invalid products response');
  };

  const testCategoriesAPI = async () => {
    const response = await fetch('/api/categories');
    if (!response.ok) throw new Error('Categories API failed');
    const data = await response.json();
    if (!Array.isArray(data)) throw new Error('Invalid categories response');
  };

  const testMentorshipAPI = async () => {
    const response = await fetch('/api/mentorship/programs');
    if (!response.ok) throw new Error('Mentorship API failed');
    const data = await response.json();
    if (!Array.isArray(data)) throw new Error('Invalid mentorship response');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Loader2 className="h-4 w-4 animate-spin text-yellow-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusBadge = (isLoading: boolean, error: any, data: any) => {
    if (isLoading) return <Badge variant="secondary">Loading...</Badge>;
    if (error) return <Badge variant="destructive">Error</Badge>;
    if (data) return <Badge variant="default">Success</Badge>;
    return <Badge variant="outline">Not tested</Badge>;
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">🧪 API Testing Dashboard</h1>
        <p className="text-muted-foreground">
          Test all backend APIs and verify frontend integration
        </p>
      </div>

      {/* Quick Test Buttons */}
      <Card>
        <CardHeader>
          <CardTitle>Quick API Tests</CardTitle>
          <CardDescription>
            Run individual API tests to verify functionality
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              onClick={() => runAPITest('products', testProductsAPI)}
              disabled={testResults.products === 'pending'}
              className="flex items-center gap-2"
            >
              {getStatusIcon(testResults.products)}
              Test Products API
            </Button>
            <Button
              onClick={() => runAPITest('categories', testCategoriesAPI)}
              disabled={testResults.categories === 'pending'}
              className="flex items-center gap-2"
            >
              {getStatusIcon(testResults.categories)}
              Test Categories API
            </Button>
            <Button
              onClick={() => runAPITest('mentorship', testMentorshipAPI)}
              disabled={testResults.mentorship === 'pending'}
              className="flex items-center gap-2"
            >
              {getStatusIcon(testResults.mentorship)}
              Test Mentorship API
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Results */}
      <Tabs defaultValue="products" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="products" className="flex items-center gap-2">
            <ShoppingCart className="h-4 w-4" />
            Products
            {getStatusBadge(productsLoading, productsError, products)}
          </TabsTrigger>
          <TabsTrigger value="categories" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Categories
            {getStatusBadge(categoriesLoading, categoriesError, categories)}
          </TabsTrigger>
          <TabsTrigger value="mentorship" className="flex items-center gap-2">
            <GraduationCap className="h-4 w-4" />
            Mentorship
            {getStatusBadge(programsLoading, programsError, programs)}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="products" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Products API Test Results</CardTitle>
              <CardDescription>
                {productsLoading && "Loading products..."}
                {productsError && `Error: ${productsError.message}`}
                {products && `Successfully loaded ${products.length} products`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {products && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {products.slice(0, 6).map((product) => (
                    <Card key={product.id} className="p-4">
                      <h4 className="font-semibold">{product.name}</h4>
                      <p className="text-sm text-muted-foreground">{product.category}</p>
                      <p className="text-lg font-bold">R{product.price}</p>
                      <Badge variant={product.stock > 0 ? "default" : "secondary"}>
                        {product.status}
                      </Badge>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="categories" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Categories API Test Results</CardTitle>
              <CardDescription>
                {categoriesLoading && "Loading categories..."}
                {categoriesError && `Error: ${categoriesError.message}`}
                {categories && `Successfully loaded ${categories.length} categories`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {categories && (
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {categories.map((category) => (
                    <Card key={category.id} className="p-4 text-center">
                      <h4 className="font-semibold">{category.name}</h4>
                      <p className="text-sm text-muted-foreground">
                        {category.count} products
                      </p>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="mentorship" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Mentorship Programs API Test Results</CardTitle>
              <CardDescription>
                {programsLoading && "Loading programs..."}
                {programsError && `Error: ${programsError.message}`}
                {programs && `Successfully loaded ${programs.length} programs`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {programs && (
                <div className="space-y-4">
                  {programs.map((program) => (
                    <Card key={program.id} className="p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-semibold">{program.name}</h4>
                          <p className="text-sm text-muted-foreground">
                            {program.description}
                          </p>
                          <p className="text-sm">
                            Duration: {program.duration_months === 0 ? 'One-time' : `${program.duration_months} months`}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-bold">R{program.price_monthly}/month</p>
                          {program.price_upfront && (
                            <p className="text-sm text-muted-foreground">
                              Or R{program.price_upfront} upfront
                            </p>
                          )}
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
