import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function POST(request: Request) {
  try {
    const supabase = await createClient();
    
    // Add yoco_payment_id column to orders table if it doesn't exist
    const { error: alterTableError } = await supabase
      .from('orders')
      .select('yoco_payment_id')
      .limit(1)
      .maybeSingle();
      
    if (alterTableError && alterTableError.message.includes('column "yoco_payment_id" does not exist')) {
      // Column doesn't exist, so add it
      const { error: addColumnError } = await supabase.rpc('add_column_if_not_exists', {
        table_name: 'orders',
        column_name: 'yoco_payment_id',
        column_type: 'text'
      });
      
      if (addColumnError) {
        // If the RPC function doesn't exist, we'll need to create it first
        if (addColumnError.message.includes('function add_column_if_not_exists') || 
            addColumnError.message.includes('does not exist')) {
          
          // Create the function first
          const { error: createFunctionError } = await supabase.rpc('create_add_column_function');
          
          if (createFunctionError) {
            // If we can't create the function, we'll need to use raw SQL
            // But we can't do that with the client API, so we'll need to tell the user
            return NextResponse.json({ 
              success: false, 
              message: 'Migration requires manual SQL execution. Please run the following SQL in your Supabase SQL editor:',
              sql: `
                ALTER TABLE public.orders 
                ADD COLUMN IF NOT EXISTS yoco_payment_id TEXT;
                
                CREATE INDEX IF NOT EXISTS orders_yoco_payment_id_idx ON public.orders (yoco_payment_id);
              `
            });
          } else {
            // Try again now that the function exists
            const { error: retryError } = await supabase.rpc('add_column_if_not_exists', {
              table_name: 'orders',
              column_name: 'yoco_payment_id',
              column_type: 'text'
            });
            
            if (retryError) {
              throw new Error(`Error adding yoco_payment_id column: ${retryError.message}`);
            }
          }
        } else {
          throw new Error(`Error adding yoco_payment_id column: ${addColumnError.message}`);
        }
      }
      
      // Create index on yoco_payment_id
      const { error: createIndexError } = await supabase.rpc('create_index_if_not_exists', {
        index_name: 'orders_yoco_payment_id_idx',
        table_name: 'orders',
        column_name: 'yoco_payment_id'
      });
      
      if (createIndexError) {
        console.error('Error creating index:', createIndexError);
        // Index creation is not critical, so we'll continue
      }
    }
    
    return NextResponse.json({ 
      success: true, 
      message: 'Migration completed successfully or column already exists' 
    });
  } catch (error: any) {
    console.error('Migration error:', error);
    return NextResponse.json(
      { 
        error: error.message,
        message: 'Please run the following SQL in your Supabase SQL editor:',
        sql: `
          ALTER TABLE public.orders 
          ADD COLUMN IF NOT EXISTS yoco_payment_id TEXT;
          
          CREATE INDEX IF NOT EXISTS orders_yoco_payment_id_idx ON public.orders (yoco_payment_id);
        `
      },
      { status: 500 }
    );
  }
} 