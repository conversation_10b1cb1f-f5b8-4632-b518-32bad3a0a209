import { render, screen, waitFor, act, fireEvent } from '@testing-library/react';
import { usePathname } from 'next/navigation';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import RoutePreloader from '../route-preloader';

// Mock Next.js navigation
vi.mock('next/navigation', () => ({
  usePathname: vi.fn(),
}));

// Mock Next.js Image component
vi.mock('next/image', () => ({
  default: ({ src, alt, ...props }: any) => (
    <img src={src} alt={alt} {...props} />
  ),
}));

// Mock window.matchMedia for reduced motion
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

describe('RoutePreloader Component', () => {
  const mockUsePathname = usePathname as ReturnType<typeof vi.fn>;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
    mockUsePathname.mockReturnValue('/');
  });

  afterEach(() => {
    vi.runOnlyPendingTimers();
    vi.useRealTimers();
  });

  it('does not show preloader on initial render', () => {
    render(<RoutePreloader />);
    
    expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
  });

  it('shows preloader when pathname changes', async () => {
    const { rerender } = render(<RoutePreloader />);
    
    // Change pathname to trigger navigation
    mockUsePathname.mockReturnValue('/shop');
    rerender(<RoutePreloader />);
    
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(screen.getByLabelText('Loading page')).toBeInTheDocument();
  });

  it('hides preloader after navigation completes', async () => {
    const { rerender } = render(<RoutePreloader />);

    // Change pathname to trigger navigation
    mockUsePathname.mockReturnValue('/shop');
    rerender(<RoutePreloader />);

    expect(screen.getByRole('progressbar')).toBeInTheDocument();

    // Advance time to complete navigation (600ms + 500ms hide delay)
    act(() => {
      vi.advanceTimersByTime(1100);
    });

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 10000 });
  });

  it('shows preloader on internal link clicks', () => {
    render(<RoutePreloader />);
    
    // Create a mock link element
    const link = document.createElement('a');
    link.href = 'http://localhost:3000/shop';
    document.body.appendChild(link);
    
    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: {
        href: 'http://localhost:3000/',
      },
      writable: true,
    });
    
    // Click the link
    fireEvent.click(link);
    
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    
    // Cleanup
    document.body.removeChild(link);
  });

  it('does not show preloader for external links', () => {
    render(<RoutePreloader />);
    
    // Create a mock external link element
    const link = document.createElement('a');
    link.href = 'https://external-site.com';
    document.body.appendChild(link);
    
    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: {
        href: 'http://localhost:3000/',
      },
      writable: true,
    });
    
    // Click the link
    fireEvent.click(link);
    
    expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    
    // Cleanup
    document.body.removeChild(link);
  });

  it('does not show preloader for same page links', () => {
    render(<RoutePreloader />);
    
    // Create a mock same-page link element
    const link = document.createElement('a');
    link.href = 'http://localhost:3000/';
    document.body.appendChild(link);
    
    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: {
        href: 'http://localhost:3000/',
      },
      writable: true,
    });
    
    // Click the link
    fireEvent.click(link);
    
    expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    
    // Cleanup
    document.body.removeChild(link);
  });

  it('does not show preloader for hash links', () => {
    render(<RoutePreloader />);
    
    // Create a mock hash link element
    const link = document.createElement('a');
    link.href = '#section';
    document.body.appendChild(link);
    
    // Click the link
    fireEvent.click(link);
    
    expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    
    // Cleanup
    document.body.removeChild(link);
  });

  it('does not show preloader for mailto links', () => {
    render(<RoutePreloader />);
    
    // Create a mock mailto link element
    const link = document.createElement('a');
    link.href = 'mailto:<EMAIL>';
    document.body.appendChild(link);
    
    // Click the link
    fireEvent.click(link);
    
    expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    
    // Cleanup
    document.body.removeChild(link);
  });

  it('respects reduced motion preferences', () => {
    // Mock reduced motion preference
    window.matchMedia = vi.fn().mockImplementation(query => ({
      matches: query === '(prefers-reduced-motion: reduce)',
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    }));

    const { rerender } = render(<RoutePreloader />);

    // Change pathname to trigger navigation
    mockUsePathname.mockReturnValue('/shop');
    rerender(<RoutePreloader />);

    // Check that animation classes are not applied
    const logoContainer = screen.getByAltText('Tennis Whisperer').closest('div');
    expect(logoContainer).not.toHaveClass('animate-virtra-logo');
  });

  it('provides proper accessibility attributes', () => {
    const { rerender } = render(<RoutePreloader />);
    
    // Change pathname to trigger navigation
    mockUsePathname.mockReturnValue('/shop');
    rerender(<RoutePreloader />);
    
    const preloader = screen.getByRole('progressbar');
    expect(preloader).toHaveAttribute('aria-label', 'Loading page');
    expect(preloader).toHaveAttribute('aria-live', 'polite');
  });

  it('cleans up event listeners and timeouts on unmount', () => {
    const removeEventListenerSpy = vi.spyOn(document, 'removeEventListener');
    const clearTimeoutSpy = vi.spyOn(global, 'clearTimeout');

    const { unmount } = render(<RoutePreloader />);

    unmount();

    // Verify that event listeners and timeouts are cleaned up
    expect(removeEventListenerSpy).toHaveBeenCalledWith('click', expect.any(Function));

    removeEventListenerSpy.mockRestore();
    clearTimeoutSpy.mockRestore();
  });
});
