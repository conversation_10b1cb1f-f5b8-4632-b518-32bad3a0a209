import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET() {
  try {
    const supabase = await createClient();
    
    // Query to get column information from PostgreSQL information_schema using raw SQL
    const { data, error } = await supabase
      .from('orders')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('Error fetching schema:', error);
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }
    
    // Get column names from the first row
    const columnNames = data && data.length > 0 ? Object.keys(data[0]) : [];
    
    return NextResponse.json({
      message: 'Orders table schema',
      columns: columnNames,
      sample: data
    });
  } catch (error: any) {
    console.error('Error:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
} 