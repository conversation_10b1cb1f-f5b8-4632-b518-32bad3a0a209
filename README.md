# Tennis Equipment Commerce (TEC)

A modern e-commerce platform for tennis equipment built with Next.js, Supabase, and Stripe.

## Features

- Modern minimalist design
- Product browsing by categories (Rackets, Balls, Apparel, Accessories)
- Product details pages
- Shopping cart functionality
- User authentication with Supabase
- Checkout process with Stripe
- Order management and history
- Responsive design for all devices

## Tech Stack

- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth)
- **Payment Processing**: Yoco
- **Styling**: Tailwind CSS, shadcn/ui components
- **Deployment**: Vercel (recommended)

## Getting Started

### Prerequisites

- Node.js 18.17.0 or later
- npm or yarn
- Supabase account
- Yoco account

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/tec.git
   cd tec
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

3. Set up environment variables:
   - Copy `.env.local.example` to `.env.local`
   - Fill in your Supabase and Stripe credentials

   ```
   # Supabase
   NEXT_PUBLIC_SUPABASE_URL=your-project-url.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

   # Yoco
   YOCO_PUBLIC_KEY=pk_test_your_public_key
   YOCO_SECRET_KEY=sk_test_your_secret_key
   ```

4. Set up the Supabase database:
   - Run the SQL migrations in the `supabase/migrations` folder
   - Or use the Supabase UI to create the tables

5. Start the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Setting Up Stripe

1. Create a Stripe account at [stripe.com](https://stripe.com)
2. Get your API keys from the Stripe Dashboard
3. Set up a webhook endpoint:
   - Use [Stripe CLI](https://stripe.com/docs/stripe-cli) for local development
   - Point the webhook to `https://your-domain.com/api/webhook` in production
4. Add your Stripe keys to `.env.local`

## Supabase Setup

1. Create a new project in Supabase
2. Run the SQL migrations in `supabase/migrations` folder
3. Set up authentication providers in the Supabase dashboard
4. Add your Supabase URL and anon key to `.env.local`

## Deployment

### Deploying to Vercel

1. Push your code to GitHub
2. Import your repository in Vercel
3. Add your environment variables in the Vercel dashboard
4. Deploy!

## Project Structure

- `/src/app` - Next.js app router pages
- `/src/components` - React components
- `/src/lib` - Utility functions and libraries
- `/src/context` - React context providers
- `/supabase` - Supabase client and migrations
- `/public` - Static assets

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.