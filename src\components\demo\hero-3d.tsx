"use client";

import { Suspense, useState, useRef, useEffect } from "react";
import { <PERSON>vas, use<PERSON>rame, useLoader } from "@react-three/fiber";
import { OrbitControls, useGLTF, PerspectiveCamera } from "@react-three/drei";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader";
import { Button } from "@/components/ui/button";
import { ArrowRight, Check, Star, Users, Award, Zap } from "lucide-react";
import Link from "next/link";
import { useCart } from "@/context/cart-context";
import { toast } from "@/components/ui/use-toast";
import * as THREE from "three";

// Tennis Ball 3D Model Component
function TennisBall({ rotation }: { rotation: [number, number, number] }) {
  const meshRef = useRef<THREE.Mesh>(null);
  
  // Try to load the GLB model, fallback to procedural ball
  let gltf;
  try {
    gltf = useGLTF("/model/tennis_ball.glb");
  } catch (error) {
    console.warn("Failed to load tennis ball model, using fallback");
  }

  useFrame((state, delta) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = rotation[0];
      meshRef.current.rotation.y = rotation[1];
      meshRef.current.rotation.z = rotation[2];
      
      // Add subtle floating animation
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
    }
  });

  if (gltf && gltf.scene) {
    return (
      <primitive 
        ref={meshRef}
        object={gltf.scene.clone()} 
        scale={[2, 2, 2]}
        position={[0, 0, 0]}
      />
    );
  }

  // Fallback procedural tennis ball
  return (
    <mesh ref={meshRef} scale={[2, 2, 2]}>
      <sphereGeometry args={[1, 32, 32]} />
      <meshStandardMaterial 
        color="#9ACD32" 
        roughness={0.8}
        metalness={0.1}
      />
      {/* Tennis ball seam lines */}
      <mesh>
        <torusGeometry args={[0.8, 0.02, 8, 32]} />
        <meshStandardMaterial color="#ffffff" />
      </mesh>
    </mesh>
  );
}

// 3D Scene Component
function Scene3D({ onRotationChange }: { onRotationChange: (rotation: [number, number, number]) => void }) {
  const controlsRef = useRef<any>(null);
  const [rotation, setRotation] = useState<[number, number, number]>([0, 0, 0]);

  useFrame(() => {
    if (controlsRef.current) {
      const spherical = new THREE.Spherical();
      spherical.setFromVector3(controlsRef.current.object.position.clone().sub(controlsRef.current.target));
      
      const newRotation: [number, number, number] = [
        spherical.phi,
        spherical.theta,
        0
      ];
      
      setRotation(newRotation);
      onRotationChange(newRotation);
    }
  });

  return (
    <>
      <PerspectiveCamera makeDefault position={[0, 0, 5]} />
      <OrbitControls 
        ref={controlsRef}
        enablePan={false}
        enableZoom={true}
        enableRotate={true}
        minDistance={3}
        maxDistance={8}
        autoRotate={true}
        autoRotateSpeed={0.5}
      />
      
      {/* Lighting */}
      <ambientLight intensity={0.6} />
      <directionalLight 
        position={[10, 10, 5]} 
        intensity={1}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
      />
      <pointLight position={[-10, -10, -5]} intensity={0.3} />
      
      {/* Tennis Ball */}
      <TennisBall rotation={rotation} />
      
      {/* Environment */}
      <mesh position={[0, -3, 0]} rotation={[-Math.PI / 2, 0, 0]} receiveShadow>
        <planeGeometry args={[20, 20]} />
        <shadowMaterial opacity={0.1} />
      </mesh>
    </>
  );
}

// Main Hero3D Component
export default function Hero3D() {
  const [rotation, setRotation] = useState<[number, number, number]>([0, 0, 0]);
  const [webglSupported, setWebglSupported] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const { addToCart } = useCart();

  useEffect(() => {
    // Check WebGL support
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    setWebglSupported(!!gl);

    // Check if mobile
    setIsMobile(window.innerWidth < 768);

    // Handle resize
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleAddToCart = () => {
    addToCart({
      id: 1,
      name: "Pro Tour Racket",
      price: 3599.99,
      image: "/images/tennis-racket.png"
    });
  };

  // Calculate parallax offset based on rotation
  const parallaxOffset = Math.sin(rotation[1]) * 20;

  if (!webglSupported) {
    return (
      <section className="relative overflow-hidden bg-gradient-to-br from-slate-50 to-green-50/30 py-20">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row items-center gap-16">
            {/* Fallback content */}
            <div className="lg:w-1/2 text-center lg:text-left">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 tracking-tight leading-tight text-slate-900">
                Experience tennis gear in
                <span className="block text-green-600">stunning 3D detail</span>
              </h1>
              <p className="text-lg md:text-xl text-slate-600 mb-10 max-w-2xl mx-auto lg:mx-0 leading-relaxed">
                Your browser doesn't support 3D graphics, but our premium tennis equipment is still amazing!
              </p>
              <Button onClick={handleAddToCart} size="lg" className="bg-green-600 hover:bg-green-700 text-white rounded-lg text-base font-semibold h-14 px-8 shadow-lg hover:shadow-xl transition-all duration-300 min-h-[44px]">
                Shop Collection
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
            
            <div className="lg:w-1/2">
              <div className="relative h-[400px] w-full rounded-2xl overflow-hidden shadow-2xl">
                <img 
                  src="/images/tennis-racket.png" 
                  alt="Tennis Equipment" 
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-slate-50 to-green-50/30 py-20">
      <div className="container mx-auto px-4">
        <div className="flex flex-col lg:flex-row items-center gap-16">
          {/* Left Column - Text + CTA with Parallax */}
          <div 
            className="lg:w-1/2 text-center lg:text-left"
            style={{
              transform: `translateX(${parallaxOffset}px)`,
              transition: 'transform 0.3s ease-out'
            }}
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 tracking-tight leading-tight text-slate-900">
              Experience tennis gear in
              <span className="block text-green-600">stunning 3D detail</span>
            </h1>

            <p className="text-lg md:text-xl text-slate-600 mb-10 max-w-2xl mx-auto lg:mx-0 leading-relaxed">
              Rotate, zoom, and explore our premium tennis equipment in interactive 3D.
            </p>

            <div className="mb-12">
              <Button onClick={handleAddToCart} size="lg" className="bg-green-600 hover:bg-green-700 text-white rounded-lg text-base font-semibold h-14 px-8 shadow-lg hover:shadow-xl transition-all duration-300 min-h-[44px]">
                Add to Cart
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>

            {/* Feature Bar */}
            <div className="flex flex-col sm:flex-row items-center lg:items-start justify-center lg:justify-start gap-6 text-sm">
              <div className="flex items-center gap-2">
                <Check className="w-4 h-4 text-green-600" />
                <span className="text-slate-600">360° Product View</span>
              </div>
              <div className="flex items-center gap-2">
                <Check className="w-4 h-4 text-green-600" />
                <span className="text-slate-600">Interactive 3D Models</span>
              </div>
              <div className="flex items-center gap-2">
                <Check className="w-4 h-4 text-green-600" />
                <span className="text-slate-600">Mobile Optimized</span>
              </div>
            </div>
          </div>

          {/* Right Column - 3D Canvas */}
          <div className="lg:w-1/2 relative">
            <div className="relative h-[400px] md:h-[500px] w-full rounded-2xl overflow-hidden shadow-2xl bg-gradient-to-br from-green-100 to-green-200">
              <Canvas
                shadows
                dpr={isMobile ? [1, 1.5] : [1, 2]}
                performance={{ min: 0.5 }}
                className="w-full h-full"
              >
                <Scene3D onRotationChange={setRotation} />
              </Canvas>
              
              {/* 3D Controls Hint */}
              <div className="absolute bottom-4 left-4 z-10">
                <div className="bg-white/95 backdrop-blur-sm px-3 py-2 rounded-lg shadow-lg">
                  <span className="text-xs sm:text-sm font-semibold text-slate-800">
                    {isMobile ? "Touch to rotate • Pinch to zoom" : "Drag to rotate • Scroll to zoom"}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
