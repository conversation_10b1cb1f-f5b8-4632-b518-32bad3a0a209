'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { createClient } from '@/utils/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';

interface ShippingFormProps {
  userData: {
    address: {
      street: string;
      city: string;
      province: string;
      postalCode: string;
      country: string;
    };
    phone: string;
    alternativePhone?: string;
  };
  userId: string;
}

export function ShippingForm({ userData, userId }: ShippingFormProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    address: userData.address.street || '',
    city: userData.address.city || '',
    province: userData.address.province || '',
    postalCode: userData.address.postalCode || '',
    phone: userData.phone || '',
    alternativePhone: userData.alternativePhone || '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
  };

  const handleSelectChange = (id: string, value: string) => {
    setFormData(prev => ({ ...prev, [id]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const supabase = createClient();
      
      // Update user profile with shipping information
      const { error } = await supabase
        .from('users')
        .update({
          shipping_details: {
            address: formData.address,
            city: formData.city,
            province: formData.province,
            postal_code: formData.postalCode,
            phone: formData.phone,
            alternative_phone: formData.alternativePhone
          }
        })
        .eq('id', userId);

      if (error) {
        throw error;
      }

      toast({
        title: 'Success',
        description: 'Your shipping information has been updated',
      });
    } catch (error: any) {
      console.error('Error updating shipping information:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to update shipping information',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2 md:col-span-2">
          <Label htmlFor="address">Street Address</Label>
          <Input 
            id="address" 
            value={formData.address}
            onChange={handleInputChange}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="city">City</Label>
          <Input 
            id="city" 
            value={formData.city}
            onChange={handleInputChange}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="province">Province</Label>
          <Select
            value={formData.province}
            onValueChange={(value) => handleSelectChange("province", value)}
          >
            <SelectTrigger id="province">
              <SelectValue placeholder="Select province" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="gauteng">Gauteng</SelectItem>
              <SelectItem value="western-cape">Western Cape</SelectItem>
              <SelectItem value="eastern-cape">Eastern Cape</SelectItem>
              <SelectItem value="kwazulu-natal">KwaZulu-Natal</SelectItem>
              <SelectItem value="free-state">Free State</SelectItem>
              <SelectItem value="north-west">North West</SelectItem>
              <SelectItem value="mpumalanga">Mpumalanga</SelectItem>
              <SelectItem value="limpopo">Limpopo</SelectItem>
              <SelectItem value="northern-cape">Northern Cape</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="postalCode">Postal Code</Label>
          <Input 
            id="postalCode" 
            value={formData.postalCode}
            onChange={handleInputChange}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number</Label>
          <Input 
            id="phone" 
            value={formData.phone}
            onChange={handleInputChange}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="alternativePhone">Alternative Phone Number</Label>
          <Input 
            id="alternativePhone" 
            value={formData.alternativePhone}
            onChange={handleInputChange}
            placeholder="Optional"
          />
        </div>
      </div>

      <div className="mt-6">
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            'Save Changes'
          )}
        </Button>
      </div>
    </form>
  );
}
