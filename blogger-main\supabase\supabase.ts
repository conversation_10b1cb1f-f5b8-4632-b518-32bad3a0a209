import { createClient, SupabaseClient } from "@supabase/supabase-js";
import { Database } from "../src/types/supabase";

// Environment variables with validation
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
const enableRealtime = import.meta.env.VITE_ENABLE_REALTIME === 'true';

// Validation
if (!supabaseUrl) {
  throw new Error("Missing VITE_SUPABASE_URL environment variable");
}

if (!supabaseAnonKey) {
  throw new Error("Missing VITE_SUPABASE_ANON_KEY environment variable");
}

// Enhanced Supabase client with real-time configuration
export const supabase: SupabaseClient<Database> = createClient(
  supabaseUrl,
  supabaseAnonKey,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
      flowType: 'pkce'
    },
    realtime: {
      params: {
        eventsPerSecond: 10,
      },
    },
    global: {
      headers: {
        'X-Client-Info': 'thabo-bester-app@1.0.0',
      },
    },
  }
);

// Real-time channel management
export class RealtimeManager {
  private static channels: Map<string, any> = new Map();

  static subscribeToTable(
    tableName: string,
    callback: (payload: any) => void,
    filter?: string
  ) {
    if (!enableRealtime) return null;

    const channelName = `${tableName}_changes`;

    // Remove existing channel if it exists
    if (this.channels.has(channelName)) {
      this.channels.get(channelName).unsubscribe();
    }

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: tableName,
          filter: filter,
        },
        callback
      )
      .subscribe();

    this.channels.set(channelName, channel);
    return channel;
  }

  static unsubscribeFromTable(tableName: string) {
    const channelName = `${tableName}_changes`;
    const channel = this.channels.get(channelName);

    if (channel) {
      channel.unsubscribe();
      this.channels.delete(channelName);
    }
  }

  static unsubscribeAll() {
    this.channels.forEach((channel) => {
      channel.unsubscribe();
    });
    this.channels.clear();
  }
}

// Export for global access
export { enableRealtime };
