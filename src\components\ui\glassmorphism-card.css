/* Glassmorphism Credit Card Styles */

/* Background circles */
.circles {
  position: absolute;
  height: 270px;
  width: 450px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(120deg, #1D976C, #2c3e50);
}

.circle-1 {
  height: 300px;
  width: 300px;
  top: 100px;
  left: -50px;
  opacity: 0.8;
  animation: float 6s cubic-bezier(0.54, 0.085, 0.5, 0.92) infinite alternate;
  animation-delay: 3.5s;
}

.circle-2 {
  height: 240px;
  width: 240px;
  bottom: 40px;
  right: -100px;
  opacity: 0.8;
  animation: float 6s cubic-bezier(0.54, 0.085, 0.5, 0.92) infinite alternate;
  animation-delay: 2s;
}

/* Card styles */
.card {
  height: 260px;
  width: 420px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(35px);
  border: 2px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 80px rgba(0, 0, 0, 0.25);
  padding: 30px;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.logo {
  padding-bottom: 40px;
  opacity: 0.9;
}

/* Input fields */
.card-number {
  position: relative;
  display: inline-block;
  overflow: hidden;
  margin-bottom: 2px;
  width: 100%;
}

.card-number-input {
  font-size: 30px !important;
  font-family: 'Space Mono', monospace !important;
  width: 100% !important;
  height: 50px !important;
  background: transparent !important;
  border: none !important;
  color: white !important;
  padding: 0 !important;
}

.group {
  display: flex;
  gap: 20px;
  width: 100%;
}

.card-name {
  position: relative;
  display: inline-block;
  overflow: hidden;
  flex: 2;
}

.card-name-input {
  background: transparent !important;
  border: none !important;
  color: white !important;
  font-family: 'Space Mono', monospace !important;
  padding: 0 !important;
  box-shadow: none !important;
  height: 26px !important;
  width: 160px !important;
  font-size: 16px !important;
}

.expiration-date {
  position: relative;
  display: inline-block;
  overflow: hidden;
  flex: 1;
}

.card-exp-input {
  background: transparent !important;
  border: none !important;
  color: white !important;
  font-family: 'Space Mono', monospace !important;
  padding: 0 !important;
  box-shadow: none !important;
  height: 26px !important;
  width: 80px !important;
  font-size: 16px !important;
}

.ccv {
  position: relative;
  display: inline-block;
  overflow: hidden;
  flex: 1;
}

.card-ccv-input {
  background: transparent !important;
  border: none !important;
  color: white !important;
  font-family: 'Space Mono', monospace !important;
  padding: 0 !important;
  box-shadow: none !important;
  height: 26px !important;
  width: 50px !important;
  font-size: 16px !important;
}

/* Underline animation */
.underline {
  transition: all 0.3s;
  display: inline-block;
  bottom: 0;
  left: -100%;
  position: absolute;
  width: 100%;
  height: 2px;
  background-color: #ffffff;
  z-index: 10;
}

.card-number:hover .underline,
.card-name:hover .underline,
.expiration-date:hover .underline,
.ccv:hover .underline {
  left: 0;
}

/* Override shadcn input hover styles */
.card-number-input:hover,
.card-name-input:hover,
.card-exp-input:hover,
.card-ccv-input:hover {
  border-bottom: 2px solid white !important;
  box-shadow: none !important;
  outline: none !important;
}

.card-number-input:focus,
.card-name-input:focus,
.card-exp-input:focus,
.card-ccv-input:focus {
  border-bottom: 2px solid white !important;
  box-shadow: none !important;
  outline: none !important;
}

/* Fix placeholder color */
.card-number-input::placeholder,
.card-name-input::placeholder,
.card-exp-input::placeholder,
.card-ccv-input::placeholder {
  color: #b5b5b5 !important;
  opacity: 1 !important;
}

/* Animations */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(10px) translateX(5px);
  }
  100% {
    transform: translateY(0px) translateX(1px);
  }
}