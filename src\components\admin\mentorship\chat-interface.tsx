"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Send } from "lucide-react";

type Message = {
  id: string;
  sender: string;
  content: string;
  timestamp: string;
  isMe: boolean;
};

const mockMessages: Message[] = [
  {
    id: "1",
    sender: "John Doe",
    content: "Hi coach, I have a question about the backhand technique",
    timestamp: "10:30 AM",
    isMe: false,
  },
  {
    id: "2",
    sender: "Coach",
    content: "Sure, what would you like to know?",
    timestamp: "10:31 AM",
    isMe: true,
  },
  // Add more mock messages as needed
];

export function ChatInterface() {
  const [messages] = useState<Message[]>(mockMessages);
  const [newMessage, setNewMessage] = useState("");

  return (
    <Card className="p-6">
      <div className="grid grid-cols-12 gap-6 h-[600px]">
        {/* Student List */}
        <div className="col-span-3 border-r">
          <h3 className="font-semibold mb-4">Students</h3>
          <div className="space-y-2">
            <Button
              variant="ghost"
              className="w-full justify-start font-normal"
            >
              John Doe
              <span className="ml-auto bg-primary/10 text-primary text-xs px-2 py-1 rounded-full">
                2
              </span>
            </Button>
            {/* Add more students */}
          </div>
        </div>

        {/* Chat Area */}
        <div className="col-span-9 flex flex-col">
          {/* Messages */}
          <div className="flex-1 overflow-y-auto space-y-4 mb-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.isMe ? "justify-end" : "justify-start"}`}
              >
                <div
                  className={`max-w-[70%] rounded-lg p-3 ${
                    message.isMe
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted"
                  }`}
                >
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-semibold text-sm">
                      {message.sender}
                    </span>
                    <span className="text-xs opacity-70">
                      {message.timestamp}
                    </span>
                  </div>
                  <p>{message.content}</p>
                </div>
              </div>
            ))}
          </div>

          {/* Message Input */}
          <div className="flex gap-2">
            <Input
              placeholder="Type your message..."
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
            />
            <Button>
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
}
