import React, { useState, useRef, useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Send, Paperclip, Smile, MoreVertical, Phone, Video } from "lucide-react";
import { format } from 'date-fns';

// Types for our chat interface
interface Message {
  id: string;
  content: string;
  sender: 'user' | 'mentor';
  timestamp: Date;
  read: boolean;
  attachmentUrl?: string;
}

export interface Contact {
  id: string;
  name: string;
  avatar?: string | null;
  lastMessage?: string | null;
  lastMessageTime?: Date | null;
  unreadCount?: number | null;
  status?: 'online' | 'offline' | 'away' | null;
  conversationId?: string | null;
}

interface ChatInterfaceProps {
  currentUserId: string;
  initialContacts?: Contact[];
  initialMessages?: Record<string, Message[]>;
  onSendMessage?: (contactId: string, message: string, attachment?: File) => Promise<void>;
}

export function ChatInterface({
  currentUserId,
  initialContacts = [],
  initialMessages = {},
  onSendMessage
}: ChatInterfaceProps) {
  const [contacts, setContacts] = useState<Contact[]>(initialContacts);
  const [messages, setMessages] = useState<Record<string, Message[]>>(initialMessages);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [messageInput, setMessageInput] = useState('');
  const [attachment, setAttachment] = useState<File | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Scroll to bottom of messages when they change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, selectedContact]);

  // Filter contacts based on search query
  const filteredContacts = contacts.filter(contact => 
    contact.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Handle sending a message
  const handleSendMessage = async () => {
    if ((!messageInput.trim() && !attachment) || !selectedContact) return;
    
    const newMessage: Message = {
      id: Date.now().toString(),
      content: messageInput,
      sender: 'user',
      timestamp: new Date(),
      read: false,
    };
    
    // Update local state
    setMessages(prev => ({
      ...prev,
      [selectedContact.id]: [...(prev[selectedContact.id] || []), newMessage]
    }));
    
    // Clear input
    setMessageInput('');
    
    // Call the onSendMessage callback if provided
    if (onSendMessage) {
      try {
        await onSendMessage(selectedContact.id, messageInput, attachment || undefined);
      } catch (error) {
        console.error('Failed to send message:', error);
        // Handle error (could show a toast notification)
      }
    }
    
    // Clear attachment
    if (attachment) {
      setAttachment(null);
    }
  };

  // Handle file selection
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setAttachment(e.target.files[0]);
    }
  };

  // Format timestamp for display
  const formatMessageTime = (date: Date) => {
    return format(date, 'HH:mm');
  };

  // Format last message time for contact list
  const formatLastMessageTime = (date?: Date) => {
    if (!date) return '';
    const today = new Date();
    if (date.toDateString() === today.toDateString()) {
      return format(date, 'HH:mm');
    }
    return format(date, 'dd/MM/yyyy');
  };

  return (
    <div className="flex h-[calc(100vh-4rem)] overflow-hidden bg-white dark:bg-gray-900 rounded-lg shadow-xl border">
      {/* Left sidebar - Contacts */}
      <div className="w-80 flex flex-col border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center bg-blue-600 dark:bg-blue-700 text-white">
          <h2 className="text-xl font-semibold">Messages</h2>
          <Button variant="ghost" size="icon" className="text-white hover:bg-blue-700 dark:hover:bg-blue-600">
            <MoreVertical className="h-5 w-5" />
          </Button>
        </div>
        
        {/* Search */}
        <div className="p-3 border-b border-gray-200 dark:border-gray-700">
          <Input
            placeholder="Search conversations"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="bg-gray-100 dark:bg-gray-700 border-0 rounded-full"
          />
        </div>
        
        {/* Contact list */}
        <div className="flex-1 overflow-y-auto">
          {filteredContacts.length === 0 ? (
            <div className="p-4 text-center text-gray-500 dark:text-gray-400">No contacts found</div>
          ) : (
            filteredContacts.map(contact => (
              <div
                key={contact.id}
                className={`flex items-center p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors ${
                  selectedContact?.id === contact.id ? 'bg-blue-50 dark:bg-blue-900/20 border-r-2 border-blue-500' : ''
                }`}
                onClick={() => setSelectedContact(contact)}
              >
                <Avatar className="h-12 w-12 mr-3">
                  <AvatarImage src={contact.avatar || undefined} alt={contact.name} />
                  <AvatarFallback className="bg-blue-500 text-white">
                    {contact.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <div className="flex justify-between items-center">
                    <h3 className="font-medium text-gray-900 dark:text-gray-100 truncate">{contact.name}</h3>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {contact.lastMessageTime ? formatLastMessageTime(contact.lastMessageTime) : ''}
                    </span>
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 truncate">{contact.lastMessage}</p>
                </div>
                {contact.unreadCount && contact.unreadCount > 0 && (
                  <div className="ml-2 bg-blue-600 dark:bg-blue-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
                    {contact.unreadCount > 99 ? '99+' : contact.unreadCount}
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
      
      {/* Right side - Chat */}
      <div className="flex-1 flex flex-col">
        {selectedContact ? (
          <>
            {/* Chat header */}
            <div className="p-4 border-b border-gray-200 flex justify-between items-center bg-white">
              <div className="flex items-center">
                <Avatar className="h-10 w-10 mr-3">
                  <AvatarImage src={selectedContact.avatar || undefined} alt={selectedContact.name} />
                  <AvatarFallback className="bg-blue-500 text-white">
                    {selectedContact.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-medium text-gray-900">{selectedContact.name}</h3>
                  <p className="text-xs text-gray-500">
                    {selectedContact.status === 'online' ? 'Online' : 'Last seen recently'}
                  </p>
                </div>
              </div>
              <div className="flex space-x-2">
                <Button variant="ghost" size="icon">
                  <Phone className="h-5 w-5" />
                </Button>
                <Button variant="ghost" size="icon">
                  <Video className="h-5 w-5" />
                </Button>
                <Button variant="ghost" size="icon">
                  <MoreVertical className="h-5 w-5" />
                </Button>
              </div>
            </div>
            
            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 bg-gray-50 dark:bg-gray-900" style={{backgroundImage: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23f3f4f6" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")'}}>
              {messages[selectedContact.id]?.length ? (
                messages[selectedContact.id].map((message, index) => (
                  <div
                    key={message.id}
                    className={`flex mb-3 ${
                      message.sender === 'user' ? 'justify-end' : 'justify-start'
                    }`}
                  >
                    {message.sender === 'mentor' && (
                      <Avatar className="h-8 w-8 mr-2 mt-1 flex-shrink-0">
                        <AvatarImage src={selectedContact.avatar || undefined} alt={selectedContact.name} />
                        <AvatarFallback className="bg-blue-500 text-white text-xs">
                          {selectedContact.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                    )}
                    <div
                      className={`max-w-[70%] rounded-2xl px-4 py-2 shadow-sm ${
                        message.sender === 'user'
                          ? 'bg-blue-600 dark:bg-blue-500 text-white rounded-br-md'
                          : 'bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 rounded-bl-md border dark:border-gray-700'
                      }`}
                    >
                      {message.content}
                      {message.attachmentUrl && (
                        <div className="mt-2">
                          <a 
                            href={message.attachmentUrl} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-sm underline"
                          >
                            View attachment
                          </a>
                        </div>
                      )}
                      <div
                        className={`text-xs mt-1 flex items-center justify-between ${
                          message.sender === 'user' ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'
                        }`}
                      >
                        <span>{formatMessageTime(message.timestamp)}</span>
                        {message.sender === 'user' && (
                          <span className="ml-2 text-blue-200">
                            {message.read ? '✓✓' : '✓'}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="h-full flex items-center justify-center text-gray-500">
                  No messages yet. Start a conversation!
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>
            
            {/* Message input */}
            <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
              {attachment && (
                <div className="mb-3 p-3 bg-gray-100 dark:bg-gray-700 rounded-lg flex justify-between items-center">
                  <span className="text-sm truncate dark:text-gray-200">{attachment.name}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setAttachment(null)}
                    className="h-6 w-6 p-0 hover:bg-gray-200 dark:hover:bg-gray-600"
                  >
                    &times;
                  </Button>
                </div>
              )}
              <div className="flex items-end space-x-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => fileInputRef.current?.click()}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  <Paperclip className="h-5 w-5" />
                </Button>
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileSelect}
                  className="hidden"
                />
                <div className="flex-1 relative">
                  <Input
                    placeholder="Write a message..."
                    value={messageInput}
                    onChange={(e) => setMessageInput(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                    className="rounded-full border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 pr-12"
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  >
                    <Smile className="h-4 w-4" />
                  </Button>
                </div>
                <Button
                  size="icon"
                  onClick={handleSendMessage}
                  className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white rounded-full h-10 w-10"
                  disabled={!messageInput.trim() && !attachment}
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </>
        ) : (
          <div className="h-full flex items-center justify-center text-gray-500 bg-gray-50">
            Select a contact to start chatting
          </div>
        )}
      </div>
    </div>
  );
}
