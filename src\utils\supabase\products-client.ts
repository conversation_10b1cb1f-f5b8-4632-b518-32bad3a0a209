import { createClient } from "./client";
import { v4 as uuidv4 } from "uuid";
import { Product, Category } from "./products";

/**
 * Client-side version of product utility functions
 * These functions use the browser client instead of the server client
 */

export async function getProductsClient(): Promise<Product[]> {
  const supabase = createClient();
  const { data, error } = await supabase.from('products').select('*');
  
  if (error) {
    console.error('Error fetching products:', error);
    return [];
  }
  
  return data || [];
}

export async function getProductsByCategoryClient(category: string): Promise<Product[]> {
  const supabase = createClient();
  const { data, error } = await supabase.from('products').select('*').eq('category', category);
  
  if (error) {
    console.error(`Error fetching products in category ${category}:`, error);
    return [];
  }
  
  return data || [];
}

export async function searchProductsClient(query: string): Promise<Product[]> {
  const supabase = createClient();
  const { data, error } = await supabase
    .from('products')
    .select('*')
    .or(`name.ilike.%${query}%,description.ilike.%${query}%`);
  
  if (error) {
    console.error(`Error searching products with query ${query}:`, error);
    return [];
  }
  
  return data || [];
}

export async function getProductClient(id: string): Promise<Product | null> {
  const supabase = createClient();
  const { data, error } = await supabase.from('products').select('*').eq('id', id).single();
  
  if (error) {
    console.error(`Error fetching product with id ${id}:`, error);
    return null;
  }
  
  return data || null;
}

export async function getCategoriesClient(): Promise<Category[]> {
  const supabase = createClient();
  const { data, error } = await supabase.from('categories').select('*');
  
  if (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
  
  return data || [];
}

export async function createProductClient(product: Omit<Product, 'id'>): Promise<Product | null> {
  // Use the API route instead of direct database calls to ensure proper authentication
  const supabase = createClient();
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    throw new Error('Authentication required');
  }

  const response = await fetch('/api/products', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session.access_token}`,
    },
    body: JSON.stringify(product),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to create product');
  }

  return response.json();
}

export async function updateProductClient(id: string, updates: Partial<Product>): Promise<Product | null> {
  // Use the API route instead of direct database calls to ensure proper authentication
  const supabase = createClient();
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    throw new Error('Authentication required');
  }

  const response = await fetch(`/api/products/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session.access_token}`,
    },
    body: JSON.stringify(updates),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to update product');
  }

  return response.json();
}

export async function deleteProductClient(id: string): Promise<boolean> {
  // Use the API route instead of direct database calls to ensure proper authentication
  const supabase = createClient();
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    throw new Error('Authentication required');
  }

  const response = await fetch(`/api/products/${id}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${session.access_token}`,
    },
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to delete product');
  }

  return true;
}

export async function createCategoryClient(category: Omit<Category, "id">): Promise<Category | null> {
  const supabase = createClient();
  const { data, error } = await supabase
    .from("categories")
    .insert([{ ...category, id: uuidv4() }])
    .select()
    .single();

  if (error) {
    console.error("Error creating category:", error);
    throw error;
  }

  return data as Category;
}

export async function updateCategoryClient(id: string, updates: Partial<Category>): Promise<Category | null> {
  const supabase = createClient();
  const { data, error } = await supabase
    .from("categories")
    .update(updates)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error(`Error updating category with id ${id}:`, error);
    throw error;
  }

  return data as Category;
}

export async function deleteCategoryClient(id: string): Promise<boolean> {
  const supabase = createClient();
  const { error } = await supabase
    .from("categories")
    .delete()
    .eq("id", id);

  if (error) {
    console.error(`Error deleting category with id ${id}:`, error);
    return false;
  }

  return true;
}
