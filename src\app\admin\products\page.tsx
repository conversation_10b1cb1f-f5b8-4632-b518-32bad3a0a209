import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Package, Plus, Search, Filter, Edit, Trash2, MoreHorizontal } from "lucide-react";
import Link from "next/link";
import { getProducts } from "@/utils/supabase/products";
import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";

export default async function ProductsPage() {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  
  // Check if user is admin (you'll need to implement this check based on your auth setup)
  if (!user) {
    redirect("/sign-in?redirect=/admin/products");
  }
  
  // Fetch products from Supabase
  const products = await getProducts();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight gradient-text">Products</h1>
        <Link href="/admin/products/new">
          <Button className="glass-effect-subtle border border-white/20 rounded-2xl px-6 py-3 neo-shadow hover:neo-shadow-light transition-neo min-h-[44px] font-medium">
            <Plus className="h-5 w-5 mr-2" />
            Add Product
          </Button>
        </Link>
      </div>

      <Card className="glass-effect-dark border border-white/10 rounded-3xl neo-shadow">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold gradient-text">Product Management</CardTitle>
          <CardDescription className="text-muted-foreground">
            Manage your product inventory, add new products, or update existing ones.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-4 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search products..."
                className="pl-12 h-12 rounded-2xl bg-muted/30 border-0 neo-shadow-inset placeholder:text-muted-foreground/70 focus:neo-shadow-light transition-neo"
              />
            </div>
            <Button variant="outline" className="sm:w-auto glass-effect-subtle border border-white/20 rounded-2xl px-6 py-3 neo-shadow hover:neo-shadow-light transition-neo min-h-[44px] font-medium">
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-border">
                  <th className="text-left font-medium py-3 px-4">Product</th>
                  <th className="text-left font-medium py-3 px-4">Category</th>
                  <th className="text-left font-medium py-3 px-4">Status</th>
                  <th className="text-right font-medium py-3 px-4">Stock</th>
                  <th className="text-right font-medium py-3 px-4">Price</th>
                  <th className="text-right font-medium py-3 px-4">Actions</th>
                </tr>
              </thead>
              <tbody>
                {products.map((product) => (
                  <tr key={product.id} className="border-b border-border">
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-md overflow-hidden bg-muted">
                          {product.image ? (
                            <img
                              src={product.image}
                              alt={product.name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center bg-muted">
                              <Package className="h-5 w-5 text-muted-foreground" />
                            </div>
                          )}
                        </div>
                        <div>
                          <p className="font-medium">{product.name}</p>
                          <p className="text-xs text-muted-foreground">ID: {product.id.substring(0, 8)}</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-3 px-4">{product.category}</td>
                    <td className="py-3 px-4">
                      <span
                        className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                          product.status === "In Stock"
                            ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                            : product.status === "Low Stock"
                            ? "bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400"
                            : "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400"
                        }`}
                      >
                        {product.status}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-right">{product.stock}</td>
                    <td className="py-3 px-4 text-right">
                      R {product.price.toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center justify-end gap-2">
                        <Link href={`/admin/products/${product.id}/edit`}>
                          <Button variant="ghost" size="icon" className="h-10 w-10 rounded-2xl glass-effect-subtle hover:neo-shadow-light transition-neo">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Link href={`/admin/products/${product.id}/delete`}>
                          <Button variant="ghost" size="icon" className="h-10 w-10 rounded-2xl glass-effect-subtle hover:neo-shadow-light transition-neo text-destructive">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button variant="ghost" size="icon" className="h-10 w-10 rounded-2xl glass-effect-subtle hover:neo-shadow-light transition-neo">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
                {products.length === 0 && (
                  <tr>
                    <td colSpan={6} className="py-6 text-center text-muted-foreground">
                      No products found. Add your first product to get started.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          <div className="flex items-center justify-between mt-6">
            <p className="text-sm text-muted-foreground font-medium">
              Showing <strong className="text-foreground">1-{products.length}</strong> of <strong className="text-foreground">{products.length}</strong> products
            </p>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                disabled
                className="glass-effect-subtle border border-white/20 rounded-2xl px-4 py-2 neo-shadow hover:neo-shadow-light transition-neo min-h-[44px] font-medium disabled:opacity-50"
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                disabled={products.length < 10}
                className="glass-effect-subtle border border-white/20 rounded-2xl px-4 py-2 neo-shadow hover:neo-shadow-light transition-neo min-h-[44px] font-medium disabled:opacity-50"
              >
                Next
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
