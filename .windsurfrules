1. Follow the latest and best coding practices.
2. Always add descriptive comments to the code.
3. Use descriptive variables that are easy to understand.
4. Always explain your code concisely but easy to understand.
5. Always add intuitive and user-friendly error handling.
6. Incorporate intuitive UI and trendy design.
7. Make sure the code is efficient and loads fast.
8. Don’t edit multiple code files in parallel, edit one at a time.
9. When you are asking to replace code. Tell what code to replace and with what.
10. For dependencies/framework/libraries, use CDN unless asked to install.
11. Follow a unified design system.
    • Define and use a shared style guide (colors, typography, spacing, components)  
    • Keep all UI elements, icons and interactions consistent across pages.

12. Adopt a mobile-first, responsive approach.
    • Start your layouts for small screens, then scale up  
    • Test on a variety of devices and breakpoints before release.

13. Ensure full accessibility compliance.
    • Use semantic HTML, proper ARIA roles and labels  
    • Provide keyboard navigation, sufficient color contrast, and alt text for images.

14. Maintain cross-browser compatibility.
    • Test on the latest versions of Chrome, Firefox, Safari and Edge  
    • Polyfill or gracefully degrade any features not supported everywhere.

15. Optimize assets and performance.
    • Compress and lazy-load images, fonts and media  
    • Minify CSS/JS, leverage HTTP/2 or CDN caching, and measure Lighthouse scores regularly.

16. organized and reusable code across the application.

# Project Structure & Architecture
- Follow Next.js patterns and use the App Router.
- Correctly determine when to use server vs. client components in Next.js.

# Styling & UI
- Use Tailwind CSS for styling.
- Use Shadcn UI for components.

# Data Fetching & Forms
- Use TanStack Query (react-query) for frontend data fetching.
- Use React Hook Form for form handling.
- Use Zod for validation.

# State Management & Logic
- Use React Context for state management.

# Backend & Database
supabase (auth and storage)

Frontend:

React with Next.js

Tailwind CSS for styling

React Hook Form for form state management

Zod for schema validation

TanStack Query (React Query) for data fetching and caching
Techstaunch
+3
Makerkit
+3
Supabase
+3

Backend:

Supabase for database, authentication, and storage

Utilities:

@supabase/supabase-js for Supabase client interactions

@supabase-cache-helpers/postgrest-react-query for enhanced React Query integration

supabase-to-zod for generating Zod schemas from Supabase types