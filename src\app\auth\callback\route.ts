import { createClient } from "../../../utils/supabase/server";
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get("code");
  const redirect_to = requestUrl.searchParams.get("redirect_to");

  if (code) {
    const supabase = await createClient();
    const { data: { session }, error } = await supabase.auth.exchangeCodeForSession(code);

    if (session && !error) {
      // Check if user has admin role for automatic redirection
      const { data: userData } = await supabase
        .from('users')
        .select('role')
        .eq('id', session.user.id)
        .single();

      // If no specific redirect is provided, determine based on user role
      if (!redirect_to) {
        if (userData?.role === 'admin') {
          return NextResponse.redirect(new URL("/admin", requestUrl.origin));
        }
        // Default to regular dashboard for non-admin users
        return NextResponse.redirect(new URL("/dashboard", requestUrl.origin));
      }
    }
  }

  // URL to redirect to after sign in process completes
  // Use provided redirect or default to dashboard
  const redirectTo = redirect_to || "/dashboard";
  return NextResponse.redirect(new URL(redirectTo, requestUrl.origin));
}
