"use client";

import { Suspense } from "react";
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import Hero3D from "@/components/demo/hero-3d";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { <PERSON><PERSON>eft, Zap, Eye, MousePointer } from "lucide-react";

export default function Hero3DDemo() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-green-50/30">
      <Navbar />
      
      {/* Demo Header */}
      <div className="pt-24 pb-8">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <Button asChild variant="outline" size="sm">
                <Link href="/">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Home
                </Link>
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-slate-900">3D Interactive Hero Demo</h1>
                <p className="text-slate-600">Interactive 3D tennis ball with parallax animations</p>
              </div>
            </div>
            
            {/* Demo Navigation */}
            <div className="flex gap-2">
              <Button asChild variant="outline" size="sm">
                <Link href="/demo/hero-ballpit">Ballpit Demo</Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href="/demo/hero-ballpit-cards">Ballpit + Cards</Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href="/demo/hero-video">Video Demo</Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href="/demo/hero-gamified">Game Demo</Link>
              </Button>
            </div>
          </div>

          {/* Demo Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-green-100">
              <div className="flex items-center gap-2 mb-2">
                <MousePointer className="w-4 h-4 text-green-600" />
                <span className="text-sm font-semibold">Interactive Controls</span>
              </div>
              <p className="text-xs text-slate-600">Drag to rotate, scroll to zoom, touch-friendly on mobile</p>
            </div>
            
            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-green-100">
              <div className="flex items-center gap-2 mb-2">
                <Zap className="w-4 h-4 text-green-600" />
                <span className="text-sm font-semibold">Performance Optimized</span>
              </div>
              <p className="text-xs text-slate-600">Reduced quality on mobile, WebGL fallback support</p>
            </div>
            
            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-green-100">
              <div className="flex items-center gap-2 mb-2">
                <Eye className="w-4 h-4 text-green-600" />
                <span className="text-sm font-semibold">Parallax Animations</span>
              </div>
              <p className="text-xs text-slate-600">Text animations triggered by ball rotation angle</p>
            </div>
          </div>
        </div>
      </div>

      {/* 3D Hero Section */}
      <Suspense fallback={
        <div className="h-[600px] flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-green-600 rounded-full flex items-center justify-center animate-pulse">
              <Zap className="w-8 h-8 text-white" />
            </div>
            <p className="text-slate-600">Loading 3D Experience...</p>
          </div>
        </div>
      }>
        <Hero3D />
      </Suspense>

      {/* Demo Info Section */}
      <section className="py-16 bg-white/50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-8">3D Interactive Hero Features</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-slate-900">Technical Implementation</h3>
                <ul className="space-y-2 text-slate-600">
                  <li>• React Three Fiber for 3D rendering</li>
                  <li>• Touch and mouse gesture controls</li>
                  <li>• Automatic quality adjustment for mobile</li>
                  <li>• WebGL fallback for unsupported devices</li>
                  <li>• Performance monitoring and optimization</li>
                </ul>
              </div>
              
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-slate-900">User Experience</h3>
                <ul className="space-y-2 text-slate-600">
                  <li>• Intuitive drag-to-rotate controls</li>
                  <li>• Pinch-to-zoom on mobile devices</li>
                  <li>• Auto-rotation when inactive</li>
                  <li>• Parallax text animations</li>
                  <li>• Smooth 60fps performance target</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
