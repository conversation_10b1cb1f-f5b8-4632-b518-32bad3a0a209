"use client";

import { useEffect, useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import "@/components/ui/glassmorphism-card.css";

interface GlassmorphismCardProps {
  cardNumber?: string;
  cardHolder?: string;
  expiryDate?: string;
  cvc?: string;
  disabled?: boolean;
  className?: string;
}

export function GlassmorphismCard({
  cardNumber = "",
  cardHolder = "",
  expiryDate = "",
  cvc = "",
  disabled = false,
  className,
}: GlassmorphismCardProps) {
  const [formattedCardNumber, setFormattedCardNumber] = useState("");
  const [formattedExpiryDate, setFormattedExpiryDate] = useState("");
  const [holder, setHolder] = useState("");
  const [cvcValue, setCvcValue] = useState("");

  useEffect(() => {
    setFormattedCardNumber(
      cardNumber.replace(/[^0-9]+/g, "").replace(/(.{4})/g, "$1 ").trim()
    );
  }, [cardNumber]);

  useEffect(() => {
    if (expiryDate.length === 2 && !expiryDate.includes("/")) {
      setFormattedExpiryDate(`${expiryDate}/`);
    } else {
      setFormattedExpiryDate(expiryDate);
    }
  }, [expiryDate]);

  useEffect(() => {
    setHolder(cardHolder);
  }, [cardHolder]);

  useEffect(() => {
    setCvcValue(cvc);
  }, [cvc]);

  return (
    <div className={cn("relative items-center", className)}>
      <div className="circles">
        <div className="circle circle-1"></div>
        <div className="circle circle-2"></div>
      </div>

      <div className="card">
          <div className="logo">
            {/* Tennis Whisperer Logo */}
            <div className="flex items-center">
              <svg
                width="48px"
                height="48px"
                viewBox="0 0 64 64"
                xmlns="http://www.w3.org/2000/svg"
                className="tennis-logo"
              >
                
                  {/* Tennis ball */}
                  <span className="text-2xl">🎾</span>
                
              </svg>
              <span className="text-white text-sm ml-2 font-semibold">Tennis Whisperer</span>
            </div>
          </div>

          <div className="card-number">
            <Label htmlFor="card-number">Card Number</Label>
            <Input
              id="card-number"
              placeholder="4242 4242 4242 4242"
              value={formattedCardNumber}
              onChange={(e) =>
                setFormattedCardNumber(
                  e.target.value.replace(/[^0-9]+/g, "").replace(/(.{4})/g, "$1 ").trim()
                )
              }
              disabled={disabled}
              className="card-number-input"
            />
            <span className="underline"></span>
          </div>

          <br />

          <div className="group grid grid-cols-3 gap-4">
            <div className="card-name">
              <Label htmlFor="card-name">Card Holder</Label>
              <Input
                id="card-name"
                placeholder="John Doe"
                value={holder}
                onChange={(e) => setHolder(e.target.value)}
                disabled={disabled}
                className="card-name-input"
              />
              <span className="underline"></span>
            </div>

            <div className="expiration-date">
              <Label htmlFor="card-exp">Exp. Date</Label>
              <Input
                id="card-exp"
                placeholder="MM/YY"
                value={formattedExpiryDate}
                onChange={(e) => setFormattedExpiryDate(e.target.value)}
                disabled={disabled}
                className="card-exp-input"
              />
              <span className="underline"></span>
            </div>

            <div className="ccv">
              <Label htmlFor="card-ccv">CVC</Label>
              <Input
                id="card-ccv"
                placeholder="123"
                value={cvcValue}
                onChange={(e) => setCvcValue(e.target.value)}
                disabled={disabled}
                className="card-ccv-input"
              />
              <span className="underline"></span>
            </div>
          </div>
      </div>
    </div>
  );
}
