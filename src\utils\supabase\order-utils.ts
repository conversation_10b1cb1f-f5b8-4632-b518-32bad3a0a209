import supabase from './supabase-client';
import { Tables, InsertTables, UpdateTables } from './database.types';
import { v4 as uuidv4 } from 'uuid';

/**
 * Order management utilities for Tennis-Gear application
 * These functions handle order CRUD operations with Supabase
 */

/**
 * Create a new order
 * @param userId User ID
 * @param items Order items
 * @param shippingDetails Shipping details
 * @param totalAmount Total order amount
 * @returns Created order or error
 */
export async function createOrder(
  userId: string,
  items: any[],
  shippingDetails: any,
  totalAmount: number
) {
  // Create the order
  const orderData: InsertTables<'orders'> = {
    id: uuidv4(),
    user_id: userId,
    items: items,
    shipping_details: shippingDetails,
    status: 'pending',
    payment_status: 'pending',
    total_amount: totalAmount,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  const { data, error } = await supabase
    .from('orders')
    .insert(orderData)
    .select()
    .single();
  
  return { data, error };
}

/**
 * Get all orders for a user
 * @param userId User ID
 * @returns List of orders or error
 */
export async function getUserOrders(userId: string) {
  const { data, error } = await supabase
    .from('orders')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });
  
  return { data, error };
}

/**
 * Get all orders (admin function)
 * @param filters Optional filters
 * @returns List of orders or error
 */
export async function getAllOrders(filters?: {
  status?: string;
  fromDate?: string;
  toDate?: string;
  minAmount?: number;
  maxAmount?: number;
}) {
  let query = supabase
    .from('orders')
    .select('*');
  
  // Apply filters if provided
  if (filters) {
    if (filters.status) {
      query = query.eq('status', filters.status);
    }
    
    if (filters.fromDate) {
      query = query.gte('created_at', filters.fromDate);
    }
    
    if (filters.toDate) {
      query = query.lte('created_at', filters.toDate);
    }
    
    if (filters.minAmount !== undefined) {
      query = query.gte('total_amount', filters.minAmount);
    }
    
    if (filters.maxAmount !== undefined) {
      query = query.lte('total_amount', filters.maxAmount);
    }
  }
  
  // Order by created_at descending (newest first)
  query = query.order('created_at', { ascending: false });
  
  const { data, error } = await query;
  
  return { data, error };
}

/**
 * Get a single order by ID
 * @param orderId Order ID
 * @returns Order data or error
 */
export async function getOrder(orderId: string) {
  const { data, error } = await supabase
    .from('orders')
    .select('*')
    .eq('id', orderId)
    .single();
  
  return { data, error };
}

/**
 * Update order status
 * @param orderId Order ID
 * @param status New status
 * @returns Updated order or error
 */
export async function updateOrderStatus(orderId: string, status: string) {
  const updateData: UpdateTables<'orders'> = { 
    status, 
    updated_at: new Date().toISOString() 
  };
  
  const { data, error } = await supabase
    .from('orders')
    .update(updateData)
    .eq('id', orderId)
    .select()
    .single();
  
  return { data, error };
}

/**
 * Update payment status with Yoco payment details
 * @param orderId Order ID
 * @param paymentStatus New payment status
 * @param yocoPaymentId Yoco payment ID
 * @param yocoTransactionId Yoco transaction ID
 * @returns Updated order or error
 */
export async function updatePaymentStatus(
  orderId: string, 
  paymentStatus: string,
  yocoPaymentId?: string,
  yocoTransactionId?: string
) {
  const updates: UpdateTables<'orders'> = { 
    payment_status: paymentStatus, 
    updated_at: new Date().toISOString() 
  };
  
  if (yocoPaymentId) {
    updates.yoco_payment_id = yocoPaymentId;
  }
  
  if (yocoTransactionId) {
    updates.yoco_transaction_id = yocoTransactionId;
  }
  
  const { data, error } = await supabase
    .from('orders')
    .update(updates)
    .eq('id', orderId)
    .select()
    .single();
  
  return { data, error };
}

/**
 * Delete an order (admin function)
 * @param orderId Order ID
 * @returns Success or error
 */
export async function deleteOrder(orderId: string) {
  const { error } = await supabase
    .from('orders')
    .delete()
    .eq('id', orderId);
  
  return { error };
}

/**
 * Subscribe to order status updates
 * @param userId User ID
 * @param callback Function to call when order status changes
 * @returns Subscription object
 */
export function subscribeToOrderUpdates(userId: string, callback: (payload: any) => void) {
  const subscription = supabase
    .channel('orders')
    .on('postgres_changes', {
      event: 'UPDATE',
      schema: 'public',
      table: 'orders',
      filter: `user_id=eq.${userId}`
    }, callback)
    .subscribe();
  
  return subscription;
}
