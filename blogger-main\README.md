# The Chronicle - Professional Blog & Ecommerce Platform

<div align="center">

![The Chronicle Logo](docs/images/logo.png)

[![Build Status](https://github.com/thechronicle/app/workflows/CI%2FCD%20Pipeline/badge.svg)](https://github.com/thechronicle/app/actions)
[![Coverage Status](https://codecov.io/gh/thechronicle/app/branch/main/graph/badge.svg)](https://codecov.io/gh/thechronicle/app)
[![Security Rating](https://sonarcloud.io/api/project_badges/measure?project=thechronicle_app&metric=security_rating)](https://sonarcloud.io/dashboard?id=thechronicle_app)
[![Quality Gate Status](https://sonarcloud.io/api/project_badges/measure?project=thechronicle_app&metric=alert_status)](https://sonarcloud.io/dashboard?id=thechronicle_app)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

**A modern, enterprise-grade news-style blog and ecommerce platform with comprehensive CMS capabilities, paywall system, and integrated shopping experience.**

[🚀 Live Demo](https://thechronicle.com) • [📖 Documentation](docs/) • [🐛 Report Bug](https://github.com/thechronicle/app/issues) • [💡 Request Feature](https://github.com/thechronicle/app/issues)

</div>

## 🌟 Overview

The Chronicle is a professional-grade content management and ecommerce platform designed for modern digital publishing. Built with enterprise-level architecture, it combines the power of a news-style blog with integrated shopping capabilities, offering a complete solution for content creators, publishers, and online businesses.

## 🌟 Features

### 🎨 **Modern Frontend**
- **React 18 + TypeScript** - Type-safe, modern development
- **Vite Build System** - Lightning-fast development and builds
- **Tailwind CSS** - Utility-first styling with custom design system
- **Responsive Design** - Mobile-first newspaper-style grid layout
- **Progressive Web App** - Offline support and app-like experience
- **SEO Optimized** - Google's top ranking system implementation

### 🔐 **Authentication & Security**
- **JWT Authentication** - Secure token-based authentication
- **Social Login** - Google, GitHub, and other OAuth providers
- **Multi-Factor Authentication** - TOTP-based 2FA security
- **Row Level Security** - Database-level access control
- **Input Validation** - Comprehensive XSS and injection protection
- **Rate Limiting** - API and login attempt protection

### 💳 **Ecommerce & Payments**
- **Stripe Integration** - Secure payment processing
- **Digital Products** - Downloadable content and subscriptions
- **Shopping Cart** - Persistent cart with local storage
- **Order Management** - Complete order lifecycle tracking
- **Subscription System** - Recurring payment support
- **Paywall System** - Premium content access control

### 📝 **Content Management**
- **Rich Text Editor** - Full-featured WYSIWYG editor
- **Media Management** - Image, video, and file uploads
- **Article System** - Complete blog post management
- **Category & Tags** - Flexible content organization
- **SEO Tools** - Meta tags, structured data, sitemaps
- **Content Scheduling** - Publish articles at specific times

### 📊 **Analytics & Monitoring**
- **Google Analytics 4** - Comprehensive user behavior tracking
- **Custom Analytics** - Business-specific metrics and KPIs
- **Error Tracking** - Real-time error monitoring and reporting
- **Performance Monitoring** - Core Web Vitals and custom metrics
- **User Engagement** - Detailed user interaction analytics

### 🛠 **Admin Dashboard**
- **User Management** - Complete user administration
- **Content Moderation** - Article and comment management
- **Analytics Dashboard** - Real-time metrics and insights
- **System Monitoring** - Health checks and performance metrics
- **Notification System** - Real-time alerts and updates

## 🏗 Tech Stack

### **Frontend**
- **React 18** - Latest React with concurrent features
- **TypeScript** - Type-safe development
- **Vite** - Next-generation build tool
- **Tailwind CSS** - Utility-first CSS framework
- **Radix UI** - Accessible component primitives
- **React Router v6** - Client-side routing
- **React Hook Form** - Performant forms with validation

### **Backend & Database**
- **Supabase** - Backend-as-a-Service platform
- **PostgreSQL** - Robust relational database
- **Row Level Security** - Database-level access control
- **Real-time Subscriptions** - Live data updates
- **Edge Functions** - Serverless compute

### **Payments & Commerce**
- **Stripe** - Payment processing and subscriptions
- **Stripe Elements** - Secure payment forms
- **Webhook Handling** - Real-time payment events
- **Tax Calculation** - Automated tax computation

### **Development & DevOps**
- **ESLint** - Code linting and formatting
- **Prettier** - Code formatting
- **Husky** - Git hooks for quality gates
- **GitHub Actions** - CI/CD pipeline
- **Docker** - Containerization
- **Vercel** - Deployment platform

## Quick Start

1. **Clone and install dependencies**
   ```bash
   npm install
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   Fill in your Supabase credentials in the `.env` file.

3. **🚨 CRITICAL: Set up database**

   **Run the database setup script in your Supabase SQL editor:**

   1. Go to your Supabase dashboard
   2. Navigate to SQL Editor
   3. Copy and paste the contents of `supabase/setup-database.sql`
   4. Run the script

   This creates all necessary tables, policies, and functions.

4. **Set your admin role**

   After creating your account, update your role in the database:
   ```sql
   UPDATE public.profiles SET role = 'admin' WHERE email = '<EMAIL>';
   ```

5. **Start development server**
   ```bash
   npm run dev
   ```

6. **Build for production**
   ```bash
   npm run build
   ```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run type-check` - Run TypeScript type checking
- `npm run check` - Run both type checking and linting
- `npm run types:supabase` - Generate Supabase types

## Environment Variables

Copy `.env.example` to `.env` and configure:

- `VITE_SUPABASE_URL` - Your Supabase project URL
- `VITE_SUPABASE_ANON_KEY` - Your Supabase anonymous key
- `SUPABASE_PROJECT_ID` - For type generation
- `VITE_TEMPO` - Enable Tempo devtools (optional)

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── auth/           # Authentication components
│   ├── dashboard/      # Dashboard-specific components
│   ├── pages/          # Page components
│   └── ui/             # Base UI components (shadcn/ui)
├── lib/                # Utility functions
├── types/              # TypeScript type definitions
└── supabase/           # Supabase configuration and auth
```

## 🚨 Troubleshooting

### "Profiles table doesn't exist" Error
**Solution**: Run the `supabase/setup-database.sql` script in your Supabase SQL editor.

### Role Not Updating After Database Change
**Solution**: Use the "Refresh Role" button in Settings or log out and log back in.

### 404 Database Errors
**Solution**: Ensure all tables are created and RLS policies are properly set up using the setup script.

### Admin Role Not Working
**Solution**: After creating your account, manually update your role:
```sql
UPDATE public.profiles SET role = 'admin' WHERE email = '<EMAIL>';
```

## Recent Improvements

✅ **Fixed Critical Database Issues:**
- Fixed profiles table 404 errors
- Added comprehensive database setup script
- Created proper RLS policies and triggers
- Fixed role caching and refresh issues

✅ **Enhanced Role Management:**
- Admins can now change user roles from dashboard
- Added role refresh functionality in settings
- Fixed role persistence after database changes
- Improved error handling and user feedback

✅ **Fixed Critical Issues:**
- Removed unused imports and missing dependencies
- Added proper ESLint configuration
- Fixed TypeScript strict mode issues
- Improved error handling with Error Boundary
- Added environment variable validation

✅ **Enhanced Developer Experience:**
- Enabled TypeScript strict mode for better type safety
- Added comprehensive linting rules
- Improved build scripts and type checking
- Added proper environment variable documentation

## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type aware lint rules:

- Configure the top-level `parserOptions` property like this:

```js
export default {
  // other rules...
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
    project: ['./tsconfig.json', './tsconfig.node.json'],
    tsconfigRootDir: __dirname,
  },
}
```

- Replace `plugin:@typescript-eslint/recommended` to `plugin:@typescript-eslint/recommended-type-checked` or `plugin:@typescript-eslint/strict-type-checked`
- Optionally add `plugin:@typescript-eslint/stylistic-type-checked`
- Install [eslint-plugin-react](https://github.com/jsx-eslint/eslint-plugin-react) and add `plugin:react/recommended` & `plugin:react/jsx-runtime` to the `extends` list
