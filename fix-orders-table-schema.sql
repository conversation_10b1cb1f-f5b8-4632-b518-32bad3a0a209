-- Fix Orders Table Schema
-- Add missing columns for admin order management

-- Add notes column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'orders' 
        AND column_name = 'notes'
    ) THEN
        ALTER TABLE public.orders ADD COLUMN notes TEXT;
        RAISE NOTICE 'Added notes column to orders table';
    ELSE
        RAISE NOTICE 'Notes column already exists in orders table';
    END IF;
END $$;

-- Add shipping_method column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'orders' 
        AND column_name = 'shipping_method'
    ) THEN
        ALTER TABLE public.orders ADD COLUMN shipping_method TEXT;
        RAISE NOTICE 'Added shipping_method column to orders table';
    ELSE
        RAISE NOTICE 'Shipping_method column already exists in orders table';
    END IF;
END $$;

-- Verify the table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'orders'
ORDER BY ordinal_position;

-- Create sample order data for testing if no orders exist
DO $$
DECLARE
    order_count INTEGER;
    sample_user_id UUID;
BEGIN
    -- Check if any orders exist
    SELECT COUNT(*) INTO order_count FROM public.orders;
    
    IF order_count = 0 THEN
        -- Get a user ID to use for sample data (preferably an admin)
        SELECT id INTO sample_user_id 
        FROM public.users 
        WHERE role = 'admin' 
        LIMIT 1;
        
        -- If no admin user exists, get any user
        IF sample_user_id IS NULL THEN
            SELECT id INTO sample_user_id 
            FROM public.users 
            LIMIT 1;
        END IF;
        
        -- If still no user, create a sample user
        IF sample_user_id IS NULL THEN
            INSERT INTO public.users (id, email, full_name, role)
            VALUES (
                '550e8400-e29b-41d4-a716-************',
                '<EMAIL>',
                'Admin User',
                'admin'
            )
            ON CONFLICT (id) DO NOTHING;
            
            sample_user_id := '550e8400-e29b-41d4-a716-************';
        END IF;
        
        -- Create sample orders for testing
        INSERT INTO public.orders (
            id,
            user_id,
            items,
            shipping_details,
            status,
            payment_status,
            total_amount,
            notes,
            shipping_method,
            created_at,
            updated_at
        ) VALUES 
        (
            '550e8400-e29b-41d4-a716-446655440001',
            sample_user_id,
            '[
                {
                    "name": "Wilson Pro Staff Tennis Racket",
                    "price": 2500.00,
                    "quantity": 1,
                    "product_id": "racket-001",
                    "image": "/images/products/wilson-pro-staff.jpg"
                },
                {
                    "name": "Tennis Ball Set (3 balls)",
                    "price": 150.00,
                    "quantity": 2,
                    "product_id": "balls-001",
                    "image": "/images/products/tennis-balls.jpg"
                }
            ]'::jsonb,
            '{
                "name": "John Smith",
                "email": "<EMAIL>",
                "phone": "+27123456789",
                "address": "123 Main Street",
                "city": "Cape Town",
                "postal_code": "8001",
                "province": "Western Cape",
                "country": "South Africa"
            }'::jsonb,
            'processing',
            'paid',
            2800.00,
            'Customer requested express delivery. Handle with care.',
            'express',
            NOW() - INTERVAL '2 days',
            NOW() - INTERVAL '1 day'
        ),
        (
            '550e8400-e29b-41d4-a716-446655440002',
            sample_user_id,
            '[
                {
                    "name": "Nike Tennis Shoes",
                    "price": 1800.00,
                    "quantity": 1,
                    "product_id": "shoes-001",
                    "image": "/images/products/nike-tennis-shoes.jpg"
                }
            ]'::jsonb,
            '{
                "name": "Sarah Johnson",
                "email": "<EMAIL>",
                "phone": "+27987654321",
                "address": "456 Oak Avenue",
                "city": "Johannesburg",
                "postal_code": "2000",
                "province": "Gauteng",
                "country": "South Africa"
            }'::jsonb,
            'delivered',
            'paid',
            1800.00,
            'Delivered successfully. Customer very satisfied.',
            'standard',
            NOW() - INTERVAL '5 days',
            NOW() - INTERVAL '1 day'
        ),
        (
            '550e8400-e29b-41d4-a716-446655440003',
            sample_user_id,
            '[
                {
                    "name": "Tennis Training Kit",
                    "price": 950.00,
                    "quantity": 1,
                    "product_id": "kit-001",
                    "image": "/images/products/training-kit.jpg"
                }
            ]'::jsonb,
            '{
                "name": "Mike Wilson",
                "email": "<EMAIL>",
                "phone": "+27555123456",
                "address": "789 Pine Road",
                "city": "Durban",
                "postal_code": "4000",
                "province": "KwaZulu-Natal",
                "country": "South Africa"
            }'::jsonb,
            'pending',
            'pending',
            950.00,
            'Waiting for payment confirmation.',
            'standard',
            NOW() - INTERVAL '1 day',
            NOW() - INTERVAL '1 day'
        );
        
        RAISE NOTICE 'Created 3 sample orders for testing';
    ELSE
        RAISE NOTICE 'Orders already exist in the database (count: %)', order_count;
    END IF;
END $$;

-- Verify sample data was created
SELECT 
    id,
    status,
    payment_status,
    total_amount,
    notes,
    shipping_method,
    created_at
FROM public.orders
ORDER BY created_at DESC
LIMIT 5;
