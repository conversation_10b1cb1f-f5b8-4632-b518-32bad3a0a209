import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { createServiceClient } from '@/utils/supabase/service';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions - any admin can log activities
    const { data: adminData } = await supabase
      .from('users')
      .select('role, admin_role')
      .eq('id', session.user.id)
      .single();

    if (!adminData || adminData.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const {
      action_type,
      action_description,
      target_id,
      target_type,
      metadata,
      success = true,
      error_message
    } = body;

    if (!action_type || !action_description) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Use service role client for database operations
    const serviceSupabase = createServiceClient();

    // Get client IP and user agent
    const forwarded = request.headers.get('x-forwarded-for');
    const ip = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || '127.0.0.1';
    const userAgent = request.headers.get('user-agent') || 'Unknown';

    // Insert activity log
    const { data: activity, error } = await serviceSupabase
      .from('admin_activity_logs')
      .insert({
        admin_id: session.user.id,
        action_type,
        action_description,
        target_id: target_id || null,
        target_type: target_type || null,
        metadata: metadata || null,
        ip_address: ip,
        user_agent: userAgent,
        success,
        error_message: error_message || null
      })
      .select()
      .single();

    if (error) {
      console.error('Activity log error:', error);
      return NextResponse.json({ error: 'Failed to log activity' }, { status: 500 });
    }

    return NextResponse.json({ success: true, activity });

  } catch (error) {
    console.error('Activity logging error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions - only main admins can view activity logs
    const { data: adminData } = await supabase
      .from('users')
      .select('role, admin_role')
      .eq('id', session.user.id)
      .single();

    if (!adminData || adminData.role !== 'admin' || adminData.admin_role !== 'admin') {
      return NextResponse.json({ error: 'Main admin access required' }, { status: 403 });
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search');
    const actionType = searchParams.get('action_type');
    const adminId = searchParams.get('admin_id');
    const dateFilter = searchParams.get('date_filter');

    const offset = (page - 1) * limit;

    // Use service role client for database operations
    const serviceSupabase = createServiceClient();

    // Build query - use simpler approach to avoid join issues
    let query = serviceSupabase
      .from('admin_activity_logs')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply filters
    if (search) {
      query = query.ilike('action_description', `%${search}%`);
    }

    if (actionType && actionType !== 'all') {
      query = query.eq('action_type', actionType);
    }

    if (adminId && adminId !== 'all') {
      query = query.eq('admin_id', adminId);
    }

    // Date filter
    if (dateFilter && dateFilter !== 'all') {
      const now = new Date();
      let startDate = new Date();
      
      switch (dateFilter) {
        case '1d':
          startDate.setDate(now.getDate() - 1);
          break;
        case '7d':
          startDate.setDate(now.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(now.getDate() - 30);
          break;
      }
      
      query = query.gte('created_at', startDate.toISOString());
    }

    const { data: activities, error, count } = await query;

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: 'Database error' }, { status: 500 });
    }

    // Fetch admin details separately to avoid join issues
    const activitiesWithAdminInfo = await Promise.all(
      (activities || []).map(async (activity) => {
        const { data: adminData } = await serviceSupabase
          .from('users')
          .select('email, full_name, admin_role')
          .eq('id', activity.admin_id)
          .single();

        return {
          ...activity,
          admin: adminData || {
            email: 'Unknown',
            full_name: 'Unknown Admin',
            admin_role: 'unknown'
          }
        };
      })
    );

    return NextResponse.json({
      activities: activitiesWithAdminInfo,
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}


