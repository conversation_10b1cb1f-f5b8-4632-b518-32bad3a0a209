{"private": true, "scripts": {"dev": "next dev", "dev:https": "next dev --experimental-https", "dev:custom-https": "node server-https.js", "generate-certs": "node generate-certificates.js", "build": "next build", "start": "next start", "migrate": "node scripts/run-migration.js", "test": "vitest --config vitest.config.mjs", "test:ui": "vitest --ui --config vitest.config.mjs", "test:run": "vitest run --config vitest.config.mjs", "magic": "node start-magic.js"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.11", "@react-three/drei": "^9.99.7", "@react-three/fiber": "^8.18.0", "@supabase-cache-helpers/postgrest-react-query": "^1.13.4", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@tanstack/react-query": "^5.77.2", "@types/react-datepicker": "^6.2.0", "@types/three": "^0.177.0", "@types/uuid": "^10.0.0", "autoprefixer": "10.4.20", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.5.0", "gsap": "^3.13.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.468.0", "next": "^14.2.28", "next-themes": "^0.2.1", "prettier": "^3.3.3", "radix-ui": "^1.1.3", "react": "^18", "react-datepicker": "^8.4.0", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.7", "supabase-to-zod": "^1.0.7", "tempo-devtools": "^2.0.102", "three": "^0.162.0", "three-stdlib": "^2.36.0", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^3.25.36"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jspdf": "^1.3.3", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@vitejs/plugin-react": "^4.1.1", "jsdom": "^23.0.1", "postcss": "^8", "tailwind-merge": "^2", "tailwindcss": "^3", "tailwindcss-animate": "^1", "typescript": "^5", "vitest": "^3.2.3"}}