-- =====================================================
-- ADD COMPREHENSIVE TEST CONTENT FOR THE CHRONICLE
-- This script adds sample articles, comments, and likes
-- Run this AFTER creating an admin user
-- =====================================================

-- STEP 1: CREATE SAMPLE ADMIN USER (if not exists)
-- =====================================================
-- Note: This will be handled by the application when a user signs up
-- The first user automatically becomes admin via the trigger function

-- STEP 2: INSERT SAMPLE ARTICLES
-- =====================================================
-- We'll use a placeholder admin user ID that will be replaced when real admin signs up
DO $$
DECLARE
    admin_user_id uuid;
    tech_category_id uuid := '550e8400-e29b-41d4-a716-************';
    business_category_id uuid := '550e8400-e29b-41d4-a716-************';
    health_category_id uuid := '550e8400-e29b-41d4-a716-************';
    finance_category_id uuid := '550e8400-e29b-41d4-a716-************';
    lifestyle_category_id uuid := '550e8400-e29b-41d4-a716-************';
BEGIN
    -- Get the first admin user (if exists)
    SELECT id INTO admin_user_id FROM public.profiles WHERE role = 'admin' LIMIT 1;
    
    -- If no admin user exists, create a placeholder that will be updated later
    IF admin_user_id IS NULL THEN
        admin_user_id := '00000000-0000-0000-0000-000000000000';
    END IF;

    -- Insert sample articles
    INSERT INTO public.articles (id, title, slug, excerpt, content, featured_image, is_published, is_premium, category_id, author_id, views, likes, comments_count, read_time, published_at) VALUES
    (
        '850e8400-e29b-41d4-a716-************',
        'The Future of Artificial Intelligence in 2024',
        'future-of-ai-2024',
        'Exploring the latest developments in AI technology and their impact on various industries.',
        '# The Future of Artificial Intelligence in 2024

Artificial Intelligence continues to evolve at an unprecedented pace, transforming industries and reshaping how we work, communicate, and live. As we progress through 2024, several key trends are emerging that will define the AI landscape.

## Key Developments

### 1. Large Language Models Evolution
The development of more sophisticated language models has revolutionized natural language processing. These models are becoming more efficient, accurate, and capable of understanding context better than ever before.

### 2. AI in Healthcare
Medical AI applications are showing remarkable progress in:
- Diagnostic imaging
- Drug discovery
- Personalized treatment plans
- Predictive analytics for patient outcomes

### 3. Autonomous Systems
Self-driving vehicles and autonomous drones are becoming more reliable and are being deployed in various commercial applications.

## Challenges and Considerations

While AI advancement brings numerous benefits, it also presents challenges:
- Ethical considerations in AI decision-making
- Job displacement concerns
- Data privacy and security
- Regulatory frameworks

## Looking Ahead

The future of AI looks promising, with continued innovation expected in areas such as quantum computing integration, edge AI deployment, and more sophisticated human-AI collaboration tools.

As we navigate this technological revolution, it''s crucial to balance innovation with responsibility, ensuring that AI development serves humanity''s best interests.',
        'https://images.unsplash.com/photo-**********019-21780ecad995?w=1200&q=80',
        true,
        false,
        tech_category_id,
        admin_user_id,
        1250,
        89,
        12,
        8,
        NOW() - INTERVAL '2 days'
    ),
    (
        '850e8400-e29b-41d4-a716-************',
        'Building Sustainable Business Models in the Digital Age',
        'sustainable-business-models-digital-age',
        'How modern businesses are adapting to create sustainable and profitable models in an increasingly digital world.',
        '# Building Sustainable Business Models in the Digital Age

The digital transformation has fundamentally changed how businesses operate, forcing companies to rethink their traditional models and embrace new approaches to sustainability and profitability.

## The Digital Shift

### Traditional vs. Digital Models
- **Traditional**: Physical presence, linear value chains, limited customer reach
- **Digital**: Global accessibility, network effects, data-driven insights

### Key Components of Digital Business Models

1. **Customer-Centric Approach**
   - Personalized experiences
   - Real-time feedback loops
   - Community building

2. **Data as a Strategic Asset**
   - Predictive analytics
   - Customer behavior insights
   - Operational optimization

3. **Platform Economics**
   - Multi-sided markets
   - Network effects
   - Ecosystem partnerships

## Sustainability Integration

Modern businesses are integrating sustainability into their core operations:

### Environmental Considerations
- Carbon footprint reduction
- Circular economy principles
- Green technology adoption

### Social Responsibility
- Ethical labor practices
- Community engagement
- Diversity and inclusion

### Economic Viability
- Long-term value creation
- Stakeholder capitalism
- Transparent reporting

## Case Studies

Several companies have successfully implemented sustainable digital business models:

- **Tech Giants**: Investing in renewable energy and carbon neutrality
- **E-commerce Platforms**: Implementing sustainable packaging and logistics
- **Financial Services**: Promoting financial inclusion and responsible lending

## Future Outlook

The convergence of digital transformation and sustainability will continue to drive innovation, creating new opportunities for businesses that can effectively balance profit with purpose.',
        'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=1200&q=80',
        true,
        true,
        business_category_id,
        admin_user_id,
        890,
        67,
        8,
        6,
        NOW() - INTERVAL '1 day'
    ),
    (
        '850e8400-e29b-41d4-a716-************',
        'Mental Health in the Workplace: A Comprehensive Guide',
        'mental-health-workplace-guide',
        'Understanding the importance of mental health support in modern workplaces and practical strategies for implementation.',
        '# Mental Health in the Workplace: A Comprehensive Guide

Mental health has become a critical concern in modern workplaces, with organizations recognizing its impact on productivity, employee satisfaction, and overall business success.

## The Current State

### Statistics and Trends
- 1 in 4 people experience mental health issues annually
- Workplace stress contributes significantly to mental health problems
- Remote work has introduced new challenges and opportunities

### Common Workplace Mental Health Issues
- Anxiety and stress disorders
- Depression
- Burnout syndrome
- Work-life balance struggles

## Creating a Supportive Environment

### Leadership''s Role
- Open communication about mental health
- Leading by example
- Providing necessary resources
- Regular check-ins with team members

### Organizational Strategies

1. **Policy Development**
   - Mental health days
   - Flexible working arrangements
   - Clear boundaries for work hours

2. **Resource Provision**
   - Employee Assistance Programs (EAPs)
   - Mental health training
   - Access to counseling services

3. **Culture Building**
   - Destigmatizing mental health discussions
   - Promoting work-life balance
   - Encouraging regular breaks

## Implementation Best Practices

### For Managers
- Regular one-on-one meetings
- Recognition and appreciation programs
- Workload management
- Professional development opportunities

### For Employees
- Self-care practices
- Seeking help when needed
- Supporting colleagues
- Maintaining boundaries

## Measuring Success

Organizations should track:
- Employee satisfaction surveys
- Absenteeism rates
- Productivity metrics
- Retention rates

## Conclusion

Investing in workplace mental health is not just the right thing to do—it''s a business imperative that leads to better outcomes for everyone involved.',
        'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=1200&q=80',
        true,
        false,
        health_category_id,
        admin_user_id,
        2100,
        156,
        24,
        10,
        NOW() - INTERVAL '3 days'
    ),
    (
        '850e8400-e29b-41d4-a716-************',
        'Cryptocurrency Market Analysis: Trends and Predictions',
        'cryptocurrency-market-analysis-2024',
        'An in-depth analysis of the current cryptocurrency market, including major trends and future predictions.',
        '# Cryptocurrency Market Analysis: Trends and Predictions

The cryptocurrency market continues to evolve rapidly, with new developments, regulations, and technological advances shaping its trajectory.

## Market Overview

### Current Market Conditions
- Total market capitalization trends
- Major cryptocurrency performance
- Institutional adoption rates
- Regulatory landscape changes

### Key Players
- Bitcoin (BTC) - Digital gold narrative
- Ethereum (ETH) - Smart contract platform
- Emerging altcoins and their use cases
- Central Bank Digital Currencies (CBDCs)

## Major Trends

### 1. Institutional Adoption
- Corporate treasury allocations
- Investment fund offerings
- Payment processor integration
- Banking sector involvement

### 2. DeFi Evolution
- Decentralized exchanges growth
- Yield farming innovations
- Cross-chain interoperability
- Risk management improvements

### 3. NFT and Digital Assets
- Beyond art and collectibles
- Utility-focused NFTs
- Gaming and metaverse integration
- Real-world asset tokenization

## Regulatory Developments

### Global Regulatory Trends
- United States: SEC guidance and enforcement
- European Union: MiCA regulation
- Asia-Pacific: Varied approaches
- Emerging markets: Innovation vs. control

### Impact on Market Dynamics
- Compliance costs and barriers
- Market legitimacy and trust
- Innovation constraints and opportunities
- Cross-border transaction implications

## Technology Advancements

### Scalability Solutions
- Layer 2 protocols
- Sharding implementations
- Alternative consensus mechanisms
- Interoperability protocols

### Security Improvements
- Multi-signature wallets
- Hardware security modules
- Smart contract auditing
- Insurance protocols

## Future Predictions

### Short-term (6-12 months)
- Continued regulatory clarity
- Institutional product launches
- Technology infrastructure improvements
- Market maturation indicators

### Long-term (2-5 years)
- Mainstream adoption milestones
- Integration with traditional finance
- New use case developments
- Global monetary system impact

## Risk Considerations

Investors should be aware of:
- Market volatility
- Regulatory uncertainty
- Technology risks
- Liquidity concerns

## Conclusion

The cryptocurrency market is at a pivotal point, with increasing legitimacy and adoption balanced against ongoing challenges and uncertainties.',
        'https://images.unsplash.com/photo-1621761191319-c6fb62004040?w=1200&q=80',
        true,
        true,
        finance_category_id,
        admin_user_id,
        1680,
        94,
        18,
        12,
        NOW() - INTERVAL '4 days'
    ),
    (
        '850e8400-e29b-41d4-a716-************',
        'The Art of Work-Life Balance in Remote Work Era',
        'work-life-balance-remote-work',
        'Practical strategies for maintaining healthy work-life balance while working remotely.',
        '# The Art of Work-Life Balance in Remote Work Era

Remote work has fundamentally changed how we approach the balance between professional responsibilities and personal life, creating both opportunities and challenges.

## The Remote Work Revolution

### Benefits of Remote Work
- Flexibility in scheduling
- Elimination of commute time
- Increased autonomy
- Access to global opportunities
- Cost savings

### Challenges Faced
- Blurred boundaries between work and personal life
- Social isolation
- Communication difficulties
- Home distractions
- Technology fatigue

## Strategies for Success

### Setting Boundaries

1. **Physical Boundaries**
   - Dedicated workspace
   - Separate work and personal devices
   - Clear start and end times

2. **Mental Boundaries**
   - Transition rituals
   - Mindfulness practices
   - Regular breaks

### Time Management

- **Time blocking techniques**
- **Priority matrix usage**
- **Regular schedule reviews**
- **Buffer time allocation**

### Communication Excellence

#### With Team Members
- Regular check-ins
- Clear expectations
- Collaborative tools usage
- Virtual team building

#### With Family/Household
- Shared calendars
- Quiet time agreements
- Childcare arrangements
- Household responsibility sharing

## Technology and Tools

### Productivity Apps
- Task management systems
- Time tracking tools
- Focus applications
- Calendar optimization

### Communication Platforms
- Video conferencing best practices
- Instant messaging etiquette
- File sharing protocols
- Virtual collaboration spaces

## Maintaining Well-being

### Physical Health
- Regular exercise routines
- Ergonomic workspace setup
- Proper nutrition
- Adequate sleep

### Mental Health
- Social connections
- Hobby engagement
- Professional development
- Stress management techniques

## Creating Sustainable Habits

### Daily Routines
- Morning rituals
- Work preparation
- End-of-day shutdown
- Evening activities

### Weekly Planning
- Goal setting
- Schedule review
- Social activities
- Personal time

## Conclusion

Achieving work-life balance in a remote work environment requires intentional effort, clear boundaries, and continuous adjustment of strategies based on what works best for your unique situation.',
        'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=1200&q=80',
        true,
        false,
        lifestyle_category_id,
        admin_user_id,
        945,
        73,
        15,
        7,
        NOW() - INTERVAL '5 days'
    )
    ON CONFLICT (id) DO NOTHING;

    RAISE NOTICE '✅ Inserted % sample articles', 5;
END $$;

-- STEP 3: CREATE SAMPLE USERS FOR TESTING
-- =====================================================
-- Note: These will be created when users actually sign up
-- For now, we'll create placeholder profiles that can be linked later

-- STEP 4: INSERT SAMPLE COMMENTS
-- =====================================================
DO $$
DECLARE
    admin_user_id uuid;
    article1_id uuid := '850e8400-e29b-41d4-a716-************';
    article2_id uuid := '850e8400-e29b-41d4-a716-************';
    article3_id uuid := '850e8400-e29b-41d4-a716-************';
    article4_id uuid := '850e8400-e29b-41d4-a716-************';
    article5_id uuid := '850e8400-e29b-41d4-a716-************';
BEGIN
    -- Get admin user ID
    SELECT id INTO admin_user_id FROM public.profiles WHERE role = 'admin' LIMIT 1;

    IF admin_user_id IS NULL THEN
        admin_user_id := '00000000-0000-0000-0000-000000000000';
    END IF;

    -- Insert sample comments
    INSERT INTO public.comments (id, content, article_id, user_id, parent_id, is_approved, created_at) VALUES
    -- Comments on AI article
    (
        '950e8400-e29b-41d4-a716-************',
        'Excellent analysis of AI trends! I particularly found the section on healthcare applications fascinating. The potential for AI in diagnostic imaging could revolutionize early disease detection.',
        article1_id,
        admin_user_id,
        NULL,
        true,
        NOW() - INTERVAL '1 day 12 hours'
    ),
    (
        '950e8400-e29b-41d4-a716-************',
        'Great article! However, I think we should also discuss the environmental impact of training large AI models. The energy consumption is becoming a significant concern.',
        article1_id,
        admin_user_id,
        NULL,
        true,
        NOW() - INTERVAL '1 day 8 hours'
    ),
    (
        '950e8400-e29b-41d4-a716-************',
        'I agree with your point about environmental concerns. Some companies are now focusing on developing more energy-efficient training methods and using renewable energy sources.',
        article1_id,
        admin_user_id,
        '950e8400-e29b-41d4-a716-************',
        true,
        NOW() - INTERVAL '1 day 6 hours'
    ),

    -- Comments on Business Models article
    (
        '950e8400-e29b-41d4-a716-************',
        'This is exactly what our company is going through right now. The shift to digital-first thinking has been challenging but necessary. Thanks for the practical insights!',
        article2_id,
        admin_user_id,
        NULL,
        true,
        NOW() - INTERVAL '18 hours'
    ),
    (
        '950e8400-e29b-41d4-a716-************',
        'The case studies section was particularly valuable. Would love to see more examples of companies that successfully made this transition.',
        article2_id,
        admin_user_id,
        NULL,
        true,
        NOW() - INTERVAL '16 hours'
    ),

    -- Comments on Mental Health article
    (
        '950e8400-e29b-41d4-a716-446655440006',
        'As someone who has struggled with workplace anxiety, I appreciate articles like this that help destigmatize mental health discussions. The practical strategies are very helpful.',
        article3_id,
        admin_user_id,
        NULL,
        true,
        NOW() - INTERVAL '2 days 4 hours'
    ),
    (
        '950e8400-e29b-41d4-a716-446655440007',
        'Our company recently implemented an EAP program based on similar recommendations. The positive impact on team morale has been noticeable.',
        article3_id,
        admin_user_id,
        NULL,
        true,
        NOW() - INTERVAL '2 days 2 hours'
    ),
    (
        '950e8400-e29b-41d4-a716-446655440008',
        'Mental health support should be a priority for all organizations. The statistics you shared are eye-opening and show why this is so important.',
        article3_id,
        admin_user_id,
        NULL,
        true,
        NOW() - INTERVAL '2 days'
    ),

    -- Comments on Cryptocurrency article
    (
        '950e8400-e29b-41d4-a716-446655440009',
        'Comprehensive analysis! The regulatory section is particularly timely given recent developments. Do you think we''ll see more clarity from regulators in 2024?',
        article4_id,
        admin_user_id,
        NULL,
        true,
        NOW() - INTERVAL '3 days 8 hours'
    ),
    (
        '950e8400-e29b-41d4-a716-446655440010',
        'The DeFi evolution section caught my attention. The innovation in this space is incredible, but the risks are also significant. Balance is key.',
        article4_id,
        admin_user_id,
        NULL,
        true,
        NOW() - INTERVAL '3 days 6 hours'
    ),

    -- Comments on Work-Life Balance article
    (
        '950e8400-e29b-41d4-a716-446655440011',
        'Working remotely for 3 years now, and these strategies really resonate with me. The physical boundaries tip about having a dedicated workspace made a huge difference.',
        article5_id,
        admin_user_id,
        NULL,
        true,
        NOW() - INTERVAL '4 days 12 hours'
    ),
    (
        '950e8400-e29b-41d4-a716-446655440012',
        'The technology section is spot-on. Finding the right tools and establishing communication protocols with the team was crucial for our remote work success.',
        article5_id,
        admin_user_id,
        NULL,
        true,
        NOW() - INTERVAL '4 days 10 hours'
    )
    ON CONFLICT (id) DO NOTHING;

    RAISE NOTICE '✅ Inserted % sample comments', 12;
END $$;

-- STEP 5: INSERT SAMPLE LIKES AND FAVORITES
-- =====================================================
DO $$
DECLARE
    admin_user_id uuid;
    article1_id uuid := '850e8400-e29b-41d4-a716-************';
    article2_id uuid := '850e8400-e29b-41d4-a716-************';
    article3_id uuid := '850e8400-e29b-41d4-a716-************';
    article4_id uuid := '850e8400-e29b-41d4-a716-************';
    article5_id uuid := '850e8400-e29b-41d4-a716-************';
BEGIN
    -- Get admin user ID
    SELECT id INTO admin_user_id FROM public.profiles WHERE role = 'admin' LIMIT 1;

    IF admin_user_id IS NULL THEN
        admin_user_id := '00000000-0000-0000-0000-000000000000';
    END IF;

    -- Insert sample likes (using both likes and article_likes tables)
    INSERT INTO public.likes (id, user_id, article_id, created_at) VALUES
    ('a50e8400-e29b-41d4-a716-************', admin_user_id, article1_id, NOW() - INTERVAL '1 day'),
    ('a50e8400-e29b-41d4-a716-************', admin_user_id, article3_id, NOW() - INTERVAL '2 days'),
    ('a50e8400-e29b-41d4-a716-************', admin_user_id, article5_id, NOW() - INTERVAL '4 days')
    ON CONFLICT (user_id, article_id) DO NOTHING;

    INSERT INTO public.article_likes (id, article_id, user_id, created_at) VALUES
    ('b50e8400-e29b-41d4-a716-************', article1_id, admin_user_id, NOW() - INTERVAL '1 day'),
    ('b50e8400-e29b-41d4-a716-************', article2_id, admin_user_id, NOW() - INTERVAL '18 hours'),
    ('b50e8400-e29b-41d4-a716-************', article3_id, admin_user_id, NOW() - INTERVAL '2 days'),
    ('b50e8400-e29b-41d4-a716-************', article4_id, admin_user_id, NOW() - INTERVAL '3 days'),
    ('b50e8400-e29b-41d4-a716-************', article5_id, admin_user_id, NOW() - INTERVAL '4 days')
    ON CONFLICT (article_id, user_id) DO NOTHING;

    -- Insert sample favorites
    INSERT INTO public.favorites (id, user_id, article_id, created_at) VALUES
    ('c50e8400-e29b-41d4-a716-************', admin_user_id, article1_id, NOW() - INTERVAL '1 day'),
    ('c50e8400-e29b-41d4-a716-************', admin_user_id, article3_id, NOW() - INTERVAL '2 days'),
    ('c50e8400-e29b-41d4-a716-************', admin_user_id, article4_id, NOW() - INTERVAL '3 days')
    ON CONFLICT (user_id, article_id) DO NOTHING;

    RAISE NOTICE '✅ Inserted sample likes and favorites';
END $$;

-- STEP 6: INSERT SAMPLE ANALYTICS DATA
-- =====================================================
DO $$
DECLARE
    admin_user_id uuid;
    article1_id uuid := '850e8400-e29b-41d4-a716-************';
    article2_id uuid := '850e8400-e29b-41d4-a716-************';
    article3_id uuid := '850e8400-e29b-41d4-a716-************';
    article4_id uuid := '850e8400-e29b-41d4-a716-************';
    article5_id uuid := '850e8400-e29b-41d4-a716-************';
BEGIN
    -- Get admin user ID
    SELECT id INTO admin_user_id FROM public.profiles WHERE role = 'admin' LIMIT 1;

    IF admin_user_id IS NULL THEN
        admin_user_id := '00000000-0000-0000-0000-000000000000';
    END IF;

    -- Insert sample analytics events
    INSERT INTO public.analytics (id, article_id, user_id, event_type, ip_address, user_agent, referrer, session_id, created_at) VALUES
    -- Views
    ('d50e8400-e29b-41d4-a716-************', article1_id, admin_user_id, 'view', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'https://google.com', 'session_001', NOW() - INTERVAL '1 day'),
    ('d50e8400-e29b-41d4-a716-************', article2_id, admin_user_id, 'view', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'https://twitter.com', 'session_002', NOW() - INTERVAL '18 hours'),
    ('d50e8400-e29b-41d4-a716-************', article3_id, admin_user_id, 'view', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'https://linkedin.com', 'session_003', NOW() - INTERVAL '2 days'),

    -- Likes
    ('d50e8400-e29b-41d4-a716-************', article1_id, admin_user_id, 'like', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NULL, 'session_001', NOW() - INTERVAL '1 day'),
    ('d50e8400-e29b-41d4-a716-************', article3_id, admin_user_id, 'like', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NULL, 'session_003', NOW() - INTERVAL '2 days'),

    -- Comments
    ('d50e8400-e29b-41d4-a716-446655440006', article1_id, admin_user_id, 'comment', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NULL, 'session_001', NOW() - INTERVAL '1 day 12 hours'),
    ('d50e8400-e29b-41d4-a716-446655440007', article3_id, admin_user_id, 'comment', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NULL, 'session_003', NOW() - INTERVAL '2 days 4 hours'),

    -- Shares
    ('d50e8400-e29b-41d4-a716-446655440008', article1_id, admin_user_id, 'share', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NULL, 'session_001', NOW() - INTERVAL '1 day 6 hours'),
    ('d50e8400-e29b-41d4-a716-446655440009', article4_id, admin_user_id, 'share', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NULL, 'session_004', NOW() - INTERVAL '3 days 2 hours')
    ON CONFLICT (id) DO NOTHING;

    RAISE NOTICE '✅ Inserted sample analytics data';
END $$;

-- STEP 7: COMPLETION MESSAGE
-- =====================================================
DO $$
DECLARE
    articles_count INTEGER;
    comments_count INTEGER;
    likes_count INTEGER;
    favorites_count INTEGER;
    analytics_count INTEGER;
BEGIN
    -- Count inserted data
    SELECT COUNT(*) INTO articles_count FROM public.articles;
    SELECT COUNT(*) INTO comments_count FROM public.comments;
    SELECT COUNT(*) INTO likes_count FROM public.likes;
    SELECT COUNT(*) INTO favorites_count FROM public.favorites;
    SELECT COUNT(*) INTO analytics_count FROM public.analytics;

    RAISE NOTICE '🎉 TEST CONTENT SETUP COMPLETE!';
    RAISE NOTICE '==========================================';
    RAISE NOTICE '📊 CONTENT ADDED:';
    RAISE NOTICE 'Articles: %', articles_count;
    RAISE NOTICE 'Comments: %', comments_count;
    RAISE NOTICE 'Likes: %', likes_count;
    RAISE NOTICE 'Favorites: %', favorites_count;
    RAISE NOTICE 'Analytics events: %', analytics_count;
    RAISE NOTICE '==========================================';
    RAISE NOTICE '✅ FEATURES TESTED:';
    RAISE NOTICE '1. Article creation and publishing';
    RAISE NOTICE '2. Comment system with threading';
    RAISE NOTICE '3. Like and favorite functionality';
    RAISE NOTICE '4. Analytics tracking';
    RAISE NOTICE '5. Content categorization';
    RAISE NOTICE '==========================================';
    RAISE NOTICE '🚀 READY FOR TESTING:';
    RAISE NOTICE '1. Create admin user account';
    RAISE NOTICE '2. Test article viewing and interaction';
    RAISE NOTICE '3. Verify comment and like systems';
    RAISE NOTICE '4. Check analytics dashboard';
    RAISE NOTICE '5. Test product purchasing flow';
    RAISE NOTICE '==========================================';
END $$;
