# Admin Authentication System

## Overview
This document outlines the comprehensive admin authentication system for the Tennis Whisperer e-commerce platform. The system provides secure access control for administrative functions while maintaining the same design consistency as the existing user authentication forms.

## Features Implemented

### 1. Admin Sign-Up Form (`/admin/sign-up`)
**File**: `src/app/(auth)/admin/sign-up/page.tsx`

**Design Features**:
- Matches existing authentication form styling exactly
- Glassmorphism container with neomorphism shadows
- Logo avatar with Tennis Whisperer branding
- Gradient background with animated orbs
- Enhanced security with admin access code field

**Form Fields**:
- Full Name (with user icon)
- Admin Email (with envelope icon)
- Password (with lock icon)
- Admin Access Code (with shield icon)

**Security Features**:
- Admin access code verification
- Optional email domain restrictions
- Role-based user creation
- Automatic profile creation in users table

### 2. Admin Sign-In Form (`/admin/sign-in`)
**File**: `src/app/(auth)/admin/sign-in/page.tsx`

**Design Features**:
- Consistent with existing sign-in form design
- Security notice panel with shield icon
- Admin-specific branding and messaging
- Links to regular user login and admin registration

**Form Fields**:
- Admin Email (with user icon)
- Password (with lock icon)

**Security Features**:
- Role verification after authentication
- Automatic user profile creation if missing
- Security logging for admin access
- Fallback to user metadata for role checking

### 3. Authentication Actions

#### Admin Sign-Up Action
**File**: `src/app/(auth)/admin/sign-up/actions.ts`

**Features**:
- Admin access code verification
- Email domain validation (optional)
- Automatic role assignment
- User profile creation
- Email verification flow

**Environment Variables**:
```env
ADMIN_ACCESS_CODE=TENNIS_ADMIN_2024
ADMIN_EMAIL_DOMAINS=company.com,organization.org
```

#### Admin Sign-In Action
**File**: `src/app/(auth)/admin/sign-in/actions.ts`

**Features**:
- Role verification from database
- Fallback to user metadata
- Automatic profile creation
- Security logging
- Proper error handling

### 4. Admin Auth Guard
**File**: `src/components/admin/admin-auth-guard.tsx`

**Components**:
- `AdminAuthGuard`: Protects admin routes
- `useAdminUser`: Hook for admin user data

**Features**:
- Real-time authentication state monitoring
- Role verification
- Loading states
- Error handling
- Automatic redirects

### 5. Middleware Protection
**File**: `src/utils/supabase/middleware.ts`

**Enhanced Protection**:
- Admin route protection (`/admin/*`)
- Excludes auth routes (`/admin/sign-in`, `/admin/sign-up`)
- Database role verification
- Fallback to user metadata
- Automatic redirects for unauthorized access

## Security Implementation

### 1. Access Code System
```typescript
// Admin access code verification
const ADMIN_ACCESS_CODE = process.env.ADMIN_ACCESS_CODE || "TENNIS_ADMIN_2024";

if (adminCode !== ADMIN_ACCESS_CODE) {
  return encodedRedirect("error", "/admin/sign-up", "Invalid admin access code");
}
```

### 2. Email Domain Restrictions
```typescript
// Optional email domain validation
const allowedDomains = process.env.ADMIN_EMAIL_DOMAINS?.split(',') || [];
if (allowedDomains.length > 0) {
  const emailDomain = email.split('@')[1];
  if (!allowedDomains.includes(emailDomain)) {
    return encodedRedirect("error", "/admin/sign-up", "Email domain not authorized");
  }
}
```

### 3. Role-Based Access Control
```typescript
// Database role verification
const { data: userData } = await supabase
  .from('users')
  .select('role')
  .eq('id', user.id)
  .single();

if (!userData || userData.role !== 'admin') {
  return NextResponse.redirect(new URL("/admin/sign-in", request.url));
}
```

### 4. Middleware Protection
```typescript
// Admin routes protection
if (request.nextUrl.pathname.startsWith("/admin") && 
    !request.nextUrl.pathname.startsWith("/admin/sign-in") && 
    !request.nextUrl.pathname.startsWith("/admin/sign-up")) {
  // Verify authentication and admin role
}
```

## Database Schema

### Users Table Structure
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  email TEXT NOT NULL,
  full_name TEXT,
  role TEXT NOT NULL DEFAULT 'student' CHECK (role IN ('admin', 'mentor', 'student')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Row Level Security (RLS)
```sql
-- Admin access policies
CREATE POLICY "Admins can manage all users" 
  ON public.users 
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');
```

## Usage Examples

### 1. Protecting Admin Pages
```tsx
import AdminAuthGuard from '@/components/admin/admin-auth-guard';

export default function AdminPage() {
  return (
    <AdminAuthGuard>
      <div>Admin content here</div>
    </AdminAuthGuard>
  );
}
```

### 2. Using Admin User Hook
```tsx
import { useAdminUser } from '@/components/admin/admin-auth-guard';

export default function AdminComponent() {
  const { user, userData, loading } = useAdminUser();

  if (loading) return <div>Loading...</div>;

  return (
    <div>
      <h1>Welcome, {userData?.full_name}</h1>
      <p>Role: {userData?.role}</p>
    </div>
  );
}
```

### 3. API Route Protection
```typescript
// Check admin access in API routes
const { data: userData } = await supabase
  .from('users')
  .select('role')
  .eq('id', user.id)
  .single();

if (!userData || userData.role !== 'admin') {
  return NextResponse.json(
    { error: 'Forbidden - Admin access required' },
    { status: 403 }
  );
}
```

## Environment Configuration

### Required Environment Variables
```env
# Admin access code for registration
ADMIN_ACCESS_CODE=your_secure_admin_code_here

# Optional: Restrict admin emails to specific domains
ADMIN_EMAIL_DOMAINS=yourcompany.com,yourdomain.org

# Supabase configuration (existing)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## Security Best Practices

### 1. Access Code Management
- Use strong, unique admin access codes
- Rotate codes regularly
- Store in environment variables, never in code
- Consider using different codes for different environments

### 2. Email Domain Restrictions
- Limit admin registration to company domains
- Regularly review and update allowed domains
- Monitor registration attempts from unauthorized domains

### 3. Role Verification
- Always verify roles from the database
- Use user metadata as fallback only
- Log all admin access attempts
- Implement session timeout for admin users

### 4. Monitoring and Logging
- Log all admin authentication attempts
- Monitor failed login attempts
- Set up alerts for suspicious activity
- Regular security audits

## Design Consistency

### 1. Visual Elements
- **Logo**: Same Tennis Whisperer logo and styling
- **Colors**: Consistent gradient backgrounds and primary colors
- **Typography**: Matching font weights and sizes
- **Spacing**: Identical padding and margins

### 2. Form Styling
- **Container**: Glass-effect-dark with neo-shadow
- **Inputs**: Same height (h-14), rounded corners, and shadows
- **Icons**: Consistent icon placement and styling
- **Buttons**: Matching gradient and shadow effects

### 3. Layout Structure
- **Background**: Same gradient with animated orbs
- **Centering**: Identical responsive centering
- **Mobile**: Same mobile-first responsive behavior
- **Footer**: Consistent footer placement

## Testing Checklist

### 1. Authentication Flow
- [ ] Admin sign-up with valid access code
- [ ] Admin sign-up with invalid access code
- [ ] Admin sign-in with valid credentials
- [ ] Admin sign-in with invalid credentials
- [ ] Email verification flow
- [ ] Password reset flow

### 2. Authorization
- [ ] Admin route protection
- [ ] Non-admin user blocked from admin routes
- [ ] Middleware protection working
- [ ] API route protection
- [ ] Role verification from database
- [ ] Fallback to user metadata

### 3. Security
- [ ] Access code validation
- [ ] Email domain restrictions
- [ ] Session management
- [ ] Logout functionality
- [ ] Security logging
- [ ] Error handling

### 4. UI/UX
- [ ] Design consistency with existing forms
- [ ] Responsive design
- [ ] Loading states
- [ ] Error messages
- [ ] Success messages
- [ ] Navigation links

## Troubleshooting

### Common Issues
1. **Role not found in database**: Check user metadata fallback
2. **Access code not working**: Verify environment variable
3. **Email domain restriction**: Check ADMIN_EMAIL_DOMAINS setting
4. **Middleware redirect loop**: Ensure auth routes are excluded
5. **Profile creation failed**: Check database permissions

### Debug Steps
1. Check browser console for errors
2. Verify environment variables
3. Check Supabase auth logs
4. Verify database user table
5. Test with different browsers/incognito mode

## Conclusion

The admin authentication system provides secure, role-based access control while maintaining perfect design consistency with the existing Tennis Whisperer authentication forms. The system includes comprehensive security measures, proper error handling, and follows modern authentication best practices.
