"use client";

import React from "react";
import { cn } from "@/lib/utils";
import { Button, ButtonProps } from "@/components/ui/button";

interface GlowingButtonProps extends ButtonProps {
  glowColor?: string;
  className?: string;
}

export function GlowingButton({
  children,
  glowColor = "from-primary/50 via-primary/10 to-transparent",
  className,
  ...props
}: GlowingButtonProps) {
  return (
    <div className="relative group">
      {/* Glow effect */}
      <div
        className={cn(
          "absolute -inset-0.5 rounded-lg bg-gradient-to-r opacity-75 blur-sm transition-all duration-500",
          glowColor,
          "group-hover:opacity-100 group-hover:blur"
        )}
      />
      
      {/* Moving shine effect */}
      <div className="absolute inset-0 rounded-lg overflow-hidden">
        <div
          className="absolute inset-0 w-[200%] translate-x-[-50%] bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 transition-all duration-700 group-hover:animate-shine"
          style={{ transform: "translateX(-100%)" }}
        />
      </div>
      
      {/* Button */}
      <Button
        className={cn(
          "relative rounded-lg font-medium transition-all",
          "bg-primary hover:bg-primary/90 text-primary-foreground",
          className
        )}
        {...props}
      >
        {children}
      </Button>
    </div>
  );
}
