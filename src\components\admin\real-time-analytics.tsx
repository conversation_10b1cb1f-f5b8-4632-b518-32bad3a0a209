"use client";

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  RefreshCw, 
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Activity,
  Calendar,
  Clock
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface AnalyticsData {
  revenue: {
    total: number;
    today: number;
    thisWeek: number;
    thisMonth: number;
    growth: number;
  };
  orders: {
    total: number;
    pending: number;
    processing: number;
    delivered: number;
    todayCount: number;
    weeklyGrowth: number;
  };
  products: {
    total: number;
    lowStock: number;
    outOfStock: number;
    topSelling: Array<{
      id: string;
      name: string;
      sales: number;
      revenue: number;
    }>;
  };
  users: {
    total: number;
    newToday: number;
    newThisWeek: number;
    activeUsers: number;
  };
  activities: {
    totalToday: number;
    successRate: number;
    mostActiveAdmin: string;
    recentActivities: Array<{
      id: string;
      action: string;
      admin: string;
      timestamp: string;
      success: boolean;
    }>;
  };
}

export default function RealTimeAnalytics() {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [autoRefresh, setAutoRefresh] = useState(true);
  const { toast } = useToast();

  const fetchAnalytics = useCallback(async () => {
    try {
      // Fetch all analytics data in parallel
      const [ordersRes, productsRes, usersRes, activitiesRes] = await Promise.all([
        fetch('/api/admin/orders', { method: 'POST', credentials: 'include' }),
        fetch('/api/admin/products/stats', { credentials: 'include' }),
        fetch('/api/admin/users/stats', { credentials: 'include' }),
        fetch('/api/admin/activity/metrics', { credentials: 'include' })
      ]);

      if (!ordersRes.ok) throw new Error('Failed to fetch orders data');
      
      const ordersData = await ordersRes.json();
      const productsData = productsRes.ok ? await productsRes.json() : { total: 0 };
      const usersData = usersRes.ok ? await usersRes.json() : { total: 0 };
      const activitiesData = activitiesRes.ok ? await activitiesRes.json() : {};

      // Transform data into analytics format
      const analytics: AnalyticsData = {
        revenue: {
          total: ordersData.data?.total_revenue || 0,
          today: ordersData.data?.daily_revenue || 0,
          thisWeek: ordersData.data?.weekly_revenue || 0,
          thisMonth: ordersData.data?.monthly_revenue || 0,
          growth: ordersData.data?.monthly_growth || 0,
        },
        orders: {
          total: ordersData.data?.total_orders || 0,
          pending: ordersData.data?.pending_orders || 0,
          processing: ordersData.data?.processing_orders || 0,
          delivered: ordersData.data?.delivered_orders || 0,
          todayCount: ordersData.data?.orders_today || 0,
          weeklyGrowth: ordersData.data?.weekly_growth || 0,
        },
        products: {
          total: productsData.total || 0,
          lowStock: productsData.lowStock || 0,
          outOfStock: productsData.outOfStock || 0,
          topSelling: productsData.topSelling || [],
        },
        users: {
          total: usersData.total || 0,
          newToday: usersData.newToday || 0,
          newThisWeek: usersData.recent || 0,
          activeUsers: usersData.active || 0,
        },
        activities: {
          totalToday: activitiesData.activities_today || 0,
          successRate: activitiesData.success_rate || 100,
          mostActiveAdmin: activitiesData.most_active_admin || 'No activity',
          recentActivities: activitiesData.recent_activities || [],
        },
      };

      setData(analytics);
      setLastUpdated(new Date());
      setLoading(false);

    } catch (error) {
      console.error('Error fetching analytics:', error);
      toast({
        title: "Error",
        description: "Failed to fetch analytics data",
        variant: "destructive",
      });
      setLoading(false);
    }
  }, [toast]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    fetchAnalytics();

    if (autoRefresh) {
      const interval = setInterval(fetchAnalytics, 30000);
      return () => clearInterval(interval);
    }
  }, [fetchAnalytics, autoRefresh]);

  const handleRefresh = () => {
    setLoading(true);
    fetchAnalytics();
  };

  if (loading && !data) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Real-Time Analytics</h2>
          <div className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4 animate-spin" />
            <span className="text-sm text-muted-foreground">Loading...</span>
          </div>
        </div>
        <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-20 bg-muted animate-pulse rounded" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Real-Time Analytics</h2>
        <div className="flex items-center gap-4">
          <Badge variant={autoRefresh ? "default" : "secondary"}>
            {autoRefresh ? "Auto-refresh ON" : "Auto-refresh OFF"}
          </Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <div className="text-sm text-muted-foreground">
            <Clock className="h-4 w-4 inline mr-1" />
            {lastUpdated.toLocaleTimeString()}
          </div>
        </div>
      </div>

      {data && (
        <>
          {/* Key Metrics */}
          <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Revenue Today</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  R {data.revenue.today.toLocaleString('en-ZA', { minimumFractionDigits: 2 })}
                </div>
                <div className="flex items-center pt-1 text-xs text-muted-foreground">
                  <span className={`flex items-center ${data.revenue.growth >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                    {data.revenue.growth >= 0 ? (
                      <TrendingUp className="mr-1 h-3 w-3" />
                    ) : (
                      <TrendingDown className="mr-1 h-3 w-3" />
                    )}
                    {data.revenue.growth >= 0 ? '+' : ''}{data.revenue.growth.toFixed(1)}%
                  </span>
                  <span className="ml-1">from last month</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Orders Today</CardTitle>
                <ShoppingCart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{data.orders.todayCount}</div>
                <div className="flex items-center pt-1 text-xs text-muted-foreground">
                  <span className="text-blue-500">
                    {data.orders.pending} pending, {data.orders.processing} processing
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">New Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{data.users.newToday}</div>
                <div className="flex items-center pt-1 text-xs text-muted-foreground">
                  <span className="text-green-500">
                    {data.users.newThisWeek} this week
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Admin Activities</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{data.activities.totalToday}</div>
                <div className="flex items-center pt-1 text-xs text-muted-foreground">
                  <span className="text-purple-500">
                    {data.activities.successRate.toFixed(1)}% success rate
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Analytics */}
          <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Order Status Distribution</CardTitle>
                <CardDescription>Current order status breakdown</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Pending</span>
                    <Badge variant="secondary">{data.orders.pending}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Processing</span>
                    <Badge variant="default">{data.orders.processing}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Delivered</span>
                    <Badge variant="outline">{data.orders.delivered}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Admin Activity</CardTitle>
                <CardDescription>Latest admin actions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {data.activities.recentActivities.length > 0 ? (
                    data.activities.recentActivities.slice(0, 5).map((activity) => (
                      <div key={activity.id} className="flex items-center justify-between text-sm">
                        <span className="truncate">{activity.action}</span>
                        <Badge variant={activity.success ? "default" : "destructive"}>
                          {activity.success ? "Success" : "Failed"}
                        </Badge>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-muted-foreground">No recent activities</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </>
      )}
    </div>
  );
}
