import { Suspense } from 'react';
import { default as dynamicImport } from 'next/dynamic';

// Import the client component with no SSR
const SearchPageClient = dynamicImport(() => import('./client'), { ssr: false });

// This prevents the page from being statically generated
export const dynamic = 'force-dynamic';
export const dynamicParams = true;
export const revalidate = 0;

// Server component wrapper with suspense fallback
export default function SearchPage() {
  return (
    <Suspense fallback={<div className="p-12 text-center">Loading search results...</div>}>
      <SearchPageClient />
    </Suspense>
  );
}
