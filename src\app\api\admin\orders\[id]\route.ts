import { NextRequest, NextResponse } from 'next/server';
import { createClient, createServiceRoleClient } from '@/utils/supabase/server';
import { createServiceClient } from '@/utils/supabase/service';
import { OrderUpdateData } from '@/types/orders';

/**
 * GET /api/admin/orders/[id]
 * Fetch a specific order by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('🔍 GET /api/admin/orders/[id] - Request received for order ID:', params.id);

    // Validate order ID format
    if (!params.id || params.id.trim() === '') {
      console.log('❌ GET /api/admin/orders/[id] - Invalid order ID');
      return NextResponse.json(
        { error: 'Invalid order ID provided' },
        { status: 400 }
      );
    }

    // Check authentication
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (!user) {
      console.log('❌ GET /api/admin/orders/[id] - No authenticated user:', authError?.message);
      return NextResponse.json(
        {
          error: 'Authentication required',
          details: authError?.message || 'No user session found',
          debug: {
            hasUser: false,
            authError: authError?.message
          }
        },
        { status: 401 }
      );
    }

    console.log('✅ GET /api/admin/orders/[id] - User authenticated:', user.id);

    // Check if user is admin
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role, email, full_name')
      .eq('id', user.id)
      .single();

    if (userError) {
      console.log('❌ GET /api/admin/orders/[id] - Error fetching user data:', userError);
      return NextResponse.json(
        {
          error: 'Failed to verify user permissions',
          details: userError.message,
          debug: {
            userId: user.id,
            userError: userError.message
          }
        },
        { status: 500 }
      );
    }

    if (!userData || userData.role !== 'admin') {
      console.log('❌ GET /api/admin/orders/[id] - User is not admin:', userData?.role);
      return NextResponse.json(
        {
          error: 'Admin access required',
          details: `Current role: ${userData?.role || 'unknown'}`,
          debug: {
            userId: user.id,
            userRole: userData?.role,
            requiredRole: 'admin'
          }
        },
        { status: 403 }
      );
    }

    console.log('✅ GET /api/admin/orders/[id] - Admin access confirmed for:', userData.email);

    // Use service role client for database operations
    const serviceSupabase = createServiceRoleClient();

    // First, check if the order exists at all
    const { data: orderExists, error: existsError } = await serviceSupabase
      .from('orders')
      .select('id')
      .eq('id', params.id)
      .single();

    if (existsError) {
      console.log('❌ GET /api/admin/orders/[id] - Order existence check failed:', existsError);
      if (existsError.code === 'PGRST116') {
        return NextResponse.json(
          {
            error: 'Order not found',
            details: `No order exists with ID: ${params.id}`,
            debug: {
              orderId: params.id,
              errorCode: existsError.code
            }
          },
          { status: 404 }
        );
      }
      return NextResponse.json(
        {
          error: 'Database error during order lookup',
          details: existsError.message,
          debug: {
            orderId: params.id,
            errorCode: existsError.code,
            errorMessage: existsError.message
          }
        },
        { status: 500 }
      );
    }

    console.log('✅ GET /api/admin/orders/[id] - Order exists, fetching full details');

    // Fetch the complete order with user details
    const { data: order, error } = await serviceSupabase
      .from('orders')
      .select(`
        *,
        users!user_id (
          email,
          full_name
        )
      `)
      .eq('id', params.id)
      .single();

    if (error) {
      console.error('❌ GET /api/admin/orders/[id] - Database error:', error);
      return NextResponse.json(
        {
          error: 'Failed to fetch order details',
          details: error.message,
          debug: {
            orderId: params.id,
            errorCode: error.code,
            errorMessage: error.message,
            errorDetails: error.details
          }
        },
        { status: 500 }
      );
    }

    // Transform data to match our interface
    const transformedOrder = {
      ...order,
      customer_email: order.users?.email,
      customer_name: order.users?.full_name,
      customer_phone: order.shipping_details?.phone || null, // Get phone from shipping details
      // Ensure required fields have defaults
      notes: order.notes || null,
      shipping_method: order.shipping_method || null,
      users: undefined, // Remove the nested users object
    };

    console.log('✅ GET /api/admin/orders/[id] - Order found and transformed:', {
      orderId: order.id,
      status: order.status,
      paymentStatus: order.payment_status,
      totalAmount: order.total_amount,
      customerEmail: transformedOrder.customer_email
    });

    return NextResponse.json({
      success: true,
      data: transformedOrder,
      debug: {
        orderId: params.id,
        fetchedAt: new Date().toISOString(),
        adminUser: userData.email
      }
    });

  } catch (error: any) {
    console.error('💥 GET /api/admin/orders/[id] - Unexpected error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error.message,
        debug: {
          orderId: params.id,
          errorStack: error.stack,
          errorName: error.name
        }
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/orders/[id]
 * Update an order (status, notes, shipping method, etc.)
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('PUT /api/admin/orders/[id] - Request received for order ID:', params.id);

    // Check authentication
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || userData.role !== 'admin') {
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }

    console.log('PUT /api/admin/orders/[id] - Admin access confirmed');

    const body = await request.json();
    const updateData: OrderUpdateData = body;

    console.log('PUT /api/admin/orders/[id] - Update data:', updateData);

    // Validate update data
    const allowedFields = ['status', 'payment_status', 'notes', 'shipping_method'];
    const filteredData: any = {
      updated_at: new Date().toISOString()
    };

    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key) && value !== undefined) {
        filteredData[key] = value;
      }
    }

    if (Object.keys(filteredData).length === 1) { // Only updated_at
      return NextResponse.json(
        { error: 'No valid fields to update' },
        { status: 400 }
      );
    }

    // Use service role client for database operations
    const serviceSupabase = createServiceRoleClient();

    // Get the original order data for comparison
    const { data: originalOrder } = await serviceSupabase
      .from('orders')
      .select('status, payment_status, notes, shipping_method')
      .eq('id', params.id)
      .single();

    const { data: updatedOrder, error } = await serviceSupabase
      .from('orders')
      .update(filteredData)
      .eq('id', params.id)
      .select(`
        *,
        users!user_id (
          email,
          full_name
        )
      `)
      .single();

    if (error) {
      console.error('PUT /api/admin/orders/[id] - Database error:', error);
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Order not found' },
          { status: 404 }
        );
      }
      return NextResponse.json(
        { error: 'Failed to update order', details: error.message },
        { status: 500 }
      );
    }

    // Log the activity
    try {
      const activitySupabase = createServiceClient();
      const changes: string[] = [];

      if (originalOrder) {
        if (filteredData.status && originalOrder.status !== filteredData.status) {
          changes.push(`Status: ${originalOrder.status} → ${filteredData.status}`);
        }
        if (filteredData.payment_status && originalOrder.payment_status !== filteredData.payment_status) {
          changes.push(`Payment: ${originalOrder.payment_status} → ${filteredData.payment_status}`);
        }
        if (filteredData.shipping_method && originalOrder.shipping_method !== filteredData.shipping_method) {
          changes.push(`Shipping: ${originalOrder.shipping_method || 'None'} → ${filteredData.shipping_method}`);
        }
        if (filteredData.notes !== undefined && originalOrder.notes !== filteredData.notes) {
          changes.push(`Notes updated`);
        }
      }

      if (changes.length > 0) {
        await activitySupabase
          .from('admin_activity_logs')
          .insert({
            admin_id: user.id,
            action_type: 'order_management',
            action_description: `Updated order #${params.id}: ${changes.join(', ')}`,
            target_id: params.id,
            target_type: 'order',
            metadata: {
              order_id: params.id,
              changes: filteredData,
              customer_email: updatedOrder.users?.email
            },
            success: true
          });
      }
    } catch (activityError) {
      console.error('Failed to log activity:', activityError);
      // Don't fail the request if activity logging fails
    }

    // Transform data to match our interface
    const transformedOrder = {
      ...updatedOrder,
      customer_email: updatedOrder.users?.email,
      customer_name: updatedOrder.users?.full_name,
      customer_phone: updatedOrder.shipping_details?.phone || null, // Get phone from shipping details
      users: undefined,
    };

    console.log('PUT /api/admin/orders/[id] - Order updated successfully:', updatedOrder.id);

    return NextResponse.json({
      success: true,
      data: transformedOrder
    });

  } catch (error: any) {
    console.error('PUT /api/admin/orders/[id] - General error:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/orders/[id]
 * Delete an order (admin only, for cancelled/refunded orders)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('DELETE /api/admin/orders/[id] - Request received for order ID:', params.id);

    // Check authentication
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || userData.role !== 'admin') {
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }

    // Use service role client for database operations
    const serviceSupabase = createServiceRoleClient();

    // First check if order exists and can be deleted
    const { data: order, error: fetchError } = await serviceSupabase
      .from('orders')
      .select('status, payment_status')
      .eq('id', params.id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Order not found' }, { status: 404 });
      }
      return NextResponse.json(
        { error: 'Failed to fetch order', details: fetchError.message },
        { status: 500 }
      );
    }

    // Only allow deletion of cancelled or refunded orders
    if (!['cancelled', 'refunded'].includes(order.status)) {
      return NextResponse.json(
        { error: 'Only cancelled or refunded orders can be deleted' },
        { status: 400 }
      );
    }

    const { error } = await serviceSupabase
      .from('orders')
      .delete()
      .eq('id', params.id);

    if (error) {
      console.error('DELETE /api/admin/orders/[id] - Database error:', error);
      return NextResponse.json(
        { error: 'Failed to delete order', details: error.message },
        { status: 500 }
      );
    }

    // Log the activity
    try {
      const activitySupabase = createServiceClient();
      await activitySupabase
        .from('admin_activity_logs')
        .insert({
          admin_id: user.id,
          action_type: 'order_management',
          action_description: `Deleted order #${params.id} (Status: ${order.status})`,
          target_id: params.id,
          target_type: 'order',
          metadata: {
            order_id: params.id,
            deleted_status: order.status,
            payment_status: order.payment_status
          },
          success: true
        });
    } catch (activityError) {
      console.error('Failed to log activity:', activityError);
      // Don't fail the request if activity logging fails
    }

    console.log('DELETE /api/admin/orders/[id] - Order deleted successfully:', params.id);

    return NextResponse.json({
      success: true,
      message: 'Order deleted successfully'
    });

  } catch (error: any) {
    console.error('DELETE /api/admin/orders/[id] - General error:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
