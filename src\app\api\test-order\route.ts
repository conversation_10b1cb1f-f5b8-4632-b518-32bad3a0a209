import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

// Simple function to generate a unique ID
function generateId(prefix = '') {
  return `${prefix}${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
}

export async function GET(request: Request) {
  try {
    const supabase = await createClient();
    
    // Generate a test order ID
    const orderId = generateId('TEC-');
    
    console.log('Creating test order with ID:', orderId);
    
    // Create a test order
    const { data: insertData, error: insertError } = await supabase
      .from('orders')
      .insert({
        id: orderId,
        user_id: 'test-user',
        items: [{ name: 'Test Item', price: 100, quantity: 1 }],
        shipping_details: {
          name: 'Test User',
          email: '<EMAIL>',
          address: '123 Test St',
          city: 'Test City',
          postal_code: '12345',
          country: 'South Africa',
          phone: '+27123456789'
        },
        status: 'pending',
        payment_status: 'pending',
        total_amount: 100,
        yoco_payment_id: orderId
      })
      .select();
    
    if (insertError) {
      console.error('Error creating test order:', insertError);
      return NextResponse.json(
        { error: `Failed to create test order: ${insertError.message}` },
        { status: 500 }
      );
    }
    
    // Try to retrieve the order
    const { data: retrieveData, error: retrieveError } = await supabase
      .from('orders')
      .select('*')
      .eq('id', orderId)
      .single();
    
    if (retrieveError) {
      console.error('Error retrieving test order:', retrieveError);
      return NextResponse.json(
        { error: `Failed to retrieve test order: ${retrieveError.message}` },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      message: 'Test order created and retrieved successfully',
      orderId,
      order: retrieveData
    });
  } catch (error: any) {
    console.error('Test order error:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
} 