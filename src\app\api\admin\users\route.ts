import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { createServiceClient } from '@/utils/supabase/service';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions
    const { data: adminData } = await supabase
      .from('users')
      .select('role, admin_role')
      .eq('id', session.user.id)
      .single();

    if (!adminData || adminData.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search');
    const roleFilter = searchParams.get('role');

    const offset = (page - 1) * limit;

    // Use service role client for database operations
    const serviceSupabase = createServiceClient();

    // Build query
    let query = serviceSupabase
      .from('users')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply filters
    if (search) {
      query = query.or(`email.ilike.%${search}%,full_name.ilike.%${search}%`);
    }

    if (roleFilter && roleFilter !== 'all') {
      query = query.eq('role', roleFilter);
    }

    const { data: users, error, count } = await query;

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: 'Database error' }, { status: 500 });
    }

    return NextResponse.json({
      users: users || [],
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions
    const { data: adminData } = await supabase
      .from('users')
      .select('role, admin_role')
      .eq('id', session.user.id)
      .single();

    if (!adminData || adminData.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Check if user has permission to manage roles
    if (adminData.admin_role !== 'admin' && adminData.admin_role !== 'senior_admin') {
      return NextResponse.json({ error: 'Insufficient permissions to manage user roles' }, { status: 403 });
    }

    const body = await request.json();
    const { userId, role, adminRole } = body;

    if (!userId || !role) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Use service role client for database operations
    const serviceSupabase = createServiceClient();

    // Prepare update data
    const updateData: any = { role };
    
    // If promoting to admin, set admin_role
    if (role === 'admin' && adminRole) {
      // Only main admins can create other main admins
      if (adminRole === 'admin' && adminData.admin_role !== 'admin') {
        return NextResponse.json({ error: 'Only main administrators can create other main administrators' }, { status: 403 });
      }
      updateData.admin_role = adminRole;
    } else if (role !== 'admin') {
      updateData.admin_role = null;
    }

    // Update user role
    const { data: updatedUser, error: updateError } = await serviceSupabase
      .from('users')
      .update(updateData)
      .eq('id', userId)
      .select()
      .single();

    if (updateError) {
      console.error('Update error:', updateError);
      return NextResponse.json({ error: 'Failed to update user role' }, { status: 500 });
    }

    // Log the activity
    await serviceSupabase.rpc('log_admin_activity', {
      p_action_type: 'user_management',
      p_action_description: `Updated user role to ${role}${role === 'admin' ? ` (${adminRole})` : ''}`,
      p_target_id: userId,
      p_target_type: 'user',
      p_metadata: {
        new_role: role,
        new_admin_role: adminRole,
        updated_user_email: updatedUser.email
      }
    });

    return NextResponse.json({ 
      message: 'User role updated successfully',
      user: updatedUser 
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
