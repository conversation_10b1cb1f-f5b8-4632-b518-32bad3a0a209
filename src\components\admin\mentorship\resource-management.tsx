"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, Upload, FileText, Video, BookOpen, BarChart, Plus } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

type Resource = {
  id: string;
  title: string;
  type: "document" | "video" | "training" | "progress";
  category: string;
  description: string;
  uploadDate: string;
  size: string;
  format: string;
  downloads: number;
  url?: string;
};

const mockResources: Resource[] = [
  {
    id: "1",
    title: "Tennis Fundamentals Guide",
    type: "document",
    category: "Basics",
    description: "A comprehensive guide to tennis fundamentals for beginners",
    uploadDate: "2025-05-01",
    size: "2.5 MB",
    format: "PDF",
    downloads: 45,
  },
  {
    id: "2",
    title: "Serve Technique Masterclass",
    type: "video",
    category: "Advanced",
    description: "In-depth video tutorial on perfecting your serve technique",
    uploadDate: "2025-05-15",
    size: "250 MB",
    format: "MP4",
    downloads: 32,
    url: "https://example.com/videos/serve-technique",
  },
  {
    id: "3",
    title: "6-Week Training Program",
    type: "training",
    category: "Programs",
    description: "Structured training program for intermediate players",
    uploadDate: "2025-05-10",
    size: "1.8 MB",
    format: "PDF",
    downloads: 28,
  },
  {
    id: "4",
    title: "Student Progress Template",
    type: "progress",
    category: "Assessment",
    description: "Template for tracking student progress and milestones",
    uploadDate: "2025-05-05",
    size: "500 KB",
    format: "XLSX",
    downloads: 15,
  },
];

export function ResourceManagement() {
  const [searchQuery, setSearchQuery] = useState("");
  const [resources] = useState<Resource[]>(mockResources);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [selectedType, setSelectedType] = useState<Resource['type']>("document");

  const getResourceIcon = (type: Resource['type']) => {
    switch (type) {
      case "document":
        return <FileText className="h-4 w-4 text-blue-500" />;
      case "video":
        return <Video className="h-4 w-4 text-red-500" />;
      case "training":
        return <BookOpen className="h-4 w-4 text-green-500" />;
      case "progress":
        return <BarChart className="h-4 w-4 text-purple-500" />;
    }
  };

  const filteredResources = resources.filter(resource =>
    resource.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    resource.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    resource.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <Tabs defaultValue="all" className="w-full">
        <div className="flex justify-between items-center mb-6">
          <TabsList>
            <TabsTrigger value="all">All Resources</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="videos">Videos</TabsTrigger>
            <TabsTrigger value="training">Training</TabsTrigger>
            <TabsTrigger value="progress">Progress</TabsTrigger>
          </TabsList>

          <div className="flex items-center gap-4">
            <div className="relative w-96">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search resources..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>
            <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Resource</DialogTitle>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label>Resource Type</Label>
                    <Select value={selectedType} onValueChange={(value: Resource['type']) => setSelectedType(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="document">Document</SelectItem>
                        <SelectItem value="video">Video</SelectItem>
                        <SelectItem value="training">Training Material</SelectItem>
                        <SelectItem value="progress">Progress Tracking</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Title</Label>
                    <Input placeholder="Enter resource title" />
                  </div>
                  <div className="space-y-2">
                    <Label>Category</Label>
                    <Input placeholder="Enter category" />
                  </div>
                  <div className="space-y-2">
                    <Label>Description</Label>
                    <Textarea placeholder="Enter resource description" />
                  </div>
                  <div className="space-y-2">
                    <Label>File</Label>
                    <div className="border-2 border-dashed rounded-lg p-4 text-center cursor-pointer hover:bg-muted/50">
                      <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                      <p className="text-sm text-muted-foreground">Click to upload or drag and drop</p>
                    </div>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <TabsContent value="all" className="mt-6">
          <Card>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Format</TableHead>
                  <TableHead>Upload Date</TableHead>
                  <TableHead>Downloads</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredResources.map((resource) => (
                  <TableRow key={resource.id}>
                    <TableCell className="flex items-center gap-2">
                      {getResourceIcon(resource.type)}
                      <div>
                        <p>{resource.title}</p>
                        <p className="text-sm text-muted-foreground">{resource.description}</p>
                      </div>
                    </TableCell>
                    <TableCell>{resource.category}</TableCell>
                    <TableCell className="capitalize">{resource.type}</TableCell>
                    <TableCell>{resource.format}</TableCell>
                    <TableCell>{resource.uploadDate}</TableCell>
                    <TableCell>{resource.downloads}</TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm">View</Button>
                      <Button variant="ghost" size="sm">Download</Button>
                      <Button variant="ghost" size="sm" className="text-destructive">Delete</Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Card>
        </TabsContent>

        {["documents", "videos", "training", "progress"].map((tab) => (
          <TabsContent key={tab} value={tab} className="mt-6">
            <Card>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Format</TableHead>
                    <TableHead>Upload Date</TableHead>
                    <TableHead>Downloads</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredResources
                    .filter(resource => resource.type === tab.slice(0, -1))
                    .map((resource) => (
                      <TableRow key={resource.id}>
                        <TableCell className="flex items-center gap-2">
                          {getResourceIcon(resource.type)}
                          <div>
                            <p>{resource.title}</p>
                            <p className="text-sm text-muted-foreground">{resource.description}</p>
                          </div>
                        </TableCell>
                        <TableCell>{resource.category}</TableCell>
                        <TableCell>{resource.format}</TableCell>
                        <TableCell>{resource.uploadDate}</TableCell>
                        <TableCell>{resource.downloads}</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">View</Button>
                          <Button variant="ghost" size="sm">Download</Button>
                          <Button variant="ghost" size="sm" className="text-destructive">Delete</Button>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </Card>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
