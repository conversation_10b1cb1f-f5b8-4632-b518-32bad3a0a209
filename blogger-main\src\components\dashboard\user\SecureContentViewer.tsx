import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import {
  Play,
  Pause,
  SkipBack,
  SkipForward,
  Volume2,
  VolumeX,
  FileText,
  Music,
  Eye,
  Lock,
  Shield,
  AlertTriangle
} from 'lucide-react';
import { supabase } from '../../../../supabase/supabase';
import { useAuth } from '../../../../supabase/auth';

interface SecureContentViewerProps {
  productId: string;
  fileId: string;
  fileName: string;
  fileType: string;
  mimeType: string;
  onClose?: () => void;
}

export function SecureContentViewer({
  productId,
  fileId,
  fileName,
  fileType,
  mimeType,
  onClose
}: SecureContentViewerProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [fileUrl, setFileUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  
  const audioRef = useRef<HTMLAudioElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    loadSecureContent();
    implementSecurityMeasures();
    
    return () => {
      if (fileUrl) {
        URL.revokeObjectURL(fileUrl);
      }
    };
  }, [productId, fileId]);

  const implementSecurityMeasures = () => {
    // Disable right-click context menu
    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault();
      return false;
    };

    // Disable keyboard shortcuts for saving/copying
    const handleKeyDown = (e: KeyboardEvent) => {
      // Disable Ctrl+S, Ctrl+A, Ctrl+C, Ctrl+V, F12, etc.
      if (
        (e.ctrlKey && (e.key === 's' || e.key === 'a' || e.key === 'c' || e.key === 'v')) ||
        e.key === 'F12' ||
        (e.ctrlKey && e.shiftKey && e.key === 'I') ||
        (e.ctrlKey && e.shiftKey && e.key === 'J') ||
        (e.ctrlKey && e.key === 'u')
      ) {
        e.preventDefault();
        toast({
          title: 'Action Blocked',
          description: 'This content is protected and cannot be copied or saved.',
          variant: 'destructive',
        });
        return false;
      }
    };

    // Disable text selection
    const handleSelectStart = (e: Event) => {
      e.preventDefault();
      return false;
    };

    // Disable drag and drop
    const handleDragStart = (e: DragEvent) => {
      e.preventDefault();
      return false;
    };

    // Add event listeners
    document.addEventListener('contextmenu', handleContextMenu);
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('selectstart', handleSelectStart);
    document.addEventListener('dragstart', handleDragStart);

    // Cleanup function
    return () => {
      document.removeEventListener('contextmenu', handleContextMenu);
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('selectstart', handleSelectStart);
      document.removeEventListener('dragstart', handleDragStart);
    };
  };

  const loadSecureContent = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Verify user has access to this digital product
      const { data: purchase, error: purchaseError } = await supabase
        .from('user_digital_purchases')
        .select('*')
        .eq('user_id', user?.id)
        .eq('product_id', productId)
        .single();

      if (purchaseError || !purchase) {
        throw new Error('You do not have access to this content');
      }

      // Check if access has expired
      if (purchase.access_expires_at && new Date(purchase.access_expires_at) < new Date()) {
        throw new Error('Your access to this content has expired');
      }

      // Get file details
      const { data: fileData, error: fileError } = await supabase
        .from('digital_product_files')
        .select('*')
        .eq('id', fileId)
        .single();

      if (fileError || !fileData) {
        throw new Error('File not found');
      }

      // Get signed URL for secure access
      const { data: urlData, error: urlError } = await supabase.storage
        .from('digital-products')
        .createSignedUrl(fileData.file_path, 3600); // 1 hour expiry

      if (urlError || !urlData) {
        throw new Error('Failed to load content');
      }

      setFileUrl(urlData.signedUrl);

      // Log access for security audit
      await supabase
        .from('user_file_access_logs')
        .insert({
          user_id: user?.id,
          product_id: productId,
          file_id: fileId,
          access_type: 'view',
          ip_address: null, // Would be set by server
          user_agent: navigator.userAgent
        });

    } catch (error: any) {
      console.error('Error loading secure content:', error);
      setError(error.message || 'Failed to load content');
      toast({
        title: 'Access Denied',
        description: error.message || 'Failed to load content',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAudioPlay = () => {
    if (audioRef.current) {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  const handleAudioPause = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
    }
  };

  const handleSeek = (time: number) => {
    if (audioRef.current) {
      audioRef.current.currentTime = time;
      setCurrentTime(time);
    }
  };

  const handleVolumeChange = (newVolume: number) => {
    if (audioRef.current) {
      audioRef.current.volume = newVolume;
      setVolume(newVolume);
      setIsMuted(newVolume === 0);
    }
  };

  const toggleMute = () => {
    if (audioRef.current) {
      if (isMuted) {
        audioRef.current.volume = volume;
        setIsMuted(false);
      } else {
        audioRef.current.volume = 0;
        setIsMuted(true);
      }
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (isLoading) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">Loading secure content...</p>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="p-8 text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Access Denied</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          {onClose && (
            <Button onClick={onClose} variant="outline">
              Go Back
            </Button>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <div 
      ref={containerRef}
      className="w-full max-w-4xl mx-auto"
      style={{ 
        userSelect: 'none',
        WebkitUserSelect: 'none',
        MozUserSelect: 'none',
        msUserSelect: 'none'
      }}
    >
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {fileType === 'audiobook' || mimeType?.startsWith('audio/') ? (
                <Music className="h-6 w-6 text-blue-600" />
              ) : (
                <FileText className="h-6 w-6 text-green-600" />
              )}
              <div>
                <CardTitle className="text-lg">{fileName}</CardTitle>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="secondary" className="text-xs">
                    {fileType === 'audiobook' ? 'Audio Book' : fileType?.toUpperCase()}
                  </Badge>
                  <div className="flex items-center gap-1 text-xs text-gray-500">
                    <Shield className="w-3 h-3" />
                    <span>Protected Content</span>
                  </div>
                </div>
              </div>
            </div>
            {onClose && (
              <Button onClick={onClose} variant="outline" size="sm">
                Close
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {/* Security Notice */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
            <div className="flex items-center gap-2 text-yellow-800">
              <Lock className="w-4 h-4" />
              <span className="text-sm font-medium">Protected Content</span>
            </div>
            <p className="text-xs text-yellow-700 mt-1">
              This content is protected. Downloading, sharing, or taking screenshots is not permitted.
            </p>
          </div>

          {/* Content Viewer */}
          {fileType === 'audiobook' || mimeType?.startsWith('audio/') ? (
            <div className="space-y-4">
              <audio
                ref={audioRef}
                src={fileUrl || undefined}
                onTimeUpdate={handleTimeUpdate}
                onLoadedMetadata={handleLoadedMetadata}
                onEnded={() => setIsPlaying(false)}
                preload="metadata"
                style={{ display: 'none' }}
              />
              
              {/* Audio Player Controls */}
              <div className="bg-gray-50 rounded-lg p-6">
                <div className="flex items-center justify-center space-x-4 mb-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSeek(Math.max(0, currentTime - 30))}
                  >
                    <SkipBack className="w-4 h-4" />
                  </Button>
                  
                  <Button
                    onClick={isPlaying ? handleAudioPause : handleAudioPlay}
                    size="lg"
                    className="rounded-full w-12 h-12"
                  >
                    {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSeek(Math.min(duration, currentTime + 30))}
                  >
                    <SkipForward className="w-4 h-4" />
                  </Button>
                </div>
                
                {/* Progress Bar */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>{formatTime(currentTime)}</span>
                    <span>{formatTime(duration)}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
                    />
                  </div>
                  <input
                    type="range"
                    min="0"
                    max={duration}
                    value={currentTime}
                    onChange={(e) => handleSeek(Number(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer opacity-0 absolute"
                  />
                </div>
                
                {/* Volume Control */}
                <div className="flex items-center justify-center space-x-2 mt-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={toggleMute}
                  >
                    {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                  </Button>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={isMuted ? 0 : volume}
                    onChange={(e) => handleVolumeChange(Number(e.target.value))}
                    className="w-20"
                  />
                </div>
              </div>
            </div>
          ) : (
            // PDF/Ebook Viewer
            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4 min-h-96">
                {fileUrl && (
                  <iframe
                    src={fileUrl}
                    className="w-full h-96 border-0 rounded"
                    title={fileName}
                    sandbox="allow-same-origin"
                    style={{
                      pointerEvents: 'auto',
                      userSelect: 'none',
                      WebkitUserSelect: 'none'
                    }}
                    onContextMenu={(e) => e.preventDefault()}
                  />
                )}
              </div>
              
              <div className="text-center text-sm text-gray-600">
                <div className="flex items-center justify-center gap-2">
                  <Eye className="w-4 h-4" />
                  <span>Viewing in secure mode - Content cannot be downloaded or shared</span>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
