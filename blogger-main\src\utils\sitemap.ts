interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

interface SitemapData {
  articles: Array<{
    slug: string;
    updated_at: string;
    published_at: string;
  }>;
  products: Array<{
    slug: string;
    updated_at: string;
    created_at: string;
  }>;
  categories: Array<{
    slug: string;
    updated_at: string;
  }>;
}

export class SitemapGenerator {
  private baseUrl: string;
  private urls: SitemapUrl[] = [];

  constructor(baseUrl: string = 'https://thechronicle.com') {
    this.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
  }

  // Add static pages
  addStaticPages() {
    const staticPages: SitemapUrl[] = [
      {
        loc: this.baseUrl,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'daily',
        priority: 1.0,
      },
      {
        loc: `${this.baseUrl}/articles`,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'daily',
        priority: 0.9,
      },
      {
        loc: `${this.baseUrl}/products`,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'daily',
        priority: 0.9,
      },
      {
        loc: `${this.baseUrl}/about`,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'monthly',
        priority: 0.7,
      },
      {
        loc: `${this.baseUrl}/contact`,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'monthly',
        priority: 0.6,
      },
    ];

    this.urls.push(...staticPages);
    return this;
  }

  // Add dynamic content
  addDynamicContent(data: SitemapData) {
    // Add articles
    data.articles.forEach(article => {
      this.urls.push({
        loc: `${this.baseUrl}/articles/${article.slug}`,
        lastmod: article.updated_at.split('T')[0],
        changefreq: 'weekly',
        priority: 0.8,
      });
    });

    // Add products
    data.products.forEach(product => {
      this.urls.push({
        loc: `${this.baseUrl}/products/${product.slug}`,
        lastmod: product.updated_at.split('T')[0],
        changefreq: 'weekly',
        priority: 0.8,
      });
    });

    // Add categories
    data.categories.forEach(category => {
      this.urls.push({
        loc: `${this.baseUrl}/articles?category=${category.slug}`,
        lastmod: category.updated_at.split('T')[0],
        changefreq: 'weekly',
        priority: 0.7,
      });
    });

    return this;
  }

  // Generate XML sitemap
  generateXML(): string {
    const xmlHeader = '<?xml version="1.0" encoding="UTF-8"?>';
    const urlsetOpen = '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';
    const urlsetClose = '</urlset>';

    const urlElements = this.urls.map(url => {
      let urlElement = `  <url>\n    <loc>${url.loc}</loc>`;
      
      if (url.lastmod) {
        urlElement += `\n    <lastmod>${url.lastmod}</lastmod>`;
      }
      
      if (url.changefreq) {
        urlElement += `\n    <changefreq>${url.changefreq}</changefreq>`;
      }
      
      if (url.priority !== undefined) {
        urlElement += `\n    <priority>${url.priority.toFixed(1)}</priority>`;
      }
      
      urlElement += '\n  </url>';
      return urlElement;
    }).join('\n');

    return `${xmlHeader}\n${urlsetOpen}\n${urlElements}\n${urlsetClose}`;
  }

  // Generate sitemap index for large sites
  generateSitemapIndex(sitemaps: string[]): string {
    const xmlHeader = '<?xml version="1.0" encoding="UTF-8"?>';
    const sitemapIndexOpen = '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';
    const sitemapIndexClose = '</sitemapindex>';

    const sitemapElements = sitemaps.map(sitemap => {
      return `  <sitemap>\n    <loc>${this.baseUrl}/${sitemap}</loc>\n    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>\n  </sitemap>`;
    }).join('\n');

    return `${xmlHeader}\n${sitemapIndexOpen}\n${sitemapElements}\n${sitemapIndexClose}`;
  }

  // Clear all URLs
  clear() {
    this.urls = [];
    return this;
  }

  // Get all URLs
  getUrls(): SitemapUrl[] {
    return this.urls;
  }
}

// Generate robots.txt content
export function generateRobotsTxt(baseUrl: string = 'https://thechronicle.com'): string {
  return `User-agent: *
Allow: /

# Sitemap
Sitemap: ${baseUrl}/sitemap.xml

# Crawl-delay for respectful crawling
Crawl-delay: 1

# Disallow admin and private areas
Disallow: /admin/
Disallow: /dashboard/
Disallow: /api/
Disallow: /checkout/
Disallow: /cart/
Disallow: /auth/
Disallow: /login/
Disallow: /register/
Disallow: /profile/
Disallow: /settings/

# Allow important pages
Allow: /
Allow: /articles/
Allow: /products/
Allow: /about/
Allow: /contact/
Allow: /search/

# Block search parameters
Disallow: /*?*
Allow: /*?utm_*
Allow: /*?ref=*

# Block duplicate content
Disallow: /print/
Disallow: /preview/
Disallow: /draft/

# Allow social media crawlers
User-agent: facebookexternalhit
Allow: /

User-agent: Twitterbot
Allow: /

User-agent: LinkedInBot
Allow: /

User-agent: WhatsApp
Allow: /

# Google-specific directives
User-agent: Googlebot
Allow: /
Crawl-delay: 1

User-agent: Googlebot-Image
Allow: /images/
Allow: /assets/

User-agent: Googlebot-News
Allow: /articles/
Allow: /news/

# Bing-specific directives
User-agent: Bingbot
Allow: /
Crawl-delay: 1`;
}

// SEO utility functions
export const SEOUtils = {
  // Generate meta description from content
  generateMetaDescription(content: string, maxLength: number = 160): string {
    const cleanContent = content
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
    
    if (cleanContent.length <= maxLength) return cleanContent;
    
    const truncated = cleanContent.substring(0, maxLength - 3);
    const lastSpace = truncated.lastIndexOf(' ');
    
    return (lastSpace > maxLength * 0.8 ? truncated.substring(0, lastSpace) : truncated) + '...';
  },

  // Generate keywords from content
  generateKeywords(content: string, title: string, maxKeywords: number = 10): string[] {
    const commonWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'
    ]);

    const text = (title + ' ' + content)
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    const words = text.split(' ').filter(word => 
      word.length > 2 && !commonWords.has(word)
    );

    const wordCount = words.reduce((acc, word) => {
      acc[word] = (acc[word] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(wordCount)
      .sort(([, a], [, b]) => b - a)
      .slice(0, maxKeywords)
      .map(([word]) => word);
  },

  // Generate SEO-friendly slug
  generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
      .replace(/^-|-$/g, '');
  },

  // Validate SEO requirements
  validateSEO(data: {
    title?: string;
    description?: string;
    keywords?: string[];
    content?: string;
  }): {
    isValid: boolean;
    issues: string[];
    suggestions: string[];
  } {
    const issues: string[] = [];
    const suggestions: string[] = [];

    // Title validation
    if (!data.title) {
      issues.push('Missing title');
    } else {
      if (data.title.length < 30) {
        suggestions.push('Title could be longer (30-60 characters recommended)');
      }
      if (data.title.length > 60) {
        issues.push('Title too long (over 60 characters)');
      }
    }

    // Description validation
    if (!data.description) {
      issues.push('Missing meta description');
    } else {
      if (data.description.length < 120) {
        suggestions.push('Meta description could be longer (120-160 characters recommended)');
      }
      if (data.description.length > 160) {
        issues.push('Meta description too long (over 160 characters)');
      }
    }

    // Keywords validation
    if (!data.keywords || data.keywords.length === 0) {
      suggestions.push('Consider adding relevant keywords');
    } else if (data.keywords.length > 10) {
      suggestions.push('Too many keywords (10 or fewer recommended)');
    }

    // Content validation
    if (data.content) {
      const wordCount = data.content.split(/\s+/).length;
      if (wordCount < 300) {
        suggestions.push('Content could be longer (300+ words recommended for SEO)');
      }
    }

    return {
      isValid: issues.length === 0,
      issues,
      suggestions,
    };
  },
};
