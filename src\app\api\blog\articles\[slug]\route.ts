import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const supabase = createClient();

    // Get the article by slug (published only)
    const { data: article, error } = await supabase
      .from('blog_articles')
      .select(`
        id,
        title,
        slug,
        excerpt,
        content,
        featured_image,
        is_featured,
        views,
        likes,
        comments_count,
        read_time,
        tags,
        published_at,
        seo_title,
        seo_description,
        blog_categories (name, color, slug),
        users (full_name)
      `)
      .eq('slug', params.slug)
      .eq('is_published', true)
      .single();

    if (error || !article) {
      return NextResponse.json({ error: 'Article not found' }, { status: 404 });
    }

    // Increment view count
    await supabase
      .from('blog_articles')
      .update({ views: (article.views || 0) + 1 })
      .eq('id', article.id);

    // Get related articles (same category, excluding current article)
    const { data: relatedArticles } = await supabase
      .from('blog_articles')
      .select(`
        id,
        title,
        slug,
        excerpt,
        featured_image,
        views,
        likes,
        read_time,
        published_at,
        blog_categories (name, color)
      `)
      .eq('is_published', true)
      .eq('category_id', article.blog_categories?.name)
      .neq('id', article.id)
      .order('published_at', { ascending: false })
      .limit(3);

    // Get comments for this article (approved only)
    const { data: comments } = await supabase
      .from('blog_comments')
      .select(`
        id,
        content,
        created_at,
        parent_id,
        users (full_name)
      `)
      .eq('article_id', article.id)
      .eq('is_approved', true)
      .order('created_at', { ascending: true });

    // Get product references for this article
    const { data: productReferences } = await supabase
      .from('blog_product_references')
      .select(`
        reference_type,
        position,
        products (
          id,
          name,
          price,
          image_url,
          slug
        )
      `)
      .eq('article_id', article.id)
      .order('position');

    return NextResponse.json({
      article: {
        ...article,
        views: (article.views || 0) + 1 // Return updated view count
      },
      relatedArticles: relatedArticles || [],
      comments: comments || [],
      productReferences: productReferences || []
    });

  } catch (error) {
    console.error('Error in blog article detail API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const supabase = createClient();
    const body = await request.json();
    const { action } = body;

    // Get the article
    const { data: article, error: articleError } = await supabase
      .from('blog_articles')
      .select('id, likes')
      .eq('slug', params.slug)
      .eq('is_published', true)
      .single();

    if (articleError || !article) {
      return NextResponse.json({ error: 'Article not found' }, { status: 404 });
    }

    if (action === 'like') {
      // Check authentication for likes
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      // Check if user already liked this article
      const { data: existingLike } = await supabase
        .from('blog_article_likes')
        .select('id')
        .eq('article_id', article.id)
        .eq('user_id', session.user.id)
        .single();

      if (existingLike) {
        // Unlike - remove the like
        await supabase
          .from('blog_article_likes')
          .delete()
          .eq('article_id', article.id)
          .eq('user_id', session.user.id);

        return NextResponse.json({ 
          liked: false, 
          likes: Math.max((article.likes || 0) - 1, 0)
        });
      } else {
        // Like - add the like
        await supabase
          .from('blog_article_likes')
          .insert({
            article_id: article.id,
            user_id: session.user.id
          });

        return NextResponse.json({ 
          liked: true, 
          likes: (article.likes || 0) + 1
        });
      }
    }

    if (action === 'comment') {
      // Check authentication for comments
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const { content, parent_id } = body;

      if (!content || !content.trim()) {
        return NextResponse.json({ error: 'Comment content is required' }, { status: 400 });
      }

      // Create comment (pending approval by default)
      const { data: comment, error: commentError } = await supabase
        .from('blog_comments')
        .insert({
          content: content.trim(),
          article_id: article.id,
          user_id: session.user.id,
          parent_id: parent_id || null,
          is_approved: false // Require admin approval
        })
        .select(`
          id,
          content,
          created_at,
          parent_id,
          is_approved,
          users (full_name)
        `)
        .single();

      if (commentError) {
        console.error('Error creating comment:', commentError);
        return NextResponse.json({ error: 'Failed to create comment' }, { status: 500 });
      }

      return NextResponse.json({ 
        comment,
        message: 'Comment submitted for approval'
      });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });

  } catch (error) {
    console.error('Error in blog article action API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
