import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '../../../../supabase/auth';
import { supabase } from '../../../../supabase/supabase';
import {
  Shield,
  Eye,
  EyeOff,
  Mail,
  Bell,
  Lock,
  Globe,
  Users,
  MessageSquare,
  Heart,
  Share2,
  Download,
  AlertTriangle,
} from 'lucide-react';

interface PrivacySettings {
  profile_visibility: 'public' | 'private' | 'friends';
  show_email: boolean;
  show_activity: boolean;
  allow_messages: boolean;
  allow_comments: boolean;
  email_notifications: boolean;
  push_notifications: boolean;
  marketing_emails: boolean;
  activity_tracking: boolean;
  data_sharing: boolean;
  show_liked_articles: boolean;
  show_reading_history: boolean;
  allow_downloads: boolean;
}

export function PrivacySettings() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [settings, setSettings] = useState<PrivacySettings>({
    profile_visibility: 'public',
    show_email: false,
    show_activity: true,
    allow_messages: true,
    allow_comments: true,
    email_notifications: true,
    push_notifications: true,
    marketing_emails: false,
    activity_tracking: true,
    data_sharing: false,
    show_liked_articles: true,
    show_reading_history: false,
    allow_downloads: true,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    loadPrivacySettings();
  }, [user]);

  const loadPrivacySettings = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      
      const { data, error } = await supabase
        .from('user_privacy_settings')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      if (data) {
        setSettings(data.settings);
      }
    } catch (error) {
      console.error('Error loading privacy settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to load privacy settings',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const savePrivacySettings = async () => {
    if (!user) return;

    try {
      setIsSaving(true);

      const { error } = await supabase
        .from('user_privacy_settings')
        .upsert({
          user_id: user.id,
          settings: settings,
          updated_at: new Date().toISOString(),
        });

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'Privacy settings saved successfully',
      });
    } catch (error) {
      console.error('Error saving privacy settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to save privacy settings',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const updateSetting = (key: keyof PrivacySettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const resetToDefaults = () => {
    setSettings({
      profile_visibility: 'public',
      show_email: false,
      show_activity: true,
      allow_messages: true,
      allow_comments: true,
      email_notifications: true,
      push_notifications: true,
      marketing_emails: false,
      activity_tracking: true,
      data_sharing: false,
      show_liked_articles: true,
      show_reading_history: false,
      allow_downloads: true,
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <Shield className="h-8 w-8 text-blue-600" />
            Privacy Settings
          </h1>
          <p className="text-gray-600 mt-1">
            Control your privacy and data sharing preferences
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={resetToDefaults}>
            Reset to Defaults
          </Button>
          <Button onClick={savePrivacySettings} disabled={isSaving}>
            {isSaving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      {/* Profile Visibility */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Profile Visibility
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <Label className="text-base font-medium">Who can see your profile?</Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="public"
                  name="profile_visibility"
                  checked={settings.profile_visibility === 'public'}
                  onChange={() => updateSetting('profile_visibility', 'public')}
                  className="w-4 h-4"
                />
                <Label htmlFor="public" className="flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  Public - Anyone can see your profile
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="friends"
                  name="profile_visibility"
                  checked={settings.profile_visibility === 'friends'}
                  onChange={() => updateSetting('profile_visibility', 'friends')}
                  className="w-4 h-4"
                />
                <Label htmlFor="friends" className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Friends only - Only people you follow can see your profile
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="private"
                  name="profile_visibility"
                  checked={settings.profile_visibility === 'private'}
                  onChange={() => updateSetting('profile_visibility', 'private')}
                  className="w-4 h-4"
                />
                <Label htmlFor="private" className="flex items-center gap-2">
                  <Lock className="h-4 w-4" />
                  Private - Only you can see your profile
                </Label>
              </div>
            </div>
          </div>

          <Separator />

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <Label htmlFor="show_email">Show email address on profile</Label>
              </div>
              <Switch
                id="show_email"
                checked={settings.show_email}
                onCheckedChange={(checked) => updateSetting('show_email', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                <Label htmlFor="show_activity">Show activity status</Label>
              </div>
              <Switch
                id="show_activity"
                checked={settings.show_activity}
                onCheckedChange={(checked) => updateSetting('show_activity', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Communication Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Communication
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              <Label htmlFor="allow_messages">Allow direct messages</Label>
            </div>
            <Switch
              id="allow_messages"
              checked={settings.allow_messages}
              onCheckedChange={(checked) => updateSetting('allow_messages', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              <Label htmlFor="allow_comments">Allow comments on your content</Label>
            </div>
            <Switch
              id="allow_comments"
              checked={settings.allow_comments}
              onCheckedChange={(checked) => updateSetting('allow_comments', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Notification Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notifications
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              <Label htmlFor="email_notifications">Email notifications</Label>
            </div>
            <Switch
              id="email_notifications"
              checked={settings.email_notifications}
              onCheckedChange={(checked) => updateSetting('email_notifications', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              <Label htmlFor="push_notifications">Push notifications</Label>
            </div>
            <Switch
              id="push_notifications"
              checked={settings.push_notifications}
              onCheckedChange={(checked) => updateSetting('push_notifications', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Share2 className="h-4 w-4" />
              <Label htmlFor="marketing_emails">Marketing emails</Label>
            </div>
            <Switch
              id="marketing_emails"
              checked={settings.marketing_emails}
              onCheckedChange={(checked) => updateSetting('marketing_emails', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Data & Privacy */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Data & Privacy
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              <Label htmlFor="activity_tracking">Allow activity tracking</Label>
            </div>
            <Switch
              id="activity_tracking"
              checked={settings.activity_tracking}
              onCheckedChange={(checked) => updateSetting('activity_tracking', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Share2 className="h-4 w-4" />
              <Label htmlFor="data_sharing">Allow data sharing with partners</Label>
            </div>
            <Switch
              id="data_sharing"
              checked={settings.data_sharing}
              onCheckedChange={(checked) => updateSetting('data_sharing', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Heart className="h-4 w-4" />
              <Label htmlFor="show_liked_articles">Show liked articles publicly</Label>
            </div>
            <Switch
              id="show_liked_articles"
              checked={settings.show_liked_articles}
              onCheckedChange={(checked) => updateSetting('show_liked_articles', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              <Label htmlFor="show_reading_history">Show reading history</Label>
            </div>
            <Switch
              id="show_reading_history"
              checked={settings.show_reading_history}
              onCheckedChange={(checked) => updateSetting('show_reading_history', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              <Label htmlFor="allow_downloads">Allow content downloads</Label>
            </div>
            <Switch
              id="allow_downloads"
              checked={settings.allow_downloads}
              onCheckedChange={(checked) => updateSetting('allow_downloads', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Warning */}
      <Card className="border-yellow-200 bg-yellow-50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-yellow-800">Privacy Notice</h3>
              <p className="text-sm text-yellow-700 mt-1">
                These settings control how your information is shared and displayed. 
                Some features may not work properly if certain privacy settings are disabled. 
                You can change these settings at any time.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
