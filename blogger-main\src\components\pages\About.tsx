import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Navbar } from '@/components/layout/Navbar';
import { Footer } from '@/components/layout/Footer';
import { ScrollToTop } from '@/components/ui/scroll-to-top';
import { Link } from 'react-router-dom';
import {
  Users,
  Target,
  Award,
  Heart,
  Globe,
  BookOpen,
  Zap,
  Shield,
  TrendingUp,
  Mail,
  Twitter,
  Github,
  Linkedin,
} from 'lucide-react';

const teamMembers = [
  {
    name: '<PERSON>',
    role: 'Editor-in-Chief',
    bio: 'Award-winning journalist with 15+ years of experience in digital media.',
    image: 'https://api.dicebear.com/7.x/avataaars/svg?seed=sarah',
    social: {
      twitter: '#',
      linkedin: '#',
      email: '<EMAIL>',
    },
  },
  {
    name: '<PERSON>',
    role: 'Technology Writer',
    bio: 'Former software engineer turned tech journalist, covering emerging technologies.',
    image: 'https://api.dicebear.com/7.x/avataaars/svg?seed=michael',
    social: {
      twitter: '#',
      github: '#',
      email: '<EMAIL>',
    },
  },
  {
    name: '<PERSON> <PERSON>',
    role: 'Business Analyst',
    bio: 'MBA graduate specializing in market analysis and business strategy reporting.',
    image: 'https://api.dicebear.com/7.x/avataaars/svg?seed=emily',
    social: {
      linkedin: '#',
      email: '<EMAIL>',
    },
  },
  {
    name: 'David Kim',
    role: 'Creative Director',
    bio: 'Visual storyteller with expertise in multimedia content and design.',
    image: 'https://api.dicebear.com/7.x/avataaars/svg?seed=david',
    social: {
      twitter: '#',
      email: '<EMAIL>',
    },
  },
];

const values = [
  {
    icon: <Target className="h-8 w-8" />,
    title: 'Accuracy',
    description: 'We are committed to delivering factual, well-researched content that our readers can trust.',
    color: 'text-blue-600',
  },
  {
    icon: <Heart className="h-8 w-8" />,
    title: 'Integrity',
    description: 'We maintain the highest ethical standards in our reporting and business practices.',
    color: 'text-red-600',
  },
  {
    icon: <Globe className="h-8 w-8" />,
    title: 'Diversity',
    description: 'We celebrate diverse perspectives and ensure inclusive representation in our content.',
    color: 'text-green-600',
  },
  {
    icon: <Zap className="h-8 w-8" />,
    title: 'Innovation',
    description: 'We embrace new technologies and creative approaches to storytelling.',
    color: 'text-yellow-600',
  },
];

const stats = [
  { label: 'Articles Published', value: '10,000+' },
  { label: 'Monthly Readers', value: '2.5M+' },
  { label: 'Countries Reached', value: '150+' },
  { label: 'Team Members', value: '50+' },
];

export function About() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            About The Thabo Bester
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            We are a modern digital publication dedicated to delivering high-quality journalism, 
            insightful analysis, and engaging stories that matter to our global audience.
          </p>
        </div>

        {/* Mission Section */}
        <section className="mb-16">
          <Card className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            <CardContent className="p-12 text-center">
              <BookOpen className="h-16 w-16 mx-auto mb-6 opacity-90" />
              <h2 className="text-3xl font-bold mb-6">Our Mission</h2>
              <p className="text-xl leading-relaxed max-w-4xl mx-auto">
                To inform, educate, and inspire our readers through exceptional journalism 
                that bridges the gap between complex global events and everyday understanding. 
                We believe in the power of storytelling to create positive change in the world.
              </p>
            </CardContent>
          </Card>
        </section>

        {/* Stats Section */}
        <section className="mb-16">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <Card key={index} className="text-center">
                <CardContent className="p-6">
                  <div className="text-3xl font-bold text-blue-600 mb-2">
                    {stat.value}
                  </div>
                  <div className="text-gray-600 font-medium">
                    {stat.label}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Values Section */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Values</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              These core principles guide everything we do and shape our commitment to excellence.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className={`${value.color} mb-4 flex justify-center`}>
                    {value.icon}
                  </div>
                  <h3 className="text-xl font-semibold mb-3">{value.title}</h3>
                  <p className="text-gray-600">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Team Section */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Meet Our Team</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Our diverse team of journalists, writers, and creators brings together decades 
              of experience and fresh perspectives.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {teamMembers.map((member, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <img
                    src={member.image}
                    alt={member.name}
                    className="w-24 h-24 rounded-full mx-auto mb-4"
                  />
                  <h3 className="text-xl font-semibold mb-1">{member.name}</h3>
                  <Badge variant="secondary" className="mb-3">
                    {member.role}
                  </Badge>
                  <p className="text-gray-600 text-sm mb-4">{member.bio}</p>
                  
                  <div className="flex justify-center gap-3">
                    {member.social.email && (
                      <a href={`mailto:${member.social.email}`} className="text-gray-400 hover:text-blue-600">
                        <Mail className="h-4 w-4" />
                      </a>
                    )}
                    {member.social.twitter && (
                      <a href={member.social.twitter} className="text-gray-400 hover:text-blue-600">
                        <Twitter className="h-4 w-4" />
                      </a>
                    )}
                    {member.social.linkedin && (
                      <a href={member.social.linkedin} className="text-gray-400 hover:text-blue-600">
                        <Linkedin className="h-4 w-4" />
                      </a>
                    )}
                    {member.social.github && (
                      <a href={member.social.github} className="text-gray-400 hover:text-blue-600">
                        <Github className="h-4 w-4" />
                      </a>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* History Section */}
        <section className="mb-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Story</h2>
              <div className="space-y-4 text-gray-600">
                <p>
                  Founded in 2025, Thabo Bester began as a small independent publication 
                  with a vision to create meaningful journalism in the digital age. What started 
                  as a passion project has grown into a trusted source of news and analysis 
                  for millions of readers worldwide.
                </p>
                <p>
                  Our journey has been marked by a commitment to innovation, from pioneering 
                  new storytelling formats to building cutting-edge digital platforms that 
                  enhance the reader experience. We've covered major global events, uncovered 
                  important stories, and given voice to underrepresented communities.
                </p>
                <p>
                  Today, we continue to evolve and adapt, always staying true to our core 
                  mission of delivering exceptional journalism that informs, educates, and 
                  inspires positive change in the world.
                </p>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardContent className="p-6 text-center">
                  <Award className="h-12 w-12 text-yellow-600 mx-auto mb-3" />
                  <h3 className="font-semibold mb-2">Awards Won</h3>
                  <p className="text-2xl font-bold text-gray-900">25+</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6 text-center">
                  <Users className="h-12 w-12 text-blue-600 mx-auto mb-3" />
                  <h3 className="font-semibold mb-2">Community</h3>
                  <p className="text-2xl font-bold text-gray-900">500K+</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6 text-center">
                  <TrendingUp className="h-12 w-12 text-green-600 mx-auto mb-3" />
                  <h3 className="font-semibold mb-2">Growth Rate</h3>
                  <p className="text-2xl font-bold text-gray-900">300%</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6 text-center">
                  <Shield className="h-12 w-12 text-purple-600 mx-auto mb-3" />
                  <h3 className="font-semibold mb-2">Trust Score</h3>
                  <p className="text-2xl font-bold text-gray-900">98%</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="text-center">
          <Card className="bg-gray-900 text-white">
            <CardContent className="p-12">
              <h2 className="text-3xl font-bold mb-6">Join Our Journey</h2>
              <p className="text-xl mb-8 max-w-2xl mx-auto">
                Be part of our growing community and stay updated with the latest stories, 
                insights, and behind-the-scenes content.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link to="/signup">
                  <Button size="lg" className="bg-white text-gray-900 hover:bg-gray-100">
                    Subscribe to Newsletter
                  </Button>
                </Link>
                <Link to="/contact">
                  <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-gray-900">
                    Get in Touch
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>

      <Footer />
      <ScrollToTop />
    </div>
  );
}
