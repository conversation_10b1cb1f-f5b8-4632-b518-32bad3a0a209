'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  FileText, 
  Download, 
  Calendar,
  BarChart3,
  Users,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Filter
} from "lucide-react";
import { createClient } from '@/utils/supabase/client';
import { useToast } from "@/hooks/use-toast";
import { useRouter } from 'next/navigation';

interface AdminUser {
  id: string;
  email: string;
  full_name: string | null;
  admin_role: 'admin' | 'senior_admin' | 'junior_admin';
}

interface ReportConfig {
  report_type: 'activity_summary' | 'performance_metrics' | 'audit_trail';
  report_name: string;
  date_from: string;
  date_to: string;
  admin_roles: string[];
  activity_types: string[];
  file_format: 'pdf' | 'excel';
}

interface GeneratedReport {
  id: string;
  report_name: string;
  report_type: string;
  file_format: string;
  download_count: number;
  created_at: string;
  expires_at: string | null;
  filters?: any;
}

export default function AdminReportsPage() {
  const [currentUser, setCurrentUser] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [reports, setReports] = useState<GeneratedReport[]>([]);
  const [isGenerateDialogOpen, setIsGenerateDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedReport, setSelectedReport] = useState<GeneratedReport | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [reportConfig, setReportConfig] = useState<ReportConfig>({
    report_type: 'activity_summary',
    report_name: '',
    date_from: '',
    date_to: '',
    admin_roles: [],
    activity_types: [],
    file_format: 'pdf'
  });

  const { toast } = useToast();
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    checkAccess();
    fetchReports();
  }, []);

  const checkAccess = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        router.push('/admin/sign-in');
        return;
      }

      const { data: userData } = await supabase
        .from('users')
        .select('id, email, full_name, admin_role')
        .eq('id', session.user.id)
        .single();

      if (!userData || userData.admin_role !== 'admin') {
        toast({
          title: "Access Denied",
          description: "Only main administrators can access reports",
          variant: "destructive",
        });
        router.push('/admin');
        return;
      }

      setCurrentUser(userData as AdminUser);
    } catch (error) {
      console.error('Error checking access:', error);
      router.push('/admin/sign-in');
    } finally {
      setLoading(false);
    }
  };

  const fetchReports = async () => {
    try {
      const response = await fetch('/api/admin/reports');

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch reports');
      }

      const data = await response.json();
      setReports(data.reports || []);
    } catch (error) {
      console.error('Error fetching reports:', error);
      toast({
        title: "Error",
        description: `Failed to fetch reports: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    }
  };

  const generateReport = async () => {
    if (!reportConfig.report_name || !reportConfig.date_from || !reportConfig.date_to) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsGenerating(true);

      // Create report via API
      const response = await fetch('/api/admin/reports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          report_type: reportConfig.report_type,
          report_name: reportConfig.report_name,
          filters: {
            date_from: reportConfig.date_from,
            date_to: reportConfig.date_to,
            admin_roles: reportConfig.admin_roles,
            activity_types: reportConfig.activity_types
          },
          file_format: reportConfig.file_format
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate report');
      }

      // Simulate report generation (in real implementation, this would call a background job)
      await new Promise(resolve => setTimeout(resolve, 2000));

      toast({
        title: "Report Generated",
        description: `${reportConfig.report_name} has been generated successfully`,
      });

      setIsGenerateDialogOpen(false);
      setReportConfig({
        report_type: 'activity_summary',
        report_name: '',
        date_from: '',
        date_to: '',
        admin_roles: [],
        activity_types: [],
        file_format: 'pdf'
      });
      fetchReports();
    } catch (error) {
      console.error('Error generating report:', error);
      toast({
        title: "Generation Failed",
        description: "Failed to generate report",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const viewReport = (report: GeneratedReport) => {
    setSelectedReport(report);
    setIsViewDialogOpen(true);
  };

  const downloadReport = async (reportId: string, reportName: string) => {
    try {
      toast({
        title: "Download Started",
        description: `Downloading ${reportName}...`,
      });

      // Get report details first
      const report = reports.find(r => r.id === reportId);
      if (!report) {
        throw new Error('Report not found');
      }

      // Call export API to generate and download the file
      const response = await fetch('/api/admin/reports/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          report_type: report.report_type,
          file_format: report.file_format,
          filters: report.filters || {}
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate report file');
      }

      // Create download link
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `${reportName.replace(/\s+/g, '_')}.${report.file_format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "Download Complete",
        description: `${reportName} downloaded successfully`,
      });

      // Refresh reports to update download count
      fetchReports();
    } catch (error) {
      console.error('Error downloading report:', error);
      toast({
        title: "Download Failed",
        description: `Failed to download report: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    }
  };

  const deleteReport = async (reportId: string, reportName: string) => {
    try {
      const response = await fetch(`/api/admin/reports?id=${reportId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete report');
      }

      toast({
        title: "Report Deleted",
        description: `${reportName} has been deleted`,
      });

      fetchReports();
    } catch (error) {
      console.error('Error deleting report:', error);
      toast({
        title: "Deletion Failed",
        description: `Failed to delete report: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    }
  };

  const getReportTypeIcon = (reportType: string) => {
    switch (reportType) {
      case 'activity_summary':
        return <Activity className="h-4 w-4 text-blue-500" />;
      case 'performance_metrics':
        return <BarChart3 className="h-4 w-4 text-green-500" />;
      case 'audit_trail':
        return <FileText className="h-4 w-4 text-purple-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Admin Reports</h1>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (!currentUser || currentUser.admin_role !== 'admin') {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
              <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
              <p className="text-muted-foreground">
                Only main administrators can access the reporting system.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Admin Reports</h1>
          <p className="text-muted-foreground">
            Generate and manage administrative reports and analytics
          </p>
        </div>
        <Dialog open={isGenerateDialogOpen} onOpenChange={setIsGenerateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <FileText className="h-4 w-4 mr-2" />
              Generate Report
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Generate New Report</DialogTitle>
              <DialogDescription>
                Create a comprehensive administrative report with custom filters
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="report-type">Report Type</Label>
                  <Select 
                    value={reportConfig.report_type} 
                    onValueChange={(value: any) => setReportConfig(prev => ({ ...prev, report_type: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="activity_summary">Activity Summary</SelectItem>
                      <SelectItem value="performance_metrics">Performance Metrics</SelectItem>
                      <SelectItem value="audit_trail">Audit Trail</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="file-format">File Format</Label>
                  <Select 
                    value={reportConfig.file_format} 
                    onValueChange={(value: any) => setReportConfig(prev => ({ ...prev, file_format: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pdf">PDF</SelectItem>
                      <SelectItem value="excel">Excel</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div>
                <Label htmlFor="report-name">Report Name</Label>
                <Input
                  id="report-name"
                  placeholder="Enter report name..."
                  value={reportConfig.report_name}
                  onChange={(e) => setReportConfig(prev => ({ ...prev, report_name: e.target.value }))}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="date-from">From Date</Label>
                  <Input
                    id="date-from"
                    type="date"
                    value={reportConfig.date_from}
                    onChange={(e) => setReportConfig(prev => ({ ...prev, date_from: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="date-to">To Date</Label>
                  <Input
                    id="date-to"
                    type="date"
                    value={reportConfig.date_to}
                    onChange={(e) => setReportConfig(prev => ({ ...prev, date_to: e.target.value }))}
                  />
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsGenerateDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={generateReport} disabled={isGenerating}>
                {isGenerating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Generating...
                  </>
                ) : (
                  <>
                    <FileText className="h-4 w-4 mr-2" />
                    Generate Report
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* View Report Dialog */}
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                {selectedReport?.report_name}
              </DialogTitle>
              <DialogDescription>
                Report details and preview
              </DialogDescription>
            </DialogHeader>

            {selectedReport && (
              <div className="space-y-6">
                {/* Report Metadata */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm">Report Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Type:</span>
                        <span className="capitalize">{selectedReport.report_type.replace('_', ' ')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Format:</span>
                        <span className="uppercase">{selectedReport.file_format}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Downloads:</span>
                        <span>{selectedReport.download_count}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Created:</span>
                        <span>{new Date(selectedReport.created_at).toLocaleDateString()}</span>
                      </div>
                      {selectedReport.expires_at && (
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Expires:</span>
                          <span className={
                            new Date(selectedReport.expires_at) < new Date()
                              ? 'text-red-600'
                              : 'text-muted-foreground'
                          }>
                            {new Date(selectedReport.expires_at).toLocaleDateString()}
                          </span>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm">Report Filters</CardTitle>
                    </CardHeader>
                    <CardContent className="text-sm">
                      {selectedReport.filters ? (
                        <div className="space-y-2">
                          {Object.entries(selectedReport.filters as any).map(([key, value]) => (
                            <div key={key} className="flex justify-between">
                              <span className="text-muted-foreground capitalize">
                                {key.replace('_', ' ')}:
                              </span>
                              <span>{Array.isArray(value) ? value.join(', ') : String(value)}</span>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-muted-foreground">No filters applied</p>
                      )}
                    </CardContent>
                  </Card>
                </div>

                {/* Report Preview */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Report Preview</CardTitle>
                    <CardDescription>
                      This is a preview of what the report contains
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-muted/30 rounded-lg p-6 text-center">
                      <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">{selectedReport.report_name}</h3>
                      <p className="text-muted-foreground mb-4">
                        {selectedReport.report_type === 'activity_summary' && 'Summary of all administrative activities and actions'}
                        {selectedReport.report_type === 'performance_metrics' && 'Performance metrics and analytics for admin users'}
                        {selectedReport.report_type === 'audit_trail' && 'Detailed audit trail with timestamps and action details'}
                      </p>
                      <Button
                        variant="outline"
                        onClick={() => downloadReport(selectedReport.id, selectedReport.report_name)}
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Download {selectedReport.file_format.toUpperCase()}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
                Close
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Report Statistics */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Reports</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{reports.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Downloads</CardTitle>
            <Download className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {reports.reduce((sum, report) => sum + report.download_count, 0)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {reports.filter(report => {
                const reportDate = new Date(report.created_at);
                const now = new Date();
                return reportDate.getMonth() === now.getMonth() && 
                       reportDate.getFullYear() === now.getFullYear();
              }).length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Reports Table */}
      <Card>
        <CardHeader>
          <CardTitle>Generated Reports</CardTitle>
          <CardDescription>
            All administrative reports with download and management options
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Report</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Format</TableHead>
                <TableHead>Downloads</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Expires</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reports.map((report) => (
                <TableRow key={report.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getReportTypeIcon(report.report_type)}
                      <span className="font-medium">{report.report_name}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="capitalize">
                      {report.report_type.replace('_', ' ')}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className="uppercase text-xs font-mono bg-muted px-2 py-1 rounded">
                      {report.file_format}
                    </span>
                  </TableCell>
                  <TableCell>{report.download_count}</TableCell>
                  <TableCell>
                    {new Date(report.created_at).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {report.expires_at ? (
                      <span className={
                        new Date(report.expires_at) < new Date() 
                          ? 'text-red-600' 
                          : 'text-muted-foreground'
                      }>
                        {new Date(report.expires_at).toLocaleDateString()}
                      </span>
                    ) : (
                      <span className="text-muted-foreground">Never</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => viewReport(report)}
                      >
                        <FileText className="h-3 w-3 mr-1" />
                        View
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => downloadReport(report.id, report.report_name)}
                      >
                        <Download className="h-3 w-3 mr-1" />
                        Download
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => deleteReport(report.id, report.report_name)}
                        className="text-red-600 hover:text-red-700"
                      >
                        Delete
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
