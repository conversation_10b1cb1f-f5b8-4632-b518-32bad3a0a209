import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '../ui/button';
import { Card, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';
import { TrendingUp, Clock, Users, Star, ArrowRight, BookOpen, ShoppingBag } from 'lucide-react';
import { Link } from 'react-router-dom';
import { blogService, ecommerceService } from '@/lib/supabase-services';
import { formatCurrency } from '@/utils/currency';

interface TrendingItem {
  id: string;
  title: string;
  slug: string;
  type: 'article' | 'product';
  views?: number;
  likes?: number;
  price?: number;
  category?: string;
  readTime?: number;
  image?: string;
}

export function TrendingContent() {
  const [trendingItems, setTrendingItems] = useState<TrendingItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadTrendingContent();
  }, []);

  const loadTrendingContent = async () => {
    try {
      setIsLoading(true);
      
      // Get trending articles and products
      const [articles, products] = await Promise.all([
        blogService.getTrendingArticles(3),
        ecommerceService.getFeaturedProducts(2)
      ]);

      const trendingArticles: TrendingItem[] = articles.map(article => ({
        id: article.id,
        title: article.title,
        slug: article.slug,
        type: 'article' as const,
        views: article.views,
        likes: article.likes,
        category: article.category?.name,
        readTime: article.read_time,
        image: article.featured_image
      }));

      const trendingProducts: TrendingItem[] = products.map(product => ({
        id: product.id,
        title: product.name,
        slug: product.slug,
        type: 'product' as const,
        price: product.sale_price || product.price,
        category: product.category?.name,
        image: product.image_url
      }));

      // Combine and shuffle for variety
      const combined = [...trendingArticles, ...trendingProducts];
      setTrendingItems(combined.slice(0, 4));
    } catch (error) {
      console.error('Error loading trending content:', error);
      // Show empty state instead of fallback
      setTrendingItems([]);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <Card className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white">
        <CardContent className="p-8">
          <div className="animate-pulse">
            <div className="h-6 bg-white/20 rounded w-1/3 mb-4"></div>
            <div className="space-y-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-4 bg-white/20 rounded"></div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white">
      <CardContent className="p-8">
        <div className="flex items-center gap-3 mb-6">
          <TrendingUp className="h-6 w-6 text-yellow-300" />
          <h3 className="text-2xl font-bold">Trending Now</h3>
        </div>
        
        {trendingItems.length > 0 ? (
          <div className="space-y-4">
            {trendingItems.map((item, index) => (
              <Link
                key={item.id}
                to={item.type === 'article' ? `/articles/${item.slug}` : `/products/${item.slug}`}
                className="block group"
              >
                <div className="flex items-center gap-4 p-3 rounded-lg bg-white/10 hover:bg-white/20 transition-colors">
                  <div className="flex-shrink-0">
                    <Badge variant="outline" className="bg-white/20 text-white border-white/30">
                      #{index + 1}
                    </Badge>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-white group-hover:text-yellow-200 transition-colors truncate">
                      {item.title}
                    </h4>
                    
                    <div className="flex items-center gap-3 mt-1 text-sm text-indigo-200">
                      {item.type === 'article' ? (
                        <>
                          <div className="flex items-center gap-1">
                            <BookOpen className="h-3 w-3" />
                            <span>Article</span>
                          </div>
                          {item.views && (
                            <div className="flex items-center gap-1">
                              <Users className="h-3 w-3" />
                              <span>{item.views.toLocaleString()} views</span>
                            </div>
                          )}
                          {item.readTime && (
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              <span>{item.readTime} min read</span>
                            </div>
                          )}
                        </>
                      ) : (
                        <>
                          <div className="flex items-center gap-1">
                            <ShoppingBag className="h-3 w-3" />
                            <span>Product</span>
                          </div>
                          {item.price && (
                            <div className="flex items-center gap-1">
                              <Star className="h-3 w-3" />
                              <span>{formatCurrency(item.price)}</span>
                            </div>
                          )}
                        </>
                      )}
                      {item.category && (
                        <Badge variant="outline" className="bg-white/10 text-white border-white/20 text-xs">
                          {item.category}
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  <ArrowRight className="h-4 w-4 text-indigo-200 group-hover:text-white transition-colors" />
                </div>
              </Link>
            ))}
            
            <div className="pt-4 border-t border-white/20">
              <div className="flex gap-2">
                <Link to="/articles" className="flex-1">
                  <Button variant="outline" className="w-full bg-white/10 border-white/30 text-white hover:bg-white/20">
                    <BookOpen className="h-4 w-4 mr-2" />
                    All Articles
                  </Button>
                </Link>
                <Link to="/products" className="flex-1">
                  <Button variant="outline" className="w-full bg-white/10 border-white/30 text-white hover:bg-white/20">
                    <ShoppingBag className="h-4 w-4 mr-2" />
                    All Products
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <TrendingUp className="h-12 w-12 mx-auto mb-4 text-indigo-300" />
            <h4 className="text-lg font-medium mb-2">No Trending Content</h4>
            <p className="text-indigo-200 mb-4">
              Check back soon for the latest trending articles and products.
            </p>
            <div className="flex gap-2">
              <Link to="/articles" className="flex-1">
                <Button variant="outline" className="w-full bg-white/10 border-white/30 text-white hover:bg-white/20">
                  Browse Articles
                </Button>
              </Link>
              <Link to="/products" className="flex-1">
                <Button variant="outline" className="w-full bg-white/10 border-white/30 text-white hover:bg-white/20">
                  Browse Products
                </Button>
              </Link>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
