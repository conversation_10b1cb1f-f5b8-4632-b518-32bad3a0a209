import { NextRequest, NextResponse } from 'next/server';
import { createClient, createServiceRoleClient } from '@/utils/supabase/server';

/**
 * GET /api/admin/products/stats
 * Get product statistics for dashboard
 */
export async function GET(request: NextRequest) {
  try {
    console.log('GET /api/admin/products/stats - Request received');

    // Check authentication
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin role
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || userData.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 });
    }

    console.log('GET /api/admin/products/stats - Admin access confirmed');

    // Use service role client for database operations
    const serviceSupabase = createServiceRoleClient();

    // Get product statistics
    const { data: products, error, count } = await serviceSupabase
      .from('products')
      .select('id, status, created_at', { count: 'exact' })
      .eq('status', 'active');

    if (error) {
      console.error('GET /api/admin/products/stats - Database error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch product statistics', details: error.message },
        { status: 500 }
      );
    }

    // Calculate additional metrics
    const totalProducts = count || 0;
    const today = new Date();
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
    
    const recentProducts = products?.filter(product => 
      new Date(product.created_at) >= lastMonth
    ).length || 0;

    const stats = {
      total: totalProducts,
      active: totalProducts,
      recent: recentProducts,
      growth: totalProducts > 0 ? ((recentProducts / totalProducts) * 100).toFixed(1) : '0.0'
    };

    console.log('GET /api/admin/products/stats - Success:', stats);

    return NextResponse.json(stats);

  } catch (error: any) {
    console.error('GET /api/admin/products/stats - General error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
