import { NextRequest, NextResponse } from 'next/server';
import { createClient, createServiceRoleClient } from '@/utils/supabase/server';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions
    const { data: adminData } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (!adminData || adminData.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    console.log('GET /api/admin/business-analytics - Admin access confirmed');

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30d';

    // Calculate date range based on period
    let startDate: Date;
    const endDate = new Date();

    switch (period) {
      case '7d':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        startDate = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
        break;
      default: // 30d
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }

    // Use service role client for database operations
    const serviceSupabase = createServiceRoleClient();

    // Calculate analytics manually since the database structure uses JSONB items
    const metrics = await calculateAnalyticsManually(serviceSupabase, startDate, endDate);

    if ('error' in metrics) {
      console.error('GET /api/admin/business-analytics - Manual calculation error:', metrics.error);
      return NextResponse.json({ error: metrics.error }, { status: 500 });
    }

    console.log('GET /api/admin/business-analytics - Success');

    return NextResponse.json({
      success: true,
      metrics,
      period,
      date_range: {
        start: startDate.toISOString().split('T')[0],
        end: endDate.toISOString().split('T')[0]
      }
    });

  } catch (error: any) {
    console.error('GET /api/admin/business-analytics - General error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

// Manual calculation function for JSONB items structure
async function calculateAnalyticsManually(serviceSupabase: any, startDate: Date, endDate: Date) {
  try {
    // Get orders in date range with JSONB items
    const { data: orders, error: ordersError } = await serviceSupabase
      .from('orders')
      .select('id, total_amount, user_id, created_at, items')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    if (ordersError) {
      return { error: `Failed to fetch orders: ${ordersError.message}` };
    }

    // Calculate basic metrics
    const totalSales = orders?.reduce((sum, order) => sum + (order.total_amount || 0), 0) || 0;
    const totalOrders = orders?.length || 0;
    const averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0;

    // Calculate top selling products from JSONB items
    const productSales: Record<string, any> = {};
    orders?.forEach(order => {
      if (order.items && Array.isArray(order.items)) {
        order.items.forEach((item: any) => {
          const productId = item.product_id;
          if (!productSales[productId]) {
            productSales[productId] = {
              product_id: productId,
              product_name: item.name || 'Unknown Product',
              total_quantity: 0,
              total_revenue: 0
            };
          }
          productSales[productId].total_quantity += item.quantity || 0;
          productSales[productId].total_revenue += (item.quantity || 0) * (item.price || 0);
        });
      }
    });

    const topSellingProducts = Object.values(productSales)
      .sort((a: any, b: any) => b.total_quantity - a.total_quantity)
      .slice(0, 10);

    // Get product categories for sales by category calculation
    const { data: products } = await serviceSupabase
      .from('products')
      .select('id, category');

    const productCategories: Record<string, string> = {};
    products?.forEach((product: any) => {
      productCategories[product.id] = product.category || 'uncategorized';
    });

    // Calculate sales by category from JSONB items
    const categorySales: Record<string, any> = {};
    orders?.forEach(order => {
      if (order.items && Array.isArray(order.items)) {
        order.items.forEach((item: any) => {
          const category = productCategories[item.product_id] || 'uncategorized';
          if (!categorySales[category]) {
            categorySales[category] = {
              category,
              total_sales: 0,
              total_quantity: 0
            };
          }
          categorySales[category].total_sales += (item.quantity || 0) * (item.price || 0);
          categorySales[category].total_quantity += item.quantity || 0;
        });
      }
    });

    const salesByCategory = Object.values(categorySales);

    // Calculate customer metrics
    const uniqueCustomers = new Set(orders?.map(order => order.user_id)).size;

    // Get first order dates for new customer calculation
    const { data: allOrders } = await serviceSupabase
      .from('orders')
      .select('user_id, created_at')
      .order('created_at', { ascending: true });

    const firstOrderDates: Record<string, string> = {};
    allOrders?.forEach((order: any) => {
      if (!firstOrderDates[order.user_id]) {
        firstOrderDates[order.user_id] = order.created_at;
      }
    });

    const uniqueCustomersInPeriod = new Set(orders?.map(order => order.user_id));
    let newCustomers = 0;
    let returningCustomers = 0;

    uniqueCustomersInPeriod.forEach(userId => {
      const userIdStr = String(userId);
      const firstOrderDate = new Date(firstOrderDates[userIdStr]);
      if (firstOrderDate >= startDate && firstOrderDate <= endDate) {
        newCustomers++;
      } else {
        returningCustomers++;
      }
    });

    // Calculate revenue trend (simplified daily aggregation)
    const revenueTrend: any[] = [];
    const dayMs = 24 * 60 * 60 * 1000;
    for (let d = new Date(startDate); d <= endDate; d.setTime(d.getTime() + dayMs)) {
      const dayStart = new Date(d);
      const dayEnd = new Date(d.getTime() + dayMs - 1);
      
      const dayRevenue = orders?.filter(order => {
        const orderDate = new Date(order.created_at);
        return orderDate >= dayStart && orderDate <= dayEnd;
      }).reduce((sum, order) => sum + (order.total_amount || 0), 0) || 0;

      revenueTrend.push({
        date: d.toISOString().split('T')[0],
        revenue: dayRevenue
      });
    }

    const metrics = {
      total_sales: totalSales,
      total_orders: totalOrders,
      average_order_value: averageOrderValue,
      top_selling_products: topSellingProducts,
      sales_by_category: salesByCategory,
      customer_metrics: {
        total_customers: uniqueCustomers,
        new_customers: newCustomers,
        returning_customers: returningCustomers
      },
      revenue_trend: revenueTrend
    };

    return metrics;

  } catch (error: any) {
    console.error('Manual calculation error:', error);
    return { error: 'Failed to calculate analytics' };
  }
}
