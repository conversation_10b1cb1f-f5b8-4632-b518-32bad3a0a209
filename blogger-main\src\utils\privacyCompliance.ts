// Privacy Compliance System for GDPR/CCPA/PIPEDA
// Automated compliance checking and privacy policy generation

export interface ComplianceFramework {
  name: string;
  jurisdiction: string;
  requirements: ComplianceRequirement[];
  penalties: {
    maxFine: string;
    percentage: string;
  };
}

export interface ComplianceRequirement {
  id: string;
  title: string;
  description: string;
  mandatory: boolean;
  category: 'consent' | 'data_protection' | 'user_rights' | 'security' | 'documentation';
  implementation: 'technical' | 'legal' | 'procedural';
  status: 'compliant' | 'partial' | 'non_compliant' | 'not_applicable';
  evidence?: string[];
  lastChecked?: Date;
}

export interface PrivacyPolicySection {
  id: string;
  title: string;
  content: string;
  required: boolean;
  frameworks: string[];
  lastUpdated: Date;
}

export interface ComplianceReport {
  framework: string;
  overallScore: number;
  status: 'compliant' | 'needs_attention' | 'non_compliant';
  requirements: ComplianceRequirement[];
  recommendations: string[];
  nextReviewDate: Date;
  generatedAt: Date;
}

class PrivacyComplianceManager {
  private frameworks: Map<string, ComplianceFramework> = new Map();
  private policyTemplate: Map<string, PrivacyPolicySection> = new Map();

  constructor() {
    this.initializeFrameworks();
    this.initializePolicyTemplate();
  }

  /**
   * Initialize compliance frameworks
   */
  private initializeFrameworks(): void {
    // GDPR Framework
    const gdpr: ComplianceFramework = {
      name: 'General Data Protection Regulation',
      jurisdiction: 'European Union',
      penalties: {
        maxFine: '€20 million',
        percentage: '4% of annual global turnover',
      },
      requirements: [
        {
          id: 'gdpr_consent',
          title: 'Lawful Basis for Processing',
          description: 'Obtain valid consent or establish another lawful basis for data processing',
          mandatory: true,
          category: 'consent',
          implementation: 'technical',
          status: 'compliant',
        },
        {
          id: 'gdpr_transparency',
          title: 'Transparency and Information',
          description: 'Provide clear information about data processing activities',
          mandatory: true,
          category: 'documentation',
          implementation: 'legal',
          status: 'compliant',
        },
        {
          id: 'gdpr_data_minimization',
          title: 'Data Minimization',
          description: 'Process only data that is necessary for the specified purpose',
          mandatory: true,
          category: 'data_protection',
          implementation: 'technical',
          status: 'partial',
        },
        {
          id: 'gdpr_right_access',
          title: 'Right of Access',
          description: 'Provide individuals with access to their personal data',
          mandatory: true,
          category: 'user_rights',
          implementation: 'technical',
          status: 'compliant',
        },
        {
          id: 'gdpr_right_erasure',
          title: 'Right to Erasure',
          description: 'Enable individuals to request deletion of their personal data',
          mandatory: true,
          category: 'user_rights',
          implementation: 'technical',
          status: 'compliant',
        },
        {
          id: 'gdpr_data_portability',
          title: 'Right to Data Portability',
          description: 'Provide data in a structured, machine-readable format',
          mandatory: true,
          category: 'user_rights',
          implementation: 'technical',
          status: 'compliant',
        },
        {
          id: 'gdpr_security',
          title: 'Security of Processing',
          description: 'Implement appropriate technical and organizational measures',
          mandatory: true,
          category: 'security',
          implementation: 'technical',
          status: 'compliant',
        },
        {
          id: 'gdpr_breach_notification',
          title: 'Data Breach Notification',
          description: 'Notify authorities and individuals of data breaches within 72 hours',
          mandatory: true,
          category: 'security',
          implementation: 'procedural',
          status: 'partial',
        },
        {
          id: 'gdpr_dpia',
          title: 'Data Protection Impact Assessment',
          description: 'Conduct DPIA for high-risk processing activities',
          mandatory: false,
          category: 'documentation',
          implementation: 'procedural',
          status: 'not_applicable',
        },
        {
          id: 'gdpr_dpo',
          title: 'Data Protection Officer',
          description: 'Appoint a DPO if required by processing activities',
          mandatory: false,
          category: 'documentation',
          implementation: 'procedural',
          status: 'not_applicable',
        },
      ],
    };

    // CCPA Framework
    const ccpa: ComplianceFramework = {
      name: 'California Consumer Privacy Act',
      jurisdiction: 'California, USA',
      penalties: {
        maxFine: '$7,500 per violation',
        percentage: 'N/A',
      },
      requirements: [
        {
          id: 'ccpa_notice',
          title: 'Notice at Collection',
          description: 'Inform consumers about data collection at the point of collection',
          mandatory: true,
          category: 'documentation',
          implementation: 'legal',
          status: 'compliant',
        },
        {
          id: 'ccpa_right_know',
          title: 'Right to Know',
          description: 'Provide information about personal information collected and used',
          mandatory: true,
          category: 'user_rights',
          implementation: 'technical',
          status: 'compliant',
        },
        {
          id: 'ccpa_right_delete',
          title: 'Right to Delete',
          description: 'Enable consumers to request deletion of personal information',
          mandatory: true,
          category: 'user_rights',
          implementation: 'technical',
          status: 'compliant',
        },
        {
          id: 'ccpa_opt_out',
          title: 'Right to Opt-Out of Sale',
          description: 'Provide mechanism to opt-out of personal information sale',
          mandatory: true,
          category: 'user_rights',
          implementation: 'technical',
          status: 'compliant',
        },
        {
          id: 'ccpa_non_discrimination',
          title: 'Non-Discrimination',
          description: 'Do not discriminate against consumers exercising their rights',
          mandatory: true,
          category: 'user_rights',
          implementation: 'procedural',
          status: 'compliant',
        },
      ],
    };

    this.frameworks.set('gdpr', gdpr);
    this.frameworks.set('ccpa', ccpa);
  }

  /**
   * Initialize privacy policy template
   */
  private initializePolicyTemplate(): void {
    const sections: PrivacyPolicySection[] = [
      {
        id: 'introduction',
        title: 'Introduction',
        content: 'This Privacy Policy describes how The Chronicle collects, uses, and protects your personal information.',
        required: true,
        frameworks: ['gdpr', 'ccpa'],
        lastUpdated: new Date(),
      },
      {
        id: 'data_collection',
        title: 'Information We Collect',
        content: 'We collect information you provide directly, information collected automatically, and information from third parties.',
        required: true,
        frameworks: ['gdpr', 'ccpa'],
        lastUpdated: new Date(),
      },
      {
        id: 'data_use',
        title: 'How We Use Your Information',
        content: 'We use your information to provide services, improve our platform, and communicate with you.',
        required: true,
        frameworks: ['gdpr', 'ccpa'],
        lastUpdated: new Date(),
      },
      {
        id: 'data_sharing',
        title: 'Information Sharing',
        content: 'We may share your information with service providers, business partners, and as required by law.',
        required: true,
        frameworks: ['gdpr', 'ccpa'],
        lastUpdated: new Date(),
      },
      {
        id: 'user_rights',
        title: 'Your Rights',
        content: 'You have rights to access, correct, delete, and port your personal information.',
        required: true,
        frameworks: ['gdpr', 'ccpa'],
        lastUpdated: new Date(),
      },
      {
        id: 'security',
        title: 'Data Security',
        content: 'We implement appropriate security measures to protect your personal information.',
        required: true,
        frameworks: ['gdpr', 'ccpa'],
        lastUpdated: new Date(),
      },
      {
        id: 'retention',
        title: 'Data Retention',
        content: 'We retain your information only as long as necessary for the purposes outlined in this policy.',
        required: true,
        frameworks: ['gdpr', 'ccpa'],
        lastUpdated: new Date(),
      },
      {
        id: 'cookies',
        title: 'Cookies and Tracking',
        content: 'We use cookies and similar technologies to enhance your experience and analyze usage.',
        required: true,
        frameworks: ['gdpr', 'ccpa'],
        lastUpdated: new Date(),
      },
      {
        id: 'international_transfers',
        title: 'International Data Transfers',
        content: 'We may transfer your information to countries outside your jurisdiction with appropriate safeguards.',
        required: false,
        frameworks: ['gdpr'],
        lastUpdated: new Date(),
      },
      {
        id: 'contact',
        title: 'Contact Information',
        content: 'Contact us with questions about this Privacy Policy or our data practices.',
        required: true,
        frameworks: ['gdpr', 'ccpa'],
        lastUpdated: new Date(),
      },
    ];

    sections.forEach(section => {
      this.policyTemplate.set(section.id, section);
    });
  }

  /**
   * Generate compliance report for a specific framework
   */
  generateComplianceReport(frameworkId: string): ComplianceReport {
    const framework = this.frameworks.get(frameworkId);
    if (!framework) {
      throw new Error(`Framework ${frameworkId} not found`);
    }

    const compliantCount = framework.requirements.filter(req => req.status === 'compliant').length;
    const totalCount = framework.requirements.filter(req => req.mandatory).length;
    const overallScore = Math.round((compliantCount / totalCount) * 100);

    let status: ComplianceReport['status'] = 'compliant';
    if (overallScore < 100) {
      status = framework.requirements.some(req => req.mandatory && req.status === 'non_compliant') 
        ? 'non_compliant' 
        : 'needs_attention';
    }

    const recommendations = this.generateRecommendations(framework.requirements);
    const nextReviewDate = new Date();
    nextReviewDate.setMonth(nextReviewDate.getMonth() + 3); // Review every 3 months

    return {
      framework: framework.name,
      overallScore,
      status,
      requirements: framework.requirements,
      recommendations,
      nextReviewDate,
      generatedAt: new Date(),
    };
  }

  /**
   * Generate privacy policy
   */
  generatePrivacyPolicy(frameworks: string[] = ['gdpr', 'ccpa']): string {
    let policy = '';
    
    // Header
    policy += '# Privacy Policy\n\n';
    policy += `**Last Updated:** ${new Date().toLocaleDateString()}\n\n`;

    // Generate sections
    for (const [sectionId, section] of this.policyTemplate) {
      // Check if section is required for any of the specified frameworks
      const isRequired = frameworks.some(framework => section.frameworks.includes(framework));
      
      if (isRequired || section.required) {
        policy += `## ${section.title}\n\n`;
        policy += `${this.expandSectionContent(section, frameworks)}\n\n`;
      }
    }

    return policy;
  }

  /**
   * Check compliance status
   */
  checkCompliance(): { [framework: string]: ComplianceReport } {
    const reports: { [framework: string]: ComplianceReport } = {};
    
    for (const frameworkId of this.frameworks.keys()) {
      reports[frameworkId] = this.generateComplianceReport(frameworkId);
    }
    
    return reports;
  }

  /**
   * Update requirement status
   */
  updateRequirementStatus(
    frameworkId: string, 
    requirementId: string, 
    status: ComplianceRequirement['status'],
    evidence?: string[]
  ): void {
    const framework = this.frameworks.get(frameworkId);
    if (!framework) return;

    const requirement = framework.requirements.find(req => req.id === requirementId);
    if (requirement) {
      requirement.status = status;
      requirement.lastChecked = new Date();
      if (evidence) {
        requirement.evidence = evidence;
      }
    }
  }

  /**
   * Get data processing activities
   */
  getDataProcessingActivities(): any[] {
    return [
      {
        id: 'user_registration',
        purpose: 'Account creation and management',
        dataTypes: ['name', 'email', 'password'],
        legalBasis: 'contract',
        retention: '7 years after account closure',
        recipients: ['internal staff'],
      },
      {
        id: 'analytics',
        purpose: 'Website analytics and improvement',
        dataTypes: ['IP address', 'browser data', 'usage patterns'],
        legalBasis: 'legitimate_interests',
        retention: '2 years',
        recipients: ['Google Analytics'],
      },
      {
        id: 'marketing',
        purpose: 'Email marketing and communications',
        dataTypes: ['email', 'preferences', 'engagement data'],
        legalBasis: 'consent',
        retention: '3 years or until consent withdrawn',
        recipients: ['email service provider'],
      },
      {
        id: 'payments',
        purpose: 'Payment processing',
        dataTypes: ['payment information', 'billing address'],
        legalBasis: 'contract',
        retention: '7 years (legal requirement)',
        recipients: ['Stripe'],
      },
    ];
  }

  /**
   * Generate cookie policy
   */
  generateCookiePolicy(): string {
    return `
# Cookie Policy

## What are cookies?
Cookies are small text files stored on your device when you visit our website.

## Types of cookies we use:

### Necessary Cookies
- Session management
- Security features
- Basic functionality

### Analytics Cookies
- Google Analytics
- Performance monitoring
- Usage statistics

### Marketing Cookies
- Advertising personalization
- Campaign tracking
- Social media integration

## Managing cookies
You can control cookies through your browser settings or our consent manager.

## Contact us
For questions about our cookie policy, contact <EMAIL>
    `.trim();
  }

  // Private helper methods

  private generateRecommendations(requirements: ComplianceRequirement[]): string[] {
    const recommendations: string[] = [];

    requirements.forEach(req => {
      if (req.status === 'non_compliant' && req.mandatory) {
        recommendations.push(`Critical: Implement ${req.title} - ${req.description}`);
      } else if (req.status === 'partial') {
        recommendations.push(`Improve: Complete implementation of ${req.title}`);
      }
    });

    return recommendations;
  }

  private expandSectionContent(section: PrivacyPolicySection, frameworks: string[]): string {
    let content = section.content;

    // Add framework-specific content
    if (frameworks.includes('gdpr') && section.id === 'user_rights') {
      content += '\n\nUnder GDPR, you have the right to access, rectify, erase, restrict processing, data portability, and object to processing of your personal data.';
    }

    if (frameworks.includes('ccpa') && section.id === 'user_rights') {
      content += '\n\nUnder CCPA, California residents have the right to know, delete, opt-out of sale, and non-discrimination regarding their personal information.';
    }

    return content;
  }
}

// Global instance
export const privacyComplianceManager = new PrivacyComplianceManager();

// Utility functions
export function generateComplianceReport(framework: string): ComplianceReport {
  return privacyComplianceManager.generateComplianceReport(framework);
}

export function generatePrivacyPolicy(frameworks?: string[]): string {
  return privacyComplianceManager.generatePrivacyPolicy(frameworks);
}

export function checkAllCompliance(): { [framework: string]: ComplianceReport } {
  return privacyComplianceManager.checkCompliance();
}

export function updateComplianceStatus(
  framework: string,
  requirement: string,
  status: ComplianceRequirement['status'],
  evidence?: string[]
): void {
  privacyComplianceManager.updateRequirementStatus(framework, requirement, status, evidence);
}

export default privacyComplianceManager;
