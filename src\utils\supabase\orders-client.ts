import { createClient } from './client';
import { v4 as uuidv4 } from 'uuid';
import { CartItem } from '@/context/cart-context';
import { Order, OrderItem, ShippingDetails } from './orders';

// Create a new order (client-side version)
export async function createOrderClient(userId: string, items: CartItem[], shippingDetails: ShippingDetails, totalAmount: number) {
  const supabase = createClient();
  
  // Convert cart items to order items
  const orderItems: OrderItem[] = items.map(item => ({
    product_id: item.id.toString(),
    name: item.name,
    price: item.price,
    quantity: item.quantity,
    image: item.image
  }));
  
  // Create the order object
  const order: Omit<Order, 'id'> = {
    user_id: userId,
    items: orderItems,
    shipping_details: shippingDetails,
    status: 'pending',
    payment_status: 'pending',
    total_amount: totalAmount
  };
  
  // Insert the order into the database
  const { data, error } = await supabase
    .from('orders')
    .insert([order])
    .select()
    .single();
  
  if (error) {
    console.error('Error creating order:', error);
    throw error;
  }
  
  return data as Order;
}

// Get all orders for a user (client-side version)
export async function getUserOrdersClient(userId: string) {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('orders')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });
  
  if (error) {
    console.error('Error fetching user orders:', error);
    throw error;
  }
  
  return data as Order[];
}

// Get a single order by ID (client-side version)
export async function getOrderClient(orderId: string) {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('orders')
    .select('*')
    .eq('id', orderId)
    .single();
  
  if (error) {
    console.error(`Error fetching order ${orderId}:`, error);
    throw error;
  }
  
  return data as Order;
}

// Update an order's status (client-side version)
export async function updateOrderStatusClient(orderId: string, status: string, paymentStatus?: string) {
  const supabase = createClient();
  
  const updates: { status: string; payment_status?: string } = { status };
  
  if (paymentStatus) {
    updates.payment_status = paymentStatus;
  }
  
  const { data, error } = await supabase
    .from('orders')
    .update(updates)
    .eq('id', orderId)
    .select()
    .single();
  
  if (error) {
    console.error(`Error updating order ${orderId}:`, error);
    throw error;
  }
  
  return data as Order;
}
