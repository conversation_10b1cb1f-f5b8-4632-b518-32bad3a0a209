// Test script to verify admin API endpoints
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://riypdqccaejgqwjfiumr.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJpeXBkcWNjYWVqZ3F3amZpdW1yIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNDg4ODIsImV4cCI6MjA2MTkyNDg4Mn0.fNtcChA4D153tGB1jVxUS4fvUS0BrG64mMEVGP2sPDA';

async function testAdminAPI() {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  console.log('Testing admin API endpoints...');

  try {
    // First, try to sign in with an existing admin user
    console.log('Attempting to sign in with admin user...');
    
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'test123' // This might not work, but let's try
    });

    if (authError) {
      console.log('Auth failed (expected):', authError.message);
      console.log('Skipping API test - need valid credentials');
      return;
    }

    console.log('Auth successful! Testing API endpoints...');

    // Test the orders API endpoint
    const response = await fetch('http://localhost:3001/api/admin/orders?page=1&limit=5', {
      headers: {
        'Authorization': `Bearer ${authData.session.access_token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('API request failed:', response.status, response.statusText);
      const errorText = await response.text();
      console.error('Error response:', errorText);
      return;
    }

    const result = await response.json();
    console.log('API Response:', {
      success: result.success,
      totalOrders: result.data?.total,
      ordersReturned: result.data?.orders?.length,
      sampleOrder: result.data?.orders?.[0] ? {
        id: result.data.orders[0].id,
        customer_name: result.data.orders[0].customer_name,
        customer_email: result.data.orders[0].customer_email,
        status: result.data.orders[0].status,
        total_amount: result.data.orders[0].total_amount
      } : null
    });

    // Test stats endpoint
    const statsResponse = await fetch('http://localhost:3001/api/admin/orders', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authData.session.access_token}`,
        'Content-Type': 'application/json',
      },
    });

    if (statsResponse.ok) {
      const statsResult = await statsResponse.json();
      console.log('Stats API Response:', statsResult.data);
    } else {
      console.error('Stats API failed:', statsResponse.status);
    }

  } catch (error) {
    console.error('General error:', error.message);
  }
}

testAdminAPI();
