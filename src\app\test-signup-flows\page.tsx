"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>ircle, XCircle, AlertCircle, RefreshCw, Trash2, Play } from 'lucide-react';

interface TestResult {
  success: boolean;
  testType?: string;
  result?: {
    authCreated: boolean;
    profileCreated: boolean;
    expectedRole: string;
    actualRole: string;
    roleMatches: boolean;
    userData: {
      id: string;
      email: string;
      full_name: string;
      role: string;
      created_at: string;
    };
  };
  error?: string;
  step?: string;
}

export default function TestSignupFlowsPage() {
  const [testResults, setTestResults] = useState<Record<string, TestResult>>({});
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  const [cleanupLoading, setCleanupLoading] = useState(false);

  const testTypes = [
    { 
      id: 'regular', 
      name: 'Regular Signup', 
      description: 'Default signup flow → user role',
      expectedRole: 'user'
    },
    { 
      id: 'checkout', 
      name: 'Checkout Signup', 
      description: 'Signup during checkout → user role',
      expectedRole: 'user'
    },
    { 
      id: 'mentorship', 
      name: 'Mentorship Signup', 
      description: 'Signup for mentorship → student role',
      expectedRole: 'student'
    },
    { 
      id: 'admin', 
      name: 'Admin Signup', 
      description: 'Admin signup with explicit role → admin role',
      expectedRole: 'admin'
    }
  ];

  const runTest = async (testType: string) => {
    setLoading(prev => ({ ...prev, [testType]: true }));
    
    try {
      const response = await fetch('/api/test-signup-flows', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ testType })
      });
      
      const result = await response.json();
      setTestResults(prev => ({ ...prev, [testType]: result }));
    } catch (err: any) {
      setTestResults(prev => ({ 
        ...prev, 
        [testType]: { success: false, error: err.message } 
      }));
    } finally {
      setLoading(prev => ({ ...prev, [testType]: false }));
    }
  };

  const runAllTests = async () => {
    for (const test of testTypes) {
      await runTest(test.id);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  };

  const cleanup = async () => {
    setCleanupLoading(true);
    
    try {
      const response = await fetch('/api/test-signup-flows', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ cleanup: true })
      });
      
      const result = await response.json();
      console.log('Cleanup result:', result);
      
      // Clear test results after cleanup
      setTestResults({});
    } catch (err: any) {
      console.error('Cleanup error:', err);
    } finally {
      setCleanupLoading(false);
    }
  };

  const getStatusIcon = (result: TestResult) => {
    if (!result.success) {
      return <XCircle className="h-5 w-5 text-red-500" />;
    }
    if (result.result?.roleMatches) {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    }
    return <AlertCircle className="h-5 w-5 text-yellow-500" />;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Signup Flow Testing</h1>
          <p className="text-muted-foreground">
            Test end-to-end signup flows and verify automatic role assignment for different contexts.
          </p>
        </div>

        <div className="mb-6 flex gap-4">
          <Button 
            onClick={runAllTests} 
            disabled={Object.values(loading).some(Boolean)}
            className="flex items-center gap-2"
          >
            <Play className="h-4 w-4" />
            Run All Tests
          </Button>
          
          <Button 
            onClick={cleanup} 
            disabled={cleanupLoading}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Trash2 className={`h-4 w-4 ${cleanupLoading ? 'animate-spin' : ''}`} />
            Cleanup Test Users
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {testTypes.map((test) => {
            const result = testResults[test.id];
            const isLoading = loading[test.id];

            return (
              <Card key={test.id}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>{test.name}</span>
                    {result && getStatusIcon(result)}
                  </CardTitle>
                  <CardDescription>{test.description}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Badge variant="outline">Expected: {test.expectedRole}</Badge>
                    <Button
                      onClick={() => runTest(test.id)}
                      disabled={isLoading}
                      size="sm"
                      className="flex items-center gap-2"
                    >
                      <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                      {isLoading ? 'Testing...' : 'Test'}
                    </Button>
                  </div>

                  {result && (
                    <div className="space-y-2">
                      {result.success ? (
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Actual Role:</span>
                            <Badge variant={result.result?.roleMatches ? "default" : "destructive"}>
                              {result.result?.actualRole}
                            </Badge>
                          </div>
                          
                          <div className="text-xs text-muted-foreground space-y-1">
                            <div>✓ Auth User Created: {result.result?.authCreated ? 'Yes' : 'No'}</div>
                            <div>✓ Profile Created: {result.result?.profileCreated ? 'Yes' : 'No'}</div>
                            <div>✓ Role Matches: {result.result?.roleMatches ? 'Yes' : 'No'}</div>
                          </div>

                          {result.result?.userData && (
                            <div className="text-xs bg-muted p-2 rounded">
                              <div>Email: {result.result.userData.email}</div>
                              <div>Name: {result.result.userData.full_name}</div>
                              <div>ID: {result.result.userData.id.substring(0, 8)}...</div>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="text-red-600 text-sm">
                          <div>❌ Error: {result.error}</div>
                          {result.step && <div>Step: {result.step}</div>}
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>

        <div className="mt-8 p-4 bg-muted rounded-lg">
          <h3 className="font-medium mb-2">Test Information</h3>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Tests create actual users in the database with different signup contexts</li>
            <li>• Each test verifies that the trigger function assigns the correct role</li>
            <li>• Use "Cleanup Test Users" to remove test data after testing</li>
            <li>• Tests use email addresses like test-[type]@example.com</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
