"use client";

import { useEffect, useState } from 'react';

interface ParallaxScrollOptions {
  imageHeight?: number; // Height of the fixed image in vh
  contentOffset?: number; // Offset for content in vh
  enableParallax?: boolean; // Enable/disable parallax effect
}

export const useParallaxScroll = (options: ParallaxScrollOptions = {}) => {
  const {
    imageHeight = 60,
    contentOffset = 55,
    enableParallax = true
  } = options;

  const [scrollY, setScrollY] = useState(0);
  const [imageOpacity, setImageOpacity] = useState(1);
  const [contentTransform, setContentTransform] = useState(0);

  useEffect(() => {
    if (!enableParallax) return;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);

      // Calculate image opacity based on scroll
      const maxScroll = window.innerHeight * (imageHeight / 100);
      const opacity = Math.max(0, 1 - (currentScrollY / maxScroll) * 1.5);
      setImageOpacity(opacity);

      // Calculate content transform for parallax effect
      const transform = currentScrollY * 0.5;
      setContentTransform(transform);
    };

    // Throttle scroll events for better performance
    let ticking = false;
    const throttledHandleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledHandleScroll, { passive: true });
    
    // Initial call
    handleScroll();

    return () => {
      window.removeEventListener('scroll', throttledHandleScroll);
    };
  }, [imageHeight, enableParallax]);

  return {
    scrollY,
    imageOpacity,
    contentTransform,
    imageStyles: {
      opacity: imageOpacity,
      transform: `translateY(${contentTransform}px)`,
    },
    contentStyles: {
      transform: `translateY(-${contentTransform * 0.3}px)`,
    }
  };
};

// Hook for mobile-specific parallax behavior
export const useMobileParallax = () => {
  const [isScrolling, setIsScrolling] = useState(false);
  const [scrollProgress, setScrollProgress] = useState(0);

  useEffect(() => {
    let scrollTimeout: NodeJS.Timeout;

    const handleScroll = () => {
      setIsScrolling(true);
      
      // Clear existing timeout
      clearTimeout(scrollTimeout);
      
      // Set scrolling to false after scroll ends
      scrollTimeout = setTimeout(() => {
        setIsScrolling(false);
      }, 150);

      // Calculate scroll progress
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = Math.min(scrollTop / docHeight, 1);
      setScrollProgress(progress);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(scrollTimeout);
    };
  }, []);

  return {
    isScrolling,
    scrollProgress,
  };
};

// Hook for smooth scroll to element
export const useSmoothScroll = () => {
  const scrollToElement = (elementId: string, offset: number = 0) => {
    const element = document.getElementById(elementId);
    if (element) {
      const elementPosition = element.offsetTop - offset;
      window.scrollTo({
        top: elementPosition,
        behavior: 'smooth'
      });
    }
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return {
    scrollToElement,
    scrollToTop,
  };
};

// Hook for detecting scroll direction
export const useScrollDirection = () => {
  const [scrollDirection, setScrollDirection] = useState<'up' | 'down' | null>(null);
  const [lastScrollY, setLastScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      if (currentScrollY > lastScrollY) {
        setScrollDirection('down');
      } else if (currentScrollY < lastScrollY) {
        setScrollDirection('up');
      }
      
      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [lastScrollY]);

  return scrollDirection;
};

// Hook for intersection observer (useful for animations on scroll)
export const useIntersectionObserver = (
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasIntersected, setHasIntersected] = useState(false);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        if (entry.isIntersecting && !hasIntersected) {
          setHasIntersected(true);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px',
        ...options,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [elementRef, hasIntersected, options]);

  return {
    isIntersecting,
    hasIntersected,
  };
};
