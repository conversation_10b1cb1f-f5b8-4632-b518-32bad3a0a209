import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { createServiceClient } from '@/utils/supabase/service';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions - only main admins can access reports
    const { data: adminData } = await supabase
      .from('users')
      .select('role, admin_role')
      .eq('id', session.user.id)
      .single();

    if (!adminData || adminData.role !== 'admin' || adminData.admin_role !== 'admin') {
      return NextResponse.json({ error: 'Main admin access required' }, { status: 403 });
    }

    // Use service role client for database operations
    const serviceSupabase = createServiceClient();

    // Fetch reports
    const { data: reports, error } = await serviceSupabase
      .from('admin_reports')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: 'Database error' }, { status: 500 });
    }

    return NextResponse.json({
      reports: reports || []
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions - only main admins can create reports
    const { data: adminData } = await supabase
      .from('users')
      .select('role, admin_role')
      .eq('id', session.user.id)
      .single();

    if (!adminData || adminData.role !== 'admin' || adminData.admin_role !== 'admin') {
      return NextResponse.json({ error: 'Main admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { 
      report_type, 
      report_name, 
      filters,
      file_format = 'pdf'
    } = body;

    if (!report_type || !report_name) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Use service role client for database operations
    const serviceSupabase = createServiceClient();

    // Create report record
    const { data: report, error: reportError } = await serviceSupabase
      .from('admin_reports')
      .insert([
        {
          generated_by: session.user.id,
          report_type,
          report_name,
          filters,
          file_format,
          expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
        }
      ])
      .select()
      .single();

    if (reportError) {
      console.error('Report creation error:', reportError);
      return NextResponse.json({ error: 'Failed to create report' }, { status: 500 });
    }

    // Log the activity
    await serviceSupabase.rpc('log_admin_activity', {
      p_action_type: 'system_config',
      p_action_description: `Generated ${report_type} report: ${report_name}`,
      p_target_id: report.id,
      p_target_type: 'system',
      p_metadata: {
        report_type,
        file_format,
        filters
      }
    });

    return NextResponse.json({ 
      message: 'Report created successfully',
      report 
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions
    const { data: adminData } = await supabase
      .from('users')
      .select('role, admin_role')
      .eq('id', session.user.id)
      .single();

    if (!adminData || adminData.role !== 'admin' || adminData.admin_role !== 'admin') {
      return NextResponse.json({ error: 'Main admin access required' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const reportId = searchParams.get('id');

    if (!reportId) {
      return NextResponse.json({ error: 'Report ID required' }, { status: 400 });
    }

    // Use service role client for database operations
    const serviceSupabase = createServiceClient();

    // Get report details before deletion for logging
    const { data: report } = await serviceSupabase
      .from('admin_reports')
      .select('report_name')
      .eq('id', reportId)
      .single();

    // Delete report
    const { error: deleteError } = await serviceSupabase
      .from('admin_reports')
      .delete()
      .eq('id', reportId);

    if (deleteError) {
      console.error('Delete error:', deleteError);
      return NextResponse.json({ error: 'Failed to delete report' }, { status: 500 });
    }

    // Log the activity
    if (report) {
      await serviceSupabase.rpc('log_admin_activity', {
        p_action_type: 'system_config',
        p_action_description: `Deleted report: ${report.report_name}`,
        p_target_id: reportId,
        p_target_type: 'system'
      });
    }

    return NextResponse.json({ 
      message: 'Report deleted successfully'
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
