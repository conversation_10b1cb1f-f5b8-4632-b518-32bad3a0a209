-- =====================================================
-- DIAGNOSE USER AUTHENTICATION ISSUES
-- Run this in Supabase SQL Editor to see what's wrong
-- =====================================================

-- 1. CHECK IF PROFILES TABLE EXISTS
SELECT 
    'PROFILES TABLE CHECK' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles' AND table_schema = 'public')
        THEN '✅ PROFILES TABLE EXISTS'
        ELSE '❌ PROFILES TABLE MISSING'
    END as status;

-- 2. CHECK PROFILES TABLE STRUCTURE (if it exists)
SELECT 
    'PROFILES COLUMNS' as check_type,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'profiles' AND table_schema = 'public'
ORDER BY ordinal_position;

-- 3. CHECK AUTH.USERS TABLE
SELECT 
    'AUTH USERS COUNT' as check_type,
    COUNT(*) as user_count,
    'Users in auth.users table' as description
FROM auth.users;

-- 4. CHECK PROFILES COUNT (if table exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles' AND table_schema = 'public') THEN
        RAISE NOTICE 'PROFILES COUNT: %', (SELECT COUNT(*) FROM public.profiles);
    ELSE
        RAISE NOTICE 'PROFILES TABLE DOES NOT EXIST';
    END IF;
END $$;

-- 5. CHECK FOR ORPHANED AUTH USERS (users without profiles)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles' AND table_schema = 'public') THEN
        RAISE NOTICE 'ORPHANED AUTH USERS: %', (
            SELECT COUNT(*) 
            FROM auth.users 
            WHERE id NOT IN (SELECT id FROM public.profiles WHERE id IS NOT NULL)
        );
    END IF;
END $$;

-- 6. CHECK FOR ORPHANED PROFILES (profiles without auth users)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles' AND table_schema = 'public') THEN
        RAISE NOTICE 'ORPHANED PROFILES: %', (
            SELECT COUNT(*) 
            FROM public.profiles 
            WHERE id NOT IN (SELECT id FROM auth.users WHERE id IS NOT NULL)
        );
    END IF;
END $$;

-- 7. CHECK HANDLE_NEW_USER FUNCTION
SELECT 
    'FUNCTION CHECK' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'handle_new_user' AND routine_schema = 'public')
        THEN '✅ HANDLE_NEW_USER FUNCTION EXISTS'
        ELSE '❌ HANDLE_NEW_USER FUNCTION MISSING'
    END as status;

-- 8. CHECK TRIGGER ON AUTH.USERS
SELECT 
    'TRIGGER CHECK' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.triggers 
            WHERE trigger_name = 'on_auth_user_created' 
            AND event_object_table = 'users'
            AND event_object_schema = 'auth'
        )
        THEN '✅ USER CREATION TRIGGER EXISTS'
        ELSE '❌ USER CREATION TRIGGER MISSING'
    END as status;

-- 9. LIST ALL USERS WITH THEIR EMAILS (for debugging)
SELECT 
    'USER LIST' as check_type,
    id,
    email,
    created_at,
    email_confirmed_at,
    last_sign_in_at
FROM auth.users
ORDER BY created_at DESC
LIMIT 10;

-- 10. CHECK RLS POLICIES ON PROFILES (if table exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles' AND table_schema = 'public') THEN
        RAISE NOTICE 'RLS ENABLED ON PROFILES: %', (
            SELECT rowsecurity 
            FROM pg_tables 
            WHERE tablename = 'profiles' AND schemaname = 'public'
        );
    END IF;
END $$;

-- 11. SUMMARY AND RECOMMENDATIONS
DO $$
DECLARE
    profiles_exists BOOLEAN;
    function_exists BOOLEAN;
    trigger_exists BOOLEAN;
    auth_count INTEGER;
    profiles_count INTEGER := 0;
BEGIN
    -- Check if profiles table exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'profiles' AND table_schema = 'public'
    ) INTO profiles_exists;
    
    -- Check if function exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.routines 
        WHERE routine_name = 'handle_new_user' AND routine_schema = 'public'
    ) INTO function_exists;
    
    -- Check if trigger exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name = 'on_auth_user_created' 
        AND event_object_table = 'users'
        AND event_object_schema = 'auth'
    ) INTO trigger_exists;
    
    -- Get auth users count
    SELECT COUNT(*) INTO auth_count FROM auth.users;
    
    -- Get profiles count if table exists
    IF profiles_exists THEN
        EXECUTE 'SELECT COUNT(*) FROM public.profiles' INTO profiles_count;
    END IF;
    
    RAISE NOTICE '==========================================';
    RAISE NOTICE '🔍 DIAGNOSIS SUMMARY';
    RAISE NOTICE '==========================================';
    RAISE NOTICE 'Profiles table exists: %', profiles_exists;
    RAISE NOTICE 'Handle_new_user function exists: %', function_exists;
    RAISE NOTICE 'User creation trigger exists: %', trigger_exists;
    RAISE NOTICE 'Auth users count: %', auth_count;
    RAISE NOTICE 'Profiles count: %', profiles_count;
    RAISE NOTICE '==========================================';
    
    -- Provide recommendations
    IF NOT profiles_exists THEN
        RAISE NOTICE '❌ PROBLEM: Profiles table is missing!';
        RAISE NOTICE '🔧 SOLUTION: Run force-sync-all-tables.sql or fix-profiles-table.sql';
    ELSIF auth_count > profiles_count THEN
        RAISE NOTICE '⚠️  PROBLEM: % auth users have no profiles!', (auth_count - profiles_count);
        RAISE NOTICE '🔧 SOLUTION: Run fix-profiles-table.sql to create missing profiles';
    ELSIF NOT function_exists OR NOT trigger_exists THEN
        RAISE NOTICE '⚠️  PROBLEM: User creation function or trigger is missing!';
        RAISE NOTICE '🔧 SOLUTION: Run update-policies-functions.sql';
    ELSE
        RAISE NOTICE '✅ Everything looks good! The "user already exists" error might be:';
        RAISE NOTICE '   - Email confirmation issues';
        RAISE NOTICE '   - Cached auth state';
        RAISE NOTICE '   - Try clearing browser storage and cookies';
    END IF;
    
    RAISE NOTICE '==========================================';
END $$;
