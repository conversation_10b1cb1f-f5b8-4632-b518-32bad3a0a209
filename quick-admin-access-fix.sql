-- Quick Admin Access Fix (CORRECTED VERSION)
-- Run this in your Supabase SQL Editor to fix admin access issues

-- Step 1: Check what users exist in auth.users with admin role
SELECT 
    'Auth users with admin role:' as info,
    id,
    email,
    raw_user_meta_data->>'role' as role,
    raw_user_meta_data->>'full_name' as full_name,
    created_at
FROM auth.users 
WHERE raw_user_meta_data->>'role' = 'admin'
ORDER BY created_at DESC;

-- Step 2: Check what users exist in public.users
SELECT 
    'Public users:' as info,
    id,
    email,
    role,
    admin_role,
    created_at
FROM public.users
ORDER BY created_at DESC
LIMIT 10;

-- Step 3: Ensure user_role enum exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('admin', 'user', 'student');
        RAISE NOTICE 'Created user_role enum';
    END IF;
END $$;

-- Step 4: Fix missing users - Insert admin users from auth.users into public.users
INSERT INTO public.users (
    id,
    email,
    full_name,
    name,
    role,
    admin_role,
    token_identifier,
    created_at,
    updated_at
)
SELECT 
    au.id,
    au.email,
    COALESCE(au.raw_user_meta_data->>'full_name', 'Admin User'),
    COALESCE(au.raw_user_meta_data->>'full_name', 'Admin User'),
    'admin'::user_role,
    'admin',
    au.id::text,
    au.created_at,
    NOW()
FROM auth.users au
LEFT JOIN public.users pu ON au.id = pu.id
WHERE au.raw_user_meta_data->>'role' = 'admin'
AND pu.id IS NULL
ON CONFLICT (id) DO UPDATE SET
    role = 'admin'::user_role,
    admin_role = 'admin',
    updated_at = NOW();

-- Step 5: Update existing users to have admin role if they should
UPDATE public.users 
SET 
    role = 'admin'::user_role,
    admin_role = 'admin',
    updated_at = NOW()
WHERE id IN (
    SELECT au.id 
    FROM auth.users au 
    WHERE au.raw_user_meta_data->>'role' = 'admin'
)
AND role != 'admin';

-- Step 6: Add admin_role column if it doesn't exist
DO $$
BEGIN
    -- Add admin_role column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'admin_role'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.users ADD COLUMN admin_role TEXT;
        RAISE NOTICE 'Added admin_role column';
    END IF;
END $$;

-- Step 7: Set admin_role for admin users
UPDATE public.users 
SET admin_role = 'admin'
WHERE role = 'admin' 
AND (admin_role IS NULL OR admin_role != 'admin');

-- Step 8: Verify the fix
SELECT 
    'Final admin users:' as info,
    id,
    email,
    full_name,
    role,
    admin_role,
    created_at
FROM public.users
WHERE role = 'admin'
ORDER BY created_at DESC;

-- Step 9: Check RLS policies that might be blocking access
SELECT 
    'Current RLS policies on users table:' as info,
    policyname,
    cmd,
    permissive,
    roles,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'users' 
AND schemaname = 'public';

-- Step 10: Instructions for next steps
SELECT 
    'NEXT STEPS:' as info,
    '1. If admin users were created above, try signing in again' as step1,
    '2. Clear browser cache and cookies completely' as step2,
    '3. If still getting errors, run the full fix-admin-auth-critical.sql' as step3,
    '4. Check browser console for any remaining errors' as step4;
