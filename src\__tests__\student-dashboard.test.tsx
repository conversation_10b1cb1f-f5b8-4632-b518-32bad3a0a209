import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { 
  getStudentDashboardData, 
  getStudentProgress, 
  getStudentUpcomingSessions,
  getStudentEnrollments,
  getStudentResources,
  requestSession 
} from '@/utils/supabase/mentorship-utils';

// Mock the utility functions
vi.mock('@/utils/supabase/mentorship-utils', () => ({
  getStudentDashboardData: vi.fn(),
  getStudentProgress: vi.fn(),
  getStudentUpcomingSessions: vi.fn(),
  getStudentEnrollments: vi.fn(),
  getStudentResources: vi.fn(),
  requestSession: vi.fn(),
}));

// Mock Supabase client
vi.mock('@supabase/ssr', () => ({
  createBrowserClient: vi.fn(() => ({
    auth: {
      getUser: vi.fn(() => Promise.resolve({
        data: { user: { id: 'test-user-id', email: '<EMAIL>' } },
        error: null
      }))
    }
  }))
}));

// Mock Next.js router
vi.mock('next/navigation', () => ({
  redirect: vi.fn(),
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }))
}));

// Mock toast hook
vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(() => ({
    toast: vi.fn()
  }))
}));

describe('Student Dashboard Utility Functions', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getStudentDashboardData', () => {
    it('should return dashboard data successfully', async () => {
      const mockData = {
        enrollments: [
          {
            id: '1',
            program: { name: 'Tennis Mastery' },
            mentor: { user: { full_name: 'Coach John' } },
            status: 'active'
          }
        ],
        upcomingSessions: [
          {
            id: '1',
            scheduled_at: '2025-01-25T10:00:00Z',
            notes: 'Training session',
            enrollment: {
              mentor: { user: { full_name: 'Coach John' } }
            }
          }
        ],
        progress: {
          completionPercentage: 75,
          completedSessions: 5,
          totalSessions: 10,
          activeEnrollments: 1
        },
        nextSession: null
      };

      (getStudentDashboardData as any).mockResolvedValue({
        data: mockData,
        error: null
      });

      const result = await getStudentDashboardData('test-user-id');
      
      expect(result.data).toEqual(mockData);
      expect(result.error).toBeNull();
      expect(getStudentDashboardData).toHaveBeenCalledWith('test-user-id');
    });

    it('should handle errors gracefully', async () => {
      const mockError = new Error('Database connection failed');
      
      (getStudentDashboardData as any).mockResolvedValue({
        data: null,
        error: mockError
      });

      const result = await getStudentDashboardData('test-user-id');
      
      expect(result.data).toBeNull();
      expect(result.error).toEqual(mockError);
    });
  });

  describe('getStudentProgress', () => {
    it('should calculate progress correctly', async () => {
      const mockProgress = {
        totalEnrollments: 2,
        activeEnrollments: 1,
        totalSessions: 10,
        completedSessions: 7,
        upcomingSessions: 2,
        totalProgramSessions: 24,
        completionPercentage: 29,
        enrollments: [],
        recentSessions: []
      };

      (getStudentProgress as any).mockResolvedValue({
        data: mockProgress,
        error: null
      });

      const result = await getStudentProgress('test-user-id');
      
      expect(result.data).toEqual(mockProgress);
      expect(result.data?.completionPercentage).toBe(29);
      expect(result.data?.completedSessions).toBe(7);
    });
  });

  describe('getStudentUpcomingSessions', () => {
    it('should return upcoming sessions only', async () => {
      const mockSessions = [
        {
          id: '1',
          scheduled_at: '2025-01-25T10:00:00Z',
          status: 'scheduled',
          notes: 'Training session',
          enrollment: {
            mentor: { user: { full_name: 'Coach John' } },
            program: { name: 'Tennis Mastery' }
          }
        }
      ];

      (getStudentUpcomingSessions as any).mockResolvedValue({
        data: mockSessions,
        error: null
      });

      const result = await getStudentUpcomingSessions('test-user-id');
      
      expect(result.data).toEqual(mockSessions);
      expect(result.data?.[0].status).toBe('scheduled');
    });
  });

  describe('getStudentEnrollments', () => {
    it('should return student enrollments', async () => {
      const mockEnrollments = [
        {
          id: '1',
          student_id: 'test-user-id',
          program: { name: 'Tennis Mastery', duration_months: 6 },
          mentor: { user: { full_name: 'Coach John' } },
          status: 'active',
          start_date: '2025-01-01',
          end_date: '2025-07-01'
        }
      ];

      (getStudentEnrollments as any).mockResolvedValue({
        data: mockEnrollments,
        error: null
      });

      const result = await getStudentEnrollments('test-user-id');
      
      expect(result.data).toEqual(mockEnrollments);
      expect(result.data?.[0].status).toBe('active');
    });
  });

  describe('getStudentResources', () => {
    it('should return available resources', async () => {
      const mockResources = [
        {
          id: '1',
          title: 'Tennis Fundamentals',
          description: 'Basic tennis techniques',
          type: 'video',
          category: 'Fundamentals',
          download_count: 45
        },
        {
          id: '2',
          title: 'Serve Guide',
          description: 'Improve your serve',
          type: 'document',
          category: 'Technique',
          download_count: 32
        }
      ];

      (getStudentResources as any).mockResolvedValue({
        data: mockResources,
        error: null
      });

      const result = await getStudentResources('test-user-id');
      
      expect(result.data).toEqual(mockResources);
      expect(result.data?.length).toBe(2);
    });
  });

  describe('requestSession', () => {
    it('should create session request successfully', async () => {
      const sessionRequest = {
        student_id: 'test-user-id',
        enrollment_id: 'enrollment-1',
        preferred_date: '2025-01-25',
        preferred_time: '10:00',
        session_type: 'training',
        notes: 'Focus on serve technique'
      };

      const mockSession = {
        id: 'session-1',
        ...sessionRequest,
        scheduled_at: '2025-01-25T10:00:00Z',
        status: 'scheduled'
      };

      (requestSession as any).mockResolvedValue({
        data: mockSession,
        error: null
      });

      const result = await requestSession(sessionRequest);
      
      expect(result.data).toEqual(mockSession);
      expect(result.error).toBeNull();
      expect(requestSession).toHaveBeenCalledWith(sessionRequest);
    });

    it('should handle session request errors', async () => {
      const sessionRequest = {
        student_id: 'test-user-id',
        enrollment_id: 'enrollment-1',
        preferred_date: '2025-01-25',
        preferred_time: '10:00',
        session_type: 'training'
      };

      const mockError = new Error('Failed to create session');

      (requestSession as any).mockResolvedValue({
        data: null,
        error: mockError
      });

      const result = await requestSession(sessionRequest);
      
      expect(result.data).toBeNull();
      expect(result.error).toEqual(mockError);
    });
  });
});

describe('Student Dashboard Integration', () => {
  it('should handle empty states gracefully', async () => {
    // Test empty enrollments
    (getStudentEnrollments as any).mockResolvedValue({
      data: [],
      error: null
    });

    const enrollmentsResult = await getStudentEnrollments('test-user-id');
    expect(enrollmentsResult.data).toEqual([]);

    // Test empty sessions
    (getStudentUpcomingSessions as any).mockResolvedValue({
      data: [],
      error: null
    });

    const sessionsResult = await getStudentUpcomingSessions('test-user-id');
    expect(sessionsResult.data).toEqual([]);

    // Test empty resources
    (getStudentResources as any).mockResolvedValue({
      data: [],
      error: null
    });

    const resourcesResult = await getStudentResources('test-user-id');
    expect(resourcesResult.data).toEqual([]);
  });

  it('should handle network errors', async () => {
    const networkError = new Error('Network request failed');

    (getStudentDashboardData as any).mockResolvedValue({
      data: null,
      error: networkError
    });

    const result = await getStudentDashboardData('test-user-id');
    
    expect(result.data).toBeNull();
    expect(result.error).toEqual(networkError);
  });
});
