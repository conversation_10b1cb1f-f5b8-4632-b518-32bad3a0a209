-- Fix Admin Access Issue
-- This script diagnoses and fixes admin authentication problems

-- Step 1: Check current users table structure
DO $$
BEGIN
    RAISE NOTICE 'Checking users table structure...';
END $$;

SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'users' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Step 2: Check if there are any users in auth.users
DO $$
BEGIN
    RAISE NOTICE 'Checking auth.users table...';
END $$;

SELECT 
    id,
    email,
    created_at,
    user_metadata
FROM auth.users
ORDER BY created_at DESC
LIMIT 5;

-- Step 3: Check current users in public.users table
DO $$
BEGIN
    RAISE NOTICE 'Checking public.users table...';
END $$;

SELECT 
    id,
    email,
    full_name,
    role,
    admin_role,
    created_at
FROM public.users
ORDER BY created_at DESC
LIMIT 10;

-- Step 4: Fix missing admin_role column if needed
DO $$
BEGIN
    -- Check if admin_role column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'admin_role'
        AND table_schema = 'public'
    ) THEN
        -- Create admin_role enum if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'admin_role') THEN
            CREATE TYPE admin_role AS ENUM ('admin', 'senior_admin', 'junior_admin');
            RAISE NOTICE 'Created admin_role enum';
        END IF;
        
        -- Add admin_role column
        ALTER TABLE public.users ADD COLUMN admin_role admin_role;
        RAISE NOTICE 'Added admin_role column to users table';
    ELSE
        RAISE NOTICE 'admin_role column already exists';
    END IF;
END $$;

-- Step 5: Fix users who should be admins but aren't in the users table
DO $$
DECLARE
    auth_user RECORD;
    user_exists BOOLEAN;
BEGIN
    RAISE NOTICE 'Checking for admin users in auth.users who are missing from public.users...';
    
    -- Loop through auth.users with admin role in metadata
    FOR auth_user IN 
        SELECT id, email, user_metadata, created_at
        FROM auth.users 
        WHERE user_metadata->>'role' = 'admin'
    LOOP
        -- Check if user exists in public.users
        SELECT EXISTS(
            SELECT 1 FROM public.users WHERE id = auth_user.id
        ) INTO user_exists;
        
        IF NOT user_exists THEN
            -- Insert missing admin user
            INSERT INTO public.users (
                id,
                email,
                full_name,
                name,
                role,
                admin_role,
                token_identifier,
                created_at,
                updated_at
            ) VALUES (
                auth_user.id,
                auth_user.email,
                COALESCE(auth_user.user_metadata->>'full_name', 'Admin User'),
                COALESCE(auth_user.user_metadata->>'full_name', 'Admin User'),
                'admin',
                'admin',
                auth_user.id::text,
                auth_user.created_at,
                NOW()
            );
            
            RAISE NOTICE 'Created missing admin user: %', auth_user.email;
        ELSE
            -- Update existing user to ensure they have admin role
            UPDATE public.users 
            SET 
                role = 'admin',
                admin_role = COALESCE(admin_role, 'admin'),
                updated_at = NOW()
            WHERE id = auth_user.id 
            AND role != 'admin';
            
            IF FOUND THEN
                RAISE NOTICE 'Updated user to admin: %', auth_user.email;
            END IF;
        END IF;
    END LOOP;
END $$;

-- Step 6: Ensure all admin users have proper admin_role
UPDATE public.users 
SET admin_role = 'admin'
WHERE role = 'admin' 
AND admin_role IS NULL;

-- Step 7: Check final state
DO $$
BEGIN
    RAISE NOTICE 'Final check - Admin users in public.users:';
END $$;

SELECT 
    id,
    email,
    full_name,
    role,
    admin_role,
    created_at
FROM public.users
WHERE role = 'admin'
ORDER BY created_at DESC;

-- Step 8: Verify RLS policies are not blocking access
DO $$
BEGIN
    RAISE NOTICE 'Checking RLS policies on users table...';
END $$;

SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'users' 
AND schemaname = 'public';

-- Step 9: Temporarily disable RLS for testing (ONLY FOR DEBUGGING)
-- UNCOMMENT ONLY IF NEEDED FOR TESTING
-- ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
-- RAISE NOTICE 'RLS DISABLED FOR TESTING - REMEMBER TO RE-ENABLE!';

-- Step 10: Show instructions
DO $$
BEGIN
    RAISE NOTICE '=== ADMIN ACCESS FIX COMPLETE ===';
    RAISE NOTICE 'If you still have issues:';
    RAISE NOTICE '1. Check that your admin user exists in both auth.users and public.users';
    RAISE NOTICE '2. Verify the user has role = admin and admin_role = admin';
    RAISE NOTICE '3. Check browser console for any JavaScript errors';
    RAISE NOTICE '4. Clear browser cache and cookies';
    RAISE NOTICE '5. Try signing out and signing in again';
END $$;
