import supabase from './supabase-client';
import { Tables, InsertTables, UpdateTables } from './database.types';
import { v4 as uuidv4 } from 'uuid';

/**
 * Chat utilities for Tennis-Gear application
 * These functions handle real-time messaging between mentors and students
 */

/**
 * Get all conversations for a user
 * @param userId User ID
 * @returns List of conversations or error
 */
export async function getUserConversations(userId: string) {
  // Get all conversations where the user is either the sender or receiver
  const { data, error } = await supabase
    .from('messages')
    .select(`
      conversation_id,
      sender:sender_id (id, full_name, avatar_url),
      receiver:receiver_id (id, full_name, avatar_url)
    `)
    .or(`sender_id.eq.${userId},receiver_id.eq.${userId}`)
    .order('created_at', { ascending: false });
  
  if (error) {
    return { data: null, error };
  }
  
  // Group by conversation_id and get the latest message for each conversation
  const conversations = data.reduce((acc: any, message: any) => {
    if (!acc[message.conversation_id]) {
      // Determine if the other user is the sender or receiver
      const otherUser = message.sender.id === userId ? message.receiver : message.sender;
      
      acc[message.conversation_id] = {
        id: message.conversation_id,
        otherUser
      };
    }
    return acc;
  }, {});
  
  return { data: Object.values(conversations), error: null };
}

/**
 * Get messages for a conversation
 * @param conversationId Conversation ID
 * @param limit Number of messages to fetch
 * @param offset Offset for pagination
 * @returns List of messages or error
 */
export async function getConversationMessages(
  conversationId: string,
  limit: number = 50,
  offset: number = 0
) {
  const { data, error } = await supabase
    .from('messages')
    .select(`
      *,
      sender:sender_id (id, full_name, avatar_url),
      receiver:receiver_id (id, full_name, avatar_url)
    `)
    .eq('conversation_id', conversationId)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);
  
  return { data, error };
}

/**
 * Get or create a conversation between two users
 * @param userId1 First user ID
 * @param userId2 Second user ID
 * @returns Conversation ID or error
 */
export async function getOrCreateConversation(userId1: string, userId2: string) {
  // Check if a conversation already exists
  const { data, error } = await supabase
    .from('messages')
    .select('conversation_id')
    .or(`and(sender_id.eq.${userId1},receiver_id.eq.${userId2}),and(sender_id.eq.${userId2},receiver_id.eq.${userId1})`)
    .limit(1);
  
  if (error) {
    return { conversationId: null, error };
  }
  
  // If a conversation exists, return its ID
  if (data && data.length > 0) {
    return { conversationId: data[0].conversation_id, error: null };
  }
  
  // Otherwise, generate a new conversation ID
  const conversationId = uuidv4();
  
  return { conversationId, error: null };
}

/**
 * Send a message
 * @param message Message data
 * @returns Created message or error
 */
export async function sendMessage(message: {
  conversation_id: string;
  sender_id: string;
  receiver_id: string;
  content: string;
  attachment_url?: string;
}) {
  const messageData: InsertTables<'messages'> = {
    ...message,
    id: uuidv4(),
    read: false,
    created_at: new Date().toISOString()
  };
  
  const { data, error } = await supabase
    .from('messages')
    .insert(messageData)
    .select()
    .single();
  
  return { data, error };
}

/**
 * Upload a message attachment
 * @param file File to upload
 * @param senderId Sender ID
 * @returns Public URL of the uploaded file or error
 */
export async function uploadMessageAttachment(file: File, senderId: string) {
  try {
    // Generate a unique file path
    const fileExt = file.name.split('.').pop();
    const fileName = `${uuidv4()}.${fileExt}`;
    const filePath = `${senderId}/${fileName}`;
    
    // Upload file to storage
    const { data, error } = await supabase
      .storage
      .from('message-attachments')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });
    
    if (error) {
      throw error;
    }
    
    // Get the public URL
    const { data: { publicUrl } } = supabase
      .storage
      .from('message-attachments')
      .getPublicUrl(filePath);
    
    return { url: publicUrl, error: null };
  } catch (error) {
    console.error('Error uploading attachment:', error);
    return { url: null, error };
  }
}

/**
 * Mark messages as read
 * @param conversationId Conversation ID
 * @param userId User ID of the reader
 * @returns Success or error
 */
export async function markMessagesAsRead(conversationId: string, userId: string) {
  const updateData: UpdateTables<'messages'> = { read: true };
  
  const { error } = await supabase
    .from('messages')
    .update(updateData)
    .eq('conversation_id', conversationId)
    .eq('receiver_id', userId)
    .eq('read', false);
  
  return { error };
}

/**
 * Get unread message count for a user
 * @param userId User ID
 * @returns Count of unread messages or error
 */
export async function getUnreadMessageCount(userId: string) {
  const { data, error, count } = await supabase
    .from('messages')
    .select('id', { count: 'exact' })
    .eq('receiver_id', userId)
    .eq('read', false);
  
  return { count, error };
}

/**
 * Delete a message
 * @param messageId Message ID
 * @param userId User ID (for verification)
 * @returns Success or error
 */
export async function deleteMessage(messageId: string, userId: string) {
  // First check if the user is the sender of the message
  const { data: message, error: getError } = await supabase
    .from('messages')
    .select('sender_id, attachment_url')
    .eq('id', messageId)
    .single();
  
  if (getError) {
    return { error: getError };
  }
  
  // Verify the user is the sender
  if (message.sender_id !== userId) {
    return { error: new Error('Unauthorized: You can only delete messages you sent') };
  }
  
  // If there's an attachment, delete it from storage
  if (message.attachment_url) {
    const url = new URL(message.attachment_url);
    const pathMatch = url.pathname.match(/\/message-attachments\/object\/public\/(.+)$/);
    
    if (pathMatch && pathMatch[1]) {
      const filePath = decodeURIComponent(pathMatch[1]);
      
      // Delete the file from storage
      const { error: storageError } = await supabase
        .storage
        .from('message-attachments')
        .remove([filePath]);
      
      if (storageError) {
        console.error('Error deleting attachment from storage:', storageError);
        // Continue to delete the message even if attachment deletion fails
      }
    }
  }
  
  // Delete the message
  const { error } = await supabase
    .from('messages')
    .delete()
    .eq('id', messageId);
  
  return { error };
}

/**
 * Subscribe to new messages in a conversation
 * @param conversationId Conversation ID
 * @param callback Function to call when a new message is received
 * @returns Subscription object
 */
export function subscribeToConversation(conversationId: string, callback: (payload: any) => void) {
  const subscription = supabase
    .channel(`conversation:${conversationId}`)
    .on('postgres_changes', {
      event: 'INSERT',
      schema: 'public',
      table: 'messages',
      filter: `conversation_id=eq.${conversationId}`
    }, callback)
    .subscribe();
  
  return subscription;
}

/**
 * Subscribe to all new messages for a user
 * @param userId User ID
 * @param callback Function to call when a new message is received
 * @returns Subscription object
 */
export function subscribeToUserMessages(userId: string, callback: (payload: any) => void) {
  const subscription = supabase
    .channel(`user-messages:${userId}`)
    .on('postgres_changes', {
      event: 'INSERT',
      schema: 'public',
      table: 'messages',
      filter: `receiver_id=eq.${userId}`
    }, callback)
    .subscribe();
  
  return subscription;
}
