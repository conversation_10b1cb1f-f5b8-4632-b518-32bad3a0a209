import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveImageProps {
  src: string;
  alt: string;
  sizes?: string;
  className?: string;
  width?: number;
  height?: number;
  priority?: boolean;
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * Responsive image component with lazy loading and placeholder
 */
export function ResponsiveImage({
  src,
  alt,
  sizes = '100vw',
  className,
  width,
  height,
  priority = false,
  loading = 'lazy',
  onLoad,
  onError,
}: ResponsiveImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState(false);
  
  // Generate srcSet for responsive images
  const generateSrcSet = () => {
    if (!src) return '';
    
    // Extract base URL and extension
    const lastDot = src.lastIndexOf('.');
    const basePath = lastDot !== -1 ? src.substring(0, lastDot) : src;
    const extension = lastDot !== -1 ? src.substring(lastDot) : '';
    
    // Generate srcSet with different sizes
    const widths = [640, 750, 828, 1080, 1200, 1920];
    return widths
      .map(w => `${basePath}-${w}w${extension} ${w}w`)
      .join(', ');
  };
  
  // Handle image load
  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };
  
  // Handle image error
  const handleError = () => {
    setError(true);
    onError?.();
  };
  
  // Determine loading attribute
  const loadingAttr = priority ? 'eager' : loading;
  
  // Use IntersectionObserver for better lazy loading
  useEffect(() => {
    if (typeof window === 'undefined' || priority || loading === 'eager') {
      return;
    }
    
    const imgElement = document.querySelector(`img[data-src="${src}"]`);
    if (!imgElement) return;
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          img.src = img.dataset.src || '';
          observer.unobserve(img);
        }
      });
    }, {
      rootMargin: '200px 0px',
      threshold: 0.01
    });
    
    observer.observe(imgElement);
    
    return () => {
      if (imgElement) observer.unobserve(imgElement);
    };
  }, [src, priority, loading]);
  
  return (
    <div className={cn(
      'relative overflow-hidden',
      isLoaded ? 'bg-transparent' : 'bg-gray-100 animate-pulse',
      className
    )}>
      {!error ? (
        <img
          src={priority || loading === 'eager' ? src : ''}
          data-src={src}
          srcSet={generateSrcSet()}
          sizes={sizes}
          alt={alt}
          width={width}
          height={height}
          loading={loadingAttr}
          onLoad={handleLoad}
          onError={handleError}
          className={cn(
            'transition-opacity duration-300',
            isLoaded ? 'opacity-100' : 'opacity-0',
            className
          )}
        />
      ) : (
        <div className="flex items-center justify-center w-full h-full bg-gray-100 text-gray-400">
          <span>Image not available</span>
        </div>
      )}
    </div>
  );
}