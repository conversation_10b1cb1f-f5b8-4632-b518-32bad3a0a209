'use client';

import { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Download, Search, Filter, Eye, Edit, Trash2, RefreshCw, AlertCircle, ChevronLeft, ChevronRight } from "lucide-react";
import { useOrders, useOrderStats, useOrderStatusColor, usePaymentStatusColor } from '@/hooks/useOrders';
import { OrderFilters, ORDER_STATUS_OPTIONS, PAYMENT_STATUS_OPTIONS } from '@/types/orders';
import Link from 'next/link';

export default function OrdersPage() {
  const [filters, setFilters] = useState<OrderFilters & { page: number; limit: number }>({
    page: 1,
    limit: 10,
  });
  const [showFilters, setShowFilters] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch orders and stats using React Query
  const {
    data: ordersData,
    isLoading: ordersLoading,
    error: ordersError,
    refetch: refetchOrders
  } = useOrders(filters);

  const {
    data: statsData,
    isLoading: statsLoading,
    error: statsError,
    refetch: refetchStats
  } = useOrderStats();

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<OrderFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: 1, // Reset to first page when filters change
    }));
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    handleFilterChange({ search: searchTerm || undefined });
  };

  // Handle refresh
  const handleRefresh = () => {
    refetchOrders();
    refetchStats();
  };

  // Handle export (placeholder for now)
  const handleExport = () => {
    // TODO: Implement export functionality
    console.log('Export orders with filters:', filters);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight gradient-text">Orders</h1>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={ordersLoading || statsLoading}
            className="glass-effect-subtle border border-white/20 rounded-2xl px-6 py-3 neo-shadow hover:neo-shadow-light transition-neo min-h-[44px] font-medium"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${(ordersLoading || statsLoading) ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            variant="outline"
            onClick={handleExport}
            className="glass-effect-subtle border border-white/20 rounded-2xl px-6 py-3 neo-shadow hover:neo-shadow-light transition-neo min-h-[44px] font-medium"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsLoading ? (
          // Loading skeleton
          Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <div className="h-4 bg-muted rounded animate-pulse" />
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-muted rounded animate-pulse mb-2" />
                <div className="h-3 bg-muted rounded animate-pulse w-2/3" />
              </CardContent>
            </Card>
          ))
        ) : statsError ? (
          <Card className="col-span-full">
            <CardContent className="flex items-center justify-center py-6">
              <div className="flex items-center gap-2 text-destructive">
                <AlertCircle className="h-4 w-4" />
                <span>Failed to load statistics</span>
              </div>
            </CardContent>
          </Card>
        ) : (
          <>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{statsData?.total_orders?.toLocaleString() || 0}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  {statsData?.monthly_growth ? `${statsData.monthly_growth > 0 ? '+' : ''}${statsData.monthly_growth}% from last month` : 'No growth data'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Pending</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{statsData?.pending_orders?.toLocaleString() || 0}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Awaiting confirmation
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Processing</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{statsData?.processing_orders?.toLocaleString() || 0}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  In progress
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Revenue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">R {statsData?.total_revenue?.toLocaleString('en-ZA', { minimumFractionDigits: 2 }) || '0.00'}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Total paid orders
                </p>
              </CardContent>
            </Card>
          </>
        )}
      </div>

      {/* Orders Table */}
      <Card className="glass-effect-dark border border-white/10 rounded-3xl neo-shadow">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold gradient-text">Order Management</CardTitle>
          <CardDescription className="text-muted-foreground">
            View and manage customer orders, track shipments, and process returns.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <form onSubmit={handleSearch} className="relative flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search orders by ID, customer name, or email..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </form>
            <Button
              variant="outline"
              className="sm:w-auto"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>

          {/* Filter Panel */}
          {showFilters && (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6 p-4 border rounded-lg bg-muted/50">
              <div>
                <label className="text-sm font-medium mb-2 block">Status</label>
                <Select
                  value={filters.status || ''}
                  onValueChange={(value) => handleFilterChange({ status: (value as any) || undefined })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All statuses</SelectItem>
                    {ORDER_STATUS_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Payment Status</label>
                <Select
                  value={filters.payment_status || ''}
                  onValueChange={(value) => handleFilterChange({ payment_status: (value as any) || undefined })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All payments" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All payments</SelectItem>
                    {PAYMENT_STATUS_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Date From</label>
                <Input
                  type="date"
                  value={filters.date_from || ''}
                  onChange={(e) => handleFilterChange({ date_from: e.target.value || undefined })}
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Date To</label>
                <Input
                  type="date"
                  value={filters.date_to || ''}
                  onChange={(e) => handleFilterChange({ date_to: e.target.value || undefined })}
                />
              </div>
            </div>
          )}

          {/* Orders Table */}
          {ordersLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-4 p-4">
                  <div className="h-4 bg-muted rounded animate-pulse flex-1" />
                  <div className="h-4 bg-muted rounded animate-pulse w-24" />
                  <div className="h-4 bg-muted rounded animate-pulse w-20" />
                  <div className="h-4 bg-muted rounded animate-pulse w-16" />
                </div>
              ))}
            </div>
          ) : ordersError ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Failed to load orders</h3>
                <p className="text-muted-foreground mb-4">
                  {ordersError.message || 'An error occurred while fetching orders'}
                </p>
                <Button onClick={handleRefresh} variant="outline">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </div>
            </div>
          ) : !ordersData?.orders?.length ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="h-12 w-12 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                  <Search className="h-6 w-6 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2">No orders found</h3>
                <p className="text-muted-foreground">
                  {filters.search || filters.status || filters.payment_status
                    ? 'Try adjusting your search or filters'
                    : 'No orders have been placed yet'}
                </p>
              </div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-border">
                    <th className="text-left font-medium py-3 px-4">Order ID</th>
                    <th className="text-left font-medium py-3 px-4">Customer</th>
                    <th className="text-left font-medium py-3 px-4">Date</th>
                    <th className="text-left font-medium py-3 px-4">Status</th>
                    <th className="text-left font-medium py-3 px-4">Payment</th>
                    <th className="text-right font-medium py-3 px-4">Items</th>
                    <th className="text-right font-medium py-3 px-4">Total</th>
                    <th className="text-right font-medium py-3 px-4">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {ordersData.orders.map((order) => (
                    <OrderRow key={order.id} order={order} />
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Pagination */}
          {ordersData && ordersData.orders.length > 0 && (
            <div className="flex items-center justify-between mt-6">
              <p className="text-sm text-muted-foreground">
                Showing <strong>{((filters.page - 1) * filters.limit) + 1}-{Math.min(filters.page * filters.limit, ordersData.total)}</strong> of <strong>{ordersData.total.toLocaleString()}</strong> orders
              </p>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={filters.page <= 1}
                  onClick={() => handlePageChange(filters.page - 1)}
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </Button>
                <span className="text-sm text-muted-foreground px-2">
                  Page {filters.page} of {ordersData.total_pages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={filters.page >= ordersData.total_pages}
                  onClick={() => handlePageChange(filters.page + 1)}
                >
                  Next
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// OrderRow component for individual order display
function OrderRow({ order }: { order: any }) {
  const statusColor = useOrderStatusColor(order.status);
  const paymentColor = usePaymentStatusColor(order.payment_status);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getItemCount = (items: any[]) => {
    return items.reduce((total, item) => total + (item.quantity || 1), 0);
  };

  return (
    <tr className="border-b border-border hover:bg-muted/50">
      <td className="py-3 px-4 font-medium">
        <Link
          href={`/admin/orders/${order.id}`}
          className="text-primary hover:underline"
        >
          {order.id}
        </Link>
      </td>
      <td className="py-3 px-4">
        <div>
          <p className="font-medium">{order.customer_name || 'Unknown'}</p>
          <p className="text-xs text-muted-foreground">{order.customer_email}</p>
        </div>
      </td>
      <td className="py-3 px-4">{formatDate(order.created_at)}</td>
      <td className="py-3 px-4">
        <Badge variant="secondary" className={statusColor}>
          {order.status}
        </Badge>
      </td>
      <td className="py-3 px-4">
        <Badge variant="secondary" className={paymentColor}>
          {order.payment_status}
        </Badge>
      </td>
      <td className="py-3 px-4 text-right">{getItemCount(order.items)}</td>
      <td className="py-3 px-4 text-right font-medium">
        R {Number(order.total_amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
      </td>
      <td className="py-3 px-4">
        <div className="flex items-center justify-end gap-2">
          <Link href={`/admin/orders/${order.id}`}>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
          <Link href={`/admin/orders/${order.id}/edit`}>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Edit className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      </td>
    </tr>
  );
}
