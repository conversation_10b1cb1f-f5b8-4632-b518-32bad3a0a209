// SEO Configuration for The Chronicle
// Following Google's E-A-T (Expertise, Authoritativeness, Trustworthiness) guidelines

export const seoConfig = {
  // Site Information
  siteName: 'The Chronicle',
  siteUrl: 'https://thechronicle.com',
  defaultTitle: 'The Chronicle - Premium News & Ecommerce Platform',
  titleTemplate: '%s | The Chronicle',
  defaultDescription: 'Discover the latest news, insights, and premium products at The Chronicle. Your trusted source for quality journalism and curated shopping experiences.',
  
  // Social Media
  social: {
    twitter: '@thechronicle',
    facebook: 'thechronicle',
    instagram: 'thechronicle',
    linkedin: 'company/thechronicle',
    youtube: 'thechronicle',
  },

  // Default Images
  defaultImage: '/og-image.jpg',
  logo: '/logo.png',
  favicon: '/favicon.ico',

  // Organization Schema
  organization: {
    '@type': 'Organization',
    name: 'The Chronicle',
    url: 'https://thechronicle.com',
    logo: 'https://thechronicle.com/logo.png',
    description: 'Premium news and ecommerce platform delivering quality journalism and curated products.',
    foundingDate: '2024',
    founders: [
      {
        '@type': 'Person',
        name: 'The Chronicle Editorial Team',
      }
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '******-0123',
      contactType: 'customer service',
      availableLanguage: 'English',
      areaServed: 'Worldwide',
    },
    sameAs: [
      'https://twitter.com/thechronicle',
      'https://facebook.com/thechronicle',
      'https://linkedin.com/company/thechronicle',
      'https://instagram.com/thechronicle',
    ],
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'ZA',
      addressLocality: 'Sandton',
      addressRegion: 'Gauteng',
      postalCode: '2057',
    },
  },

  // Website Schema
  website: {
    '@type': 'WebSite',
    name: 'The Chronicle',
    url: 'https://thechronicle.com',
    description: 'Premium news and ecommerce platform',
    publisher: {
      '@type': 'Organization',
      name: 'The Chronicle',
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: 'https://thechronicle.com/search?q={search_term_string}',
      },
      'query-input': 'required name=search_term_string',
    },
  },

  // Default Keywords
  defaultKeywords: [
    'news',
    'journalism',
    'ecommerce',
    'premium products',
    'technology',
    'business',
    'lifestyle',
    'shopping',
    'articles',
    'blog',
    'marketplace',
    'digital products',
    'subscription',
    'newsletter',
    'analysis',
    'insights',
    'expert opinion',
    'quality content',
    'trusted source',
    'breaking news',
  ],

  // Page-specific SEO configurations
  pages: {
    home: {
      title: 'Home',
      description: 'Welcome to The Chronicle - your premier destination for quality journalism and curated premium products. Discover the latest news, insights, and exclusive shopping experiences.',
      keywords: ['homepage', 'news platform', 'ecommerce', 'journalism', 'premium content'],
    },
    articles: {
      title: 'Articles',
      description: 'Explore our comprehensive collection of articles covering technology, business, lifestyle, and more. Quality journalism and expert insights from The Chronicle.',
      keywords: ['articles', 'news', 'journalism', 'blog', 'insights', 'analysis'],
    },
    products: {
      title: 'Products',
      description: 'Discover our curated selection of premium products. From digital courses to physical goods, find quality items that enhance your lifestyle.',
      keywords: ['products', 'ecommerce', 'shopping', 'premium', 'marketplace', 'digital', 'physical'],
    },
    about: {
      title: 'About Us',
      description: 'Learn about The Chronicle\'s mission to deliver quality journalism and curated products. Meet our team and discover our commitment to excellence.',
      keywords: ['about', 'company', 'mission', 'team', 'journalism', 'quality'],
    },
    contact: {
      title: 'Contact Us',
      description: 'Get in touch with The Chronicle team. We\'re here to help with your questions, feedback, and partnership opportunities.',
      keywords: ['contact', 'support', 'feedback', 'partnership', 'help'],
    },
  },

  // Technical SEO Settings
  technical: {
    // Core Web Vitals targets
    performance: {
      lcp: 2.5, // Largest Contentful Paint (seconds)
      fid: 100, // First Input Delay (milliseconds)
      cls: 0.1, // Cumulative Layout Shift
    },
    
    // Structured Data Types
    structuredDataTypes: [
      'Organization',
      'WebSite',
      'Article',
      'Product',
      'BreadcrumbList',
      'NewsArticle',
      'BlogPosting',
      'Review',
      'Rating',
    ],

    // Robots directives
    robots: {
      index: true,
      follow: true,
      'max-snippet': -1,
      'max-image-preview': 'large',
      'max-video-preview': -1,
    },

    // Canonical URL settings
    canonical: {
      trailingSlash: false,
      protocol: 'https',
    },
  },

  // Content Guidelines for SEO
  contentGuidelines: {
    title: {
      minLength: 30,
      maxLength: 60,
      includeKeyword: true,
      unique: true,
    },
    description: {
      minLength: 120,
      maxLength: 160,
      includeKeyword: true,
      actionOriented: true,
    },
    headings: {
      h1: {
        count: 1,
        includeKeyword: true,
      },
      h2: {
        minCount: 2,
        maxCount: 6,
      },
    },
    content: {
      minWordCount: 300,
      keywordDensity: {
        min: 0.5, // 0.5%
        max: 2.5, // 2.5%
      },
      readabilityScore: 60, // Flesch Reading Ease
    },
    images: {
      altText: true,
      titleAttribute: true,
      compression: true,
      lazyLoading: true,
      webpFormat: true,
    },
  },

  // Analytics and Tracking
  analytics: {
    googleAnalytics: 'G-XXXXXXXXXX', // Replace with actual GA4 ID
    googleTagManager: 'GTM-XXXXXXX', // Replace with actual GTM ID
    facebookPixel: 'XXXXXXXXXXXXXXX', // Replace with actual Pixel ID
    linkedInInsight: 'XXXXXXX', // Replace with actual Partner ID
  },

  // Local SEO (if applicable)
  localSEO: {
    businessType: 'Media Company',
    serviceArea: 'Worldwide',
    languages: ['en'],
    currency: 'USD',
  },

  // E-commerce SEO
  ecommerce: {
    productSchema: {
      brand: 'The Chronicle',
      availability: 'https://schema.org/InStock',
      condition: 'https://schema.org/NewCondition',
      priceValidUntil: '2025-12-31',
    },
    reviewSchema: {
      ratingScale: 5,
      worstRating: 1,
      bestRating: 5,
    },
  },
};

// SEO Utility Functions
export const seoUtils = {
  // Generate page title
  generateTitle: (pageTitle?: string): string => {
    if (!pageTitle) return seoConfig.defaultTitle;
    return `${pageTitle} | ${seoConfig.siteName}`;
  },

  // Generate canonical URL
  generateCanonicalUrl: (path: string): string => {
    const cleanPath = path.replace(/\/$/, '') || '/';
    return `${seoConfig.siteUrl}${cleanPath}`;
  },

  // Generate Open Graph image URL
  generateOGImageUrl: (title: string, description?: string): string => {
    // This would integrate with a service like Vercel OG or similar
    const params = new URLSearchParams({
      title,
      ...(description && { description }),
    });
    return `${seoConfig.siteUrl}/api/og?${params.toString()}`;
  },

  // Validate SEO requirements
  validateSEO: (data: {
    title?: string;
    description?: string;
    content?: string;
  }) => {
    const issues: string[] = [];
    const warnings: string[] = [];

    // Title validation
    if (!data.title) {
      issues.push('Missing title');
    } else {
      if (data.title.length < seoConfig.contentGuidelines.title.minLength) {
        warnings.push(`Title too short (${data.title.length} chars, min ${seoConfig.contentGuidelines.title.minLength})`);
      }
      if (data.title.length > seoConfig.contentGuidelines.title.maxLength) {
        issues.push(`Title too long (${data.title.length} chars, max ${seoConfig.contentGuidelines.title.maxLength})`);
      }
    }

    // Description validation
    if (!data.description) {
      issues.push('Missing meta description');
    } else {
      if (data.description.length < seoConfig.contentGuidelines.description.minLength) {
        warnings.push(`Description too short (${data.description.length} chars, min ${seoConfig.contentGuidelines.description.minLength})`);
      }
      if (data.description.length > seoConfig.contentGuidelines.description.maxLength) {
        issues.push(`Description too long (${data.description.length} chars, max ${seoConfig.contentGuidelines.description.maxLength})`);
      }
    }

    // Content validation
    if (data.content) {
      const wordCount = data.content.split(/\s+/).length;
      if (wordCount < seoConfig.contentGuidelines.content.minWordCount) {
        warnings.push(`Content too short (${wordCount} words, min ${seoConfig.contentGuidelines.content.minWordCount})`);
      }
    }

    return {
      isValid: issues.length === 0,
      issues,
      warnings,
    };
  },
};

export default seoConfig;
