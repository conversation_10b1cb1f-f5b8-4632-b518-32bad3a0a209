import { NextResponse } from 'next/server';
import yocoConfig, { formatAmount, createPaymentUrl } from '@/lib/yoco';
import { createClient } from '@/utils/supabase/server';

/**
 * API endpoint for handling consultation payment requests
 * Integrates with Yoco payment gateway for South African payments
 */
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { consultationId, userId, items, notes, customerDetails } = body;

    // Validate required fields
    if (!consultationId) {
      return NextResponse.json(
        { error: 'Missing consultation ID' },
        { status: 400 }
      );
    }
    
    // Ensure we have a userId (even if it's a guest ID)
    const finalUserId = userId || `guest-${Date.now()}`;
    
    // Validate customer details
    if (!customerDetails || !customerDetails.name || !customerDetails.email) {
      return NextResponse.json(
        { error: 'Missing customer details' },
        { status: 400 }
      );
    }

    // Calculate the total amount (using the first item's price)
    const totalAmount = items.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0);
    
    // Generate a unique payment reference
    const paymentReference = `CONS-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

    // Store payment information in database
    let consultationData: any = null;
    try {
      const supabase = await createClient();

      // Try to update consultation with payment reference
      const { data, error } = await supabase
        .from('consultations')
        .update({
          payment_reference: paymentReference,
          payment_status: 'pending',
          payment_amount: totalAmount,
          yoco_payment_id: paymentReference // Set yoco_payment_id to the same as payment_reference for consistency
        })
        .eq('id', consultationId)
        .select();

      if (error) {
        console.error('Error updating consultation with payment details:', error);
        
        // If the table doesn't exist, create a fallback consultation record
        if (error.message && error.message.includes('does not exist')) {
          console.log('Database error creating consultation:', error);
          console.log('Using fallback consultation creation mechanism');
          
          // Create a mock consultation object for tracking
          consultationData = {
            id: consultationId,
            first_name: customerDetails.name.split(' ')[0] || 'Customer',
            last_name: customerDetails.name.split(' ').slice(1).join(' ') || '',
            email: customerDetails.email,
            phone_number: customerDetails.phone || '',
            location: 'Location TBD',
            scheduled_date: new Date(Date.now() + 86400000).toISOString().split('T')[0], // Tomorrow
            scheduled_time: '10:30',
            reason: notes || 'How to get started',
            status: 'pending',
            created_at: new Date().toISOString(),
            user_id: null,
            payment_reference: paymentReference,
            payment_status: 'pending',
            payment_amount: totalAmount,
            updated_at: new Date().toISOString()
          };
          
          console.log('Created mock consultation:', consultationData);
        }
      } else if (data && data.length > 0) {
        consultationData = data[0];
      }
    } catch (err) {
      console.error('Error storing payment information:', err);
    }

    // Create payment data for Yoco
    const origin = request.headers.get('origin') || process.env.NEXT_PUBLIC_BASE_URL || '';
    
    // Create a description for the payment
    const description = `Tennis Consultation: ${customerDetails.name}`;
    
    // Create metadata for the payment
    const metadata = {
      consultation_id: consultationId,
      user_id: finalUserId,
      customer_name: customerDetails.name,
      customer_email: customerDetails.email,
      customer_phone: customerDetails.phone || '',
      notes: notes ? notes.substring(0, 100) : ''
    };

    try {
      // Create the payment URL
      const paymentUrl = await createPaymentUrl(
        totalAmount,
        'ZAR',
        description,
        metadata,
        `${origin}/consultation/success?reference=${paymentReference}`,
        `${origin}/consultation/cancel?reference=${paymentReference}`
      );

      // Return the payment URL and other relevant data
      return NextResponse.json({
        success: true,
        consultationId,
        paymentReference,
        paymentUrl,
        amount: totalAmount
      });
    } catch (paymentError: any) {
      console.error('Error creating payment URL:', paymentError);
      return NextResponse.json(
        { error: `Payment processing error: ${paymentError.message}` },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Error creating consultation payment:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
