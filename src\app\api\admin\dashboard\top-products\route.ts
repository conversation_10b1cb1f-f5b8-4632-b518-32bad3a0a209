import { NextRequest, NextResponse } from 'next/server';
import { createClient, createServiceRoleClient } from '@/utils/supabase/server';

/**
 * GET /api/admin/dashboard/top-products
 * Get top-selling products for dashboard overview
 */
export async function GET(request: NextRequest) {
  try {
    console.log('GET /api/admin/dashboard/top-products - Request received');

    // Check authentication
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin role
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || userData.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 });
    }

    console.log('GET /api/admin/dashboard/top-products - Admin access confirmed');

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '5');
    const period = searchParams.get('period') || 'month'; // month, week, all

    // Use service role client for database operations
    const serviceSupabase = createServiceRoleClient();

    // Calculate date range based on period
    let dateFilter = '';
    const now = new Date();
    
    if (period === 'week') {
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      dateFilter = weekAgo.toISOString();
    } else if (period === 'month') {
      const monthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
      dateFilter = monthAgo.toISOString();
    }

    // Get top products by analyzing order items
    // This query aggregates sales data from order items
    let query = `
      SELECT 
        p.id,
        p.name,
        p.category,
        p.price,
        p.image_url,
        COUNT(oi.product_id) as sales_count,
        SUM(oi.quantity) as total_quantity,
        SUM(oi.price * oi.quantity) as total_revenue,
        AVG(oi.price) as avg_price
      FROM products p
      LEFT JOIN order_items oi ON p.id = oi.product_id
      LEFT JOIN orders o ON oi.order_id = o.id
      WHERE o.status != 'cancelled'
    `;

    if (dateFilter) {
      query += ` AND o.created_at >= '${dateFilter}'`;
    }

    query += `
      GROUP BY p.id, p.name, p.category, p.price, p.image_url
      HAVING COUNT(oi.product_id) > 0
      ORDER BY total_revenue DESC, sales_count DESC
      LIMIT ${limit}
    `;

    const { data: topProducts, error } = await serviceSupabase.rpc('execute_sql', {
      sql_query: query
    });

    // If the RPC doesn't work, fall back to a simpler approach
    let products: any[] = [];
    if (error || !topProducts) {
      console.log('RPC failed, using fallback approach');
      
      // Fallback: Get products and calculate sales manually
      const { data: allProducts, error: productsError } = await serviceSupabase
        .from('products')
        .select('*')
        .eq('status', 'active')
        .limit(limit * 2); // Get more to filter later

      if (productsError) {
        throw new Error(`Failed to fetch products: ${productsError.message}`);
      }

      // For each product, calculate sales
      const productSales = await Promise.all(
        (allProducts || []).map(async (product) => {
          let orderItemsQuery = serviceSupabase
            .from('order_items')
            .select(`
              quantity,
              price,
              orders!inner(
                status,
                created_at
              )
            `)
            .eq('product_id', product.id)
            .neq('orders.status', 'cancelled');

          if (dateFilter) {
            orderItemsQuery = orderItemsQuery.gte('orders.created_at', dateFilter);
          }

          const { data: orderItems } = await orderItemsQuery;

          const salesCount = orderItems?.length || 0;
          const totalQuantity = orderItems?.reduce((sum, item) => sum + item.quantity, 0) || 0;
          const totalRevenue = orderItems?.reduce((sum, item) => sum + (item.price * item.quantity), 0) || 0;

          return {
            ...product,
            sales_count: salesCount,
            total_quantity: totalQuantity,
            total_revenue: totalRevenue,
            avg_price: product.price
          };
        })
      );

      // Sort by revenue and take top products
      products = productSales
        .filter(p => p.sales_count > 0)
        .sort((a, b) => b.total_revenue - a.total_revenue)
        .slice(0, limit);
    } else {
      products = topProducts;
    }

    // Calculate trends (compare with previous period)
    const trendsPromises = products.map(async (product) => {
      let previousPeriodStart = '';
      let previousPeriodEnd = dateFilter;

      if (period === 'week') {
        const twoWeeksAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);
        const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        previousPeriodStart = twoWeeksAgo.toISOString();
        previousPeriodEnd = oneWeekAgo.toISOString();
      } else if (period === 'month') {
        const twoMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 2, now.getDate());
        const oneMonthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
        previousPeriodStart = twoMonthsAgo.toISOString();
        previousPeriodEnd = oneMonthAgo.toISOString();
      }

      if (previousPeriodStart && previousPeriodEnd) {
        const { data: previousItems } = await serviceSupabase
          .from('order_items')
          .select(`
            quantity,
            price,
            orders!inner(
              status,
              created_at
            )
          `)
          .eq('product_id', product.id)
          .neq('orders.status', 'cancelled')
          .gte('orders.created_at', previousPeriodStart)
          .lt('orders.created_at', previousPeriodEnd);

        const previousRevenue = previousItems?.reduce((sum, item) => sum + (item.price * item.quantity), 0) || 0;
        const currentRevenue = product.total_revenue;
        
        let trend = 'stable';
        if (currentRevenue > previousRevenue) {
          trend = 'up';
        } else if (currentRevenue < previousRevenue) {
          trend = 'down';
        }

        return { ...product, trend, previous_revenue: previousRevenue };
      }

      return { ...product, trend: 'stable', previous_revenue: 0 };
    });

    const productsWithTrends = await Promise.all(trendsPromises);

    // Transform data for frontend
    const transformedProducts = productsWithTrends.map(product => ({
      id: product.id,
      name: product.name,
      category: product.category,
      sales_count: product.sales_count,
      total_quantity: product.total_quantity,
      total_revenue: product.total_revenue,
      avg_price: product.avg_price,
      trend: product.trend,
      formatted_revenue: `R ${product.total_revenue.toLocaleString('en-ZA', { minimumFractionDigits: 2 })}`,
      image_url: product.image_url
    }));

    console.log('GET /api/admin/dashboard/top-products - Success:', {
      period,
      productsCount: transformedProducts.length
    });

    return NextResponse.json({
      success: true,
      data: {
        products: transformedProducts,
        period,
        total_count: transformedProducts.length
      }
    });

  } catch (error: any) {
    console.error('GET /api/admin/dashboard/top-products - General error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
