import { NextResponse } from 'next/server';
import yocoConfig, { formatAmount } from '@/lib/yoco';
import { createClient } from '@/utils/supabase/server';

/**
 * This API route handles creating a checkout session for mentorship programs
 * It integrates with Yoco payment gateway for South African payments
 */
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { priceId, billingCycle, programName, programId, amount, programType, duration } = body;

    if (!programId || !amount) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Generate a unique subscription ID that includes program type and duration
    const subscriptionId = `TEC-MENTOR-${Date.now()}-${programType}-${duration}-${Math.random().toString(36).substring(2, 7)}`;

    // Get user information if authenticated
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    const userId = user?.id || 'guest';

    // Store subscription information in database
    if (userId !== 'guest') {
      try {
        const { error } = await supabase
          .from('mentorship_subscriptions')
          .insert({
            id: subscriptionId,
            user_id: userId,
            program_id: programId,
            program_name: programName,
            program_type: programType || '',
            duration: duration || 0,
            billing_cycle: billingCycle,
            amount: amount,
            status: 'pending',
            payment_status: 'pending',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (error) {
          console.error('Error creating subscription record:', error);
        }
      } catch (err) {
        console.error('Error storing subscription:', err);
      }
    }

    // Create payment data for Yoco
    const origin = request.headers.get('origin') || process.env.NEXT_PUBLIC_BASE_URL || '';
    const description = billingCycle === 'full' 
      ? `${programName} (Full Payment)` 
      : `${programName} (Monthly Payment)`;

    // Create a payment URL (this would be replaced with actual Yoco checkout URL in production)
    // For now, we'll use our simulated payment page
    // Set success URL based on program type - use main success page for 6-month and 12-month programs
    let successUrl = '';
    
    // Check if this is a 6-month or 12-month program
    if (programType && ['6-month', '12-month'].includes(programType)) {
      // Use the main success page for 6-month and 12-month mentorship programs
      successUrl = `/success?order_id=${subscriptionId}`;
    } else {
      // Use the consultation success page for consultation bookings
      successUrl = `/consultation/success?reference=${subscriptionId}`;
    }
    
    const paymentUrl = `/api/yoco-redirect?orderId=${subscriptionId}&amount=${formatAmount(amount)}&successUrl=${encodeURIComponent(successUrl)}`;

    return NextResponse.json({
      subscriptionId,
      paymentUrl,
      success: true
    });
  } catch (error: any) {
    console.error('Error creating checkout session:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
