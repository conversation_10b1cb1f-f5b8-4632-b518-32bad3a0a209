# Admin Authentication Troubleshooting Guide

## Issue Fixed: "Database error saving new user"

### Problem
The admin sign-up was failing with "Database error saving new user" because:

1. **Schema Mismatch**: The `users` table had a different structure than expected
2. **Required Fields**: `token_identifier` was NOT NULL but not being provided
3. **Role Enum**: The role field used a custom `user_role` enum instead of text
4. **Missing Fields**: Some expected fields were missing or had different names

### Solution Applied

#### 1. Database Schema Updates
- Made `token_identifier` nullable to prevent insertion errors
- Verified `user_role` enum has both 'user' and 'admin' values
- Added automatic user profile creation trigger
- Updated RLS policies for proper access control

#### 2. Code Updates
- Updated admin sign-up action to work with current schema
- Added proper error handling and fallback logic
- Updated admin sign-in action for role verification
- Fixed admin auth guard to work with current user structure

#### 3. Automatic Profile Creation
- Created `handle_new_user()` function that automatically creates user profiles
- Added trigger that runs when new users are created in `auth.users`
- Handles role assignment from user metadata

### Current Database Structure

```sql
-- Users table structure
CREATE TABLE public.users (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  avatar_url TEXT,
  user_id TEXT,
  token_identifier TEXT, -- Now nullable
  image TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE,
  email TEXT,
  name TEXT,
  full_name TEXT,
  role user_role NOT NULL DEFAULT 'user' -- Enum: 'user' | 'admin'
);
```

### Testing the Fix

#### 1. Admin Sign-Up Test
1. Go to `/admin/sign-up`
2. Fill in the form:
   - Full Name: Your Name
   - Admin Email: <EMAIL>
   - Password: secure-password
   - Admin Access Code: `TENNIS_ADMIN_2024`
3. Submit the form
4. Check for email verification

#### 2. Admin Sign-In Test
1. After email verification, go to `/admin/sign-in`
2. Enter your admin credentials
3. Should redirect to `/admin` dashboard

#### 3. Database Verification
Run this query in Supabase SQL Editor:
```sql
SELECT id, email, full_name, name, role, created_at 
FROM public.users 
WHERE role = 'admin';
```

### Environment Variables Required

Make sure these are set in your `.env.local`:
```env
# Required for Supabase
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Admin authentication
ADMIN_ACCESS_CODE=TENNIS_ADMIN_2024

# Optional: Restrict admin emails to specific domains
# ADMIN_EMAIL_DOMAINS=yourcompany.com,yourdomain.org
```

### Common Issues and Solutions

#### Issue 1: "Invalid admin access code"
**Solution**: Check that `ADMIN_ACCESS_CODE` environment variable is set correctly.

#### Issue 2: "Admin access required"
**Solution**: 
1. Check user role in database
2. Verify RLS policies allow role checking
3. Check user metadata has correct role

#### Issue 3: "Database error saving new user"
**Solution**: 
1. Run the `admin-auth-setup.sql` script
2. Check that `token_identifier` is nullable
3. Verify trigger function exists

#### Issue 4: Email verification not working
**Solution**:
1. Check Supabase email settings
2. Verify email templates are configured
3. Check spam folder

### Manual Database Fixes

If you still encounter issues, run these queries:

#### 1. Fix token_identifier column
```sql
ALTER TABLE public.users ALTER COLUMN token_identifier DROP NOT NULL;
```

#### 2. Add missing admin role to enum
```sql
ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'admin';
```

#### 3. Create admin user manually (if needed)
```sql
-- Replace with actual user ID from auth.users
INSERT INTO public.users (
  id, 
  email, 
  full_name, 
  name, 
  role, 
  token_identifier,
  created_at, 
  updated_at
) VALUES (
  'user-uuid-from-auth-table',
  '<EMAIL>',
  'Admin User',
  'Admin User',
  'admin',
  'user-uuid-from-auth-table',
  NOW(),
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  role = 'admin',
  updated_at = NOW();
```

#### 4. Check RLS policies
```sql
-- View current policies
SELECT policyname, cmd, permissive 
FROM pg_policies 
WHERE tablename = 'users' AND schemaname = 'public';
```

### Security Notes

1. **Access Code**: Change the default access code in production
2. **Email Domains**: Consider restricting admin emails to company domains
3. **RLS Policies**: Ensure proper row-level security is enabled
4. **Monitoring**: Log admin access attempts for security auditing

### Next Steps After Fix

1. **Test Admin Flow**: Complete sign-up and sign-in process
2. **Access Admin Dashboard**: Verify `/admin` routes work
3. **Test Permissions**: Ensure admin-only features are accessible
4. **Update Documentation**: Update any internal docs with new access codes

### Support

If you continue to experience issues:

1. Check browser console for JavaScript errors
2. Check Supabase logs for database errors
3. Verify environment variables are loaded
4. Test with a fresh browser session/incognito mode
5. Check network tab for failed API requests

The admin authentication system should now work correctly with the fixed database schema and updated code.
