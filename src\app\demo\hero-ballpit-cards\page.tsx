"use client";

import { Suspense } from "react";
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import HeroBallpitCards from "@/components/demo/hero-ballpit-cards";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { <PERSON>Left, Layers, <PERSON>Pointer, <PERSON>rk<PERSON>, Award, Settings, Eye, Zap } from "lucide-react";

export default function HeroBallpitCardsDemo() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-green-50/30">
      <Navbar />
      
      {/* Demo Header */}
      <div className="pt-24 pb-8">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <Button asChild variant="outline" size="sm">
                <Link href="/">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Home
                </Link>
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-slate-900">Ballpit + CardSwap Hero Demo</h1>
                <p className="text-slate-600">Interactive physics background with rotating content cards</p>
              </div>
            </div>
            
            {/* Demo Navigation */}
            <div className="flex gap-2">
              <Button asChild variant="outline" size="sm">
                <Link href="/demo/hero-3d">3D Demo</Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href="/demo/hero-ballpit">Ballpit Demo</Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href="/demo/hero-video">Video Demo</Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href="/demo/hero-gamified">Game Demo</Link>
              </Button>
            </div>
          </div>

          {/* Demo Features */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 neo-shadow-light border border-green-100">
              <div className="flex items-center gap-3 mb-2">
                <Layers className="w-5 h-5 text-green-600" />
                <span className="font-semibold text-slate-800">Layered Design</span>
              </div>
              <p className="text-sm text-slate-600">Ballpit background with card overlay</p>
            </div>
            
            <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 neo-shadow-light border border-blue-100">
              <div className="flex items-center gap-3 mb-2">
                <MousePointer className="w-5 h-5 text-blue-600" />
                <span className="font-semibold text-slate-800">Interactive Physics</span>
              </div>
              <p className="text-sm text-slate-600">Cursor-following tennis balls</p>
            </div>
            
            <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 neo-shadow-light border border-purple-100">
              <div className="flex items-center gap-3 mb-2">
                <Sparkles className="w-5 h-5 text-purple-600" />
                <span className="font-semibold text-slate-800">Auto-Rotating Cards</span>
              </div>
              <p className="text-sm text-slate-600">5-second interval card swapping</p>
            </div>
            
            <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 neo-shadow-light border border-orange-100">
              <div className="flex items-center gap-3 mb-2">
                <Award className="w-5 h-5 text-orange-600" />
                <span className="font-semibold text-slate-800">Neomorphism UI</span>
              </div>
              <p className="text-sm text-slate-600">Modern design aesthetic</p>
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 neo-shadow-light border border-slate-200 mb-8">
            <h3 className="text-lg font-semibold text-slate-900 mb-4 flex items-center gap-2">
              <Settings className="w-5 h-5 text-green-600" />
              Technical Specifications
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <h4 className="font-medium text-slate-800 mb-2">CardSwap Configuration</h4>
                <ul className="text-sm text-slate-600 space-y-1">
                  <li>• Card Distance: 60px</li>
                  <li>• Vertical Distance: 70px</li>
                  <li>• Auto-advance Delay: 5000ms</li>
                  <li>• Pause on Hover: Disabled</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-slate-800 mb-2">Ballpit Physics</h4>
                <ul className="text-sm text-slate-600 space-y-1">
                  <li>• Gravity: 0.3</li>
                  <li>• Friction: 0.985</li>
                  <li>• Wall Bounce: 0.9</li>
                  <li>• Cursor Following: Enabled</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-slate-800 mb-2">Performance</h4>
                <ul className="text-sm text-slate-600 space-y-1">
                  <li>• Mobile: 60 balls</li>
                  <li>• Tablet: 90 balls</li>
                  <li>• Desktop: 120 balls</li>
                  <li>• Responsive optimization</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Card Content Preview */}
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 neo-shadow-light border border-slate-200">
            <h3 className="text-lg font-semibold text-slate-900 mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5 text-blue-600" />
              Card Content Preview
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="border border-green-200 rounded-xl p-4 bg-green-50/50">
                <h4 className="font-semibold text-green-800 mb-2">Card 1: Tennis Equipment</h4>
                <p className="text-sm text-green-700">Premium tennis gear and professional equipment showcase</p>
              </div>
              <div className="border border-blue-200 rounded-xl p-4 bg-blue-50/50">
                <h4 className="font-semibold text-blue-800 mb-2">Card 2: Mentorship Programs</h4>
                <p className="text-sm text-blue-700">6-month and 12-month coaching tier promotions</p>
              </div>
              <div className="border border-purple-200 rounded-xl p-4 bg-purple-50/50">
                <h4 className="font-semibold text-purple-800 mb-2">Card 3: Blog Preview</h4>
                <p className="text-sm text-purple-700">Tennis insights and tips section (coming soon)</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Hero Demo Section */}
      <Suspense fallback={
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-green-600 rounded-full flex items-center justify-center animate-pulse">
              <Zap className="w-8 h-8 text-white" />
            </div>
            <p className="text-lg font-medium text-slate-700">Loading Interactive Demo...</p>
          </div>
        </div>
      }>
        <HeroBallpitCards />
      </Suspense>

      <Footer />
    </div>
  );
}
