{"ci": {"collect": {"url": ["http://localhost:3000", "http://localhost:3000/articles", "http://localhost:3000/products", "http://localhost:3000/about", "http://localhost:3000/contact"], "startServerCommand": "npm run preview", "startServerReadyPattern": "Local:", "startServerReadyTimeout": 30000, "numberOfRuns": 3}, "assert": {"assertions": {"categories:performance": ["warn", {"minScore": 0.8}], "categories:accessibility": ["error", {"minScore": 0.9}], "categories:best-practices": ["warn", {"minScore": 0.8}], "categories:seo": ["error", {"minScore": 0.9}], "categories:pwa": ["warn", {"minScore": 0.6}]}}, "upload": {"target": "temporary-public-storage"}, "server": {"port": 9001, "storage": ".lighthouseci"}}}