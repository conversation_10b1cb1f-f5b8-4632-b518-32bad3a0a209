import { NextResponse } from 'next/server';
import { createClient, createServiceRoleClient } from '../../../../utils/supabase/server';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient();
    const { id } = params;

    const { data: product, error } = await supabase
      .from('products')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching product:', error);
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(product);
  } catch (error: any) {
    console.error('Error in product GET API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log('PUT /api/products/[id] - Request received for product ID:', params.id);

    // Check for Authorization header
    const authHeader = request.headers.get('Authorization');
    console.log('PUT /api/products/[id] - Authorization header present:', !!authHeader);

    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    console.log('PUT /api/products/[id] - Auth check result:', {
      user: user ? { id: user.id, email: user.email } : null,
      error: authError?.message
    });

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized', details: authError?.message },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || userData.role !== 'admin') {
      console.log('PUT /api/products/[id] - Access denied. User role:', userData?.role);
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }

    console.log('PUT /api/products/[id] - Admin access confirmed for user:', user.id);

    const { id } = params;
    const body = await request.json();
    const { name, price, description, image, images, category, stock, status, features, rating, reviews } = body;

    console.log('PUT /api/products/[id] - Update data received:', { name, price, description, category, stock, status });

    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (name !== undefined) updateData.name = name;
    if (price !== undefined) updateData.price = price;
    if (description !== undefined) updateData.description = description;
    if (image !== undefined) updateData.image = image;
    if (images !== undefined) updateData.images = images;
    if (category !== undefined) updateData.category = category;
    if (features !== undefined) updateData.features = features;
    if (rating !== undefined) updateData.rating = rating;
    if (reviews !== undefined) updateData.reviews = reviews;
    if (stock !== undefined) {
      updateData.stock = stock;
      // Use provided status or calculate based on stock
      updateData.status = status || (stock > 0 ? 'In Stock' : 'Out of Stock');
    } else if (status !== undefined) {
      updateData.status = status;
    }

    // Use service role client for the actual database operation to bypass RLS
    console.log('PUT /api/products/[id] - About to update with service role client. Update data:', updateData);

    const serviceSupabase = createServiceRoleClient();
    const { data: product, error } = await serviceSupabase
      .from('products')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('PUT /api/products/[id] - Database update error:', error);
      console.error('PUT /api/products/[id] - Error details:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code
      });
      return NextResponse.json(
        { error: 'Failed to update product', details: error.message },
        { status: 500 }
      );
    }

    console.log('PUT /api/products/[id] - Product updated successfully:', product.id);

    return NextResponse.json(product);
  } catch (error: any) {
    console.error('Error in product PUT API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || userData.role !== 'admin') {
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }

    const { id } = params;

    // Use service role client for the actual database operation to bypass RLS
    const serviceSupabase = createServiceRoleClient();
    const { error } = await serviceSupabase
      .from('products')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting product:', error);
      return NextResponse.json(
        { error: 'Failed to delete product' },
        { status: 500 }
      );
    }

    return NextResponse.json({ message: 'Product deleted successfully' });
  } catch (error: any) {
    console.error('Error in product DELETE API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
