'use client';

import { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Calendar,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  RefreshCw,
  AlertCircle,
  ChevronLeft,
  ChevronRight,
  CheckCircle,
  XCircle,
  Clock,
  Download,
  MoreHorizontal,
  Phone,
  Mail,
  User,
  MapPin
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  useConsultations, 
  useConsultationStats, 
  useUpdateConsultation,
  useBulkConsultationAction,
  useDeleteConsultation,
  useConsultationStatusColor,
  usePaymentStatusColor
} from '@/hooks/useConsultations';
import { 
  ConsultationFilters, 
  CONSULTATION_STATUS_OPTIONS, 
  PAYMENT_STATUS_OPTIONS,
  AdminConsultationView
} from '@/types/consultations';
import Link from 'next/link';
import { formatConsultationDateTime } from '@/types/consultations';

export default function ConsultationsPage() {
  const [filters, setFilters] = useState<ConsultationFilters & { page: number; limit: number }>({
    page: 1,
    limit: 10,
  });
  const [showFilters, setShowFilters] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedConsultations, setSelectedConsultations] = useState<string[]>([]);

  // Fetch consultations and stats using React Query
  const {
    data: consultationsData,
    isLoading: consultationsLoading,
    error: consultationsError,
    refetch: refetchConsultations
  } = useConsultations(filters);

  const {
    data: statsData,
    isLoading: statsLoading,
    error: statsError,
    refetch: refetchStats
  } = useConsultationStats();

  // Mutations
  const updateConsultation = useUpdateConsultation();
  const bulkAction = useBulkConsultationAction();
  const deleteConsultation = useDeleteConsultation();

  // Utility hooks
  const getStatusColor = useConsultationStatusColor();
  const getPaymentStatusColor = usePaymentStatusColor();

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<ConsultationFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: 1, // Reset to first page when filters change
    }));
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    handleFilterChange({ search: searchTerm });
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({ ...prev, page: newPage }));
  };

  // Handle consultation selection
  const handleSelectConsultation = (consultationId: string, checked: boolean) => {
    if (checked) {
      setSelectedConsultations(prev => [...prev, consultationId]);
    } else {
      setSelectedConsultations(prev => prev.filter(id => id !== consultationId));
    }
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked && consultationsData?.consultations) {
      setSelectedConsultations(consultationsData.consultations.map(c => c.id));
    } else {
      setSelectedConsultations([]);
    }
  };

  // Handle bulk actions
  const handleBulkAction = (action: 'confirm' | 'cancel' | 'mark_completed') => {
    if (selectedConsultations.length === 0) return;

    bulkAction.mutate({
      consultation_ids: selectedConsultations,
      action,
    }, {
      onSuccess: () => {
        setSelectedConsultations([]);
      }
    });
  };

  // Handle status update
  const handleStatusUpdate = (consultationId: string, status: string) => {
    updateConsultation.mutate({
      id: consultationId,
      update: { status: status as any }
    });
  };

  // Handle delete
  const handleDelete = (consultationId: string) => {
    if (confirm('Are you sure you want to delete this consultation? This action cannot be undone.')) {
      deleteConsultation.mutate(consultationId);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return `R${amount.toFixed(2)}`;
  };

  if (consultationsError) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2 text-red-600">
          <AlertCircle className="h-5 w-5" />
          <span>Error loading consultations: {consultationsError.message}</span>
        </div>
        <Button onClick={() => refetchConsultations()} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Consultations</h1>
          <p className="text-muted-foreground">
            Manage consultation bookings and appointments
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => refetchConsultations()} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {statsLoading ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-muted rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : statsData ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium text-muted-foreground">Total</span>
              </div>
              <div className="text-2xl font-bold">{statsData.total_consultations}</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-yellow-600" />
                <span className="text-sm font-medium text-muted-foreground">Pending</span>
              </div>
              <div className="text-2xl font-bold">{statsData.pending_consultations}</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-muted-foreground">Completed</span>
              </div>
              <div className="text-2xl font-bold">{statsData.completed_consultations}</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-muted-foreground">Revenue</span>
              </div>
              <div className="text-2xl font-bold">{formatCurrency(statsData.total_revenue)}</div>
              {statsData.monthly_growth !== 0 && (
                <div className={`text-sm ${statsData.monthly_growth > 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {statsData.monthly_growth > 0 ? '+' : ''}{statsData.monthly_growth.toFixed(1)}% from last month
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      ) : null}

      {/* Consultations Table */}
      <Card>
        <CardHeader>
          <CardTitle>Consultation Management</CardTitle>
          <CardDescription>
            View and manage consultation bookings, update statuses, and track appointments.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <form onSubmit={handleSearch} className="relative flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by name, email, or phone..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </form>
            <Button
              variant="outline"
              className="sm:w-auto"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>

          {/* Filter Panel */}
          {showFilters && (
            <div className="grid gap-4 md:grid-cols-4 mb-6 p-4 border rounded-lg bg-muted/50">
              <Select
                value={filters.status || 'all'}
                onValueChange={(value) => handleFilterChange({ status: value === 'all' ? undefined : value as any })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  {CONSULTATION_STATUS_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select
                value={filters.payment_status || 'all'}
                onValueChange={(value) => handleFilterChange({ payment_status: value === 'all' ? undefined : value as any })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Payment Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Payment Statuses</SelectItem>
                  {PAYMENT_STATUS_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Input
                type="date"
                placeholder="From Date"
                value={filters.date_from || ''}
                onChange={(e) => handleFilterChange({ date_from: e.target.value || undefined })}
              />

              <Input
                type="date"
                placeholder="To Date"
                value={filters.date_to || ''}
                onChange={(e) => handleFilterChange({ date_to: e.target.value || undefined })}
              />
            </div>
          )}

          {/* Bulk Actions */}
          {selectedConsultations.length > 0 && (
            <div className="flex items-center gap-2 mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <span className="text-sm font-medium">
                {selectedConsultations.length} consultation(s) selected
              </span>
              <div className="flex gap-2 ml-auto">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction('confirm')}
                  disabled={bulkAction.isPending}
                >
                  Confirm
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction('mark_completed')}
                  disabled={bulkAction.isPending}
                >
                  Mark Completed
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => handleBulkAction('cancel')}
                  disabled={bulkAction.isPending}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}

          {/* Loading State */}
          {consultationsLoading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-16 bg-muted rounded"></div>
                </div>
              ))}
            </div>
          ) : (
            <>
              {/* Desktop Table */}
              <div className="hidden md:block overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b border-border">
                      <th className="text-left font-medium py-3 px-4">
                        <Checkbox
                          checked={
                            consultationsData?.consultations && consultationsData.consultations.length > 0 &&
                            selectedConsultations.length === consultationsData.consultations.length
                          }
                          onCheckedChange={handleSelectAll}
                        />
                      </th>
                      <th className="text-left font-medium py-3 px-4">Customer</th>
                      <th className="text-left font-medium py-3 px-4">Date & Time</th>
                      <th className="text-left font-medium py-3 px-4">Location</th>
                      <th className="text-left font-medium py-3 px-4">Status</th>
                      <th className="text-left font-medium py-3 px-4">Payment</th>
                      <th className="text-right font-medium py-3 px-4">Amount</th>
                      <th className="text-right font-medium py-3 px-4">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {consultationsData?.consultations.map((consultation) => (
                      <ConsultationTableRow
                        key={consultation.id}
                        consultation={consultation}
                        isSelected={selectedConsultations.includes(consultation.id)}
                        onSelect={handleSelectConsultation}
                        onStatusUpdate={handleStatusUpdate}
                        onDelete={handleDelete}
                        getStatusColor={getStatusColor}
                        getPaymentStatusColor={getPaymentStatusColor}
                        formatDate={formatDate}
                        formatCurrency={formatCurrency}
                      />
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Mobile Cards */}
              <div className="md:hidden space-y-4">
                {consultationsData?.consultations.map((consultation) => (
                  <ConsultationMobileCard
                    key={consultation.id}
                    consultation={consultation}
                    isSelected={selectedConsultations.includes(consultation.id)}
                    onSelect={handleSelectConsultation}
                    onStatusUpdate={handleStatusUpdate}
                    onDelete={handleDelete}
                    getStatusColor={getStatusColor}
                    getPaymentStatusColor={getPaymentStatusColor}
                    formatDate={formatDate}
                    formatCurrency={formatCurrency}
                  />
                ))}
              </div>

              {/* Pagination */}
              {consultationsData && consultationsData.totalPages > 1 && (
                <div className="flex items-center justify-between mt-6">
                  <div className="text-sm text-muted-foreground">
                    Showing {((consultationsData.page - 1) * consultationsData.limit) + 1} to{' '}
                    {Math.min(consultationsData.page * consultationsData.limit, consultationsData.total)} of{' '}
                    {consultationsData.total} consultations
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(consultationsData.page - 1)}
                      disabled={consultationsData.page <= 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </Button>
                    <span className="text-sm">
                      Page {consultationsData.page} of {consultationsData.totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(consultationsData.page + 1)}
                      disabled={consultationsData.page >= consultationsData.totalPages}
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}

              {/* Empty State */}
              {consultationsData?.consultations.length === 0 && (
                <div className="text-center py-12">
                  <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No consultations found</h3>
                  <p className="text-muted-foreground">
                    {filters.search || filters.status || filters.payment_status
                      ? 'Try adjusting your filters to see more results.'
                      : 'No consultation bookings have been made yet.'}
                  </p>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Desktop Table Row Component
interface ConsultationTableRowProps {
  consultation: AdminConsultationView;
  isSelected: boolean;
  onSelect: (id: string, checked: boolean) => void;
  onStatusUpdate: (id: string, status: string) => void;
  onDelete: (id: string) => void;
  getStatusColor: (status: string) => string;
  getPaymentStatusColor: (status: string) => string;
  formatDate: (date: string) => string;
  formatCurrency: (amount: number) => string;
}

function ConsultationTableRow({
  consultation,
  isSelected,
  onSelect,
  onStatusUpdate,
  onDelete,
  getStatusColor,
  getPaymentStatusColor,
  formatDate,
  formatCurrency,
}: ConsultationTableRowProps) {
  return (
    <tr className="border-b border-border hover:bg-muted/50">
      <td className="py-3 px-4">
        <Checkbox
          checked={isSelected}
          onCheckedChange={(checked) => onSelect(consultation.id, checked as boolean)}
        />
      </td>
      <td className="py-3 px-4">
        <div>
          <p className="font-medium">{consultation.customer_name}</p>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Mail className="h-3 w-3" />
            <span>{consultation.email}</span>
          </div>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Phone className="h-3 w-3" />
            <span>{consultation.phone_number}</span>
          </div>
        </div>
      </td>
      <td className="py-3 px-4">
        <div>
          <p className="font-medium">{formatDate(consultation.scheduled_date)}</p>
          <p className="text-sm text-muted-foreground">{consultation.scheduled_time}</p>
          {consultation.is_overdue && (
            <Badge variant="destructive" className="text-xs mt-1">
              Overdue
            </Badge>
          )}
        </div>
      </td>
      <td className="py-3 px-4">
        <div className="flex items-center gap-2">
          <MapPin className="h-3 w-3 text-muted-foreground" />
          <span className="text-sm">{consultation.location || 'Not specified'}</span>
        </div>
      </td>
      <td className="py-3 px-4">
        <Badge variant="secondary" className={getStatusColor(consultation.status)}>
          {consultation.status}
        </Badge>
      </td>
      <td className="py-3 px-4">
        <Badge variant="secondary" className={getPaymentStatusColor(consultation.payment_status || 'pending')}>
          {consultation.payment_status || 'pending'}
        </Badge>
      </td>
      <td className="py-3 px-4 text-right">
        {consultation.payment_amount ? formatCurrency(consultation.payment_amount) : 'N/A'}
      </td>
      <td className="py-3 px-4 text-right">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => onStatusUpdate(consultation.id, 'confirmed')}>
              <CheckCircle className="h-4 w-4 mr-2" />
              Confirm
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onStatusUpdate(consultation.id, 'completed')}>
              <CheckCircle className="h-4 w-4 mr-2" />
              Mark Completed
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onStatusUpdate(consultation.id, 'cancelled')}>
              <XCircle className="h-4 w-4 mr-2" />
              Cancel
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => onDelete(consultation.id)}
              className="text-red-600"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </td>
    </tr>
  );
}

// Mobile Card Component
interface ConsultationMobileCardProps {
  consultation: AdminConsultationView;
  isSelected: boolean;
  onSelect: (id: string, checked: boolean) => void;
  onStatusUpdate: (id: string, status: string) => void;
  onDelete: (id: string) => void;
  getStatusColor: (status: string) => string;
  getPaymentStatusColor: (status: string) => string;
  formatDate: (date: string) => string;
  formatCurrency: (amount: number) => string;
}

function ConsultationMobileCard({
  consultation,
  isSelected,
  onSelect,
  onStatusUpdate,
  onDelete,
  getStatusColor,
  getPaymentStatusColor,
  formatDate,
  formatCurrency,
}: ConsultationMobileCardProps) {
  return (
    <Card className="p-4">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <Checkbox
            checked={isSelected}
            onCheckedChange={(checked) => onSelect(consultation.id, checked as boolean)}
          />
          <div>
            <h3 className="font-medium">{consultation.customer_name}</h3>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Mail className="h-3 w-3" />
              <span>{consultation.email}</span>
            </div>
          </div>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => onStatusUpdate(consultation.id, 'confirmed')}>
              <CheckCircle className="h-4 w-4 mr-2" />
              Confirm
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onStatusUpdate(consultation.id, 'completed')}>
              <CheckCircle className="h-4 w-4 mr-2" />
              Mark Completed
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onStatusUpdate(consultation.id, 'cancelled')}>
              <XCircle className="h-4 w-4 mr-2" />
              Cancel
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => onDelete(consultation.id)}
              className="text-red-600"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="space-y-2">
        <div className="flex items-center gap-2 text-sm">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <span>{formatDate(consultation.scheduled_date)} at {consultation.scheduled_time}</span>
          {consultation.is_overdue && (
            <Badge variant="destructive" className="text-xs">
              Overdue
            </Badge>
          )}
        </div>

        <div className="flex items-center gap-2 text-sm">
          <Phone className="h-4 w-4 text-muted-foreground" />
          <span>{consultation.phone_number}</span>
        </div>

        <div className="flex items-center gap-2 text-sm">
          <MapPin className="h-4 w-4 text-muted-foreground" />
          <span>{consultation.location || 'Not specified'}</span>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex gap-2">
            <Badge variant="secondary" className={getStatusColor(consultation.status)}>
              {consultation.status}
            </Badge>
            <Badge variant="secondary" className={getPaymentStatusColor(consultation.payment_status || 'pending')}>
              {consultation.payment_status || 'pending'}
            </Badge>
          </div>
          <span className="font-medium">
            {consultation.payment_amount ? formatCurrency(consultation.payment_amount) : 'N/A'}
          </span>
        </div>

        {consultation.reason && (
          <div className="text-sm text-muted-foreground">
            <strong>Reason:</strong> {consultation.reason.length > 100
              ? `${consultation.reason.substring(0, 100)}...`
              : consultation.reason}
          </div>
        )}
      </div>
    </Card>
  );
}
