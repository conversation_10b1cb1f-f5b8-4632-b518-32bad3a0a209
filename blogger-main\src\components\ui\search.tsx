import React, { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Search as SearchIcon, X, Filter, SortAsc, SortDesc } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SearchResult {
  id: string;
  title: string;
  description?: string;
  category?: string;
  type?: string;
  url?: string;
  image?: string;
}

interface SearchProps {
  placeholder?: string;
  onSearch?: (query: string) => void;
  onFilter?: (filters: Record<string, string>) => void;
  results?: SearchResult[];
  isLoading?: boolean;
  showFilters?: boolean;
  filters?: Array<{
    key: string;
    label: string;
    options: Array<{ value: string; label: string }>;
  }>;
  className?: string;
  variant?: 'default' | 'compact' | 'hero';
}

export function Search({
  placeholder = "Search...",
  onSearch,
  onFilter,
  results = [],
  isLoading = false,
  showFilters = false,
  filters = [],
  className,
  variant = 'default'
}: SearchProps) {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState<Record<string, string>>({});
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const searchRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (onSearch && query.length > 0) {
        onSearch(query);
        setIsOpen(true);
      } else {
        setIsOpen(false);
      }
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [query, onSearch]);

  useEffect(() => {
    if (onFilter) {
      onFilter(selectedFilters);
    }
  }, [selectedFilters, onFilter]);

  const handleFilterChange = (key: string, value: string) => {
    setSelectedFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const clearFilter = (key: string) => {
    setSelectedFilters(prev => {
      const newFilters = { ...prev };
      delete newFilters[key];
      return newFilters;
    });
  };

  const clearAllFilters = () => {
    setSelectedFilters({});
  };

  const getVariantClasses = () => {
    switch (variant) {
      case 'hero':
        return 'w-full max-w-2xl mx-auto';
      case 'compact':
        return 'w-64';
      default:
        return 'w-full max-w-md';
    }
  };

  const getInputClasses = () => {
    switch (variant) {
      case 'hero':
        return 'h-14 text-lg pl-14 pr-14 rounded-2xl border-2 shadow-lg';
      case 'compact':
        return 'h-9 text-sm pl-9 pr-9 rounded-lg';
      default:
        return 'h-11 pl-11 pr-11 rounded-xl border-2';
    }
  };

  const getIconSize = () => {
    switch (variant) {
      case 'hero':
        return 'w-6 h-6';
      case 'compact':
        return 'w-4 h-4';
      default:
        return 'w-5 h-5';
    }
  };

  return (
    <div ref={searchRef} className={cn('relative', getVariantClasses(), className)}>
      {/* Search Input */}
      <div className="relative">
        <SearchIcon className={cn(
          'absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400',
          getIconSize(),
          variant === 'hero' && 'left-5'
        )} />
        <Input
          type="text"
          placeholder={placeholder}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className={cn(
            'transition-all duration-200 focus:ring-2 focus:ring-primary/20',
            getInputClasses(),
            isOpen && 'rounded-b-none border-b-0'
          )}
        />
        {query && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setQuery('');
              setIsOpen(false);
            }}
            className={cn(
              'absolute right-3 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-gray-100',
              variant === 'hero' && 'right-5 h-8 w-8'
            )}
          >
            <X className={cn('text-gray-400', getIconSize())} />
          </Button>
        )}
      </div>

      {/* Filters */}
      {showFilters && filters.length > 0 && (
        <div className="mt-3 space-y-2">
          <div className="flex items-center gap-2 flex-wrap">
            {filters.map((filter) => (
              <select
                key={filter.key}
                value={selectedFilters[filter.key] || ''}
                onChange={(e) => handleFilterChange(filter.key, e.target.value)}
                className="text-xs border rounded px-2 py-1 bg-white"
              >
                <option value="">{filter.label}</option>
                {filter.options.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            ))}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="text-xs"
            >
              {sortOrder === 'asc' ? <SortAsc className="w-3 h-3" /> : <SortDesc className="w-3 h-3" />}
            </Button>
          </div>

          {/* Active Filters */}
          {Object.keys(selectedFilters).length > 0 && (
            <div className="flex items-center gap-2 flex-wrap">
              <span className="text-xs text-gray-500">Filters:</span>
              {Object.entries(selectedFilters).map(([key, value]) => {
                const filter = filters.find(f => f.key === key);
                const option = filter?.options.find(o => o.value === value);
                return (
                  <Badge
                    key={key}
                    variant="secondary"
                    className="text-xs flex items-center gap-1"
                  >
                    {option?.label || value}
                    <X
                      className="w-3 h-3 cursor-pointer hover:text-red-500"
                      onClick={() => clearFilter(key)}
                    />
                  </Badge>
                );
              })}
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="text-xs text-red-500 hover:text-red-700"
              >
                Clear all
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Search Results Dropdown */}
      {isOpen && (query.length > 0 || results.length > 0) && (
        <div className="absolute top-full left-0 right-0 bg-white border-2 border-t-0 rounded-b-xl shadow-lg max-h-96 overflow-y-auto z-50">
          {isLoading ? (
            <div className="p-4 text-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
              <p className="text-sm text-gray-500 mt-2">Searching...</p>
            </div>
          ) : results.length > 0 ? (
            <div className="py-2">
              {results.map((result) => (
                <div
                  key={result.id}
                  className="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b last:border-b-0"
                  onClick={() => {
                    if (result.url) {
                      window.location.href = result.url;
                    }
                    setIsOpen(false);
                  }}
                >
                  <div className="flex items-start gap-3">
                    {result.image && (
                      <img
                        src={result.image}
                        alt={result.title}
                        className="w-10 h-10 rounded object-cover flex-shrink-0"
                      />
                    )}
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-sm text-gray-900 truncate">
                        {result.title}
                      </h4>
                      {result.description && (
                        <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                          {result.description}
                        </p>
                      )}
                      <div className="flex items-center gap-2 mt-2">
                        {result.category && (
                          <Badge variant="outline" className="text-xs">
                            {result.category}
                          </Badge>
                        )}
                        {result.type && (
                          <Badge variant="secondary" className="text-xs">
                            {result.type}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : query.length > 0 ? (
            <div className="p-4 text-center">
              <p className="text-sm text-gray-500">No results found for "{query}"</p>
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
}
