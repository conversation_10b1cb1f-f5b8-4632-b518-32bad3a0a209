"use client";

import { Suspense } from "react";
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import HeroBallpit from "@/components/demo/hero-ballpit";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>Pointer, <PERSON><PERSON><PERSON>, <PERSON>, Settings, Eye } from "lucide-react";

export default function HeroBallpitDemo() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-green-50/30">
      <Navbar />
      
      {/* Demo Header */}
      <div className="pt-24 pb-8">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <Button asChild variant="outline" size="sm">
                <Link href="/">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Home
                </Link>
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-slate-900">Interactive Ballpit Hero Demo</h1>
                <p className="text-slate-600">3D physics simulation with tennis ball interactions</p>
              </div>
            </div>
            
            {/* Demo Navigation */}
            <div className="flex gap-2">
              <Button asChild variant="outline" size="sm">
                <Link href="/demo/hero-3d">3D Demo</Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href="/demo/hero-ballpit-cards">Ballpit + Cards</Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href="/demo/hero-video">Video Demo</Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href="/demo/hero-gamified">Game Demo</Link>
              </Button>
            </div>
          </div>

          {/* Demo Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-green-100 neo-shadow-light">
              <div className="flex items-center gap-2 mb-2">
                <MousePointer className="w-4 h-4 text-green-600" />
                <span className="text-sm font-semibold">Physics Interaction</span>
              </div>
              <p className="text-xs text-slate-600">Move cursor to attract tennis balls with realistic physics</p>
            </div>
            
            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-green-100 neo-shadow-light">
              <div className="flex items-center gap-2 mb-2">
                <Zap className="w-4 h-4 text-green-600" />
                <span className="text-sm font-semibold">Performance Optimized</span>
              </div>
              <p className="text-xs text-slate-600">Adaptive ball count based on device capabilities</p>
            </div>
            
            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-green-100 neo-shadow-light">
              <div className="flex items-center gap-2 mb-2">
                <Sparkles className="w-4 h-4 text-green-600" />
                <span className="text-sm font-semibold">Realistic Physics</span>
              </div>
              <p className="text-xs text-slate-600">Gravity, collision detection, and ball interactions</p>
            </div>
          </div>
        </div>
      </div>

      {/* Ballpit Hero Section */}
      <Suspense fallback={
        <div className="h-[600px] flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-green-600 rounded-full flex items-center justify-center animate-pulse">
              <Zap className="w-8 h-8 text-white" />
            </div>
            <p className="text-slate-600">Loading Physics Engine...</p>
          </div>
        </div>
      }>
        <HeroBallpit />
      </Suspense>

      {/* Demo Info Section */}
      <section className="py-16 bg-white/50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-8">Interactive Ballpit Features</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-slate-900">Technical Implementation</h3>
                <ul className="space-y-2 text-slate-600">
                  <li>• Three.js WebGL rendering engine</li>
                  <li>• Real-time physics simulation</li>
                  <li>• Collision detection and response</li>
                  <li>• Adaptive performance scaling</li>
                  <li>• Memory management and cleanup</li>
                  <li>• Cross-browser compatibility</li>
                </ul>
              </div>
              
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-slate-900">User Experience</h3>
                <ul className="space-y-2 text-slate-600">
                  <li>• Intuitive cursor-following interaction</li>
                  <li>• Smooth 60fps performance target</li>
                  <li>• Mobile-optimized ball count</li>
                  <li>• Realistic gravity and friction</li>
                  <li>• Visual feedback and indicators</li>
                  <li>• Accessibility considerations</li>
                </ul>
              </div>
            </div>

            {/* Physics Configuration */}
            <div className="mt-12 bg-white/80 backdrop-blur-sm rounded-2xl p-8 neo-shadow-light">
              <h3 className="text-xl font-semibold text-slate-900 mb-6 flex items-center gap-2">
                <Settings className="w-5 h-5 text-green-600" />
                Physics Configuration
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 mb-2">80-150</div>
                  <div className="text-sm text-slate-600">Tennis Balls</div>
                  <div className="text-xs text-slate-500 mt-1">Adaptive based on device</div>
                </div>
                
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 mb-2">0.5</div>
                  <div className="text-sm text-slate-600">Gravity Force</div>
                  <div className="text-xs text-slate-500 mt-1">Realistic physics</div>
                </div>
                
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 mb-2">0.98</div>
                  <div className="text-sm text-slate-600">Friction</div>
                  <div className="text-xs text-slate-500 mt-1">Smooth movement</div>
                </div>
              </div>
            </div>

            {/* Performance Metrics */}
            <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 neo-shadow-light">
                <h4 className="text-lg font-semibold text-slate-900 mb-4 flex items-center gap-2">
                  <Zap className="w-4 h-4 text-green-600" />
                  Performance Targets
                </h4>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-slate-600">Frame Rate</span>
                    <span className="font-semibold text-green-600">60 FPS</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Load Time</span>
                    <span className="font-semibold text-green-600">&lt; 500ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Memory Usage</span>
                    <span className="font-semibold text-green-600">Optimized</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 neo-shadow-light">
                <h4 className="text-lg font-semibold text-slate-900 mb-4 flex items-center gap-2">
                  <Eye className="w-4 h-4 text-green-600" />
                  Visual Features
                </h4>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-slate-600">Neomorphism Design</span>
                    <span className="font-semibold text-green-600">✓</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Responsive Layout</span>
                    <span className="font-semibold text-green-600">✓</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Tennis Branding</span>
                    <span className="font-semibold text-green-600">✓</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
