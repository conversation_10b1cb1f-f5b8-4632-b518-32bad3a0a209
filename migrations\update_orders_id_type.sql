-- First, modify the orders table to use text IDs instead of UUIDs
ALTER TABLE public.orders ALTER COLUMN id TYPE TEXT;

-- Update foreign key references if needed
-- For example, if there are tables referencing orders.id, you would update them like:
-- ALTER TABLE public.order_items ALTER COLUMN order_id TYPE TEXT;

-- Make sure the yoco_payment_id column exists
ALTER TABLE public.orders ADD COLUMN IF NOT EXISTS yoco_payment_id TEXT;

-- Create index on yoco_payment_id for faster lookups
CREATE INDEX IF NOT EXISTS orders_yoco_payment_id_idx ON public.orders (yoco_payment_id);

-- Add a comment explaining the change
COMMENT ON TABLE public.orders IS 'Order records with text IDs to support custom prefixed IDs (e.g., TEC-123456)'; 