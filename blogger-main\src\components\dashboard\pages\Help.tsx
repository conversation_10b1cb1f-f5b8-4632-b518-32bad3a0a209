import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  HelpCircle,
  Search,
  ChevronDown,
  ChevronRight,
  Book,
  MessageSquare,
  Mail,
  Phone,
  ExternalLink,
  User,
  ShoppingCart,
  CreditCard,
  Settings,
  Shield,
  Church,
  FileText,
  Video,
  Download,
  Star,
} from 'lucide-react';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
  tags: string[];
}

const faqData: FAQItem[] = [
  {
    id: '1',
    question: 'How do I create an account?',
    answer: 'To create an account, click the "Sign Up" button in the top right corner. You can register using your email address or sign up with Google, GitHub, Facebook, or Twitter. After registration, you\'ll receive a confirmation email to verify your account.',
    category: 'Account',
    tags: ['registration', 'signup', 'account'],
  },
  {
    id: '2',
    question: 'How do I reset my password?',
    answer: 'If you\'ve forgotten your password, click "Forgot Password" on the login page. Enter your email address and we\'ll send you a password reset link. Follow the instructions in the email to create a new password.',
    category: 'Account',
    tags: ['password', 'reset', 'login'],
  },
  {
    id: '3',
    question: 'How do I purchase products?',
    answer: 'Browse our products page, select items you want to purchase, and add them to your cart. When ready, go to your cart and click "Checkout". You can pay using various methods including credit cards through our secure payment system.',
    category: 'Shopping',
    tags: ['purchase', 'buy', 'checkout', 'payment'],
  },
  {
    id: '4',
    question: 'What payment methods do you accept?',
    answer: 'We accept major credit cards (Visa, MasterCard, American Express), debit cards, and digital wallets. All payments are processed securely through our payment partners. Prices are displayed in South African Rand (ZAR).',
    category: 'Payment',
    tags: ['payment', 'credit card', 'ZAR', 'currency'],
  },
  {
    id: '5',
    question: 'How do I access digital products after purchase?',
    answer: 'After purchasing digital products like e-books or courses, you can access them from your dashboard under "My Products". Digital content is available for immediate download or viewing, depending on the product type.',
    category: 'Digital Products',
    tags: ['digital', 'download', 'ebook', 'course'],
  },
  {
    id: '6',
    question: 'What is the Prayer Room?',
    answer: 'The Prayer Room is a special section where daily prayers are shared. You can view prayers, like them, and leave comments. Admins post new prayers regularly with text, images, or videos to inspire and guide the community.',
    category: 'Prayer Room',
    tags: ['prayer', 'spiritual', 'daily', 'community'],
  },
  {
    id: '7',
    question: 'How do I manage my privacy settings?',
    answer: 'Go to your dashboard and click on "Privacy Settings". Here you can control who sees your profile, manage notification preferences, and adjust data sharing settings. You can make your profile public, private, or visible to friends only.',
    category: 'Privacy',
    tags: ['privacy', 'settings', 'profile', 'visibility'],
  },
  {
    id: '8',
    question: 'How do I contact customer support?',
    answer: 'You can contact us through the contact form on our website, send an <NAME_EMAIL>, or call us at +27 11 234 5678. Our office is located in Morningside, Sandton. We respond to inquiries within 24 hours.',
    category: 'Support',
    tags: ['contact', 'support', 'help', 'email', 'phone'],
  },
  {
    id: '9',
    question: 'Can I get a refund for my purchase?',
    answer: 'Yes, we offer refunds within 30 days of purchase for most products. Digital products may have different refund policies. Contact our support team with your order details to initiate a refund request.',
    category: 'Refunds',
    tags: ['refund', 'return', 'money back', 'policy'],
  },
  {
    id: '10',
    question: 'How do I update my profile information?',
    answer: 'Go to your dashboard and click on "Profile Settings". Here you can update your name, email, profile picture, and other personal information. Changes are saved automatically when you click "Save Changes".',
    category: 'Profile',
    tags: ['profile', 'update', 'information', 'settings'],
  },
];

const categories = [
  { name: 'All', icon: HelpCircle, count: faqData.length },
  { name: 'Account', icon: User, count: faqData.filter(item => item.category === 'Account').length },
  { name: 'Shopping', icon: ShoppingCart, count: faqData.filter(item => item.category === 'Shopping').length },
  { name: 'Payment', icon: CreditCard, count: faqData.filter(item => item.category === 'Payment').length },
  { name: 'Digital Products', icon: Download, count: faqData.filter(item => item.category === 'Digital Products').length },
  { name: 'Prayer Room', icon: Church, count: faqData.filter(item => item.category === 'Prayer Room').length },
  { name: 'Privacy', icon: Shield, count: faqData.filter(item => item.category === 'Privacy').length },
  { name: 'Support', icon: MessageSquare, count: faqData.filter(item => item.category === 'Support').length },
];

export function Help() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [openItems, setOpenItems] = useState<string[]>([]);

  const filteredFAQs = faqData.filter(item => {
    const matchesSearch = item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.answer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'All' || item.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const toggleItem = (itemId: string) => {
    setOpenItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 flex items-center justify-center gap-2 mb-4">
          <HelpCircle className="h-8 w-8 text-blue-600" />
          Help Center
        </h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Find answers to common questions about using the Thabo Bester platform. 
          If you can't find what you're looking for, don't hesitate to contact our support team.
        </p>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              placeholder="Search for help topics..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 text-lg h-12"
            />
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Categories Sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Categories</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {categories.map((category) => {
                const Icon = category.icon;
                return (
                  <Button
                    key={category.name}
                    variant={selectedCategory === category.name ? 'default' : 'ghost'}
                    className="w-full justify-start"
                    onClick={() => setSelectedCategory(category.name)}
                  >
                    <Icon className="h-4 w-4 mr-2" />
                    {category.name}
                    <Badge variant="secondary" className="ml-auto">
                      {category.count}
                    </Badge>
                  </Button>
                );
              })}
            </CardContent>
          </Card>

          {/* Quick Links */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="text-lg">Quick Links</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="ghost" className="w-full justify-start" asChild>
                <a href="/contact">
                  <Mail className="h-4 w-4 mr-2" />
                  Contact Support
                </a>
              </Button>
              <Button variant="ghost" className="w-full justify-start" asChild>
                <a href="/dashboard/prayers">
                  <Church className="h-4 w-4 mr-2" />
                  Prayer Room
                </a>
              </Button>
              <Button variant="ghost" className="w-full justify-start" asChild>
                <a href="/products">
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  Browse Products
                </a>
              </Button>
              <Button variant="ghost" className="w-full justify-start" asChild>
                <a href="/dashboard/settings">
                  <Settings className="h-4 w-4 mr-2" />
                  Account Settings
                </a>
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* FAQ Content */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Frequently Asked Questions</span>
                <Badge variant="outline">
                  {filteredFAQs.length} {filteredFAQs.length === 1 ? 'result' : 'results'}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {filteredFAQs.length > 0 ? (
                <div className="space-y-4">
                  {filteredFAQs.map((faq) => (
                    <Collapsible
                      key={faq.id}
                      open={openItems.includes(faq.id)}
                      onOpenChange={() => toggleItem(faq.id)}
                    >
                      <CollapsibleTrigger asChild>
                        <Button
                          variant="ghost"
                          className="w-full justify-between p-4 h-auto text-left border rounded-lg hover:bg-gray-50"
                        >
                          <div className="flex-1">
                            <h3 className="font-medium text-gray-900 mb-1">
                              {faq.question}
                            </h3>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">
                                {faq.category}
                              </Badge>
                              {faq.tags.slice(0, 2).map(tag => (
                                <Badge key={tag} variant="secondary" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          {openItems.includes(faq.id) ? (
                            <ChevronDown className="h-5 w-5 text-gray-500" />
                          ) : (
                            <ChevronRight className="h-5 w-5 text-gray-500" />
                          )}
                        </Button>
                      </CollapsibleTrigger>
                      <CollapsibleContent className="px-4 pb-4">
                        <div className="pt-3 border-t">
                          <p className="text-gray-700 leading-relaxed">
                            {faq.answer}
                          </p>
                        </div>
                      </CollapsibleContent>
                    </Collapsible>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
                  <p className="text-gray-500 mb-4">
                    Try adjusting your search terms or browse different categories.
                  </p>
                  <Button variant="outline" onClick={() => setSearchTerm('')}>
                    Clear Search
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Contact Support */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="pt-6">
          <div className="text-center">
            <MessageSquare className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Still need help?
            </h3>
            <p className="text-gray-600 mb-6">
              Our support team is here to help you with any questions or issues.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild>
                <a href="/contact">
                  <Mail className="h-4 w-4 mr-2" />
                  Contact Support
                </a>
              </Button>
              <Button variant="outline" asChild>
                <a href="mailto:<EMAIL>">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Email Us Directly
                </a>
              </Button>
            </div>
            <div className="mt-4 text-sm text-gray-500">
              <p>📧 <EMAIL></p>
              <p>📞 +27 11 234 5678</p>
              <p>📍 Morningside, Sandton, 2057, South Africa</p>
              <p>🕒 Mon-Fri: 8AM-5PM SAST</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
