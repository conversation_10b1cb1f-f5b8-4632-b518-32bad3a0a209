-- Test User Deletion Fix
-- Run this AFTER running fix-user-deletion-constraints.sql
-- This script tests that user deletion now works properly

-- ============================================================================
-- STEP 1: VERIFY FOREIGN KEY CONSTRAINTS ARE FIXED
-- ============================================================================

-- Check all foreign key constraints that reference auth.users
SELECT 
    '🔍 Foreign Key Constraints Audit' as info,
    tc.table_name,
    tc.constraint_name,
    kcu.column_name,
    CASE 
        WHEN rc.delete_rule = 'CASCADE' THEN '✅ CASCADE DELETE'
        WHEN rc.delete_rule = 'SET NULL' THEN '✅ SET NULL'
        WHEN rc.delete_rule = 'RESTRICT' THEN '❌ RESTRICT (PROBLEMATIC)'
        WHEN rc.delete_rule = 'NO ACTION' THEN '❌ NO ACTION (PROBLEMATIC)'
        ELSE '❓ UNKNOWN'
    END as deletion_behavior
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu 
    ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage ccu 
    ON ccu.constraint_name = tc.constraint_name
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
AND ccu.table_name = 'users'
AND ccu.table_schema = 'auth'
AND tc.table_schema = 'public'
ORDER BY tc.table_name;

-- ============================================================================
-- STEP 2: CHECK RLS POLICIES FOR USER DELETION
-- ============================================================================

-- Check if admin deletion policies exist
SELECT 
    '🔍 RLS Policies for User Deletion' as info,
    policyname,
    cmd,
    permissive,
    CASE 
        WHEN cmd = 'DELETE' THEN '✅ DELETE POLICY EXISTS'
        ELSE '📝 OTHER POLICY'
    END as policy_type
FROM pg_policies
WHERE tablename = 'users'
AND schemaname = 'public'
ORDER BY cmd;

-- ============================================================================
-- STEP 3: VERIFY HELPER FUNCTION EXISTS
-- ============================================================================

-- Check if the safe deletion function exists
SELECT 
    '🔍 Safe Deletion Function' as info,
    proname as function_name,
    CASE 
        WHEN proname = 'delete_user_safely' THEN '✅ FUNCTION EXISTS'
        ELSE '📝 OTHER FUNCTION'
    END as status
FROM pg_proc 
WHERE proname = 'delete_user_safely'
AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');

-- ============================================================================
-- STEP 4: CREATE TEST USER (OPTIONAL - FOR TESTING ONLY)
-- ============================================================================

-- UNCOMMENT THE FOLLOWING SECTION ONLY IF YOU WANT TO TEST WITH A REAL USER
-- WARNING: This will create a test user that you can then delete to verify the fix

/*
-- Create a test user in auth.users (this would normally be done through Supabase Auth)
-- NOTE: This is for testing purposes only - normally users are created through the auth system

DO $$
DECLARE
    test_user_id UUID := '00000000-0000-0000-0000-000000000001';
    test_email TEXT := '<EMAIL>';
BEGIN
    -- Insert test user into auth.users (if it doesn't exist)
    INSERT INTO auth.users (
        id, 
        email, 
        encrypted_password, 
        email_confirmed_at, 
        created_at, 
        updated_at,
        raw_user_meta_data
    ) VALUES (
        test_user_id,
        test_email,
        crypt('testpassword123', gen_salt('bf')),
        NOW(),
        NOW(),
        NOW(),
        '{"full_name": "Test User", "role": "user"}'::jsonb
    )
    ON CONFLICT (id) DO NOTHING;
    
    -- The trigger should automatically create the public.users record
    
    -- Create some test data in related tables
    INSERT INTO public.orders (id, user_id, items, shipping_details, total_amount) VALUES
    ('00000000-0000-0000-0000-000000000002', test_user_id, '[]'::jsonb, '{}'::jsonb, 100.00)
    ON CONFLICT DO NOTHING;
    
    INSERT INTO public.messages (id, sender_id, recipient_id, content) VALUES
    ('00000000-0000-0000-0000-000000000003', test_user_id, test_user_id, 'Test message')
    ON CONFLICT DO NOTHING;
    
    RAISE NOTICE '✅ Test user created with ID: %', test_user_id;
    RAISE NOTICE '📧 Test email: %', test_email;
    RAISE NOTICE '🗑️ You can now test deletion in Supabase dashboard';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Failed to create test user: %', SQLERRM;
END $$;
*/

-- ============================================================================
-- STEP 5: VERIFICATION SUMMARY
-- ============================================================================

DO $$
DECLARE
    constraint_count INTEGER;
    policy_count INTEGER;
    function_exists BOOLEAN;
BEGIN
    -- Count foreign key constraints with proper deletion behavior
    SELECT COUNT(*) INTO constraint_count
    FROM information_schema.table_constraints tc
    JOIN information_schema.referential_constraints rc
        ON tc.constraint_name = rc.constraint_name
    JOIN information_schema.constraint_column_usage ccu 
        ON ccu.constraint_name = tc.constraint_name
    WHERE tc.constraint_type = 'FOREIGN KEY'
    AND ccu.table_name = 'users'
    AND ccu.table_schema = 'auth'
    AND tc.table_schema = 'public'
    AND rc.delete_rule IN ('CASCADE', 'SET NULL');
    
    -- Count deletion policies
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies
    WHERE tablename = 'users'
    AND schemaname = 'public'
    AND cmd = 'DELETE';
    
    -- Check if function exists
    SELECT EXISTS(
        SELECT 1 FROM pg_proc 
        WHERE proname = 'delete_user_safely'
        AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
    ) INTO function_exists;
    
    -- Display summary
    RAISE NOTICE '==========================================';
    RAISE NOTICE '🔍 USER DELETION FIX VERIFICATION SUMMARY';
    RAISE NOTICE '==========================================';
    RAISE NOTICE '✅ Foreign key constraints fixed: % tables', constraint_count;
    RAISE NOTICE '✅ RLS deletion policies: % policies', policy_count;
    RAISE NOTICE '✅ Safe deletion function: %', CASE WHEN function_exists THEN 'EXISTS' ELSE 'MISSING' END;
    
    IF constraint_count > 0 AND policy_count > 0 AND function_exists THEN
        RAISE NOTICE '🎉 ALL CHECKS PASSED - User deletion should now work!';
        RAISE NOTICE '📝 You can now delete users from the Supabase dashboard';
        RAISE NOTICE '🔧 Or use: SELECT delete_user_safely(''user-id-here'');';
    ELSE
        RAISE NOTICE '❌ Some checks failed - please run fix-user-deletion-constraints.sql first';
    END IF;
    
    RAISE NOTICE '==========================================';
END $$;
