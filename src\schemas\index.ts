/**
 * Zod schemas for Tennis-Gear database tables
 * 
 * This file exports Zod schemas that match the database structure
 * and provide runtime type validation for our application.
 */

import { z } from 'zod';

// User schema
export const userSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  full_name: z.string().nullable(),
  role: z.enum(['admin', 'mentor', 'student', 'user']).default('user'),
  admin_role: z.enum(['admin', 'senior_admin', 'junior_admin']).nullable(),
  created_at: z.string().datetime().nullable(),
  updated_at: z.string().datetime().nullable(),
});

// Admin activity log schema
export const adminActivityLogSchema = z.object({
  id: z.string().uuid(),
  admin_id: z.string().uuid(),
  action_type: z.enum(['order_management', 'product_management', 'user_management', 'system_config']),
  action_description: z.string(),
  target_id: z.string().uuid().nullable(),
  target_type: z.enum(['order', 'product', 'user', 'system']).nullable(),
  metadata: z.record(z.any()).nullable(),
  ip_address: z.string().nullable(),
  user_agent: z.string().nullable(),
  success: z.boolean().default(true),
  error_message: z.string().nullable(),
  created_at: z.string().datetime(),
});

// Admin report schema
export const adminReportSchema = z.object({
  id: z.string().uuid(),
  generated_by: z.string().uuid(),
  report_type: z.enum(['activity_summary', 'performance_metrics', 'audit_trail']),
  report_name: z.string(),
  filters: z.record(z.any()).nullable(),
  file_path: z.string().nullable(),
  file_format: z.enum(['pdf', 'excel']),
  download_count: z.number().default(0),
  created_at: z.string().datetime(),
  expires_at: z.string().datetime().nullable(),
});

export type User = z.infer<typeof userSchema>;

// Product schema
export const productSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1, "Product name is required"),
  description: z.string().nullable(),
  price: z.number().positive("Price must be positive"),
  category: z.string().min(1, "Category is required"),
  stock_quantity: z.number().int().nonnegative("Stock cannot be negative"),
  images: z.array(z.string()).default([]),
  created_at: z.string().datetime().nullable(),
  updated_at: z.string().datetime().nullable(),
});

export type Product = z.infer<typeof productSchema>;

// Order schema
export const orderSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  status: z.enum(['pending', 'processing', 'completed', 'cancelled']),
  total_amount: z.number().positive("Total amount must be positive"),
  created_at: z.string().datetime().nullable(),
  updated_at: z.string().datetime().nullable(),
});

export type Order = z.infer<typeof orderSchema>;

// Order Item schema
export const orderItemSchema = z.object({
  id: z.string().uuid(),
  order_id: z.string().uuid(),
  product_id: z.string().uuid(),
  quantity: z.number().int().positive("Quantity must be positive"),
  unit_price: z.number().positive("Unit price must be positive"),
  created_at: z.string().datetime().nullable(),
});

export type OrderItem = z.infer<typeof orderItemSchema>;

// Mentorship Program schema
export const mentorshipProgramSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1, "Program name is required"),
  description: z.string().nullable(),
  duration_months: z.number().int().positive("Duration must be positive"),
  price_monthly: z.number().positive("Monthly price must be positive"),
  price_upfront: z.number().positive("Upfront price must be positive").nullable(),
  created_at: z.string().datetime().nullable(),
  updated_at: z.string().datetime().nullable(),
});

export type MentorshipProgram = z.infer<typeof mentorshipProgramSchema>;

// Student Enrollment schema
export const studentEnrollmentSchema = z.object({
  id: z.string().uuid(),
  student_id: z.string().uuid(),
  program_id: z.string().uuid(),
  mentor_id: z.string().uuid(),
  start_date: z.string().datetime(),
  end_date: z.string().datetime(),
  payment_type: z.enum(['monthly', 'upfront']),
  status: z.enum(['active', 'completed', 'cancelled']),
  created_at: z.string().datetime().nullable(),
  updated_at: z.string().datetime().nullable(),
});

export type StudentEnrollment = z.infer<typeof studentEnrollmentSchema>;

// Mentorship Session schema
export const mentorshipSessionSchema = z.object({
  id: z.string().uuid(),
  enrollment_id: z.string().uuid(),
  scheduled_at: z.string().datetime(),
  duration_minutes: z.number().int().positive("Duration must be positive"),
  status: z.enum(['scheduled', 'completed', 'cancelled']),
  notes: z.string().nullable(),
  created_at: z.string().datetime().nullable(),
  updated_at: z.string().datetime().nullable(),
});

export type MentorshipSession = z.infer<typeof mentorshipSessionSchema>;

// Resource schema
export const resourceSchema = z.object({
  id: z.string().uuid(),
  title: z.string().min(1, "Title is required"),
  description: z.string().nullable(),
  type: z.enum(['document', 'video', 'training', 'progress']),
  category: z.string().min(1, "Category is required"),
  format: z.string().min(1, "Format is required"),
  file_path: z.string().min(1, "File path is required"),
  size_bytes: z.number().int().nonnegative("Size cannot be negative"),
  download_count: z.number().int().nonnegative().default(0),
  created_by: z.string().uuid(),
  created_at: z.string().datetime().nullable(),
  updated_at: z.string().datetime().nullable(),
});

export type Resource = z.infer<typeof resourceSchema>;

// Chat Message schema
export const chatMessageSchema = z.object({
  id: z.string().uuid(),
  sender_id: z.string().uuid(),
  receiver_id: z.string().uuid(),
  content: z.string().min(1, "Message content is required"),
  read_at: z.string().datetime().nullable(),
  created_at: z.string().datetime().nullable(),
});

export type ChatMessage = z.infer<typeof chatMessageSchema>;

// User Profile schema
export const userProfileSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  avatar_url: z.string().nullable(),
  phone_number: z.string().nullable(),
  address: z.string().nullable(),
  city: z.string().nullable(),
  state: z.string().nullable(),
  postal_code: z.string().nullable(),
  country: z.string().nullable(),
  date_of_birth: z.string().datetime().nullable(),
  bio: z.string().nullable(),
  preferences: z.record(z.any()).nullable(),
  created_at: z.string().datetime().nullable(),
  updated_at: z.string().datetime().nullable(),
});

export type UserProfile = z.infer<typeof userProfileSchema>;
