import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Package,
  Users,
  ShoppingCart,
  Download,
  Eye,
  Calendar,
  BarChart3,
  PieChart,
  Activity
} from 'lucide-react';
import { supabase } from '../../../../supabase/supabase';

interface ProductMetrics {
  id: string;
  name: string;
  product_type: 'physical' | 'digital';
  category?: { name: string; color: string };
  total_sales: number;
  total_revenue: number;
  units_sold: number;
  conversion_rate: number;
  avg_order_value: number;
  views: number;
  digital_downloads?: number;
  last_sale_date?: string;
  trend: 'up' | 'down' | 'stable';
  trend_percentage: number;
}

interface SalesData {
  date: string;
  revenue: number;
  units: number;
  digital_downloads: number;
}

export function ProductAnalytics() {
  const [metrics, setMetrics] = useState<ProductMetrics[]>([]);
  const [salesData, setSalesData] = useState<SalesData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30d');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [categories, setCategories] = useState<any[]>([]);

  useEffect(() => {
    loadAnalytics();
    loadCategories();
  }, [timeRange, selectedCategory]);

  const loadCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('product_categories')
        .select('*')
        .order('name');

      if (error) throw error;
      setCategories(data || []);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const loadAnalytics = async () => {
    try {
      setIsLoading(true);
      
      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
      startDate.setDate(endDate.getDate() - days);

      // Load product metrics with sales data
      const { data: products, error: productsError } = await supabase
        .from('products')
        .select(`
          id,
          name,
          product_type,
          price,
          views,
          category:product_categories(name, color),
          orders:order_items(
            quantity,
            price,
            order:orders(
              created_at,
              status
            )
          )
        `)
        .eq('status', 'active');

      if (productsError) throw productsError;

      // Process metrics for each product
      const processedMetrics: ProductMetrics[] = products.map(product => {
        const validOrders = product.orders?.filter(item => 
          item.order?.status === 'completed' &&
          new Date(item.order.created_at) >= startDate
        ) || [];

        const totalRevenue = validOrders.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const unitsSold = validOrders.reduce((sum, item) => sum + item.quantity, 0);
        const totalSales = validOrders.length;

        // Calculate trend (simplified - comparing with previous period)
        const midDate = new Date(startDate.getTime() + (endDate.getTime() - startDate.getTime()) / 2);
        const recentOrders = validOrders.filter(item => new Date(item.order.created_at) >= midDate);
        const oldOrders = validOrders.filter(item => new Date(item.order.created_at) < midDate);
        
        const recentRevenue = recentOrders.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const oldRevenue = oldOrders.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        
        let trend: 'up' | 'down' | 'stable' = 'stable';
        let trendPercentage = 0;
        
        if (oldRevenue > 0) {
          trendPercentage = ((recentRevenue - oldRevenue) / oldRevenue) * 100;
          trend = trendPercentage > 5 ? 'up' : trendPercentage < -5 ? 'down' : 'stable';
        } else if (recentRevenue > 0) {
          trend = 'up';
          trendPercentage = 100;
        }

        return {
          id: product.id,
          name: product.name,
          product_type: product.product_type,
          category: product.category,
          total_sales: totalSales,
          total_revenue: totalRevenue,
          units_sold: unitsSold,
          conversion_rate: product.views > 0 ? (totalSales / product.views) * 100 : 0,
          avg_order_value: totalSales > 0 ? totalRevenue / totalSales : 0,
          views: product.views || 0,
          digital_downloads: product.product_type === 'digital' ? unitsSold : undefined,
          last_sale_date: validOrders.length > 0 ? 
            Math.max(...validOrders.map(item => new Date(item.order.created_at).getTime())) : undefined,
          trend,
          trend_percentage: Math.abs(trendPercentage)
        };
      });

      // Sort by revenue
      processedMetrics.sort((a, b) => b.total_revenue - a.total_revenue);
      setMetrics(processedMetrics);

      // Generate daily sales data for charts
      const dailySales: SalesData[] = [];
      for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
        const dayStart = new Date(d);
        const dayEnd = new Date(d);
        dayEnd.setHours(23, 59, 59, 999);

        const dayOrders = products.flatMap(p => p.orders || []).filter(item =>
          item.order?.status === 'completed' &&
          new Date(item.order.created_at) >= dayStart &&
          new Date(item.order.created_at) <= dayEnd
        );

        const dayRevenue = dayOrders.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const dayUnits = dayOrders.reduce((sum, item) => sum + item.quantity, 0);
        const digitalDownloads = dayOrders.filter(item => 
          products.find(p => p.id === item.product_id)?.product_type === 'digital'
        ).reduce((sum, item) => sum + item.quantity, 0);

        dailySales.push({
          date: d.toISOString().split('T')[0],
          revenue: dayRevenue,
          units: dayUnits,
          digital_downloads: digitalDownloads
        });
      }

      setSalesData(dailySales);
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'down': return <TrendingDown className="w-4 h-4 text-red-600" />;
      default: return <Activity className="w-4 h-4 text-gray-600" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up': return 'text-green-600';
      case 'down': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  // Filter metrics based on selected category
  const filteredMetrics = selectedCategory === 'all' 
    ? metrics 
    : metrics.filter(m => m.category?.name === selectedCategory);

  // Calculate summary stats
  const totalRevenue = filteredMetrics.reduce((sum, m) => sum + m.total_revenue, 0);
  const totalUnits = filteredMetrics.reduce((sum, m) => sum + m.units_sold, 0);
  const totalViews = filteredMetrics.reduce((sum, m) => sum + m.views, 0);
  const avgConversion = filteredMetrics.length > 0 
    ? filteredMetrics.reduce((sum, m) => sum + m.conversion_rate, 0) / filteredMetrics.length 
    : 0;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 p-3 sm:p-4 lg:p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">Product Analytics</h1>
          <p className="text-sm sm:text-base text-gray-600">Track product performance and sales metrics</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-full sm:w-32 text-sm">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-full sm:w-40 text-sm">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category.id} value={category.name}>
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: category.color }}
                    />
                    {category.name}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
        <Card>
          <CardContent className="p-3 sm:p-4 lg:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-lg sm:text-2xl font-bold text-gray-900">
                  {formatCurrency(totalRevenue)}
                </p>
              </div>
              <DollarSign className="h-6 w-6 sm:h-8 sm:w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 sm:p-4 lg:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Units Sold</p>
                <p className="text-lg sm:text-2xl font-bold text-gray-900">{totalUnits}</p>
              </div>
              <ShoppingCart className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 sm:p-4 lg:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Total Views</p>
                <p className="text-lg sm:text-2xl font-bold text-gray-900">{totalViews.toLocaleString()}</p>
              </div>
              <Eye className="h-6 w-6 sm:h-8 sm:w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 sm:p-4 lg:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Avg Conversion</p>
                <p className="text-lg sm:text-2xl font-bold text-gray-900">{avgConversion.toFixed(1)}%</p>
              </div>
              <TrendingUp className="h-6 w-6 sm:h-8 sm:w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Product Performance Table */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg sm:text-xl">Product Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredMetrics.map((product) => (
              <div key={product.id} className="flex flex-col sm:flex-row sm:items-center justify-between p-4 border rounded-lg gap-4">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="font-medium text-sm sm:text-base truncate">{product.name}</h3>
                    <Badge variant={product.product_type === 'digital' ? 'default' : 'secondary'} className="text-xs">
                      {product.product_type}
                    </Badge>
                    {product.category && (
                      <div className="flex items-center gap-1">
                        <div
                          className="w-2 h-2 rounded-full"
                          style={{ backgroundColor: product.category.color }}
                        />
                        <span className="text-xs text-gray-500">{product.category.name}</span>
                      </div>
                    )}
                  </div>

                  <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500 text-xs">Revenue</p>
                      <p className="font-semibold">{formatCurrency(product.total_revenue)}</p>
                    </div>
                    <div>
                      <p className="text-gray-500 text-xs">Units Sold</p>
                      <p className="font-semibold">{product.units_sold}</p>
                    </div>
                    <div>
                      <p className="text-gray-500 text-xs">Views</p>
                      <p className="font-semibold">{product.views.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-gray-500 text-xs">Conversion</p>
                      <p className="font-semibold">{product.conversion_rate.toFixed(1)}%</p>
                    </div>
                  </div>

                  {product.product_type === 'digital' && product.digital_downloads && (
                    <div className="mt-2">
                      <div className="flex items-center gap-2 text-sm">
                        <Download className="w-4 h-4 text-blue-600" />
                        <span className="text-gray-600">Downloads: {product.digital_downloads}</span>
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-3">
                  <div className="text-right">
                    <div className="flex items-center gap-1">
                      {getTrendIcon(product.trend)}
                      <span className={`text-sm font-medium ${getTrendColor(product.trend)}`}>
                        {product.trend_percentage.toFixed(1)}%
                      </span>
                    </div>
                    <p className="text-xs text-gray-500">vs prev period</p>
                  </div>

                  <div className="text-right">
                    <p className="text-sm text-gray-500">Avg Order</p>
                    <p className="font-semibold">{formatCurrency(product.avg_order_value)}</p>
                  </div>
                </div>
              </div>
            ))}

            {filteredMetrics.length === 0 && (
              <div className="text-center py-8">
                <BarChart3 className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No data available</h3>
                <p className="mt-1 text-sm text-gray-500">
                  No sales data found for the selected time period and filters.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Top Performers */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Top Revenue Generators</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {filteredMetrics.slice(0, 5).map((product, index) => (
                <div key={product.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-xs font-bold">
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-medium text-sm truncate max-w-32 sm:max-w-48">{product.name}</p>
                      <p className="text-xs text-gray-500">{product.units_sold} units</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-sm">{formatCurrency(product.total_revenue)}</p>
                    <div className="flex items-center gap-1">
                      {getTrendIcon(product.trend)}
                      <span className={`text-xs ${getTrendColor(product.trend)}`}>
                        {product.trend_percentage.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Best Converting Products</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {filteredMetrics
                .filter(p => p.views > 0)
                .sort((a, b) => b.conversion_rate - a.conversion_rate)
                .slice(0, 5)
                .map((product, index) => (
                <div key={product.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 rounded-full bg-green-100 text-green-600 flex items-center justify-center text-xs font-bold">
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-medium text-sm truncate max-w-32 sm:max-w-48">{product.name}</p>
                      <p className="text-xs text-gray-500">{product.views} views</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-sm">{product.conversion_rate.toFixed(1)}%</p>
                    <p className="text-xs text-gray-500">{product.total_sales} sales</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
