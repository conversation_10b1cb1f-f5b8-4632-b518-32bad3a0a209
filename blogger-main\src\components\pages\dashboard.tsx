import React, { useState, useEffect } from "react";
import { Routes, Route, useLocation, useNavigate } from "react-router-dom";
import Sidebar from "../dashboard/layout/Sidebar";
import { Navbar } from "@/components/layout/Navbar";

import { CreateArticle } from "../dashboard/pages/CreateArticle";
import { MyArticles } from "../dashboard/pages/MyArticles";
import { Favorites } from "../dashboard/pages/Favorites";
import { DigitalLibrary } from "../dashboard/pages/DigitalLibrary";
import { Comments } from "../dashboard/pages/Comments";
import { OrderHistory } from "../dashboard/pages/OrderHistory";
import { PrayerRoom } from "../dashboard/pages/PrayerRoom";
import { Analytics } from "../dashboard/pages/Analytics";
import { Subscription } from "../dashboard/pages/Subscription";
import { Settings } from "../dashboard/pages/Settings";
import { PrivacySettings } from "../dashboard/pages/PrivacySettings";
import { Help } from "../dashboard/pages/Help";
import AdminPanel from "../dashboard/AdminPanel"; // Use the consolidated admin panel
import { UsersManagement } from "../dashboard/pages/UsersManagement";
import { UserDashboard } from "../dashboard/pages/UserDashboard";
import { useAuth } from "../../../supabase/auth";
import { Button } from "@/components/ui/button";
import { Menu, X } from "lucide-react";

// Admin-only components
import { EcommerceDashboard } from "../dashboard/admin/EcommerceDashboard";
import { ProductManagement } from "../dashboard/admin/ProductManagement";
import { CreateProduct } from "../dashboard/admin/CreateProduct";
import { ProductAnalytics } from "../dashboard/admin/ProductAnalytics";
import { OrderManagement } from "../dashboard/admin/OrderManagement";
import { ContactMessages } from "../dashboard/admin/ContactMessages";
import { Monitoring } from "../dashboard/admin/Monitoring";
import { Performance } from "../dashboard/admin/Performance";
import { Privacy } from "../dashboard/admin/Privacy";

const Dashboard = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const { user, isAdmin } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  // Check if mobile and handle responsive breakpoints
  useEffect(() => {
    const checkMobile = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);

      // Auto-collapse sidebar on mobile and small tablets
      if (width < 768) {
        setSidebarCollapsed(true);
        setSidebarOpen(false);
      } else if (width >= 768 && width < 1024) {
        // Tablet view - keep sidebar collapsed but allow toggle
        setSidebarCollapsed(true);
      } else {
        // Desktop view - expand sidebar by default
        setSidebarCollapsed(false);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Close mobile sidebar when route changes
  useEffect(() => {
    if (isMobile) {
      setSidebarOpen(false);
    }
  }, [location.pathname, isMobile]);

  // Handle click outside sidebar to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      const sidebar = document.querySelector('[data-sidebar]');
      const menuButton = document.querySelector('[data-menu-button]');

      if (
        isMobile &&
        sidebarOpen &&
        sidebar &&
        !sidebar.contains(target) &&
        !menuButton?.contains(target)
      ) {
        setSidebarOpen(false);
      }
    };

    if (isMobile && sidebarOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isMobile, sidebarOpen]);

  // Redirect admin users to admin panel when they access dashboard root
  useEffect(() => {
    if (user && isAdmin && location.pathname === '/dashboard') {
      console.log('Redirecting admin user to admin panel');
      navigate('/dashboard/admin', { replace: true });
    } else if (user && !isAdmin && location.pathname === '/dashboard/admin') {
      console.log('Redirecting regular user to user dashboard');
      navigate('/dashboard', { replace: true });
    }
  }, [user, isAdmin, location.pathname, navigate]);

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar variant="dashboard" />

      <div className="flex h-[calc(100vh-64px)] relative">
        {/* Mobile/Tablet Overlay */}
        {(isMobile || window.innerWidth < 1024) && sidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Sidebar */}
        <div
          data-sidebar
          className={`
            ${isMobile || window.innerWidth < 1024 ? 'fixed inset-y-0 left-0 z-50' : 'relative'}
            ${(isMobile || window.innerWidth < 1024) && !sidebarOpen ? '-translate-x-full' : 'translate-x-0'}
            transition-transform duration-300 ease-in-out
            ${isMobile ? 'w-64' : sidebarCollapsed ? 'w-16' : 'w-[280px]'}
            bg-white border-r border-gray-200 shadow-lg lg:shadow-none
          `}
        >
          <Sidebar
            isAdmin={isAdmin}
            collapsed={!isMobile && sidebarCollapsed}
            onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
            isMobile={isMobile}
            onMobileClose={() => setSidebarOpen(false)}
          />
        </div>

        {/* Main Content */}
        <main className="flex-1 overflow-auto min-w-0">
          {/* Mobile/Tablet Menu Button */}
          {(isMobile || window.innerWidth < 1024) && (
            <div className="sticky top-0 z-30 p-3 sm:p-4 border-b border-gray-200 bg-white/95 backdrop-blur-sm">
              <Button
                data-menu-button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="flex items-center"
              >
                {sidebarOpen ? <X className="h-4 w-4 sm:h-5 sm:w-5" /> : <Menu className="h-4 w-4 sm:h-5 sm:w-5" />}
                <span className="ml-2 text-sm sm:text-base">Menu</span>
              </Button>
            </div>
          )}

          <div className="container mx-auto p-3 sm:p-4 lg:p-6 max-w-full">
            <Routes>
              {/* Default route - redirect based on user role */}
              <Route
                path=""
                element={isAdmin ? <AdminPanel /> : <UserDashboard />}
              />

              {/* Common routes for all authenticated users */}
              <Route path="analytics" element={<Analytics />} />
              <Route path="settings" element={<Settings />} />
              <Route path="privacy-settings" element={<PrivacySettings />} />
              <Route path="help" element={<Help />} />

              {/* Admin-only routes */}
              {isAdmin ? (
                <>
                  <Route path="admin" element={<AdminPanel />} />
                  <Route path="content" element={<MyArticles />} />
                  <Route path="articles" element={<MyArticles />} />
                  <Route path="create-article" element={<CreateArticle />} />
                  <Route path="ecommerce" element={<EcommerceDashboard />} />
                  <Route path="products" element={<ProductManagement />} />
                  <Route path="products/create" element={<CreateProduct />} />
                  <Route path="product-analytics" element={<ProductAnalytics />} />
                  <Route path="orders" element={<OrderManagement />} />
                  <Route path="users" element={<UsersManagement />} />
                  <Route path="prayers" element={<PrayerRoom />} />
                  <Route path="messages" element={<ContactMessages />} />
                  <Route path="monitoring" element={<Monitoring />} />
                  <Route path="performance" element={<Performance />} />
                  <Route path="privacy" element={<Privacy />} />
                </>
              ) : (
                <>
                  {/* User-only routes */}
                  <Route path="favorites" element={<Favorites />} />
                  <Route path="digital-library" element={<DigitalLibrary />} />
                  <Route path="comments" element={<Comments />} />
                  <Route path="orders" element={<OrderHistory />} />
                  <Route path="prayers" element={<PrayerRoom />} />
                  <Route path="subscription" element={<Subscription />} />
                </>
              )}
            </Routes>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Dashboard;
