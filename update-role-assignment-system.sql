-- Update Role Assignment System for Context-Based User Roles
-- Run this in your Supabase SQL Editor to implement automatic role assignment

-- 1. Update user_role enum to include 'student' value
DO $$
BEGIN
    -- Check if 'student' value exists in the enum
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum
        WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')
        AND enumlabel = 'student'
    ) THEN
        -- Add 'student' to existing enum
        ALTER TYPE user_role ADD VALUE 'student';
        RAISE NOTICE 'Added student value to user_role enum';
    ELSE
        RAISE NOTICE 'Student value already exists in user_role enum';
    END IF;
END $$;

-- 2. Update the handle_new_user function for context-based role assignment
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  user_role_value TEXT;
  signup_context TEXT;
BEGIN
  -- Only proceed if this is a real user signup (has email)
  IF NEW.email IS NULL OR NEW.email = '' THEN
    RETURN NEW;
  END IF;
  
  -- Get signup context from metadata
  signup_context := NEW.raw_user_meta_data->>'signup_context';
  
  -- Determine role based on context and metadata
  IF NEW.raw_user_meta_data->>'role' IS NOT NULL THEN
    -- Explicit role provided (e.g., admin signup)
    user_role_value := NEW.raw_user_meta_data->>'role';
  ELSIF signup_context = 'mentorship' THEN
    -- Mentorship program signup
    user_role_value := 'student';
  ELSIF signup_context = 'checkout' THEN
    -- Checkout/purchase signup
    user_role_value := 'user';
  ELSE
    -- Default role for regular signup
    user_role_value := 'user';
  END IF;
  
  -- Insert new user profile with data from auth.users
  INSERT INTO public.users (id, email, full_name, name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    user_role_value::user_role
  )
  ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = COALESCE(EXCLUDED.full_name, public.users.full_name),
    name = COALESCE(EXCLUDED.name, public.users.name),
    role = COALESCE(EXCLUDED.role::TEXT, public.users.role::TEXT)::user_role,
    updated_at = NOW();
  
  RETURN NEW;
  
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the auth.users insertion
    RAISE WARNING 'Failed to create user profile for %: % (SQLSTATE: %)', NEW.id, SQLERRM, SQLSTATE;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Ensure the trigger is properly set up
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 4. Grant necessary permissions for the new role
GRANT USAGE ON TYPE user_role TO authenticated;
GRANT USAGE ON TYPE user_role TO anon;

-- 5. Verify the setup
SELECT 
  'user_role enum values' as info,
  enumlabel as role_value
FROM pg_enum
WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')
ORDER BY enumsortorder;

-- 6. Test the trigger function (optional verification)
SELECT 
  'Trigger function updated' as status,
  'handle_new_user' as function_name,
  'Context-based role assignment enabled' as description;
