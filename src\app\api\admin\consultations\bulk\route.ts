import { NextRequest, NextResponse } from 'next/server';
import { createClient, createServiceRoleClient } from '@/utils/supabase/server';
import { consultationBulkActionSchema } from '@/types/consultations';
import { SystemActivityLogger } from '@/utils/admin-activity-logger';

/**
 * POST /api/admin/consultations/bulk
 * Perform bulk actions on multiple consultations
 */
export async function POST(request: NextRequest) {
  try {
    console.log('POST /api/admin/consultations/bulk - Starting request');

    // Verify admin authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const { data: userData } = await supabase
      .from('users')
      .select('role, admin_role')
      .eq('id', session.user.id)
      .single();

    if (!userData || userData.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    
    // Validate the bulk action data
    const validationResult = consultationBulkActionSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid bulk action data', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { consultation_ids, action, reason } = validationResult.data;

    if (consultation_ids.length === 0) {
      return NextResponse.json(
        { error: 'No consultations selected' },
        { status: 400 }
      );
    }

    // Use service role client for database operations
    const serviceSupabase = createServiceRoleClient();

    // Get current consultations for logging
    const { data: consultations } = await serviceSupabase
      .from('consultations')
      .select('id, first_name, last_name, email, status')
      .in('id', consultation_ids);

    if (!consultations || consultations.length === 0) {
      return NextResponse.json(
        { error: 'No valid consultations found' },
        { status: 404 }
      );
    }

    // Determine the new status based on action
    let newStatus: string;
    let updateData: any = {
      updated_at: new Date().toISOString(),
    };

    switch (action) {
      case 'confirm':
        newStatus = 'confirmed';
        updateData.status = newStatus;
        break;
      case 'cancel':
        newStatus = 'cancelled';
        updateData.status = newStatus;
        if (reason) {
          updateData.cancellation_reason = reason;
        }
        break;
      case 'mark_completed':
        newStatus = 'completed';
        updateData.status = newStatus;
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid bulk action' },
          { status: 400 }
        );
    }

    // Perform the bulk update
    const { data: updatedConsultations, error } = await serviceSupabase
      .from('consultations')
      .update(updateData)
      .in('id', consultation_ids)
      .select();

    if (error) {
      console.error('POST /api/admin/consultations/bulk - Database error:', error);
      return NextResponse.json({ error: 'Failed to perform bulk action' }, { status: 500 });
    }

    // Log the activity for each consultation
    try {
      const logPromises = consultations.map(consultation => {
        const customerName = `${consultation.first_name} ${consultation.last_name} (${consultation.email})`;
        const description = `Bulk action: ${action} - Status changed from ${consultation.status} to ${newStatus} for ${customerName}`;
        
        return SystemActivityLogger.consultationUpdated(
          consultation.id,
          description,
          session.user.id
        );
      });

      await Promise.all(logPromises);
    } catch (logError) {
      console.error('Failed to log bulk activity:', logError);
      // Don't fail the request if logging fails
    }

    console.log(`POST /api/admin/consultations/bulk - Bulk action ${action} completed for ${consultation_ids.length} consultations`);

    return NextResponse.json({
      success: true,
      message: `Bulk action ${action} completed successfully`,
      updated_count: updatedConsultations?.length || 0,
      consultations: updatedConsultations,
    });

  } catch (error: any) {
    console.error('POST /api/admin/consultations/bulk - Error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
