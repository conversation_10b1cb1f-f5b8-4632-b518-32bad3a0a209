#!/usr/bin/env node
// start-magic.js

const { spawn } = require("child_process");

// 1️⃣ Pull the key from the ENV
const API_KEY = process.env.MAGIC_API_KEY;
if (!API_KEY) {
  console.error(
    "\n❌  ERROR: Please set the MAGIC_API_KEY environment variable first.\n"
  );
  process.exit(1);
}

// 2️⃣ Use `npm exec` instead of `npx`
const child = spawn(
  "npm",
  [
    "exec",            // run a binary from npm
    "--",              // end npm flags
    "@21st-dev/magic@latest"
  ],
  {
    shell: true,       // auto-pick cmd.exe on Win, /bin/sh on Mac/Linux
    stdio: "inherit",  // pipe MCP server logs straight to your console
    env: {
      ...process.env,  // keep everything else
      API_KEY,         // inject your key into the child’s env
    },
  }
);

child.on("exit", (code) => {
  console.log(`\nMagic server exited with code ${code}`);
  process.exit(code);
});
