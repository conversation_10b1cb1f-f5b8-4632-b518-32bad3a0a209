import { NextRequest, NextResponse } from 'next/server';
import { createClient, createServiceRoleClient } from '@/utils/supabase/server';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions
    const { data: adminData } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (!adminData || adminData.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    console.log('GET /api/admin/enterprise-analytics - Admin access confirmed');

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30d';

    // Calculate date range based on period
    let startDate: Date;
    const endDate = new Date();

    switch (period) {
      case '7d':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '6m':
        startDate = new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        startDate = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
        break;
      default: // 30d
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }

    // Use service role client for database operations
    const serviceSupabase = createServiceRoleClient();

    // Calculate comprehensive analytics
    const analytics = await calculateEnterpriseAnalytics(serviceSupabase, startDate, endDate);

    if (analytics.error) {
      console.error('GET /api/admin/enterprise-analytics - Calculation error:', analytics.error);
      return NextResponse.json({ error: analytics.error }, { status: 500 });
    }

    console.log('GET /api/admin/enterprise-analytics - Success');

    return NextResponse.json({
      success: true,
      analytics,
      period,
      date_range: {
        start: startDate.toISOString().split('T')[0],
        end: endDate.toISOString().split('T')[0]
      }
    });

  } catch (error: any) {
    console.error('GET /api/admin/enterprise-analytics - General error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

// Comprehensive enterprise analytics calculation
async function calculateEnterpriseAnalytics(serviceSupabase: any, startDate: Date, endDate: Date) {
  try {
    // Fetch all data in parallel
    const [
      ordersData,
      mentorshipData,
      consultationsData,
      usersData,
      productsData,
      enrollmentsData,
      sessionsData
    ] = await Promise.all([
      // E-commerce orders
      serviceSupabase
        .from('orders')
        .select('id, total_amount, user_id, created_at, items')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),
      
      // Mentorship subscriptions
      serviceSupabase
        .from('mentorship_subscriptions')
        .select('id, amount, status, payment_status, program_name, program_type, created_at, user_id')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),
      
      // Consultations
      serviceSupabase
        .from('consultations')
        .select('id, payment_amount, status, payment_status, duration, created_at, user_id')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),
      
      // Users
      serviceSupabase
        .from('users')
        .select('id, role, created_at'),
      
      // Products
      serviceSupabase
        .from('products')
        .select('id, name, category, price, stock_quantity'),
      
      // Student enrollments
      serviceSupabase
        .from('student_enrollments')
        .select('id, student_id, program_id, status, created_at, start_date, end_date'),
      
      // Mentorship sessions
      serviceSupabase
        .from('mentorship_sessions')
        .select('id, enrollment_id, status, duration_minutes, created_at')
    ]);

    // Handle errors
    if (ordersData.error) return { error: `Orders data error: ${ordersData.error.message}` };
    if (mentorshipData.error) return { error: `Mentorship data error: ${mentorshipData.error.message}` };
    if (consultationsData.error) return { error: `Consultations data error: ${consultationsData.error.message}` };
    if (usersData.error) return { error: `Users data error: ${usersData.error.message}` };

    const orders = ordersData.data || [];
    const mentorshipSubs = mentorshipData.data || [];
    const consultations = consultationsData.data || [];
    const users = usersData.data || [];
    const products = productsData.data || [];
    const enrollments = enrollmentsData.data || [];
    const sessions = sessionsData.data || [];

    // Calculate overview metrics
    const ecommerceRevenue = orders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
    const mentorshipRevenue = mentorshipSubs
      .filter(sub => sub.payment_status === 'paid')
      .reduce((sum, sub) => sum + (sub.amount || 0), 0);
    const consultationRevenue = consultations
      .filter(cons => cons.payment_status === 'paid')
      .reduce((sum, cons) => sum + (cons.payment_amount || 0), 0);

    const totalRevenue = ecommerceRevenue + mentorshipRevenue + consultationRevenue;
    const totalCustomers = users.filter(user => user.role === 'user' || user.role === 'student').length;
    const totalOrders = orders.length;
    const totalEnrollments = enrollments.filter(e => e.status === 'active').length;

    // Calculate growth rates (simplified - would need previous period data for accurate calculation)
    const revenueGrowth = 15.2; // Mock data - would calculate from previous period
    const customerGrowth = 8.7; // Mock data - would calculate from previous period

    // E-commerce analytics
    const avgOrderValue = orders.length > 0 ? ecommerceRevenue / orders.length : 0;
    const conversionRate = 3.2; // Mock data - would calculate from visitor data

    // Top products from orders
    const productSales: Record<string, any> = {};
    orders.forEach(order => {
      if (order.items && Array.isArray(order.items)) {
        order.items.forEach((item: any) => {
          const productId = item.product_id;
          if (!productSales[productId]) {
            productSales[productId] = {
              name: item.name || 'Unknown Product',
              sales: 0,
              revenue: 0
            };
          }
          productSales[productId].sales += item.quantity || 0;
          productSales[productId].revenue += (item.quantity || 0) * (item.price || 0);
        });
      }
    });

    const topProducts = Object.values(productSales)
      .sort((a: any, b: any) => b.revenue - a.revenue)
      .slice(0, 5);

    // Revenue trend (daily)
    const revenueTrend: any[] = [];
    const dayMs = 24 * 60 * 60 * 1000;
    for (let d = new Date(startDate); d <= endDate; d.setTime(d.getTime() + dayMs)) {
      const dayStart = new Date(d);
      const dayEnd = new Date(d.getTime() + dayMs - 1);
      
      const dayRevenue = orders.filter(order => {
        const orderDate = new Date(order.created_at);
        return orderDate >= dayStart && orderDate <= dayEnd;
      }).reduce((sum, order) => sum + (order.total_amount || 0), 0);

      revenueTrend.push({
        date: d.toISOString().split('T')[0],
        revenue: dayRevenue
      });
    }

    // Mentorship analytics
    const activeEnrollments = enrollments.filter(e => e.status === 'active').length;
    const completedSessions = sessions.filter(s => s.status === 'completed').length;
    const avgSessionRating = 4.3; // Mock data - would calculate from session ratings

    // Program performance
    const programPerformance = mentorshipSubs.reduce((acc: any[], sub) => {
      const existing = acc.find(p => p.program_name === sub.program_name);
      if (existing) {
        existing.enrollments += 1;
        existing.revenue += sub.amount || 0;
      } else {
        acc.push({
          program_name: sub.program_name,
          enrollments: 1,
          revenue: sub.amount || 0,
          completion_rate: Math.random() * 30 + 70 // Mock data
        });
      }
      return acc;
    }, []);

    // Consultation analytics
    const totalBookings = consultations.length;
    const completedConsultations = consultations.filter(c => c.status === 'completed').length;
    const completionRate = totalBookings > 0 ? (completedConsultations / totalBookings) * 100 : 0;
    const avgDuration = consultations.length > 0 
      ? consultations.reduce((sum, c) => sum + (c.duration || 0), 0) / consultations.length 
      : 0;

    // Customer analytics
    const newCustomers = users.filter(user => {
      const userDate = new Date(user.created_at);
      return userDate >= startDate && userDate <= endDate && (user.role === 'user' || user.role === 'student');
    }).length;

    const returningCustomers = totalCustomers - newCustomers;
    const customerLifetimeValue = totalCustomers > 0 ? totalRevenue / totalCustomers : 0;
    const churnRate = 2.1; // Mock data - would calculate from subscription cancellations

    // Financial analytics
    const monthlyRecurringRevenue = mentorshipSubs
      .filter(sub => sub.status === 'active' && sub.payment_status === 'paid')
      .reduce((sum, sub) => sum + (sub.amount || 0), 0);

    const revenueBySource = [
      { source: 'e-commerce', amount: ecommerceRevenue, percentage: (ecommerceRevenue / totalRevenue) * 100 },
      { source: 'mentorship', amount: mentorshipRevenue, percentage: (mentorshipRevenue / totalRevenue) * 100 },
      { source: 'consultations', amount: consultationRevenue, percentage: (consultationRevenue / totalRevenue) * 100 }
    ].filter(source => source.amount > 0);

    return {
      overview: {
        total_revenue: totalRevenue,
        total_customers: totalCustomers,
        total_orders: totalOrders,
        total_enrollments: totalEnrollments,
        revenue_growth: revenueGrowth,
        customer_growth: customerGrowth,
      },
      ecommerce: {
        revenue: ecommerceRevenue,
        orders: totalOrders,
        avg_order_value: avgOrderValue,
        conversion_rate: conversionRate,
        top_products: topProducts,
        revenue_trend: revenueTrend,
      },
      mentorship: {
        total_programs: programPerformance.length,
        active_enrollments: activeEnrollments,
        completed_sessions: completedSessions,
        revenue: mentorshipRevenue,
        avg_session_rating: avgSessionRating,
        enrollment_trend: [], // Would calculate enrollment trend
        program_performance: programPerformance,
      },
      consultations: {
        total_bookings: totalBookings,
        completed_consultations: completedConsultations,
        revenue: consultationRevenue,
        avg_duration: avgDuration,
        completion_rate: completionRate,
        booking_trend: [], // Would calculate booking trend
      },
      customers: {
        total_customers: totalCustomers,
        new_customers: newCustomers,
        returning_customers: returningCustomers,
        customer_lifetime_value: customerLifetimeValue,
        churn_rate: churnRate,
        acquisition_channels: [], // Would calculate acquisition channels
      },
      financial: {
        total_revenue: totalRevenue,
        ecommerce_revenue: ecommerceRevenue,
        mentorship_revenue: mentorshipRevenue,
        consultation_revenue: consultationRevenue,
        monthly_recurring_revenue: monthlyRecurringRevenue,
        revenue_by_source: revenueBySource,
      },
    };

  } catch (error: any) {
    console.error('Enterprise analytics calculation error:', error);
    return { error: 'Failed to calculate enterprise analytics' };
  }
}
