'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, ArrowRight, Database, FileCheck, Layers } from "lucide-react";
import Link from "next/link";

/**
 * Examples Page
 * 
 * This page provides links to example implementations of various technologies
 * used in the Tennis-Gear application.
 */
export default function ExamplesPage() {
  return (
    <div className="container py-10 space-y-8">
      <div className="flex items-center gap-2">
        <Link href="/admin">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-3xl font-bold tracking-tight">Implementation Examples</h1>
      </div>
      
      <p className="text-muted-foreground">
        These examples demonstrate the implementation of various technologies used in the Tennis-Gear application.
        Each example includes a live demo and usage guide to help you understand how to use these technologies in your own code.
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              TanStack Query with Supabase
            </CardTitle>
            <CardDescription>
              Data fetching and caching with TanStack Query and Supabase
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Learn how to use TanStack Query to fetch and cache data from Supabase.
              This example demonstrates how to create custom hooks for data fetching and mutations.
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/admin/examples/tanstack-query" className="w-full">
              <Button className="w-full">
                View Example
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </CardFooter>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileCheck className="h-5 w-5" />
              Zod Validation
            </CardTitle>
            <CardDescription>
              Form validation with Zod and React Hook Form
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Learn how to use Zod with React Hook Form for form validation.
              This example demonstrates how to create schemas and validate form inputs.
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/admin/examples/zod-validation" className="w-full">
              <Button className="w-full">
                View Example
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </CardFooter>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Layers className="h-5 w-5" />
              Full Stack Integration
            </CardTitle>
            <CardDescription>
              Complete integration of all technologies
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This example demonstrates how to integrate all technologies together:
              TanStack Query, Zod, React Hook Form, and Supabase.
            </p>
          </CardContent>
          <CardFooter>
            <Button className="w-full" disabled>
              Coming Soon
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>
      </div>
      
      <div className="bg-muted p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Implementation Notes</h2>
        <ul className="space-y-2 text-sm">
          <li>
            <strong>TanStack Query</strong> - Provides data fetching, caching, and synchronization capabilities.
          </li>
          <li>
            <strong>Zod</strong> - TypeScript-first schema validation with static type inference.
          </li>
          <li>
            <strong>React Hook Form</strong> - Performant, flexible and extensible forms with easy-to-use validation.
          </li>
          <li>
            <strong>Supabase</strong> - Open-source Firebase alternative with PostgreSQL database.
          </li>
        </ul>
      </div>
    </div>
  );
}
