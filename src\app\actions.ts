"use server";

import { encodedRedirect } from "@/utils/utils";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { createClient } from "../utils/supabase/server";

export const signUpAction = async (formData: FormData, signupContext?: string) => {
  const email = formData.get("email")?.toString();
  const password = formData.get("password")?.toString();
  const fullName = formData.get("full_name")?.toString() || '';
  const context = formData.get("signup_context")?.toString() || signupContext || 'regular';
  const supabase = await createClient();
  const origin = headers().get("origin");

  if (!email || !password) {
    return encodedRedirect(
      "error",
      "/sign-up",
      "Email and password are required",
    );
  }

  console.log("Signup with context:", context);

  const { data: { user }, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${origin}/auth/callback`,
      data: {
        full_name: fullName,
        email: email,
        signup_context: context, // Pass context to trigger function
      }
    },
  });

  console.log("After signUp", error);

  if (error) {
    console.error(error.code + " " + error.message);
    return encodedRedirect("error", "/sign-up", error.message);
  }

  if (user) {
    console.log("User created with context:", context);
    console.log("User metadata:", user.user_metadata);

    // Wait for the trigger to create the user profile
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Verify the user profile was created by the trigger
    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error("Error checking user profile:", profileError);

      if (profileError.code === 'PGRST116') {
        // User profile not found - trigger didn't work, try manual creation
        console.log("Trigger didn't create profile, attempting manual creation...");

        // Determine role based on context
        let userRole = 'user';
        if (context === 'mentorship') {
          userRole = 'student';
        } else if (context === 'checkout') {
          userRole = 'user';
        }

        try {
          const { error: createError } = await supabase
            .from('users')
            .insert({
              id: user.id,
              name: fullName,
              full_name: fullName,
              email: email,
              role: userRole,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (createError) {
            console.error('Error creating user profile manually:', createError);
          } else {
            console.log('User profile created manually with role:', userRole);
          }
        } catch (err) {
          console.error('Error in manual user profile creation:', err);
        }
      }
    } else {
      console.log("User profile created successfully by trigger:", userProfile);
      console.log("Assigned role:", userProfile.role);
    }
  }

  return encodedRedirect(
    "success",
    "/sign-up",
    "Thanks for signing up! Please check your email for a verification link.",
  );
};

// Specific signup action for checkout context
export const checkoutSignUpAction = async (formData: FormData) => {
  return signUpAction(formData, 'checkout');
};

// Specific signup action for mentorship context
export const mentorshipSignUpAction = async (formData: FormData) => {
  return signUpAction(formData, 'mentorship');
};

export const signInAction = async (formData: FormData) => {
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;
  const redirectTo = formData.get("redirect_to") as string || "/dashboard";
  const supabase = await createClient();

  const { error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    return encodedRedirect("error", "/sign-in", error.message);
  }

  return redirect(redirectTo);
};

export const forgotPasswordAction = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const supabase = await createClient();
  const origin = headers().get("origin");
  const callbackUrl = formData.get("callbackUrl")?.toString();

  if (!email) {
    return encodedRedirect("error", "/forgot-password", "Email is required");
  }

  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${origin}/auth/callback?redirect_to=/protected/reset-password`,
  });

  if (error) {
    console.error(error.message);
    return encodedRedirect(
      "error",
      "/forgot-password",
      "Could not reset password",
    );
  }

  if (callbackUrl) {
    return redirect(callbackUrl);
  }

  return encodedRedirect(
    "success",
    "/forgot-password",
    "Check your email for a link to reset your password.",
  );
};

export const resetPasswordAction = async (formData: FormData) => {
  const supabase = await createClient();

  const password = formData.get("password") as string;
  const confirmPassword = formData.get("confirmPassword") as string;

  if (!password || !confirmPassword) {
    encodedRedirect(
      "error",
      "/protected/reset-password",
      "Password and confirm password are required",
    );
  }

  if (password !== confirmPassword) {
    encodedRedirect(
      "error",
      "/dashboard/reset-password",
      "Passwords do not match",
    );
  }

  const { error } = await supabase.auth.updateUser({
    password: password,
  });

  if (error) {
    encodedRedirect(
      "error",
      "/dashboard/reset-password",
      "Password update failed",
    );
  }

  encodedRedirect("success", "/protected/reset-password", "Password updated");
};

export const signOutAction = async () => {
  const supabase = await createClient();
  await supabase.auth.signOut();
  return redirect("/sign-in");
};
