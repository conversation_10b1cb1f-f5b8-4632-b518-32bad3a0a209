"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { useCart } from "@/context/cart-context";
import { createClient } from "@/utils/supabase/client";
import { createOrderClient } from "@/utils/supabase/orders-client";
import { ShippingDetails } from "@/utils/supabase/orders";
import { CreditCard, Truck, ShieldCheck, Loader2 } from "lucide-react";
import { GlassmorphismCard } from "@/components/ui/glassmorphism-card";

export default function CheckoutPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { cartItems, subtotal, clearCart } = useCart();
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [shippingDetails, setShippingDetails] = useState<ShippingDetails>({
    name: "",
    email: "",
    address: "",
    city: "",
    postal_code: "",
    country: "South Africa",
    phone: "",
    alternative_phone: ""
  });

  // Calculate shipping and total
  const shipping = subtotal > 1000 ? 0 : 99.99;
  const total = subtotal + shipping;

  // Check if user is logged in
  useEffect(() => {
    async function checkUser() {
      const supabase = createClient();
      const { data } = await supabase.auth.getUser();
      
      if (data.user) {
        setUser(data.user);
        // Pre-fill email if available
        setShippingDetails(prev => ({
          ...prev,
          email: data.user.email || ""
        }));
      } else {
        // Redirect to login if not authenticated
        router.push("/sign-in?redirect=/checkout");
      }
    }
    
    checkUser();
  }, [router]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setShippingDetails(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (cartItems.length === 0) {
      toast({
        title: "Cart is empty",
        description: "Please add items to your cart before checking out.",
        variant: "destructive"
      });
      return;
    }
    
    // Validate form
    const requiredFields = ['name', 'email', 'address', 'city', 'postal_code', 'country'];
    const missingFields = requiredFields.filter(field => !shippingDetails[field as keyof ShippingDetails]);
    
    if (missingFields.length > 0) {
      toast({
        title: "Missing information",
        description: `Please fill in all required fields: ${missingFields.join(', ')}.`,
        variant: "destructive"
      });
      return;
    }
    
    setLoading(true);
    
    try {
      if (!user) {
        throw new Error("You must be logged in to place an order");
      }
      
      // Create Yoco payment session
      const response = await fetch('/api/yoco-checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          items: cartItems,
          shippingDetails,
          shippingMethod: 'standard',
          userId: user.id
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Something went wrong');
      }

      // Redirect to Yoco payment page
      if (data.url) {
        clearCart(); // Clear cart before redirecting
        window.location.href = data.url;
      } else {
        throw new Error('No checkout URL returned');
      }
    } catch (error: any) {
      console.error("Error placing order:", error);
      toast({
        title: "Error placing order",
        description: error.message || "An error occurred while placing your order. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Redirect to cart if empty
  useEffect(() => {
    if (cartItems.length === 0) {
      router.push("/cart");
    }
  }, [cartItems, router]);

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />

      <main className="flex-grow container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-foreground mb-8">Checkout</h1>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Shipping & Payment Form */}
          <div className="lg:col-span-2">
            <form onSubmit={handleSubmit}>
              {/* Shipping Information */}
              <div className="bg-card border border-border rounded-lg p-6 mb-8">
                <h2 className="text-xl font-semibold text-foreground mb-4 flex items-center gap-2">
                  <Truck className="h-5 w-5 text-primary" />
                  Shipping Information
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name *</Label>
                    <Input
                      id="name"
                      name="name"
                      value={shippingDetails.name}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={shippingDetails.email}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="address">Street Address *</Label>
                    <Input
                      id="address"
                      name="address"
                      value={shippingDetails.address}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="city">City *</Label>
                    <Input
                      id="city"
                      name="city"
                      value={shippingDetails.city}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="postal_code">Postal Code *</Label>
                    <Input
                      id="postal_code"
                      name="postal_code"
                      value={shippingDetails.postal_code}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="country">Country *</Label>
                    <Input
                      id="country"
                      name="country"
                      value={shippingDetails.country}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      name="phone"
                      value={shippingDetails.phone}
                      onChange={handleInputChange}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="alternative_phone">Alternative Phone Number</Label>
                    <Input
                      id="alternative_phone"
                      name="alternative_phone"
                      value={shippingDetails.alternative_phone || ""}
                      onChange={handleInputChange}
                      placeholder="Optional"
                    />
                  </div>
                </div>
              </div>

              {/* Payment Information */}
              {/* <div className="bg-gradient-to-br from-gray-950 to-gray-900 border border-border rounded-lg p-6 mb-8 relative overflow-hidden "> */}
{/*                 <h2 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
                  <CreditCard className="h-5 w-5 text-primary" />
                  Card Details
                </h2> */}

                {/* Glassmorphism Credit Card */}
{/*                 <GlassmorphismCard 
                  cardNumber=""
                  cardHolder=""
                  expiryDate=""
                  cvc=""
                  disabled={false}
                  className="mt-2 flex justify-center mx-auto items-center mb-6"
                /> */}
              {/* </div> */}

              {/* Submit Button */}
              <Button
                type="submit"
                className="w-full py-6 text-lg"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  `Pay R ${total.toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
                )}
              </Button> 
            </form>
          </div>

          {/* Order Summary */}
          <div>
            <div className="bg-card border border-border rounded-lg p-6 sticky top-6">
              <h2 className="text-xl font-semibold text-foreground mb-4">Order Summary</h2>

              {/* Items */}
              <div className="space-y-4 mb-6">
                {cartItems.map((item) => (
                  <div key={item.id} className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <div className="bg-muted rounded-md w-10 h-10 flex items-center justify-center text-xs font-medium">
                        {item.quantity}
                      </div>
                      <span className="text-sm">{item.name}</span>
                    </div>
                    <span className="font-medium">
                      R {(item.price * item.quantity).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    </span>
                  </div>
                ))}
              </div>

              {/* Totals */}
              <div className="space-y-3 border-t border-border pt-4 mb-6">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Subtotal</span>
                  <span>
                    R {subtotal.toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Shipping</span>
                  <span>
                    {shipping === 0 ? 'Free' : `R ${shipping.toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                  </span>
                </div>
                {shipping > 0 && (
                  <div className="text-xs text-muted-foreground">
                    Free shipping on orders over R 1,000.00
                  </div>
                )}
                <div className="flex justify-between font-semibold text-lg pt-2 border-t border-border mt-2">
                  <span>Total</span>
                  <span className="text-primary">
                    R {total.toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </span>
                </div>
              </div>

              {/* Security Note */}
              <div className="text-xs text-muted-foreground">
                <p className="flex items-center gap-1 mb-1">
                  <ShieldCheck className="h-3 w-3 text-green-500" />
                  <span>Secure checkout</span>
                </p>
                <p className="mt-2">Your personal and payment information is protected by secure encryption.</p>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
