import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(
  request: Request,
  { params }: { params: { sessionId: string } }
) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    // Get the session ID from the URL
    const sessionId = params.sessionId;

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    // If user is authenticated, try to find their order
    if (user) {
      const { data: order, error } = await supabase
        .from('orders')
        .select('*')
        .eq('stripe_session_id', sessionId)
        .eq('user_id', user.id)
        .single();

      if (order) {
        return NextResponse.json(order);
      }
    }

    // If no order found for the authenticated user, or user is not authenticated,
    // try to find any order with this session ID (for admins or guest orders)
    const { data: order, error } = await supabase
      .from('orders')
      .select('*')
      .eq('stripe_session_id', sessionId)
      .single();

    if (error) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(order);
  } catch (error: any) {
    console.error('Error retrieving order by session ID:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
