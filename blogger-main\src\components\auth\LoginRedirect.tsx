import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../supabase/auth';
import { LoadingScreen } from '../ui/loading-spinner';

export function LoginRedirect() {
  const { user, isAdmin, loading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!loading && user) {
      // User is authenticated, redirect based on role
      if (isAdmin) {
        console.log('Redirecting admin user to admin dashboard');
        navigate('/dashboard/admin', { replace: true });
      } else {
        console.log('Redirecting regular user to user dashboard');
        navigate('/dashboard', { replace: true });
      }
    } else if (!loading && !user) {
      // User is not authenticated, redirect to login
      navigate('/login', { replace: true });
    }
  }, [user, isAdmin, loading, navigate]);

  if (loading) {
    return <LoadingScreen text="Checking authentication..." />;
  }

  return <LoadingScreen text="Redirecting..." />;
}
