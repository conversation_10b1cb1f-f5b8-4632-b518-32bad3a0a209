import { NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/utils/supabase/server';

/**
 * Test API endpoint to verify service role client is working correctly
 * This endpoint tests if the service role client can bypass RLS policies
 */
export async function GET() {
  try {
    const serviceSupabase = createServiceRoleClient();
    
    // Test 1: Try to read products (should work)
    const { data: products, error: readError } = await serviceSupabase
      .from('products')
      .select('id, name, price')
      .limit(1);

    if (readError) {
      return NextResponse.json({
        success: false,
        test: 'read_products',
        error: readError.message,
        details: readError
      });
    }

    // Test 2: Try to update a product (should work with service role)
    if (products && products.length > 0) {
      const testProduct = products[0];
      const { data: updateResult, error: updateError } = await serviceSupabase
        .from('products')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', testProduct.id)
        .select()
        .single();

      if (updateError) {
        return NextResponse.json({
          success: false,
          test: 'update_product',
          productId: testProduct.id,
          error: updateError.message,
          details: updateError
        });
      }

      return NextResponse.json({
        success: true,
        message: 'Service role client is working correctly',
        tests: {
          read_products: 'PASSED',
          update_product: 'PASSED'
        },
        testProduct: {
          id: testProduct.id,
          name: testProduct.name,
          updated: updateResult.updated_at
        }
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Service role client can read products',
      tests: {
        read_products: 'PASSED',
        update_product: 'SKIPPED (no products found)'
      },
      productsFound: products?.length || 0
    });

  } catch (error: any) {
    console.error('Service role test error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function POST() {
  return NextResponse.json({
    message: 'Use GET method to test service role client'
  });
}
