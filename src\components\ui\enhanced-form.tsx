"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { Input } from "./input";
import { Button } from "./button";
import { Label } from "./label";

// Enhanced Input Component matching auth form style
interface EnhancedInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  icon?: React.ReactNode;
  label?: string;
  error?: string;
}

export const EnhancedInput = React.forwardRef<HTMLInputElement, EnhancedInputProps>(
  ({ className, type, icon, label, error, ...props }, ref) => {
    return (
      <div className="space-y-2">
        {label && (
          <Label htmlFor={props.id} className="text-sm font-medium text-foreground">
            {label}
          </Label>
        )}
        <div className="relative">
          <Input
            type={type}
            className={cn(
              "w-full h-14 rounded-2xl form-input neo-shadow-inset transition-neo focus:neo-shadow-light",
              icon && "pl-12",
              error && "border-red-500/50 focus:border-red-500",
              className
            )}
            ref={ref}
            {...props}
          />
          {icon && (
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground">
              {icon}
            </div>
          )}
        </div>
        {error && (
          <p className="text-sm text-red-500 mt-1">{error}</p>
        )}
      </div>
    );
  }
);
EnhancedInput.displayName = "EnhancedInput";

// Enhanced Button Component matching auth form style
interface EnhancedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "outline";
  size?: "sm" | "md" | "lg";
  loading?: boolean;
  children: React.ReactNode;
}

export const EnhancedButton = React.forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  ({ className, variant = "primary", size = "md", loading, children, disabled, ...props }, ref) => {
    const baseClasses = "font-semibold rounded-2xl transition-neo focus-ring disabled:opacity-50 disabled:cursor-not-allowed";
    
    const variantClasses = {
      primary: "btn-primary text-white neo-shadow hover:neo-shadow-hover",
      secondary: "bg-muted/30 text-foreground neo-shadow-light hover:bg-muted/50",
      outline: "border border-primary/20 glass-effect-subtle hover:border-primary/40 text-foreground"
    };

    const sizeClasses = {
      sm: "h-10 px-4 text-sm",
      md: "h-12 px-6 text-base",
      lg: "h-14 px-8 text-lg"
    };

    return (
      <Button
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[size],
          className
        )}
        disabled={disabled || loading}
        ref={ref}
        {...props}
      >
        {loading ? (
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            Loading...
          </div>
        ) : (
          children
        )}
      </Button>
    );
  }
);
EnhancedButton.displayName = "EnhancedButton";

// Enhanced Form Container
interface EnhancedFormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  title?: string;
  subtitle?: string;
  children: React.ReactNode;
}

export const EnhancedForm = React.forwardRef<HTMLFormElement, EnhancedFormProps>(
  ({ className, title, subtitle, children, ...props }, ref) => {
    return (
      <div className="w-full max-w-md mx-auto">
        {(title || subtitle) && (
          <div className="text-center mb-8">
            {title && (
              <h1 className="text-2xl font-bold tracking-tight text-foreground mb-2">
                {title}
              </h1>
            )}
            {subtitle && (
              <p className="text-muted-foreground text-sm">
                {subtitle}
              </p>
            )}
          </div>
        )}
        
        <div className="glass-effect-dark rounded-3xl p-8 neo-shadow">
          <form
            className={cn("flex flex-col space-y-6", className)}
            ref={ref}
            {...props}
          >
            {children}
          </form>
        </div>
      </div>
    );
  }
);
EnhancedForm.displayName = "EnhancedForm";

// Enhanced Card Component
interface EnhancedCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  hover?: boolean;
}

export const EnhancedCard = React.forwardRef<HTMLDivElement, EnhancedCardProps>(
  ({ className, children, hover = false, ...props }, ref) => {
    return (
      <div
        className={cn(
          "modern-card p-6 neo-shadow-light",
          hover && "interactive-card",
          className
        )}
        ref={ref}
        {...props}
      >
        {children}
      </div>
    );
  }
);
EnhancedCard.displayName = "EnhancedCard";

// Enhanced Section Container
interface EnhancedSectionProps extends React.HTMLAttributes<HTMLElement> {
  children: React.ReactNode;
  background?: "default" | "muted" | "gradient";
}

export const EnhancedSection = React.forwardRef<HTMLElement, EnhancedSectionProps>(
  ({ className, children, background = "default", ...props }, ref) => {
    const backgroundClasses = {
      default: "bg-background",
      muted: "bg-muted/20",
      gradient: "bg-gradient-to-br from-background via-muted/20 to-background"
    };

    return (
      <section
        className={cn(
          "relative overflow-hidden",
          backgroundClasses[background],
          className
        )}
        ref={ref}
        {...props}
      >
        {background === "gradient" && (
          <div className="absolute inset-0 overflow-hidden -z-10">
            <div className="absolute h-[400px] w-[400px] rounded-full gradient-blue top-[-200px] left-[-200px] blur-[100px] opacity-20 animate-pulse"></div>
            <div className="absolute h-[300px] w-[300px] rounded-full gradient-purple bottom-[-150px] right-[-150px] blur-[80px] opacity-25 animate-pulse"></div>
          </div>
        )}
        {children}
      </section>
    );
  }
);
EnhancedSection.displayName = "EnhancedSection";
