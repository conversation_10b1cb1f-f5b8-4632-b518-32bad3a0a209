import { createClient } from './client';
import { Tables, InsertTables, UpdateTables } from './database.types';
import { v4 as uuidv4 } from 'uuid';

/**
 * Resource management utilities for Tennis-Gear application
 * These functions handle learning resources for the mentorship program
 */

/**
 * Upload a file to storage
 * @param file File to upload
 * @param bucket Storage bucket name
 * @param path File path within the bucket
 * @returns Object with URL and path if successful, null if error
 */
export async function uploadFile(file: File, bucket: string, path?: string) {
  const supabase = createClient();
  
  try {
    // Generate a unique file path if not provided
    if (!path) {
      const fileExt = file.name.split('.').pop();
      const fileName = `${uuidv4()}.${fileExt}`;
      path = fileName;
    }
    
    // Upload file to storage
    const { data, error } = await supabase
      .storage
      .from(bucket)
      .upload(path, file, {
        cacheControl: '3600',
        upsert: false
      });
    
    if (error) {
      throw error;
    }
    
    // Get the public URL
    const { data: { publicUrl } } = supabase
      .storage
      .from(bucket)
      .getPublicUrl(path);
    
    return { url: publicUrl, path };
  } catch (error) {
    console.error(`Error uploading file to ${bucket}:`, error);
    return null;
  }
}

/**
 * Upload a resource file to storage and create a database record
 * @param file File to upload
 * @param resourceData Resource metadata
 * @param userId User ID of the uploader
 * @returns Created resource or error
 */
export async function uploadResource(
  file: File,
  resourceData: {
    title: string;
    description?: string;
    type: Tables<'resources'>['type'];
    category: string;
  },
  userId: string
) {
  const supabase = createClient();
  
  try {
    // Generate a unique file path
    const fileExt = file.name.split('.').pop();
    const fileName = `${uuidv4()}.${fileExt}`;
    const filePath = `${resourceData.type}/${fileName}`;
    
    // Upload file to storage
    const { data: fileData, error: fileError } = await supabase
      .storage
      .from('resource-files')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });
    
    if (fileError) {
      throw fileError;
    }
    
    // Get the public URL
    const { data: { publicUrl } } = supabase
      .storage
      .from('resource-files')
      .getPublicUrl(filePath);
    
    // Create resource record in database
    const resourceRecord: InsertTables<'resources'> = {
      id: uuidv4(),
      title: resourceData.title,
      description: resourceData.description || null,
      type: resourceData.type,
      category: resourceData.category,
      format: fileExt || '',
      file_path: publicUrl,
      size_bytes: file.size,
      download_count: 0,
      created_by: userId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    const { data, error } = await supabase
      .from('resources')
      .insert(resourceRecord)
      .select()
      .single();
    
    if (error) {
      throw error;
    }
    
    return { data, error: null };
  } catch (error) {
    console.error('Error uploading resource:', error);
    return { data: null, error };
  }
}

/**
 * Get all resources
 * @param filters Optional filters
 * @returns List of resources or error
 */
export async function getResources(filters?: {
  type?: Tables<'resources'>['type'];
  category?: string;
  createdBy?: string;
}) {
  const supabase = createClient();
  
  let query = supabase
    .from('resources')
    .select(`
      *,
      creator:created_by (
        id,
        full_name,
        avatar_url
      )
    `);
  
  // Apply filters if provided
  if (filters) {
    if (filters.type) {
      query = query.eq('type', filters.type);
    }
    
    if (filters.category) {
      query = query.eq('category', filters.category);
    }
    
    if (filters.createdBy) {
      query = query.eq('created_by', filters.createdBy);
    }
  }
  
  // Order by created_at descending (newest first)
  query = query.order('created_at', { ascending: false });
  
  const { data, error } = await query;
  
  return { data, error };
}

/**
 * Get resources by type
 * @param resourceType Resource type
 * @returns List of resources of the specified type or error
 */
export async function getResourcesByType(resourceType: Tables<'resources'>['type']) {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('resources')
    .select(`
      *,
      creator:created_by (
        id,
        full_name,
        avatar_url
      )
    `)
    .eq('type', resourceType)
    .order('created_at', { ascending: false });
  
  return { data, error };
}

/**
 * Get a single resource by ID
 * @param resourceId Resource ID
 * @returns Resource data or error
 */
export async function getResource(resourceId: string) {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('resources')
    .select(`
      *,
      creator:created_by (
        id,
        full_name,
        avatar_url
      )
    `)
    .eq('id', resourceId)
    .single();
  
  return { data, error };
}

/**
 * Update resource metadata
 * @param resourceId Resource ID
 * @param updates Resource data to update
 * @returns Updated resource or error
 */
export async function updateResource(resourceId: string, updates: {
  title?: string;
  description?: string;
  category?: string;
}) {
  const supabase = createClient();
  
  const updateData: UpdateTables<'resources'> = {
    ...updates,
    updated_at: new Date().toISOString()
  };
  
  const { data, error } = await supabase
    .from('resources')
    .update(updateData)
    .eq('id', resourceId)
    .select()
    .single();
  
  return { data, error };
}

/**
 * Increment download count for a resource
 * @param resourceId Resource ID
 * @returns Updated resource or error
 */
export async function incrementDownloadCount(resourceId: string) {
  const supabase = createClient();
  
  // First get the current download count
  const { data: resource, error: getError } = await supabase
    .from('resources')
    .select('download_count')
    .eq('id', resourceId)
    .single();
  
  if (getError) {
    return { data: null, error: getError };
  }
  
  // Increment the count
  const newCount = (resource.download_count || 0) + 1;
  
  const updateData: UpdateTables<'resources'> = {
    download_count: newCount,
    updated_at: new Date().toISOString()
  };
  
  const { data, error } = await supabase
    .from('resources')
    .update(updateData)
    .eq('id', resourceId)
    .select()
    .single();
  
  return { data, error };
}

/**
 * Delete a resource
 * @param resourceId Resource ID
 * @returns Success or error
 */
export async function deleteResource(resourceId: string) {
  const supabase = createClient();
  
  // First get the resource to get the file path
  const { data: resource, error: getError } = await supabase
    .from('resources')
    .select('file_path, type')
    .eq('id', resourceId)
    .single();
  
  if (getError) {
    return { error: getError };
  }
  
  // Extract the path from the URL
  const url = new URL(resource.file_path);
  const pathMatch = url.pathname.match(/\/resource-files\/object\/public\/(.+)$/);
  
  if (pathMatch && pathMatch[1]) {
    const filePath = decodeURIComponent(pathMatch[1]);
    
    // Delete the file from storage
    const { error: storageError } = await supabase
      .storage
      .from('resource-files')
      .remove([filePath]);
    
    if (storageError) {
      console.error('Error deleting file from storage:', storageError);
      // Continue to delete the database record even if storage deletion fails
    }
  }
  
  // Delete the database record
  const { error } = await supabase
    .from('resources')
    .delete()
    .eq('id', resourceId);
  
  return { error };
}

/**
 * Get resource categories
 * @returns List of unique resource categories or error
 */
export async function getResourceCategories() {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('resources')
    .select('category')
    .order('category', { ascending: true });
  
  if (error) {
    return { data: null, error };
  }
  
  // Extract unique categories
  const categories = data ? Array.from(new Set(data.map(item => item.category))) : [];
  
  return { data: categories, error: null };
}
