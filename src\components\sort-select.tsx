"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useCallback } from "react";

interface SortSelectProps {
  defaultValue?: string;
}

export function SortSelect({ defaultValue }: SortSelectProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const createQueryString = useCallback(
    (name: string, value: string) => {
      const params = new URLSearchParams(searchParams.toString());
      if (value) {
        params.set(name, value);
      } else {
        params.delete(name);
      }
      
      return params.toString();
    },
    [searchParams]
  );

  return (
    <select 
      className="bg-transparent border-none text-foreground focus:outline-none"
      onChange={(e) => {
        router.push(`/shop?${createQueryString('sort', e.target.value)}`);
      }}
      defaultValue={defaultValue || ""}
    >
      <option value="">Featured</option>
      <option value="price-asc">Price: Low to High</option>
      <option value="price-desc">Price: High to Low</option>
      <option value="name-asc">Name: A to Z</option>
      <option value="name-desc">Name: Z to A</option>
      <option value="rating">Highest Rated</option>
    </select>
  );
}