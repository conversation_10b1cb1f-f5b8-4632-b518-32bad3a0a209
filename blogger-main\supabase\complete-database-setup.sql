-- =====================================================
-- COMPLETE DATABASE SETUP FOR THE CHRONICLE PROJECT
-- This script sets up the entire database with all tables, policies, and test data
-- =====================================================

-- STEP 1: CLEAN UP EXISTING DATA (for fresh start)
-- =====================================================
DO $$
BEGIN
    -- Drop existing tables in correct order to handle dependencies
    DROP TABLE IF EXISTS public.favorites CASCADE;
    DROP TABLE IF EXISTS public.likes CASCADE;
    DROP TABLE IF EXISTS public.article_likes CASCADE;
    DROP TABLE IF EXISTS public.comments CASCADE;
    DROP TABLE IF EXISTS public.order_items CASCADE;
    DROP TABLE IF EXISTS public.orders CASCADE;
    DROP TABLE IF EXISTS public.products CASCADE;
    DROP TABLE IF EXISTS public.product_categories CASCADE;
    DROP TABLE IF EXISTS public.articles CASCADE;
    DROP TABLE IF EXISTS public.categories CASCADE;
    DROP TABLE IF EXISTS public.contact_messages CASCADE;
    DROP TABLE IF EXISTS public.user_subscriptions CASCADE;
    DROP TABLE IF EXISTS public.newsletter_subscribers CASCADE;
    DROP TABLE IF EXISTS public.analytics CASCADE;
    DROP TABLE IF EXISTS public.notifications CASCADE;
    DROP TABLE IF EXISTS public.media_uploads CASCADE;
    DROP TABLE IF EXISTS public.article_engagement CASCADE;
    
    RAISE NOTICE '🧹 Cleaned up existing tables';
END $$;

-- STEP 2: CREATE CORE TABLES
-- =====================================================

-- Profiles table (extends auth.users)
CREATE TABLE public.profiles (
    id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    first_name text,
    last_name text,
    email text,
    role text DEFAULT 'user' CHECK (role IN ('user', 'admin')),
    avatar_url text,
    bio text,
    website text,
    location text,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- Categories table
CREATE TABLE public.categories (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text NOT NULL,
    slug text UNIQUE NOT NULL,
    description text,
    color text DEFAULT '#3B82F6',
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- Product categories table
CREATE TABLE public.product_categories (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text NOT NULL,
    slug text UNIQUE NOT NULL,
    description text,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- Articles table
CREATE TABLE public.articles (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    title text NOT NULL,
    slug text UNIQUE NOT NULL,
    excerpt text,
    content text,
    featured_image text,
    is_published boolean DEFAULT false,
    is_premium boolean DEFAULT false,
    category_id uuid REFERENCES public.categories(id),
    author_id uuid REFERENCES public.profiles(id),
    views integer DEFAULT 0,
    likes integer DEFAULT 0,
    comments_count integer DEFAULT 0,
    read_time integer DEFAULT 5,
    published_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- Products table
CREATE TABLE public.products (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text NOT NULL,
    slug text UNIQUE NOT NULL,
    description text,
    short_description text,
    price decimal(10,2) NOT NULL,
    sale_price decimal(10,2),
    image_url text,
    image_urls text[],
    type text DEFAULT 'digital' CHECK (type IN ('digital', 'physical', 'subscription')),
    status text DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'draft')),
    stock_quantity integer,
    category_id uuid REFERENCES public.product_categories(id),
    rating decimal(3,2) DEFAULT 0,
    reviews_count integer DEFAULT 0,
    is_featured boolean DEFAULT false,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- Comments table
CREATE TABLE public.comments (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    content text NOT NULL,
    article_id uuid REFERENCES public.articles(id) ON DELETE CASCADE,
    user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE,
    parent_id uuid REFERENCES public.comments(id) ON DELETE CASCADE,
    is_approved boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- Article likes table
CREATE TABLE public.article_likes (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    article_id uuid REFERENCES public.articles(id) ON DELETE CASCADE,
    user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    UNIQUE(article_id, user_id)
);

-- Likes table (general)
CREATE TABLE public.likes (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE,
    article_id uuid REFERENCES public.articles(id) ON DELETE CASCADE,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    UNIQUE(user_id, article_id)
);

-- Favorites table
CREATE TABLE public.favorites (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE,
    article_id uuid REFERENCES public.articles(id) ON DELETE CASCADE,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    UNIQUE(user_id, article_id)
);

-- Orders table
CREATE TABLE public.orders (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE,
    status text DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'cancelled', 'refunded')),
    total_amount decimal(10,2) NOT NULL,
    stripe_payment_intent_id text,
    billing_address jsonb,
    shipping_address jsonb,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- Order items table
CREATE TABLE public.order_items (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id uuid REFERENCES public.orders(id) ON DELETE CASCADE,
    product_id uuid REFERENCES public.products(id) ON DELETE CASCADE,
    quantity integer NOT NULL DEFAULT 1,
    price decimal(10,2) NOT NULL,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- Contact messages table
CREATE TABLE public.contact_messages (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text NOT NULL,
    email text NOT NULL,
    subject text,
    message text NOT NULL,
    status text DEFAULT 'unread' CHECK (status IN ('unread', 'read', 'replied', 'archived')),
    ip_address text,
    user_agent text,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- User subscriptions table
CREATE TABLE public.user_subscriptions (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid UNIQUE REFERENCES public.profiles(id) ON DELETE CASCADE,
    plan text NOT NULL CHECK (plan IN ('free', 'premium', 'pro')),
    status text NOT NULL CHECK (status IN ('active', 'cancelled', 'expired', 'trialing')),
    start_date timestamp with time zone DEFAULT timezone('utc'::text, now()),
    end_date timestamp with time zone,
    stripe_subscription_id text,
    stripe_customer_id text,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- Newsletter subscribers table
CREATE TABLE public.newsletter_subscribers (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    email text UNIQUE NOT NULL,
    name text,
    status text DEFAULT 'active' CHECK (status IN ('active', 'unsubscribed', 'bounced')),
    subscribed_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    unsubscribed_at timestamp with time zone,
    metadata jsonb DEFAULT '{}'::jsonb
);

-- Analytics table
CREATE TABLE public.analytics (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    article_id uuid REFERENCES public.articles(id) ON DELETE CASCADE,
    user_id uuid REFERENCES public.profiles(id) ON DELETE SET NULL,
    event_type text NOT NULL, -- 'view', 'like', 'share', 'comment'
    ip_address text,
    user_agent text,
    referrer text,
    session_id text,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- Notifications table
CREATE TABLE public.notifications (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE,
    title text NOT NULL,
    message text NOT NULL,
    type text DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
    is_read boolean DEFAULT false,
    action_url text,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- Media uploads table
CREATE TABLE public.media_uploads (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE,
    filename text NOT NULL,
    original_filename text NOT NULL,
    file_size bigint,
    mime_type text,
    file_path text NOT NULL,
    bucket_name text NOT NULL,
    is_public boolean DEFAULT true,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

RAISE NOTICE '✅ Created all core tables';

-- STEP 3: ENABLE ROW LEVEL SECURITY
-- =====================================================
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.article_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.contact_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.newsletter_subscribers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.media_uploads ENABLE ROW LEVEL SECURITY;

-- STEP 4: CREATE RLS POLICIES
-- =====================================================

-- Profiles policies
CREATE POLICY "Users can view their own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update their own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert their own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);
CREATE POLICY "Admins can view all profiles" ON public.profiles
    FOR SELECT USING (EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin'));
CREATE POLICY "Admins can update all profiles" ON public.profiles
    FOR UPDATE USING (EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin'));

-- Categories policies
CREATE POLICY "Anyone can view categories" ON public.categories FOR SELECT USING (true);
CREATE POLICY "Only admins can manage categories" ON public.categories
    FOR ALL USING (EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin'));

-- Product categories policies
CREATE POLICY "Anyone can view product categories" ON public.product_categories FOR SELECT USING (true);
CREATE POLICY "Only admins can manage product categories" ON public.product_categories
    FOR ALL USING (EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin'));

-- Articles policies
CREATE POLICY "Anyone can view published articles" ON public.articles FOR SELECT USING (is_published = true);
CREATE POLICY "Authors can view their own articles" ON public.articles FOR SELECT USING (auth.uid() = author_id);
CREATE POLICY "Admins can view all articles" ON public.articles
    FOR SELECT USING (EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin'));
CREATE POLICY "Authors can create articles" ON public.articles FOR INSERT WITH CHECK (auth.uid() = author_id);
CREATE POLICY "Authors can update their own articles" ON public.articles FOR UPDATE USING (auth.uid() = author_id);
CREATE POLICY "Admins can manage all articles" ON public.articles
    FOR ALL USING (EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin'));

-- Products policies
CREATE POLICY "Anyone can view active products" ON public.products FOR SELECT USING (status = 'active');
CREATE POLICY "Admins can manage all products" ON public.products
    FOR ALL USING (EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin'));

-- Comments policies
CREATE POLICY "Anyone can view approved comments" ON public.comments FOR SELECT USING (is_approved = true);
CREATE POLICY "Users can create comments" ON public.comments FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own comments" ON public.comments FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Admins can manage all comments" ON public.comments
    FOR ALL USING (EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin'));

-- Likes policies
CREATE POLICY "Users can manage their own likes" ON public.likes FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can manage their own article likes" ON public.article_likes FOR ALL USING (auth.uid() = user_id);

-- Favorites policies
CREATE POLICY "Users can manage their own favorites" ON public.favorites FOR ALL USING (auth.uid() = user_id);

-- Orders policies
CREATE POLICY "Users can view their own orders" ON public.orders FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create their own orders" ON public.orders FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Admins can view all orders" ON public.orders
    FOR SELECT USING (EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin'));

-- Order items policies
CREATE POLICY "Users can view their order items" ON public.order_items
    FOR SELECT USING (EXISTS (SELECT 1 FROM public.orders WHERE id = order_id AND user_id = auth.uid()));
CREATE POLICY "Users can create their order items" ON public.order_items
    FOR INSERT WITH CHECK (EXISTS (SELECT 1 FROM public.orders WHERE id = order_id AND user_id = auth.uid()));

-- Contact messages policies (admin only)
CREATE POLICY "Admins can manage contact messages" ON public.contact_messages
    FOR ALL USING (EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin'));

-- User subscriptions policies
CREATE POLICY "Users can view their subscriptions" ON public.user_subscriptions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update their subscriptions" ON public.user_subscriptions FOR UPDATE USING (auth.uid() = user_id);

-- Newsletter subscribers policies (admin only)
CREATE POLICY "Admins can manage newsletter subscribers" ON public.newsletter_subscribers
    FOR ALL USING (EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin'));

-- Analytics policies
CREATE POLICY "Admins can view analytics" ON public.analytics
    FOR SELECT USING (EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin'));
CREATE POLICY "Anyone can create analytics" ON public.analytics FOR INSERT WITH CHECK (true);

-- Notifications policies
CREATE POLICY "Users can view their notifications" ON public.notifications FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update their notifications" ON public.notifications FOR UPDATE USING (auth.uid() = user_id);

-- Media uploads policies
CREATE POLICY "Users can view public media" ON public.media_uploads FOR SELECT USING (is_public = true OR auth.uid() = user_id);
CREATE POLICY "Users can manage their media" ON public.media_uploads FOR ALL USING (auth.uid() = user_id);

RAISE NOTICE '✅ Created all RLS policies';

-- STEP 5: CREATE STORAGE BUCKETS
-- =====================================================
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types) VALUES
    ('avatars', 'avatars', true, 52428800, ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']),
    ('articles', 'articles', true, 52428800, ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'video/webm']),
    ('products', 'products', true, 52428800, ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']),
    ('media', 'media', true, 104857600, ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'video/webm', 'audio/mpeg', 'audio/wav', 'application/pdf', 'application/zip'])
ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- STEP 6: CREATE STORAGE POLICIES
-- =====================================================
-- Avatar policies
CREATE POLICY "Avatar images are publicly accessible" ON storage.objects FOR SELECT USING (bucket_id = 'avatars');
CREATE POLICY "Users can upload their own avatar" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'avatars' AND auth.uid() IS NOT NULL);
CREATE POLICY "Users can update their own avatar" ON storage.objects
    FOR UPDATE USING (bucket_id = 'avatars' AND auth.uid() IS NOT NULL);
CREATE POLICY "Users can delete their own avatar" ON storage.objects
    FOR DELETE USING (bucket_id = 'avatars' AND auth.uid() IS NOT NULL);

-- Article media policies
CREATE POLICY "Article images are publicly accessible" ON storage.objects FOR SELECT USING (bucket_id = 'articles');
CREATE POLICY "Authenticated users can upload article media" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'articles' AND auth.uid() IS NOT NULL);

-- Product media policies
CREATE POLICY "Product images are publicly accessible" ON storage.objects FOR SELECT USING (bucket_id = 'products');
CREATE POLICY "Authenticated users can upload product media" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'products' AND auth.uid() IS NOT NULL);

-- General media policies
CREATE POLICY "Media files are publicly accessible" ON storage.objects FOR SELECT USING (bucket_id = 'media');
CREATE POLICY "Authenticated users can upload media" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'media' AND auth.uid() IS NOT NULL);

-- STEP 7: CREATE FUNCTIONS
-- =====================================================

-- Function to handle new user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, first_name, last_name, role)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'first_name', NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
        NEW.raw_user_meta_data->>'last_name',
        CASE
            WHEN (SELECT COUNT(*) FROM auth.users WHERE id != NEW.id) = 0 THEN 'admin'
            ELSE 'user'
        END
    )
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name,
        updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for auto profile creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update user role (admin only)
CREATE OR REPLACE FUNCTION public.update_user_role(target_user_id uuid, new_role text)
RETURNS json AS $$
DECLARE
    current_user_role text;
BEGIN
    SELECT role INTO current_user_role FROM public.profiles WHERE id = auth.uid();

    IF current_user_role != 'admin' THEN
        RETURN json_build_object('success', false, 'error', 'Unauthorized');
    END IF;

    UPDATE public.profiles SET role = new_role, updated_at = NOW() WHERE id = target_user_id;

    IF FOUND THEN
        RETURN json_build_object('success', true);
    ELSE
        RETURN json_build_object('success', false, 'error', 'User not found');
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

RAISE NOTICE '✅ Created storage buckets, policies, and functions';

-- STEP 8: INSERT TEST DATA
-- =====================================================

-- Insert categories
INSERT INTO public.categories (id, name, slug, description, color) VALUES
('550e8400-e29b-41d4-a716-************', 'Technology', 'technology', 'Latest tech news and innovations', '#3B82F6'),
('550e8400-e29b-41d4-a716-************', 'Business', 'business', 'Business insights and market analysis', '#10B981'),
('550e8400-e29b-41d4-a716-************', 'Health', 'health', 'Health and wellness articles', '#EF4444'),
('550e8400-e29b-41d4-a716-************', 'Finance', 'finance', 'Financial news and investment advice', '#F59E0B'),
('550e8400-e29b-41d4-a716-************', 'Lifestyle', 'lifestyle', 'Lifestyle and culture content', '#8B5CF6'),
('550e8400-e29b-41d4-a716-************', 'Sports', 'sports', 'Sports news and analysis', '#EF4444'),
('550e8400-e29b-41d4-a716-************', 'Entertainment', 'entertainment', 'Entertainment and celebrity news', '#F59E0B'),
('550e8400-e29b-41d4-a716-************', 'Politics', 'politics', 'Political news and commentary', '#6B7280'),
('550e8400-e29b-41d4-a716-************', 'Science', 'science', 'Scientific discoveries and research', '#10B981'),
('550e8400-e29b-41d4-a716-************', 'Travel', 'travel', 'Travel guides and destination reviews', '#8B5CF6')
ON CONFLICT (id) DO NOTHING;

-- Insert product categories
INSERT INTO public.product_categories (id, name, slug, description, is_active) VALUES
('650e8400-e29b-41d4-a716-************', 'Digital Products', 'digital-products', 'Courses, ebooks, and digital downloads', true),
('650e8400-e29b-41d4-a716-************', 'Subscriptions', 'subscriptions', 'Premium memberships and subscriptions', true),
('650e8400-e29b-41d4-a716-************', 'Physical Products', 'physical-products', 'Books, merchandise, and physical items', true),
('650e8400-e29b-41d4-a716-************', 'Courses', 'courses', 'Online courses and educational content', true),
('650e8400-e29b-41d4-a716-************', 'Books', 'books', 'Physical and digital books', true),
('650e8400-e29b-41d4-a716-************', 'Merchandise', 'merchandise', 'Branded merchandise and accessories', true)
ON CONFLICT (id) DO NOTHING;

-- Insert sample products
INSERT INTO public.products (id, name, slug, description, short_description, price, sale_price, type, category_id, image_urls, stock_quantity, status, is_featured, metadata) VALUES
(
    '750e8400-e29b-41d4-a716-************',
    'Premium Membership',
    'premium-membership',
    'Get unlimited access to all premium articles, exclusive content, and member-only features. Join our community of informed readers and stay ahead with the latest insights.',
    'Unlimited access to premium content',
    29.99,
    19.99,
    'subscription',
    '650e8400-e29b-41d4-a716-************',
    '["https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&q=80"]',
    NULL,
    'active',
    true,
    '{"billing_period": "monthly", "features": ["unlimited_articles", "exclusive_content", "ad_free", "early_access"]}'
),
(
    '750e8400-e29b-41d4-a716-************',
    'Digital Marketing Masterclass',
    'digital-marketing-masterclass',
    'Learn the latest digital marketing strategies from industry experts. This comprehensive course covers SEO, social media marketing, content marketing, email campaigns, and analytics. Perfect for entrepreneurs and marketing professionals.',
    'Complete digital marketing course',
    199.99,
    149.99,
    'digital',
    '650e8400-e29b-41d4-a716-************',
    '["https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&q=80"]',
    NULL,
    'active',
    true,
    '{"duration": "8 weeks", "modules": 12, "certificate": true, "level": "intermediate"}'
),
(
    '750e8400-e29b-41d4-a716-************',
    'The Chronicle Handbook',
    'chronicle-handbook',
    'A comprehensive guide to modern journalism and media literacy. Written by our editorial team with insights from industry professionals. Learn about fact-checking, ethical reporting, and digital storytelling.',
    'Essential guide to modern journalism',
    39.99,
    NULL,
    'physical',
    '650e8400-e29b-41d4-a716-************',
    '["https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=800&q=80"]',
    50,
    'active',
    false,
    '{"pages": 320, "format": "hardcover", "isbn": "978-1234567890", "weight": "1.2kg"}'
),
(
    '750e8400-e29b-41d4-a716-************',
    'AI Writing Assistant Pro',
    'ai-writing-assistant-pro',
    'Advanced AI-powered writing tool that helps journalists, bloggers, and content creators produce high-quality articles faster. Includes grammar checking, style suggestions, and fact-checking capabilities.',
    'AI-powered writing and editing tool',
    79.99,
    59.99,
    'digital',
    '650e8400-e29b-41d4-a716-************',
    '["https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&q=80"]',
    NULL,
    'active',
    true,
    '{"features": ["grammar_check", "style_suggestions", "fact_checking", "plagiarism_detection"], "platforms": ["web", "desktop", "mobile"]}'
),
(
    '750e8400-e29b-41d4-a716-************',
    'Chronicle Coffee Mug',
    'chronicle-coffee-mug',
    'Start your day with our premium ceramic coffee mug featuring The Chronicle logo. Perfect for journalists, writers, and news enthusiasts. Dishwasher and microwave safe.',
    'Premium ceramic coffee mug',
    24.99,
    NULL,
    'physical',
    '650e8400-e29b-41d4-a716-************',
    '["https://images.unsplash.com/photo-1514228742587-6b1558fcf93a?w=800&q=80"]',
    100,
    'active',
    false,
    '{"material": "ceramic", "capacity": "350ml", "color": "white", "dishwasher_safe": true}'
)
ON CONFLICT (id) DO NOTHING;

RAISE NOTICE '✅ Inserted test categories and products';

-- STEP 9: CREATE INDEXES FOR PERFORMANCE
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON public.profiles(role);
CREATE INDEX IF NOT EXISTS idx_articles_author_id ON public.articles(author_id);
CREATE INDEX IF NOT EXISTS idx_articles_category_id ON public.articles(category_id);
CREATE INDEX IF NOT EXISTS idx_articles_is_published ON public.articles(is_published);
CREATE INDEX IF NOT EXISTS idx_articles_published_at ON public.articles(published_at);
CREATE INDEX IF NOT EXISTS idx_articles_slug ON public.articles(slug);
CREATE INDEX IF NOT EXISTS idx_products_category_id ON public.products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_status ON public.products(status);
CREATE INDEX IF NOT EXISTS idx_products_is_featured ON public.products(is_featured);
CREATE INDEX IF NOT EXISTS idx_comments_article_id ON public.comments(article_id);
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON public.comments(user_id);
CREATE INDEX IF NOT EXISTS idx_comments_parent_id ON public.comments(parent_id);
CREATE INDEX IF NOT EXISTS idx_likes_article_id ON public.likes(article_id);
CREATE INDEX IF NOT EXISTS idx_likes_user_id ON public.likes(user_id);
CREATE INDEX IF NOT EXISTS idx_article_likes_article_id ON public.article_likes(article_id);
CREATE INDEX IF NOT EXISTS idx_article_likes_user_id ON public.article_likes(user_id);
CREATE INDEX IF NOT EXISTS idx_favorites_user_id ON public.favorites(user_id);
CREATE INDEX IF NOT EXISTS idx_favorites_article_id ON public.favorites(article_id);
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON public.orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON public.orders(status);
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON public.order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON public.order_items(product_id);
CREATE INDEX IF NOT EXISTS idx_contact_messages_status ON public.contact_messages(status);
CREATE INDEX IF NOT EXISTS idx_contact_messages_created_at ON public.contact_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON public.user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON public.user_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_analytics_article_id ON public.analytics(article_id);
CREATE INDEX IF NOT EXISTS idx_analytics_created_at ON public.analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON public.notifications(is_read);

-- STEP 10: GRANT PERMISSIONS
-- =====================================================
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

-- STEP 11: REFRESH SCHEMA CACHE
-- =====================================================
NOTIFY pgrst, 'reload schema';

-- STEP 12: COMPLETION MESSAGE
-- =====================================================
DO $$
DECLARE
    tables_count INTEGER;
    buckets_count INTEGER;
    categories_count INTEGER;
    products_count INTEGER;
BEGIN
    -- Count tables
    SELECT COUNT(*) INTO tables_count
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name IN ('profiles', 'categories', 'product_categories', 'articles', 'products', 'comments', 'article_likes', 'likes', 'favorites', 'orders', 'order_items', 'contact_messages', 'user_subscriptions', 'newsletter_subscribers', 'analytics', 'notifications', 'media_uploads');

    -- Count storage buckets
    SELECT COUNT(*) INTO buckets_count
    FROM storage.buckets
    WHERE id IN ('avatars', 'articles', 'products', 'media');

    -- Count categories and products
    SELECT COUNT(*) INTO categories_count FROM public.categories;
    SELECT COUNT(*) INTO products_count FROM public.products;

    RAISE NOTICE '🎉 THE CHRONICLE DATABASE SETUP COMPLETE!';
    RAISE NOTICE '==========================================';
    RAISE NOTICE '📊 SETUP RESULTS:';
    RAISE NOTICE 'Tables created: %/17', tables_count;
    RAISE NOTICE 'Storage buckets: %/4', buckets_count;
    RAISE NOTICE 'Categories: %', categories_count;
    RAISE NOTICE 'Products: %', products_count;
    RAISE NOTICE '==========================================';
    RAISE NOTICE '✅ FEATURES ENABLED:';
    RAISE NOTICE '1. Complete database schema';
    RAISE NOTICE '2. Row Level Security (RLS) policies';
    RAISE NOTICE '3. Storage buckets with policies';
    RAISE NOTICE '4. Auto user profile creation';
    RAISE NOTICE '5. Admin role management functions';
    RAISE NOTICE '6. Performance indexes';
    RAISE NOTICE '7. Test data (categories & products)';
    RAISE NOTICE '==========================================';
    RAISE NOTICE '🚀 NEXT STEPS:';
    RAISE NOTICE '1. Create admin user account';
    RAISE NOTICE '2. Add sample articles and content';
    RAISE NOTICE '3. Test all functionality';
    RAISE NOTICE '4. Configure payment processing';
    RAISE NOTICE '==========================================';
END $$;
