import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Outfit } from "next/font/google";
import "./globals.css";
import Script from "next/script";
import { TempoInit } from "@/components/tempo-init";
import { ThemeProvider } from "@/components/theme-provider";
import { CartProvider } from "@/context/cart-context";
import { Toaster } from "@/components/ui/toaster";
import TennisBallParticles from "@/components/tennis-ball-particles";
import { QueryProvider } from "@/providers/query-provider";
import BottomNavigation from "@/components/bottom-navigation";
import Preloader from "@/components/preloader";
import RoutePreloader from "@/components/route-preloader";

const outfit = Outfit({
  subsets: ["latin"],
  variable: "--font-outfit",
});

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Tennis Gear | Premium Tennis Equipment & Apparel",
  description: "Shop our collection of professional-grade tennis rackets, balls, apparel, and accessories. Trusted by amateurs and pros alike.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning className={`${outfit.variable} ${inter.variable} dark`}>
      <Script src="https://api.tempolabs.ai/proxy-asset?url=https://storage.googleapis.com/tempo-public-assets/error-handling.js" />
      <body className="font-outfit">
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <QueryProvider>
            <CartProvider>
              <Preloader />
              <RoutePreloader />
              {children}
              <Toaster />
              <TennisBallParticles />
              <BottomNavigation />
            </CartProvider>
          </QueryProvider>
        </ThemeProvider>
        <TempoInit />
      </body>
    </html>
  );
}