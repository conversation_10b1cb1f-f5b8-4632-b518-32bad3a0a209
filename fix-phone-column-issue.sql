-- Fix Phone Column Issue in Admin Order Details
-- This addresses the "column users_1.phone does not exist" error

-- Option 1: Add phone column to users table (if you want to store phone in users table)
-- Uncomment the following lines if you want to add phone to users table:
/*
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'users' 
        AND column_name = 'phone'
    ) THEN
        ALTER TABLE public.users ADD COLUMN phone TEXT;
        RAISE NOTICE 'Added phone column to users table';
    ELSE
        RAISE NOTICE 'Phone column already exists in users table';
    END IF;
END $$;
*/

-- Option 2: Verify that shipping_details in orders table contains phone
-- This is the current approach - phone is stored in shipping_details JSON

-- Check current structure of orders table
SELECT 'Current orders table structure:' as info;
SELECT 
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'orders'
AND column_name IN ('shipping_details', 'notes', 'shipping_method')
ORDER BY ordinal_position;

-- Check current structure of users table
SELECT 'Current users table structure:' as info;
SELECT 
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'users'
ORDER BY ordinal_position;

-- Verify that existing orders have phone in shipping_details
SELECT 'Sample shipping_details with phone:' as info;
SELECT 
    id,
    shipping_details->>'phone' as customer_phone,
    shipping_details->>'name' as customer_name,
    shipping_details->>'email' as customer_email
FROM public.orders 
WHERE shipping_details IS NOT NULL
LIMIT 3;

-- Update any existing orders that might not have phone in shipping_details
-- This ensures all orders have a phone field in shipping_details
UPDATE public.orders 
SET shipping_details = shipping_details || '{"phone": "+27000000000"}'::jsonb
WHERE shipping_details IS NOT NULL 
AND shipping_details->>'phone' IS NULL;

-- Verify the update
SELECT 'Orders after phone update:' as info;
SELECT 
    id,
    shipping_details->>'phone' as customer_phone,
    status,
    total_amount
FROM public.orders 
LIMIT 5;

-- Create a view for easier order queries (optional)
CREATE OR REPLACE VIEW admin_orders_view AS
SELECT 
    o.*,
    u.email as customer_email,
    u.full_name as customer_name,
    o.shipping_details->>'phone' as customer_phone,
    o.shipping_details->>'name' as shipping_name,
    o.shipping_details->>'address' as shipping_address,
    o.shipping_details->>'city' as shipping_city
FROM public.orders o
LEFT JOIN public.users u ON o.user_id = u.id;

-- Grant access to the view
GRANT SELECT ON admin_orders_view TO authenticated;
GRANT ALL ON admin_orders_view TO service_role;

SELECT 'Setup completed successfully!' as result;
