"use client";

import Link from "next/link";
import { <PERSON><PERSON><PERSON>, Check, Star, Users, Award, Zap } from "lucide-react";
import Image from "next/image";
import { But<PERSON> } from "./ui/button";
import { useState } from "react";

export default function Hero() {
  const [imageError, setImageError] = useState(false);

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-slate-50 to-green-50/30">
      {/* Subtle court texture background */}
      <div className="absolute inset-0 bg-[radial-gradient(#10b981_0.5px,transparent_0.5px)] [background-size:24px_24px] opacity-10" />

      {/* Subtle atmospheric gradient */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-green-500/5 to-transparent" />

      <div className="relative pt-20 pb-24 md:pt-28 md:pb-32">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-16">
            {/* Left Column - Text + CTA */}
            <div className="lg:w-1/2 text-center lg:text-left">
              {/* Main Headline */}
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 tracking-tight leading-tight text-slate-900">
                Elevate your game with
                <span className="block text-green-600">premium tennis gear</span>
              </h1>

              {/* Subheadline */}
              <p className="text-lg md:text-xl text-slate-600 mb-10 max-w-2xl mx-auto lg:mx-0 leading-relaxed">
                Shop our professional rackets, balls, apparel & more.
                Trusted by amateurs and pros alike.
              </p>

              {/* Primary CTA Button */}
              <div className="mb-12">
                <Button asChild size="lg" className="bg-green-600 hover:bg-green-700 text-white rounded-lg text-base font-semibold h-14 px-8 shadow-lg hover:shadow-xl transition-all duration-300 min-h-[44px]">
                  <Link href="/shop">
                    Shop Collection
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
              </div>

              {/* Feature Bar - Key Guarantees */}
              <div className="flex flex-col sm:flex-row items-center lg:items-start justify-center lg:justify-start gap-6 text-sm">
                <div className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-600" />
                  <span className="text-slate-600">Free shipping over R 1,000</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-600" />
                  <span className="text-slate-600">30-day returns</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-600" />
                  <span className="text-slate-600">Price match guarantee</span>
                </div>
              </div>
            </div>

            {/* Right Column - Hero Image with Mobile Optimization */}
            <div className="lg:w-1/2 relative w-full">
              {/* Mobile: Compact height, Desktop: Full height */}
              <div className="relative h-[280px] sm:h-[350px] md:h-[450px] lg:h-[500px] w-full rounded-2xl overflow-hidden group shadow-2xl">
                {!imageError ? (
                  <>
                    {/* Main image */}
                    <Image
                      src="https://images.unsplash.com/photo-1595435934249-5df7ed86e1c0?w=800&q=80"
                      alt="Premium tennis racket and ball - Professional tennis equipment"
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-700"
                      priority
                      onError={() => setImageError(true)}
                    />

                    {/* Subtle overlay for text readability */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent" />

                    {/* Trust Badge Overlay */}
                    <div className="absolute bottom-4 left-4 z-10">
                      <div className="bg-white/95 backdrop-blur-sm px-3 py-2 rounded-lg shadow-lg">
                        <span className="text-xs sm:text-sm font-semibold text-slate-800">Trusted by pros</span>
                      </div>
                    </div>
                  </>
                ) : (
                  /* Fallback content when image fails to load */
                  <div className="absolute inset-0 bg-gradient-to-br from-green-100 to-green-200 flex items-center justify-center">
                    <div className="text-center p-6">
                      <div className="w-16 h-16 mx-auto mb-4 bg-green-600 rounded-full flex items-center justify-center">
                        <Zap className="w-8 h-8 text-white" />
                      </div>
                      <h3 className="text-lg font-bold text-green-800 mb-2">Premium Quality</h3>
                      <p className="text-sm text-green-700">Professional tennis equipment for every player</p>
                    </div>
                  </div>
                )}

                {/* Subtle accent border */}
                <div className="absolute inset-0 rounded-2xl ring-1 ring-green-500/20"></div>
              </div>

              {/* Mobile: Additional engaging content below image */}
              <div className="mt-6 lg:hidden">
                <div className="grid grid-cols-2 gap-4">
                  {/* Quick Stats */}
                  <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-green-100">
                    <div className="flex items-center gap-2 mb-2">
                      <Star className="w-4 h-4 text-yellow-500" />
                      <span className="text-sm font-semibold text-slate-800">4.9/5</span>
                    </div>
                    <p className="text-xs text-slate-600">Customer Rating</p>
                  </div>

                  <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-green-100">
                    <div className="flex items-center gap-2 mb-2">
                      <Users className="w-4 h-4 text-green-600" />
                      <span className="text-sm font-semibold text-slate-800">10K+</span>
                    </div>
                    <p className="text-xs text-slate-600">Happy Players</p>
                  </div>
                </div>

                {/* Mobile testimonial */}
                <div className="mt-4 bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-green-100">
                  <div className="flex items-center gap-2 mb-2">
                    <Award className="w-4 h-4 text-green-600" />
                    <span className="text-sm font-semibold text-slate-800">Pro Approved</span>
                  </div>
                  <p className="text-xs text-slate-600 italic">"The quality is exceptional. These rackets have improved my game significantly."</p>
                  <p className="text-xs text-slate-500 mt-1">- Sarah M., Tennis Coach</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
