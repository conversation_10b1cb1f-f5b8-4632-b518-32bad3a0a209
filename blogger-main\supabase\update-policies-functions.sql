-- =====================================================
-- UPDATE RLS POLICIES AND FUNCTIONS FOR THABO BESTER PROJECT
-- This script updates all policies and functions to latest version
-- =====================================================

-- 1. DROP ALL EXISTING POLICIES
DROP POLICY IF EXISTS "Users can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Anyone can view published articles" ON public.articles;
DROP POLICY IF EXISTS "Authors can manage their articles" ON public.articles;
DROP POLICY IF EXISTS "Admins can manage all articles" ON public.articles;
DROP POLICY IF EXISTS "Anyone can view active products" ON public.products;
DROP POLICY IF EXISTS "Admins can manage products" ON public.products;
DROP POLICY IF EXISTS "Anyone can view categories" ON public.categories;
DROP POLICY IF EXISTS "Admins can manage categories" ON public.categories;
DROP POLICY IF EXISTS "Anyone can view product categories" ON public.product_categories;
DROP POLICY IF EXISTS "Admins can manage product categories" ON public.product_categories;
DROP POLICY IF EXISTS "Anyone can view approved comments" ON public.comments;
DROP POLICY IF EXISTS "Users can manage their comments" ON public.comments;
DROP POLICY IF EXISTS "Users can manage their likes" ON public.article_likes;
DROP POLICY IF EXISTS "Users can manage their favorites" ON public.favorites;
DROP POLICY IF EXISTS "Users can view their orders" ON public.orders;
DROP POLICY IF EXISTS "Users can create their orders" ON public.orders;
DROP POLICY IF EXISTS "Users can view their order items" ON public.order_items;
DROP POLICY IF EXISTS "Users can view their own engagement" ON public.article_engagement;
DROP POLICY IF EXISTS "Users can insert their own engagement" ON public.article_engagement;
DROP POLICY IF EXISTS "Users can update their own engagement" ON public.article_engagement;
DROP POLICY IF EXISTS "Users can delete their own engagement" ON public.article_engagement;

-- 2. CREATE UPDATED RLS POLICIES

-- Profiles policies
CREATE POLICY "Users can view all profiles" ON public.profiles FOR SELECT USING (true);
CREATE POLICY "Users can update own profile" ON public.profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON public.profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- Articles policies
CREATE POLICY "Anyone can view published articles" ON public.articles FOR SELECT USING (is_published = true OR auth.uid() = author_id);
CREATE POLICY "Authors can manage their articles" ON public.articles FOR ALL USING (auth.uid() = author_id);
CREATE POLICY "Admins can manage all articles" ON public.articles FOR ALL USING (
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin', 'author'))
);

-- Products policies
CREATE POLICY "Anyone can view active products" ON public.products FOR SELECT USING (status = 'active');
CREATE POLICY "Admins can manage products" ON public.products FOR ALL USING (
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Categories policies
CREATE POLICY "Anyone can view categories" ON public.categories FOR SELECT USING (true);
CREATE POLICY "Admins can manage categories" ON public.categories FOR ALL USING (
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Product categories policies
CREATE POLICY "Anyone can view product categories" ON public.product_categories FOR SELECT USING (is_active = true);
CREATE POLICY "Admins can manage product categories" ON public.product_categories FOR ALL USING (
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Comments policies
CREATE POLICY "Anyone can view approved comments" ON public.comments FOR SELECT USING (is_approved = true);
CREATE POLICY "Users can manage their comments" ON public.comments FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Admins can manage all comments" ON public.comments FOR ALL USING (
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Article likes policies
CREATE POLICY "Users can manage their likes" ON public.article_likes FOR ALL USING (auth.uid() = user_id);

-- Favorites policies
CREATE POLICY "Users can manage their favorites" ON public.favorites FOR ALL USING (auth.uid() = user_id);

-- Orders policies
CREATE POLICY "Users can view their orders" ON public.orders FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create their orders" ON public.orders FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Admins can view all orders" ON public.orders FOR SELECT USING (
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Order items policies
CREATE POLICY "Users can view their order items" ON public.order_items FOR SELECT USING (
    EXISTS (SELECT 1 FROM public.orders WHERE id = order_id AND user_id = auth.uid())
);
CREATE POLICY "Admins can view all order items" ON public.order_items FOR SELECT USING (
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Article engagement policies
CREATE POLICY "Users can view their own engagement" ON public.article_engagement FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own engagement" ON public.article_engagement FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own engagement" ON public.article_engagement FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own engagement" ON public.article_engagement FOR DELETE USING (auth.uid() = user_id);

-- 3. CREATE/UPDATE FUNCTIONS

-- Function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, first_name, last_name, role, last_sign_in_at)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'first_name', NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
        NEW.raw_user_meta_data->>'last_name',
        CASE
            WHEN (SELECT COUNT(*) FROM auth.users WHERE id != NEW.id) = 0 THEN 'admin'
            ELSE 'user'
        END,
        NEW.last_sign_in_at
    )
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name,
        last_sign_in_at = EXCLUDED.last_sign_in_at,
        updated_at = timezone('utc'::text, now());

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop and recreate trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to handle article engagement
CREATE OR REPLACE FUNCTION handle_article_engagement(
    p_article_id UUID,
    p_liked BOOLEAN DEFAULT NULL,
    p_bookmarked BOOLEAN DEFAULT NULL
)
RETURNS public.article_engagement
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result public.article_engagement;
BEGIN
    INSERT INTO public.article_engagement (user_id, article_id, liked, bookmarked)
    VALUES (auth.uid(), p_article_id, COALESCE(p_liked, FALSE), COALESCE(p_bookmarked, FALSE))
    ON CONFLICT (user_id, article_id)
    DO UPDATE SET
        liked = CASE WHEN p_liked IS NOT NULL THEN p_liked ELSE article_engagement.liked END,
        bookmarked = CASE WHEN p_bookmarked IS NOT NULL THEN p_bookmarked ELSE article_engagement.bookmarked END,
        updated_at = NOW()
    RETURNING * INTO result;
    
    RETURN result;
END;
$$;

-- Function to get article engagement
CREATE OR REPLACE FUNCTION get_article_engagement(p_article_id UUID)
RETURNS TABLE(liked BOOLEAN, bookmarked BOOLEAN)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(ae.liked, FALSE) as liked,
        COALESCE(ae.bookmarked, FALSE) as bookmarked
    FROM public.article_engagement ae
    WHERE ae.user_id = auth.uid() AND ae.article_id = p_article_id
    UNION ALL
    SELECT FALSE, FALSE
    WHERE NOT EXISTS (
        SELECT 1 FROM public.article_engagement ae 
        WHERE ae.user_id = auth.uid() AND ae.article_id = p_article_id
    )
    LIMIT 1;
END;
$$;

-- 4. CREATE INDEXES FOR PERFORMANCE
CREATE INDEX IF NOT EXISTS idx_profiles_role ON public.profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_created_at ON public.profiles(created_at);
CREATE INDEX IF NOT EXISTS idx_articles_published ON public.articles(is_published, published_at);
CREATE INDEX IF NOT EXISTS idx_articles_author ON public.articles(author_id);
CREATE INDEX IF NOT EXISTS idx_articles_category ON public.articles(category_id);
CREATE INDEX IF NOT EXISTS idx_products_status ON public.products(status);
CREATE INDEX IF NOT EXISTS idx_products_category ON public.products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_featured ON public.products(is_featured);
CREATE INDEX IF NOT EXISTS idx_comments_article ON public.comments(article_id);
CREATE INDEX IF NOT EXISTS idx_comments_user ON public.comments(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_user ON public.orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON public.orders(status);
CREATE INDEX IF NOT EXISTS idx_article_engagement_user_id ON public.article_engagement(user_id);
CREATE INDEX IF NOT EXISTS idx_article_engagement_article_id ON public.article_engagement(article_id);
CREATE INDEX IF NOT EXISTS idx_article_engagement_liked ON public.article_engagement(liked) WHERE liked = TRUE;
CREATE INDEX IF NOT EXISTS idx_article_engagement_bookmarked ON public.article_engagement(bookmarked) WHERE bookmarked = TRUE;

-- Function to get user statistics (admin only)
CREATE OR REPLACE FUNCTION get_user_stats()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSON;
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM public.profiles
        WHERE id = auth.uid() AND role = 'admin'
    ) THEN
        RAISE EXCEPTION 'Access denied. Admin role required.';
    END IF;

    SELECT json_build_object(
        'total_users', (SELECT COUNT(*) FROM public.profiles),
        'new_users_today', (SELECT COUNT(*) FROM public.profiles WHERE created_at >= CURRENT_DATE),
        'new_users_week', (SELECT COUNT(*) FROM public.profiles WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'),
        'new_users_month', (SELECT COUNT(*) FROM public.profiles WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'),
        'admin_users', (SELECT COUNT(*) FROM public.profiles WHERE role = 'admin'),
        'author_users', (SELECT COUNT(*) FROM public.profiles WHERE role = 'author'),
        'regular_users', (SELECT COUNT(*) FROM public.profiles WHERE role = 'user'),
        'total_articles', (SELECT COUNT(*) FROM public.articles),
        'published_articles', (SELECT COUNT(*) FROM public.articles WHERE is_published = true),
        'total_orders', (SELECT COUNT(*) FROM public.orders),
        'total_revenue', (SELECT COALESCE(SUM(total_amount), 0) FROM public.orders WHERE status = 'completed')
    ) INTO result;

    RETURN result;
END;
$$;

-- Function to update user role (admin only)
CREATE OR REPLACE FUNCTION update_user_role(
    target_user_id UUID,
    new_role TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM public.profiles
        WHERE id = auth.uid() AND role = 'admin'
    ) THEN
        RAISE EXCEPTION 'Access denied. Admin role required.';
    END IF;

    IF new_role NOT IN ('user', 'author', 'admin') THEN
        RAISE EXCEPTION 'Invalid role. Must be user, author, or admin.';
    END IF;

    IF target_user_id = auth.uid() AND new_role != 'admin' THEN
        RAISE EXCEPTION 'Cannot change your own admin role.';
    END IF;

    UPDATE public.profiles
    SET role = new_role, updated_at = NOW()
    WHERE id = target_user_id;

    RETURN FOUND;
END;
$$;

-- 5. GRANT PERMISSIONS
GRANT EXECUTE ON FUNCTION handle_article_engagement TO authenticated;
GRANT EXECUTE ON FUNCTION get_article_engagement TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_stats TO authenticated;
GRANT EXECUTE ON FUNCTION update_user_role TO authenticated;

-- 6. REFRESH SCHEMA
NOTIFY pgrst, 'reload schema';

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'RLS policies and functions update completed!';
    RAISE NOTICE 'All policies have been updated with latest security rules.';
    RAISE NOTICE 'All functions have been created/updated.';
    RAISE NOTICE 'Admin functions have been added.';
    RAISE NOTICE 'Performance indexes have been added.';
END $$;
