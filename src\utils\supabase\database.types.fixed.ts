export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

/**
 * Database types for Supabase
 * Generated based on actual tables in the database
 */
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          created_at: string
          email: string | null
          full_name: string | null
          avatar_url: string | null
          role: 'admin' | 'mentor' | 'student'
          updated_at: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          email?: string | null
          full_name?: string | null
          avatar_url?: string | null
          role?: 'admin' | 'mentor' | 'student'
          updated_at?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          email?: string | null
          full_name?: string | null
          avatar_url?: string | null
          role?: 'admin' | 'mentor' | 'student'
          updated_at?: string | null
        }
        Relationships: []
      }
      products: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          name: string
          price: number
          description: string | null
          image: string | null
          category: string
          stock: number
          status: string
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          name: string
          price: number
          description?: string | null
          image?: string | null
          category: string
          stock: number
          status?: string
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          name?: string
          price?: number
          description?: string | null
          image?: string | null
          category?: string
          stock?: number
          status?: string
        }
        Relationships: []
      }
      categories: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          name: string
          image: string | null
          count: number
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          name: string
          image?: string | null
          count?: number
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          name?: string
          image?: string | null
          count?: number
        }
        Relationships: []
      }
      orders: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          user_id: string
          items: Json
          shipping_details: Json
          status: string
          payment_status: string
          total_amount: number
          yoco_payment_id: string | null
          yoco_transaction_id: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id: string
          items: Json
          shipping_details: Json
          status?: string
          payment_status?: string
          total_amount: number
          yoco_payment_id?: string | null
          yoco_transaction_id?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id?: string
          items?: Json
          shipping_details?: Json
          status?: string
          payment_status?: string
          total_amount?: number
          yoco_payment_id?: string | null
          yoco_transaction_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "orders_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      mentorship_programs: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          name: string
          description: string
          duration_months: number
          price_monthly: number
          price_upfront: number | null
          features: Json | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          name: string
          description: string
          duration_months: number
          price_monthly: number
          price_upfront?: number | null
          features?: Json | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          name?: string
          description?: string
          duration_months?: number
          price_monthly?: number
          price_upfront?: number | null
          features?: Json | null
        }
        Relationships: []
      }
      mentors: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          user_id: string
          bio: string
          specialties: string[]
          experience_years: number
          availability: Json | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id: string
          bio: string
          specialties: string[]
          experience_years: number
          availability?: Json | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id?: string
          bio?: string
          specialties?: string[]
          experience_years?: number
          availability?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "mentors_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      student_enrollments: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          student_id: string
          program_id: string
          mentor_id: string
          start_date: string
          end_date: string
          status: string
          payment_type: 'monthly' | 'upfront'
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          student_id: string
          program_id: string
          mentor_id: string
          start_date: string
          end_date: string
          status?: string
          payment_type: 'monthly' | 'upfront'
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          student_id?: string
          program_id?: string
          mentor_id?: string
          start_date?: string
          end_date?: string
          status?: string
          payment_type?: 'monthly' | 'upfront'
        }
        Relationships: [
          {
            foreignKeyName: "student_enrollments_student_id_fkey"
            columns: ["student_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_enrollments_program_id_fkey"
            columns: ["program_id"]
            referencedRelation: "mentorship_programs"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_enrollments_mentor_id_fkey"
            columns: ["mentor_id"]
            referencedRelation: "mentors"
            referencedColumns: ["id"]
          }
        ]
      }
      mentorship_sessions: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          enrollment_id: string
          scheduled_at: string
          duration_minutes: number
          status: string
          notes: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          enrollment_id: string
          scheduled_at: string
          duration_minutes: number
          status?: string
          notes?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          enrollment_id?: string
          scheduled_at?: string
          duration_minutes?: number
          status?: string
          notes?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "mentorship_sessions_enrollment_id_fkey"
            columns: ["enrollment_id"]
            referencedRelation: "student_enrollments"
            referencedColumns: ["id"]
          }
        ]
      }
      resources: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          title: string
          description: string | null
          type: 'document' | 'video' | 'training' | 'progress'
          category: string
          format: string
          file_path: string
          size_bytes: number
          download_count: number
          created_by: string
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          title: string
          description?: string | null
          type: 'document' | 'video' | 'training' | 'progress'
          category: string
          format: string
          file_path: string
          size_bytes: number
          download_count?: number
          created_by: string
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          title?: string
          description?: string | null
          type?: 'document' | 'video' | 'training' | 'progress'
          category?: string
          format?: string
          file_path?: string
          size_bytes?: number
          download_count?: number
          created_by?: string
        }
        Relationships: [
          {
            foreignKeyName: "resources_created_by_fkey"
            columns: ["created_by"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      messages: {
        Row: {
          id: string
          created_at: string
          conversation_id: string
          sender_id: string
          receiver_id: string
          content: string
          read: boolean
          attachment_url: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          conversation_id: string
          sender_id: string
          receiver_id: string
          content: string
          read?: boolean
          attachment_url?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          conversation_id?: string
          sender_id?: string
          receiver_id?: string
          content?: string
          read?: boolean
          attachment_url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "messages_sender_id_fkey"
            columns: ["sender_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_receiver_id_fkey"
            columns: ["receiver_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      subscriptions: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          user_id: string
          status: string
          price_id: string
          quantity: number
          cancel_at_period_end: boolean
          current_period_start: string
          current_period_end: string
          ended_at: string | null
          cancel_at: string | null
          canceled_at: string | null
          trial_start: string | null
          trial_end: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id: string
          status: string
          price_id: string
          quantity?: number
          cancel_at_period_end?: boolean
          current_period_start: string
          current_period_end: string
          ended_at?: string | null
          cancel_at?: string | null
          canceled_at?: string | null
          trial_start?: string | null
          trial_end?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id?: string
          status?: string
          price_id?: string
          quantity?: number
          cancel_at_period_end?: boolean
          current_period_start?: string
          current_period_end?: string
          ended_at?: string | null
          cancel_at?: string | null
          canceled_at?: string | null
          trial_start?: string | null
          trial_end?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      user_profiles: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          user_id: string
          full_name: string | null
          avatar_url: string | null
          billing_address: Json | null
          payment_method: Json | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id: string
          full_name?: string | null
          avatar_url?: string | null
          billing_address?: Json | null
          payment_method?: Json | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id?: string
          full_name?: string | null
          avatar_url?: string | null
          billing_address?: Json | null
          payment_method?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "user_profiles_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      user_roles: {
        Row: {
          id: string
          created_at: string
          user_id: string
          role: string
        }
        Insert: {
          id?: string
          created_at?: string
          user_id: string
          role: string
        }
        Update: {
          id?: string
          created_at?: string
          user_id?: string
          role?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_roles_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      role_permissions: {
        Row: {
          id: string
          created_at: string
          role: string
          resource: string
          permission: string
        }
        Insert: {
          id?: string
          created_at?: string
          role: string
          resource: string
          permission: string
        }
        Update: {
          id?: string
          created_at?: string
          role?: string
          resource?: string
          permission?: string
        }
        Relationships: []
      }
    }
    Views: {}
    Functions: {}
    Enums: {}
    CompositeTypes: {}
  }
}

// Helper types for Supabase tables
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type InsertTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type UpdateTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']
