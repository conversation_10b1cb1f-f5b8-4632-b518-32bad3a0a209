import { z } from 'zod';

/**
 * Consultation booking form schema
 * Validates user input for booking a consultation session
 */
export const consultationFormSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Valid email is required'),
  phoneNumber: z.string().min(10, 'Valid phone number is required'),
  location: z.string().min(5, 'Please specify the consultation location'),
  date: z.date({
    required_error: 'Please select a date',
  }),
  time: z.string({
    required_error: 'Please select a time',
  }),
  reason: z.string().min(10, 'Please provide at least 10 characters'),
});

export type ConsultationFormValues = z.infer<typeof consultationFormSchema>;

/**
 * Consultation schema for database records
 */
export const consultationSchema = z.object({
  id: z.string().uuid().optional(),
  user_id: z.string().optional(),
  first_name: z.string(),
  last_name: z.string(),
  email: z.string().email(),
  phone_number: z.string(),
  location: z.string(),
  scheduled_date: z.string(),
  scheduled_time: z.string(),
  reason: z.string(),
  status: z.enum(['pending', 'confirmed', 'cancelled', 'completed']).default('pending'),
  payment_reference: z.string().optional(),
  payment_status: z.enum(['pending', 'paid', 'failed', 'refunded']).optional(),
  payment_amount: z.number().optional(),
  created_at: z.string().datetime().optional(),
  updated_at: z.string().datetime().optional(),
});

export type Consultation = z.infer<typeof consultationSchema>;
