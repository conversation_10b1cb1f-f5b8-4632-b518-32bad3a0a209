/**
 * Zod schemas for Tennis-Gear database tables
 * 
 * This file exports Zod schemas that match the database structure
 * and provide runtime type validation for our application.
 */

import { z } from 'zod';

// User schema
export const userSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  full_name: z.string().nullable(),
  role: z.enum(['admin', 'mentor', 'student']).default('student'),
  created_at: z.string().datetime().nullable(),
  updated_at: z.string().datetime().nullable(),
});

export type User = z.infer<typeof userSchema>;

// Product schema
export const productSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1, "Product name is required"),
  description: z.string().nullable(),
  price: z.number().positive("Price must be positive"),
  category: z.string().min(1, "Category is required"),
  stock: z.number().int().nonnegative("Stock cannot be negative"),
  image: z.string().nullable(),
  status: z.string().default('In Stock'),
  created_at: z.string().datetime().nullable(),
  updated_at: z.string().datetime().nullable(),
});

export type Product = z.infer<typeof productSchema>;

// Product input schema (for creating/updating products)
export const productInputSchema = productSchema.omit({ 
  id: true, 
  created_at: true, 
  updated_at: true 
});

export type ProductInput = z.infer<typeof productInputSchema>;

// Order schema
export const orderSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  status: z.enum(['pending', 'processing', 'completed', 'cancelled']),
  total_amount: z.number().positive("Total amount must be positive"),
  created_at: z.string().datetime().nullable(),
  updated_at: z.string().datetime().nullable(),
});

export type Order = z.infer<typeof orderSchema>;

// Order Item schema
export const orderItemSchema = z.object({
  id: z.string().uuid(),
  order_id: z.string().uuid(),
  product_id: z.string().uuid(),
  quantity: z.number().int().positive("Quantity must be positive"),
  unit_price: z.number().positive("Unit price must be positive"),
  created_at: z.string().datetime().nullable(),
});

export type OrderItem = z.infer<typeof orderItemSchema>;

// Mentorship Program schema
export const mentorshipProgramSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1, "Program name is required"),
  description: z.string().nullable(),
  duration_months: z.number().int().positive("Duration must be positive"),
  price_monthly: z.number().positive("Monthly price must be positive"),
  price_upfront: z.number().positive("Upfront price must be positive").nullable(),
  created_at: z.string().datetime().nullable(),
  updated_at: z.string().datetime().nullable(),
});

export type MentorshipProgram = z.infer<typeof mentorshipProgramSchema>;

// Student Enrollment schema
export const studentEnrollmentSchema = z.object({
  id: z.string().uuid(),
  student_id: z.string().uuid(),
  program_id: z.string().uuid(),
  mentor_id: z.string().uuid(),
  start_date: z.string().datetime(),
  end_date: z.string().datetime(),
  payment_type: z.enum(['monthly', 'upfront']),
  status: z.enum(['active', 'completed', 'cancelled']),
  created_at: z.string().datetime().nullable(),
  updated_at: z.string().datetime().nullable(),
});

export type StudentEnrollment = z.infer<typeof studentEnrollmentSchema>;

// Resource schema
export const resourceSchema = z.object({
  id: z.string().uuid(),
  title: z.string().min(1, "Title is required"),
  description: z.string().nullable(),
  type: z.enum(['document', 'video', 'training', 'progress']),
  category: z.string().min(1, "Category is required"),
  format: z.string().min(1, "Format is required"),
  file_path: z.string().min(1, "File path is required"),
  size_bytes: z.number().int().nonnegative("Size cannot be negative"),
  download_count: z.number().int().nonnegative().default(0),
  created_by: z.string().uuid(),
  created_at: z.string().datetime().nullable(),
  updated_at: z.string().datetime().nullable(),
});

export type Resource = z.infer<typeof resourceSchema>;
