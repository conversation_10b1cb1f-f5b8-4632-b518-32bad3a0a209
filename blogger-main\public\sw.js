// Advanced Service Worker for The Chronicle
// Intelligent caching, offline support, and performance optimization

const CACHE_NAME = 'chronicle-v1.0.0';
const STATIC_CACHE = 'chronicle-static-v1.0.0';
const DYNAMIC_CACHE = 'chronicle-dynamic-v1.0.0';
const API_CACHE = 'chronicle-api-v1.0.0';
const IMAGE_CACHE = 'chronicle-images-v1.0.0';

// Cache strategies
const CACHE_STRATEGIES = {
  CACHE_FIRST: 'cache-first',
  NETWORK_FIRST: 'network-first',
  STALE_WHILE_REVALIDATE: 'stale-while-revalidate',
  NETWORK_ONLY: 'network-only',
  CACHE_ONLY: 'cache-only',
};

// Static assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/manifest.json',
  '/offline.html',
  '/assets/index.js',
  '/assets/index.css',
  '/assets/fonts/inter-var.woff2',
  '/assets/images/logo.png',
  '/assets/images/favicon.ico',
];

// API routes to cache with stale-while-revalidate
const API_ROUTES = [
  '/api/articles',
  '/api/products',
  '/api/categories',
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('Service Worker: Caching static files');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => self.skipWaiting())
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  const cacheAllowlist = [STATIC_CACHE, DYNAMIC_CACHE, API_CACHE, IMAGE_CACHE];
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (!cacheAllowlist.includes(cacheName)) {
            console.log('Service Worker: Deleting old cache', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => self.clients.claim())
  );
});

// Fetch event - handle different caching strategies
self.addEventListener('fetch', (event) => {
  const request = event.request;
  const url = new URL(request.url);
  
  // Skip non-GET requests and browser extensions
  if (request.method !== 'GET' || url.origin !== self.location.origin) {
    return;
  }
  
  // Handle API requests
  if (API_ROUTES.some(route => url.pathname.includes(route))) {
    event.respondWith(handleApiRequest(request));
    return;
  }
  
  // Handle image requests
  if (isImageRequest(request)) {
    event.respondWith(handleImageRequest(request));
    return;
  }
  
  // Handle static assets
  if (isStaticAsset(request)) {
    event.respondWith(handleStaticAsset(request));
    return;
  }
  
  // Handle navigation requests
  if (request.mode === 'navigate') {
    event.respondWith(handleNavigationRequest(request));
    return;
  }
  
  // Default strategy for other requests
  event.respondWith(
    caches.match(request).then((response) => {
      return response || fetch(request).then((fetchResponse) => {
        return caches.open(DYNAMIC_CACHE).then((cache) => {
          cache.put(request, fetchResponse.clone());
          return fetchResponse;
        });
      });
    }).catch(() => {
      // Return default offline page if navigation fails
      if (request.mode === 'navigate') {
        return caches.match('/offline.html');
      }
      return new Response('Network error happened', {
        status: 408,
        headers: { 'Content-Type': 'text/plain' },
      });
    })
  );
});

// Handle API requests with stale-while-revalidate
async function handleApiRequest(request) {
  const cache = await caches.open(API_CACHE);
  
  // Try to get from cache first
  const cachedResponse = await cache.match(request);
  
  // Clone the request for fetching
  const fetchPromise = fetch(request).then((networkResponse) => {
    // Cache the new response
    cache.put(request, networkResponse.clone());
    return networkResponse;
  }).catch((error) => {
    console.error('Service Worker: API fetch failed', error);
    // Return null to indicate network failure
    return null;
  });
  
  // Return cached response immediately if available
  if (cachedResponse) {
    // Update cache in background
    fetchPromise;
    return cachedResponse;
  }
  
  // Wait for network response if no cache
  const networkResponse = await fetchPromise;
  return networkResponse || new Response(JSON.stringify({ error: 'Network error' }), {
    status: 408,
    headers: { 'Content-Type': 'application/json' },
  });
}

// Handle image requests with cache-first
async function handleImageRequest(request) {
  const cache = await caches.open(IMAGE_CACHE);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    cache.put(request, networkResponse.clone());
    return networkResponse;
  } catch (error) {
    console.error('Service Worker: Image fetch failed', error);
    // Return placeholder image
    return caches.match('/assets/images/placeholder.png') || 
           new Response('Image not available', {
             status: 408,
             headers: { 'Content-Type': 'text/plain' },
           });
  }
}

// Handle static assets with cache-first
async function handleStaticAsset(request) {
  const cache = await caches.open(STATIC_CACHE);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    cache.put(request, networkResponse.clone());
    return networkResponse;
  } catch (error) {
    console.error('Service Worker: Static asset fetch failed', error);
    return new Response('Resource not available', {
      status: 408,
      headers: { 'Content-Type': 'text/plain' },
    });
  }
}

// Handle navigation requests with network-first
async function handleNavigationRequest(request) {
  try {
    // Try network first
    const networkResponse = await fetch(request);
    
    // Cache successful responses
    const cache = await caches.open(DYNAMIC_CACHE);
    cache.put(request, networkResponse.clone());
    
    return networkResponse;
  } catch (error) {
    console.error('Service Worker: Navigation fetch failed', error);
    
    // Fall back to cache
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // If no cache, return offline page
    return caches.match('/offline.html');
  }
}

// Helper functions
function isImageRequest(request) {
  const url = new URL(request.url);
  return /\.(png|jpg|jpeg|gif|webp|svg|ico)$/i.test(url.pathname);
}

function isStaticAsset(request) {
  const url = new URL(request.url);
  return /\.(js|css|woff|woff2|ttf|eot)$/i.test(url.pathname) ||
         url.pathname.startsWith('/assets/');
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

async function doBackgroundSync() {
  // Process offline queue
  const offlineQueue = await getOfflineQueue();
  
  if (offlineQueue.length === 0) {
    return;
  }
  
  console.log('Service Worker: Processing offline queue', offlineQueue.length);
  
  // Process each queued action
  for (const item of offlineQueue) {
    try {
      await processOfflineAction(item);
    } catch (error) {
      console.error('Failed to process offline action', error);
    }
  }
  
  // Clear processed items
  await clearOfflineQueue();
}

async function getOfflineQueue() {
  const db = await openDatabase();
  return new Promise((resolve) => {
    const transaction = db.transaction(['offlineQueue'], 'readonly');
    const store = transaction.objectStore('offlineQueue');
    const request = store.getAll();
    
    request.onsuccess = () => {
      resolve(request.result || []);
    };
    
    request.onerror = () => {
      console.error('Failed to get offline queue', request.error);
      resolve([]);
    };
  });
}

async function processOfflineAction(item) {
  // Process based on action type
  switch (item.type) {
    case 'article-like':
      return fetch('/api/articles/like', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(item.data),
      });
    
    case 'comment-add':
      return fetch('/api/comments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(item.data),
      });
      
    // Add more action types as needed
    
    default:
      console.warn('Unknown offline action type', item.type);
  }
}

async function clearOfflineQueue() {
  const db = await openDatabase();
  return new Promise((resolve) => {
    const transaction = db.transaction(['offlineQueue'], 'readwrite');
    const store = transaction.objectStore('offlineQueue');
    const request = store.clear();
    
    request.onsuccess = () => {
      resolve();
    };
    
    request.onerror = () => {
      console.error('Failed to clear offline queue', request.error);
      resolve();
    };
  });
}

// IndexedDB setup for offline queue
function openDatabase() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('ChronicleOfflineDB', 1);
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      if (!db.objectStoreNames.contains('offlineQueue')) {
        db.createObjectStore('offlineQueue', { keyPath: 'id', autoIncrement: true });
      }
    };
    
    request.onsuccess = () => {
      resolve(request.result);
    };
    
    request.onerror = () => {
      reject(request.error);
    };
  });
}

// Push notifications
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();
    
    const options = {
      body: data.body,
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      vibrate: [100, 50, 100],
      data: {
        dateOfArrival: Date.now(),
        primaryKey: data.primaryKey,
      },
      actions: [
        {
          action: 'explore',
          title: 'View Article',
          icon: '/icons/checkmark.png',
        },
        {
          action: 'close',
          title: 'Close',
          icon: '/icons/xmark.png',
        },
      ],
    };
    
    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/articles/' + event.notification.data.primaryKey)
    );
  }
});

// Message handling for cache management
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'CACHE_URLS') {
    event.waitUntil(
      caches.open(DYNAMIC_CACHE).then((cache) => {
        return cache.addAll(event.data.payload);
      })
    );
  }
  
  if (event.data && event.data.type === 'CLEAR_CACHE') {
    event.waitUntil(
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => caches.delete(cacheName))
        );
      })
    );
  }
});
