// Input Validation and Sanitization Utilities
// Comprehensive validation system for enterprise security

// Validation schemas
export interface ValidationRule {
  required?: boolean;
  type?: 'string' | 'number' | 'email' | 'url' | 'phone' | 'date' | 'boolean' | 'array' | 'object';
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean | string;
  sanitize?: boolean;
  allowedValues?: any[];
  nested?: ValidationSchema;
}

export interface ValidationSchema {
  [key: string]: ValidationRule;
}

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string[]>;
  sanitizedData: Record<string, any>;
}

// Common regex patterns
export const patterns = {
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  phone: /^\+?[\d\s\-()]{10,}$/,
  url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)$/,
  slug: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
  username: /^[a-zA-Z0-9_]{3,20}$/,
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  creditCard: /^\d{13,19}$/,
  zipCode: /^\d{5}(-\d{4})?$/,
  ipAddress: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
  hexColor: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
  uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
};

// Sanitization functions
export const sanitizers = {
  // Remove HTML tags and dangerous content
  html: (input: string): string => {
    // Simple HTML sanitization without external dependencies
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '') // Remove iframe tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .replace(/<(?!\/?(b|i|em|strong|p|br|ul|ol|li|a)\b)[^>]*>/gi, ''); // Allow only safe tags
  },

  // Strip all HTML tags
  stripHtml: (input: string): string => {
    return input.replace(/<[^>]*>/g, '');
  },

  // Sanitize for plain text
  text: (input: string): string => {
    return input
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .trim();
  },

  // Sanitize email
  email: (input: string): string => {
    return input.toLowerCase().trim();
  },

  // Sanitize URL
  url: (input: string): string => {
    try {
      const url = new URL(input);
      // Only allow http and https protocols
      if (!['http:', 'https:'].includes(url.protocol)) {
        return '';
      }
      return url.toString();
    } catch {
      return '';
    }
  },

  // Sanitize phone number
  phone: (input: string): string => {
    return input.replace(/[^\d+\-() \s]/g, '');
  },

  // Sanitize slug
  slug: (input: string): string => {
    return input
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  },

  // Sanitize filename
  filename: (input: string): string => {
    return input
      .replace(/[^a-zA-Z0-9._-]/g, '')
      .replace(/\.{2,}/g, '.')
      .substring(0, 255);
  },

  // Remove SQL injection patterns
  sql: (input: string): string => {
    return input
      .replace(/['";\\]/g, '')
      .replace(/\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b/gi, '');
  },
};

// Main validation class
export class Validator {
  private schema: ValidationSchema;
  private errors: Record<string, string[]> = {};
  private sanitizedData: Record<string, any> = {};

  constructor(schema: ValidationSchema) {
    this.schema = schema;
  }

  // Validate data against schema
  validate(data: Record<string, any>): ValidationResult {
    this.errors = {};
    this.sanitizedData = {};

    for (const [field, rule] of Object.entries(this.schema)) {
      const value = data[field];
      this.validateField(field, value, rule);
    }

    return {
      isValid: Object.keys(this.errors).length === 0,
      errors: this.errors,
      sanitizedData: this.sanitizedData,
    };
  }

  private validateField(field: string, value: any, rule: ValidationRule) {
    const fieldErrors: string[] = [];

    // Check required
    if (rule.required && (value === undefined || value === null || value === '')) {
      fieldErrors.push(`${field} is required`);
      this.errors[field] = fieldErrors;
      return;
    }

    // Skip validation if value is empty and not required
    if (!rule.required && (value === undefined || value === null || value === '')) {
      this.sanitizedData[field] = value;
      return;
    }

    // Sanitize value if needed
    let sanitizedValue = value;
    if (rule.sanitize && typeof value === 'string') {
      sanitizedValue = this.sanitizeValue(value, rule.type);
    }

    // Type validation
    if (rule.type) {
      const typeError = this.validateType(sanitizedValue, rule.type);
      if (typeError) {
        fieldErrors.push(typeError);
      }
    }

    // Length validation for strings
    if (typeof sanitizedValue === 'string') {
      if (rule.minLength && sanitizedValue.length < rule.minLength) {
        fieldErrors.push(`${field} must be at least ${rule.minLength} characters`);
      }
      if (rule.maxLength && sanitizedValue.length > rule.maxLength) {
        fieldErrors.push(`${field} must be no more than ${rule.maxLength} characters`);
      }
    }

    // Numeric validation
    if (typeof sanitizedValue === 'number') {
      if (rule.min !== undefined && sanitizedValue < rule.min) {
        fieldErrors.push(`${field} must be at least ${rule.min}`);
      }
      if (rule.max !== undefined && sanitizedValue > rule.max) {
        fieldErrors.push(`${field} must be no more than ${rule.max}`);
      }
    }

    // Pattern validation
    if (rule.pattern && typeof sanitizedValue === 'string') {
      if (!rule.pattern.test(sanitizedValue)) {
        fieldErrors.push(`${field} format is invalid`);
      }
    }

    // Allowed values validation
    if (rule.allowedValues && !rule.allowedValues.includes(sanitizedValue)) {
      fieldErrors.push(`${field} must be one of: ${rule.allowedValues.join(', ')}`);
    }

    // Custom validation
    if (rule.custom) {
      const customResult = rule.custom(sanitizedValue);
      if (customResult !== true) {
        fieldErrors.push(typeof customResult === 'string' ? customResult : `${field} is invalid`);
      }
    }

    // Nested object validation
    if (rule.nested && typeof sanitizedValue === 'object' && sanitizedValue !== null) {
      const nestedValidator = new Validator(rule.nested);
      const nestedResult = nestedValidator.validate(sanitizedValue);
      if (!nestedResult.isValid) {
        Object.entries(nestedResult.errors).forEach(([nestedField, nestedErrors]) => {
          fieldErrors.push(...nestedErrors.map(error => `${field}.${nestedField}: ${error}`));
        });
      } else {
        sanitizedValue = nestedResult.sanitizedData;
      }
    }

    if (fieldErrors.length > 0) {
      this.errors[field] = fieldErrors;
    }

    this.sanitizedData[field] = sanitizedValue;
  }

  private validateType(value: any, type: string): string | null {
    switch (type) {
      case 'string':
        return typeof value !== 'string' ? 'must be a string' : null;
      case 'number':
        return typeof value !== 'number' || isNaN(value) ? 'must be a number' : null;
      case 'boolean':
        return typeof value !== 'boolean' ? 'must be a boolean' : null;
      case 'array':
        return !Array.isArray(value) ? 'must be an array' : null;
      case 'object':
        return typeof value !== 'object' || Array.isArray(value) ? 'must be an object' : null;
      case 'email':
        return typeof value === 'string' && patterns.email.test(value) ? null : 'must be a valid email';
      case 'url':
        return typeof value === 'string' && patterns.url.test(value) ? null : 'must be a valid URL';
      case 'phone':
        return typeof value === 'string' && patterns.phone.test(value) ? null : 'must be a valid phone number';
      case 'date':
        return value instanceof Date || !isNaN(Date.parse(value)) ? null : 'must be a valid date';
      default:
        return null;
    }
  }

  private sanitizeValue(value: string, type?: string): string {
    switch (type) {
      case 'email':
        return sanitizers.email(value);
      case 'url':
        return sanitizers.url(value);
      case 'phone':
        return sanitizers.phone(value);
      default:
        return sanitizers.text(value);
    }
  }
}

// Predefined validation schemas
export const schemas = {
  // User registration
  userRegistration: {
    email: { required: true, type: 'email', sanitize: true },
    password: { required: true, type: 'string', minLength: 8, pattern: patterns.password },
    confirmPassword: { required: true, type: 'string' },
    firstName: { required: true, type: 'string', minLength: 2, maxLength: 50, sanitize: true },
    lastName: { required: true, type: 'string', minLength: 2, maxLength: 50, sanitize: true },
    terms: { required: true, type: 'boolean' },
  },

  // User login
  userLogin: {
    email: { required: true, type: 'email', sanitize: true },
    password: { required: true, type: 'string', minLength: 1 },
    rememberMe: { type: 'boolean' },
  },

  // Article creation
  articleCreation: {
    title: { required: true, type: 'string', minLength: 5, maxLength: 200, sanitize: true },
    content: { required: true, type: 'string', minLength: 100, sanitize: true },
    excerpt: { type: 'string', maxLength: 500, sanitize: true },
    category: { required: true, type: 'string', sanitize: true },
    tags: { type: 'array' },
    isPremium: { type: 'boolean' },
    featuredImage: { type: 'url', sanitize: true },
  },

  // Product creation
  productCreation: {
    name: { required: true, type: 'string', minLength: 3, maxLength: 100, sanitize: true },
    description: { required: true, type: 'string', minLength: 10, sanitize: true },
    price: { required: true, type: 'number', min: 0 },
    category: { required: true, type: 'string', sanitize: true },
    type: { required: true, type: 'string', allowedValues: ['digital', 'physical'] },
    stock: { type: 'number', min: 0 },
  },

  // Contact form
  contactForm: {
    name: { required: true, type: 'string', minLength: 2, maxLength: 100, sanitize: true },
    email: { required: true, type: 'email', sanitize: true },
    subject: { required: true, type: 'string', minLength: 5, maxLength: 200, sanitize: true },
    message: { required: true, type: 'string', minLength: 10, maxLength: 2000, sanitize: true },
  },

  // Newsletter signup
  newsletterSignup: {
    email: { required: true, type: 'email', sanitize: true },
    preferences: { type: 'array' },
  },
};

// Utility functions
export const validationUtils = {
  // Quick validation function
  validate: (data: Record<string, any>, schema: ValidationSchema): ValidationResult => {
    const validator = new Validator(schema);
    return validator.validate(data);
  },

  // Sanitize HTML content
  sanitizeHtml: (html: string, allowedTags?: string[]): string => {
    return sanitizers.html(html);
  },

  // Check password strength
  checkPasswordStrength: (password: string): { score: number; feedback: string[] } => {
    const feedback: string[] = [];
    let score = 0;

    if (password.length >= 8) score++;
    else feedback.push('Use at least 8 characters');

    if (/[a-z]/.test(password)) score++;
    else feedback.push('Include lowercase letters');

    if (/[A-Z]/.test(password)) score++;
    else feedback.push('Include uppercase letters');

    if (/\d/.test(password)) score++;
    else feedback.push('Include numbers');

    if (/[@$!%*?&]/.test(password)) score++;
    else feedback.push('Include special characters');

    return { score, feedback };
  },

  // Generate secure random string
  generateSecureToken: (length: number = 32): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },
};

export default Validator;
