import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Zap,
  TrendingUp,
  TrendingDown,
  Clock,
  Globe,
  Smartphone,
  Monitor,
  Wifi,
  Image,
  FileText,
  Database,
  CheckCircle,
  AlertTriangle,
  XCircle
} from 'lucide-react';

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  target: number;
  status: 'good' | 'warning' | 'poor';
  trend: 'up' | 'down' | 'stable';
}

interface CoreWebVital {
  name: string;
  value: number;
  unit: string;
  threshold: { good: number; poor: number };
  status: 'good' | 'needs-improvement' | 'poor';
  description: string;
}

export function Performance() {
  const [selectedTimeframe, setSelectedTimeframe] = useState('24h');

  const coreWebVitals: CoreWebVital[] = [
    {
      name: 'Largest Contentful Paint (LCP)',
      value: 1.8,
      unit: 's',
      threshold: { good: 2.5, poor: 4.0 },
      status: 'good',
      description: 'Measures loading performance'
    },
    {
      name: 'First Input Delay (FID)',
      value: 45,
      unit: 'ms',
      threshold: { good: 100, poor: 300 },
      status: 'good',
      description: 'Measures interactivity'
    },
    {
      name: 'Cumulative Layout Shift (CLS)',
      value: 0.08,
      unit: '',
      threshold: { good: 0.1, poor: 0.25 },
      status: 'good',
      description: 'Measures visual stability'
    }
  ];

  const performanceMetrics: PerformanceMetric[] = [
    { name: 'Page Load Time', value: 2.1, unit: 's', target: 3.0, status: 'good', trend: 'down' },
    { name: 'Time to Interactive', value: 3.2, unit: 's', target: 5.0, status: 'good', trend: 'stable' },
    { name: 'First Byte Time', value: 180, unit: 'ms', target: 200, status: 'good', trend: 'up' },
    { name: 'Bundle Size', value: 245, unit: 'KB', target: 300, status: 'good', trend: 'down' },
  ];

  const deviceMetrics = [
    { device: 'Desktop', score: 95, color: 'text-green-600' },
    { device: 'Mobile', score: 87, color: 'text-yellow-600' },
    { device: 'Tablet', score: 92, color: 'text-green-600' },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good':
        return 'bg-green-100 text-green-800';
      case 'warning':
      case 'needs-improvement':
        return 'bg-yellow-100 text-yellow-800';
      case 'poor':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good':
        return <CheckCircle className="w-4 h-4" />;
      case 'warning':
      case 'needs-improvement':
        return <AlertTriangle className="w-4 h-4" />;
      case 'poor':
        return <XCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-red-500" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-green-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getDeviceIcon = (device: string) => {
    switch (device) {
      case 'Desktop':
        return <Monitor className="w-5 h-5" />;
      case 'Mobile':
        return <Smartphone className="w-5 h-5" />;
      case 'Tablet':
        return <Monitor className="w-5 h-5" />;
      default:
        return <Globe className="w-5 h-5" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Performance Dashboard</h1>
          <p className="text-gray-600">Monitor Core Web Vitals and site performance metrics</p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
            className="border rounded-lg px-3 py-2"
            title="Select timeframe"
            aria-label="Select timeframe for performance data"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
          <Button variant="outline">
            <Zap className="w-4 h-4 mr-2" />
            Run Audit
          </Button>
        </div>
      </div>

      {/* Core Web Vitals */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Zap className="w-5 h-5 mr-2" />
            Core Web Vitals
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {coreWebVitals.map((vital) => (
              <div key={vital.name} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-sm">{vital.name}</h3>
                  <Badge className={getStatusColor(vital.status)}>
                    {getStatusIcon(vital.status)}
                    <span className="ml-1">{vital.status}</span>
                  </Badge>
                </div>
                <p className="text-3xl font-bold text-gray-900">
                  {vital.value}{vital.unit}
                </p>
                <p className="text-sm text-gray-600 mt-1">{vital.description}</p>
                <div className="mt-3">
                  <div className="flex justify-between text-xs text-gray-500 mb-1">
                    <span>Good: &lt;{vital.threshold.good}{vital.unit}</span>
                    <span>Poor: &gt;{vital.threshold.poor}{vital.unit}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        vital.status === 'good' ? 'bg-green-500' :
                        vital.status === 'needs-improvement' ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      data-width={Math.min((vital.value / vital.threshold.poor) * 100, 100)}
                      style={{ width: `${Math.min((vital.value / vital.threshold.poor) * 100, 100)}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Performance Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="w-5 h-5 mr-2" />
            Performance Metrics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {performanceMetrics.map((metric) => (
              <div key={metric.name} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-sm">{metric.name}</h3>
                  <div className="flex items-center space-x-1">
                    <Badge className={getStatusColor(metric.status)}>
                      {getStatusIcon(metric.status)}
                    </Badge>
                    {getTrendIcon(metric.trend)}
                  </div>
                </div>
                <p className="text-2xl font-bold text-gray-900">
                  {metric.value}{metric.unit}
                </p>
                <p className="text-sm text-gray-600">
                  Target: {metric.target}{metric.unit}
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Device Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Globe className="w-5 h-5 mr-2" />
              Performance by Device
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {deviceMetrics.map((device) => (
                <div key={device.device} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="text-gray-400">
                      {getDeviceIcon(device.device)}
                    </div>
                    <span className="font-medium">{device.device}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          device.score >= 90 ? 'bg-green-500' :
                          device.score >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        data-width={device.score}
                        style={{ width: `${device.score}%` }}
                      ></div>
                    </div>
                    <span className={`font-bold ${device.color}`}>{device.score}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="w-5 h-5 mr-2" />
              Optimization Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-start space-x-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                <div>
                  <p className="font-medium text-green-800">Images Optimized</p>
                  <p className="text-sm text-green-600">All images are properly compressed and sized</p>
                </div>
              </div>

              <div className="flex items-start space-x-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                <div>
                  <p className="font-medium text-yellow-800">Enable Text Compression</p>
                  <p className="text-sm text-yellow-600">Compress text-based resources with gzip</p>
                </div>
              </div>

              <div className="flex items-start space-x-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <Database className="w-5 h-5 text-blue-600 mt-0.5" />
                <div>
                  <p className="font-medium text-blue-800">Optimize Database Queries</p>
                  <p className="text-sm text-blue-600">Some queries could be optimized for better performance</p>
                </div>
              </div>

              <div className="flex items-start space-x-3 p-3 bg-purple-50 border border-purple-200 rounded-lg">
                <Wifi className="w-5 h-5 text-purple-600 mt-0.5" />
                <div>
                  <p className="font-medium text-purple-800">Implement Service Worker</p>
                  <p className="text-sm text-purple-600">Add offline capabilities and caching</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Resource Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Image className="w-5 h-5 mr-2" />
            Resource Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">245 KB</div>
              <div className="text-sm text-gray-600">JavaScript</div>
              <div className="text-xs text-gray-500">12 files</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">89 KB</div>
              <div className="text-sm text-gray-600">CSS</div>
              <div className="text-xs text-gray-500">5 files</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">1.2 MB</div>
              <div className="text-sm text-gray-600">Images</div>
              <div className="text-xs text-gray-500">23 files</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">34 KB</div>
              <div className="text-sm text-gray-600">Fonts</div>
              <div className="text-xs text-gray-500">3 files</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
