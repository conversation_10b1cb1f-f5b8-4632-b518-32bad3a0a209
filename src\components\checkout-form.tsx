"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useCart } from "@/context/cart-context";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { CreditCard, Truck, ShieldCheck, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { formatCurrency } from "@/lib/format";
import { createClient } from "@/utils/supabase/client";

interface UserData {
  id: string;
  full_name?: string;
  email?: string;
  shipping_details?: {
    first_name?: string;
    last_name?: string;
    address?: string;
    city?: string;
    postal_code?: string;
    province?: string;
    phone?: string;
    alternative_phone?: string;
  };
}

interface CheckoutFormProps {
  userData?: UserData;
  userId: string;
}

export function CheckoutForm({ userData, userId }: CheckoutFormProps) {
  const { cartItems, subtotal, clearCart } = useCart();
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [saveShippingInfo, setSaveShippingInfo] = useState(true);
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    address: "",
    city: "",
    postalCode: "",
    province: "",
    phone: "",
    alternativePhone: "",
    email: "",
    shippingMethod: "standard",
    notes: "",
    termsAccepted: false
  });

  // Populate form with user data if available
  useEffect(() => {
    if (userData) {
      const fullName = userData.full_name || '';
      const nameParts = fullName.split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      // If user has shipping details, use those
      if (userData.shipping_details) {
        setFormData(prev => ({
          ...prev,
          firstName: userData.shipping_details?.first_name || firstName,
          lastName: userData.shipping_details?.last_name || lastName,
          address: userData.shipping_details?.address || '',
          city: userData.shipping_details?.city || '',
          postalCode: userData.shipping_details?.postal_code || '',
          province: userData.shipping_details?.province || '',
          phone: userData.shipping_details?.phone || '',
          alternativePhone: userData.shipping_details?.alternative_phone || '',
          email: userData.email || ''
        }));
      } else {
        // Otherwise just use the basic user info
        setFormData(prev => ({
          ...prev,
          firstName,
          lastName,
          email: userData.email || ''
        }));
      }
    }
  }, [userData]);

  const shipping = subtotal > 1000 ? 0 : 99.99;
  const total = subtotal + shipping;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
  };

  const handleSelectChange = (id: string, value: string) => {
    setFormData(prev => ({ ...prev, [id]: value }));
  };

  const handleCheckboxChange = (id: string, checked: boolean) => {
    setFormData(prev => ({ ...prev, [id]: checked }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.termsAccepted) {
      toast({
        title: "Terms and Conditions",
        description: "Please accept the Terms and Conditions to proceed",
        variant: "destructive"
      });
      return;
    }

    // Validate form
    const requiredFields = ['firstName', 'lastName', 'address', 'city', 'postalCode', 'province', 'phone', 'email'];
    const missingFields = requiredFields.filter(field => !formData[field as keyof typeof formData]);

    if (missingFields.length > 0) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);

    try {
      // Prepare shipping details
      const shippingDetails = {
        name: `${formData.firstName} ${formData.lastName}`,
        address: formData.address,
        city: formData.city,
        postal_code: formData.postalCode,
        province: formData.province,
        country: "South Africa",
        phone: formData.phone,
        alternative_phone: formData.alternativePhone,
        email: formData.email
      };

      // If user wants to save shipping info, update their profile
      if (saveShippingInfo) {
        try {
          const supabase = createClient();
          const { error } = await supabase
            .from('users')
            .update({
              shipping_details: {
                first_name: formData.firstName,
                last_name: formData.lastName,
                address: formData.address,
                city: formData.city,
                postal_code: formData.postalCode,
                province: formData.province,
                phone: formData.phone,
                alternative_phone: formData.alternativePhone
              }
            })
            .eq('id', userId);

          if (error) {
            console.error('Error saving shipping information:', error);
          }
        } catch (err) {
          console.error('Error updating user profile:', err);
        }
      }

      // Create checkout session with Yoco payment gateway
      const response = await fetch('/api/yoco-checkout-simple', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          items: cartItems,
          shippingDetails,
          userId: userId, // Pass the user ID to associate the order
          notes: formData.notes,
          shippingMethod: formData.shippingMethod
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Something went wrong');
      }

      // Redirect to Stripe checkout
      if (data.url) {
        clearCart(); // Clear cart before redirecting
        window.location.href = data.url;
      } else {
        throw new Error('No checkout URL returned');
      }
    } catch (error: any) {
      console.error('Error creating checkout session:', error);
      toast({
        title: "Checkout Error",
        description: error.message || "Failed to create checkout session",
        variant: "destructive"
      });
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
        {/* Checkout Form */}
        <div className="lg:col-span-2 space-y-8">
          {/* Shipping Information */}
          <div className="bg-card border border-border rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-6">Shipping Information</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  placeholder="Enter your first name"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name *</Label>
                <Input
                  id="lastName"
                  placeholder="Enter your last name"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="address">Street Address *</Label>
                <Input
                  id="address"
                  placeholder="Enter your street address"
                  value={formData.address}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="city">City *</Label>
                <Input
                  id="city"
                  placeholder="Enter your city"
                  value={formData.city}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="postalCode">Postal Code *</Label>
                <Input
                  id="postalCode"
                  placeholder="Enter your postal code"
                  value={formData.postalCode}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="province">Province *</Label>
                <Select
                  onValueChange={(value) => handleSelectChange("province", value)}
                  value={formData.province}
                  required
                >
                  <SelectTrigger id="province">
                    <SelectValue placeholder="Select province" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="gauteng">Gauteng</SelectItem>
                    <SelectItem value="western-cape">Western Cape</SelectItem>
                    <SelectItem value="eastern-cape">Eastern Cape</SelectItem>
                    <SelectItem value="kwazulu-natal">KwaZulu-Natal</SelectItem>
                    <SelectItem value="free-state">Free State</SelectItem>
                    <SelectItem value="north-west">North West</SelectItem>
                    <SelectItem value="mpumalanga">Mpumalanga</SelectItem>
                    <SelectItem value="limpopo">Limpopo</SelectItem>
                    <SelectItem value="northern-cape">Northern Cape</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  placeholder="Enter your phone number"
                  value={formData.phone}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="alternativePhone">Alternative Phone Number</Label>
                <Input
                  id="alternativePhone"
                  placeholder="Enter alternative phone number"
                  value={formData.alternativePhone}
                  onChange={handleInputChange}
                />
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email address"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>
          </div>

          {/* Shipping Method */}
          <div className="bg-card border border-border rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-6">Shipping Method</h2>

            <RadioGroup
              defaultValue="standard"
              value={formData.shippingMethod}
              onValueChange={(value) => handleSelectChange("shippingMethod", value)}
            >
              <div className="flex items-start space-x-3 mb-4">
                <RadioGroupItem value="standard" id="standard" className="mt-1" />
                <div className="grid gap-1.5">
                  <Label htmlFor="standard" className="font-medium">Standard Shipping (3-5 business days)</Label>
                  <p className="text-sm text-muted-foreground">
                    {shipping === 0 ? 'Free' : formatCurrency(shipping)}
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <RadioGroupItem value="express" id="express" className="mt-1" />
                <div className="grid gap-1.5">
                  <Label htmlFor="express" className="font-medium">Express Shipping (1-2 business days)</Label>
                  <p className="text-sm text-muted-foreground">{formatCurrency(199.99)}</p>
                </div>
              </div>
            </RadioGroup>
          </div>

          {/* Additional Notes */}
          <div className="bg-card border border-border rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Additional Notes</h2>
            <div className="space-y-2">
              <Label htmlFor="notes">Order Notes (Optional)</Label>
              <textarea
                id="notes"
                className="w-full min-h-[100px] rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                placeholder="Special instructions for delivery or any other notes"
                value={formData.notes}
                onChange={handleInputChange}
              ></textarea>
            </div>
          </div>
        </div>

        {/* Order Summary */}
        <div className="lg:col-span-1">
          <div className="bg-card border border-border rounded-lg p-6 sticky top-6">
            <h2 className="text-xl font-semibold mb-6">Order Summary</h2>

            <div className="space-y-4 mb-6">
              {cartItems.map((item) => (
                <div key={item.id} className="flex gap-4">
                  <div className="relative w-16 h-16 rounded-md overflow-hidden border border-border flex-shrink-0">
                    <img src={item.image} alt={item.name} className="object-cover w-full h-full" />
                    <div className="absolute top-0 right-0 bg-primary text-primary-foreground w-5 h-5 rounded-full flex items-center justify-center text-xs">
                      {item.quantity}
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium text-sm">{item.name}</h3>
                    <p className="text-muted-foreground text-sm">
                      {formatCurrency(item.price)}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            <div className="space-y-3 border-t border-border pt-4 mb-6">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Subtotal</span>
                <span>{formatCurrency(subtotal)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Shipping</span>
                <span>
                  {shipping === 0 ? 'Free' : formatCurrency(shipping)}
                </span>
              </div>
              <div className="flex justify-between font-medium text-base pt-2 border-t border-border">
                <span>Total</span>
                <span className="text-primary">{formatCurrency(total)}</span>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-start space-x-2">
                <Checkbox
                  id="saveShippingInfo"
                  className="mt-1"
                  checked={saveShippingInfo}
                  onCheckedChange={(checked) => setSaveShippingInfo(checked as boolean)}
                />
                <div>
                  <Label htmlFor="saveShippingInfo" className="text-sm">
                    Save shipping information for future orders
                  </Label>
                </div>
              </div>

              <div className="flex items-start space-x-2">
                <Checkbox
                  id="termsAccepted"
                  className="mt-1"
                  checked={formData.termsAccepted}
                  onCheckedChange={(checked) => handleCheckboxChange("termsAccepted", checked as boolean)}
                />
                <div>
                  <Label htmlFor="termsAccepted" className="text-sm">
                    I agree to the <a href="/terms" className="text-primary hover:underline">Terms and Conditions</a> and <a href="/privacy" className="text-primary hover:underline">Privacy Policy</a>
                  </Label>
                </div>
              </div>

              <Button className="w-full" type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  "Proceed to Payment"
                )}
              </Button>

              <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground mt-4">
                <ShieldCheck className="h-4 w-4" />
                <span>Secure checkout</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  );
}
