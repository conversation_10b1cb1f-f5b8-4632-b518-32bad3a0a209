import { FormMessage, Message } from "@/components/form-message";
import Navbar from "@/components/navbar";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { signInWithCustomRedirect } from "./actions";
import Footer from "@/components/footer";
import { FaRegUser, FaLock } from "react-icons/fa";
import Image from "next/image";

interface LoginProps {
  searchParams: {
    message?: string;
    error?: string;
    redirect_to?: string;
    [key: string]: string | undefined;
  };
}

export default async function SignInPage({ searchParams }: LoginProps) {
  const message = searchParams as unknown as Message;
  const redirectTo = searchParams.redirect_to || '/';

  if ("message" in message) {
    return (
      <div className="flex h-screen w-full flex-1 items-center justify-center p-4 sm:max-w-md">
        <FormMessage message={message} />
      </div>
    );
  }

  return (
    <>
      {/* Desktop Navbar */}
      <div className="hidden md:block">
        {/* <Navbar /> */}
      </div>

      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-background via-muted/20 to-background px-4 py-8 relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 overflow-hidden -z-10">
          <div className="absolute h-[400px] w-[400px] rounded-full gradient-purple top-[-200px] left-[-200px] blur-[100px] opacity-30 animate-pulse"></div>
          <div className="absolute h-[300px] w-[300px] rounded-full gradient-blue bottom-[-150px] right-[-150px] blur-[80px] opacity-30 animate-pulse"></div>
        </div>

        <div className="w-full max-w-md">
           {/* Logo Avatar */}
                    <div className="text-center mb-8">
                      <div className="relative mx-auto w-28 h-28 mb-6">
                        <div className="w-full h-full rounded-full p-1 neo-shadow">
                          <div className="w-full h-full rounded-full bg-blur flex items-center justify-center">
                            <Link
                              href="/"
                              className="text-center"
                            >
                            <Image
                              src="/logo.svg"
                              alt="Logo"
                              width={70}
                              height={70}
                              className=" object-contain"
                              />
                              </Link>
                          </div>
                        </div>
                      </div>
            <h1 className="text-2xl font-bold tracking-tight text-foreground mb-2">WELCOME BACK !</h1>
            <p className="text-muted-foreground text-sm">
              Login to your account
            </p>
          </div>

          <div className="glass-effect-dark rounded-3xl p-8 neo-shadow">
            <form className="flex flex-col space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="relative">
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="username"
                      required
                      className="w-full h-14 pl-12 rounded-2xl bg-muted/30 border-0 neo-shadow-inset placeholder:text-muted-foreground/70"
                    />
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                        <span className="text-sm"><FaRegUser /></span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="relative">
                    <Input
                      id="password"
                      type="password"
                      name="password"
                      placeholder="password"
                      required
                      className="w-full h-14 pl-12 rounded-2xl bg-muted/30 border-0 neo-shadow-inset placeholder:text-muted-foreground/70"
                    />
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                        <span className="text-sm"><FaLock /></span>
                      
                    </div>
                  </div>
                </div>
              </div>

              <SubmitButton
                className="w-full h-14 rounded-2xl gradient-blue text-white font-semibold text-lg neo-shadow hover:neo-shadow-light transition-neo border-0"
                pendingText="Signing in..."
                formAction={async (formData: FormData) => {
                  "use server";
                  return signInWithCustomRedirect(formData, redirectTo);
                }}
              >
                Login
              </SubmitButton>

              <FormMessage message={message} />

              <div className="text-center text-sm text-muted-foreground">
                Forgot your password?
              </div>

              <div className="text-center text-sm text-muted-foreground">
                Don't have an account?{" "}
                <Link
                  className="text-primary font-medium hover:underline transition-colors"
                  href={`/sign-up${redirectTo ? `?redirect=${encodeURIComponent(redirectTo)}` : ''}`}
                >
                  Sign up
                </Link>
              </div>
            </form>
          </div>
        </div>
      </div>
      {/* <Footer /> */}
    </>
  );
}
