# Database Setup Guide

This guide will help you set up the Thabo Bester project database and resolve common issues.

## Quick Start

### 1. Fresh Database Setup

If you're setting up the database for the first time:

1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Run `supabase/complete-setup.sql` - This sets up all tables, policies, and triggers
4. Run `supabase/seed.sql` - This populates the database with sample data and creates storage buckets

### 2. Fix "User Already Exists" Issue

If you're getting "user already exists" errors when trying to create new users:

1. **IMPORTANT**: This will delete ALL existing data and users
2. Go to your Supabase project dashboard
3. Navigate to the SQL Editor
4. Run `supabase/reset-database.sql` - This completely resets the database
5. Run `supabase/complete-setup.sql` - This recreates the schema
6. Run `supabase/seed.sql` - This adds sample data

### 3. Storage Buckets

The `seed.sql` file automatically creates these storage buckets:

- **avatars**: User profile pictures (50MB limit, images only)
- **articles**: Article media (50MB limit, images/videos/audio)
- **products**: Product images (50MB limit, images only)
- **media**: General media uploads (100MB limit, all media types + PDFs)

All buckets are configured with proper policies for authenticated users.

## NPM Scripts

Use these convenient commands:

```bash
# Get help with database commands
npm run db:help

# Set up database schema (run complete-setup.sql)
npm run db:setup

# Populate with sample data (run seed.sql)
npm run db:seed

# Reset database - WARNING: Deletes all data! (run reset-database.sql)
npm run db:reset
```

## File Descriptions

### `complete-setup.sql`
- Creates all database tables
- Sets up Row Level Security (RLS) policies
- Creates functions and triggers for user management
- Configures proper relationships between tables
- **Safe to run multiple times** (uses IF NOT EXISTS)

### `seed.sql`
- Creates storage buckets with proper policies
- Populates categories and product categories
- Adds sample products
- Sets up the first user as admin automatically
- **Safe to run multiple times** (uses ON CONFLICT DO NOTHING)

### `reset-database.sql`
- **DANGER**: Completely wipes all data
- Deletes all users from auth.users
- Clears all storage buckets
- Resets all tables
- Use only when you need a completely fresh start

### `setup-database.sql` (Legacy)
- Original setup file
- Use `complete-setup.sql` instead for new setups

## Admin User Setup

The first user to sign up will automatically become an admin. This is handled by the trigger in `complete-setup.sql`.

If you need to manually make a user an admin:

```sql
UPDATE public.profiles 
SET role = 'admin' 
WHERE email = '<EMAIL>';
```

## Troubleshooting

### Storage Upload Errors

If you're getting storage upload errors:

1. Make sure you've run `seed.sql` to create the storage buckets
2. Check that the user is authenticated
3. Verify the file size is within limits
4. Ensure the file type is allowed

### RLS Policy Errors

If you're getting permission denied errors:

1. Make sure you've run `complete-setup.sql` to create all policies
2. Check that the user is properly authenticated
3. Verify the user's role if accessing admin features

### User Creation Issues

If users can't sign up:

1. Check if the `handle_new_user()` function exists
2. Verify the trigger `on_auth_user_created` is active
3. Make sure the profiles table exists

### Role Not Updating

If a user's role isn't updating in the app:

1. Use the "Refresh Role" button in Settings
2. Or log out and log back in
3. Check the database to ensure the role was actually updated

## Database Schema Overview

### Core Tables
- `profiles`: User profiles (extends auth.users)
- `categories`: Article categories
- `articles`: Blog articles
- `products`: E-commerce products
- `product_categories`: Product categories

### Engagement Tables
- `comments`: Article comments
- `article_likes`: Article likes/reactions
- `favorites`: User bookmarks

### E-commerce Tables
- `orders`: Purchase orders
- `order_items`: Individual items in orders

### Storage Buckets
- `avatars`: User profile pictures
- `articles`: Article media
- `products`: Product images
- `media`: General uploads

## Security Features

- **Row Level Security (RLS)** enabled on all tables
- **Proper policies** for user data access
- **Admin-only access** for content management
- **Secure file uploads** with type and size validation
- **Automatic user profile creation** on signup

## Next Steps

After setting up the database:

1. Configure your environment variables
2. Test user registration and login
3. Verify admin features work
4. Test file uploads
5. Check that all pages load correctly

For deployment instructions, see the main README.md file.
