"use client";

import React, { useEffect, useRef } from 'react';

interface TennisBallParticle {
  x: number;
  y: number;
  mx: number;
  my: number;
  size: number;
  decay: number;
  speed: number;
  spread: number;
  spreadX: number;
  spreadY: number;
  rotation: number;
  rotationSpeed: number;
}

interface TennisBallParticlesProps {
  className?: string;
}

const TennisBallParticles: React.FC<TennisBallParticlesProps> = ({ className }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const particlesRef = useRef<TennisBallParticle[]>([]);
  const pointerRef = useRef({ x: 0, y: 0, mx: 0, my: 0 });
  const requestRef = useRef<number>();
  const previousTimeRef = useRef<number>();

  const createParticle = (event: MouseEvent | PointerEvent, speed: number, spread: number): TennisBallParticle => {
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return null!;

    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    const mx = event.movementX * 0.1;
    const my = event.movementY * 0.1;

    return {
      x,
      y,
      mx,
      my,
      size: Math.random() * 2 + 6, // Tennis ball size
      decay: 0.02,
      speed: speed * 0.8,
      spread: spread * speed * 0.8,
      spreadX: (Math.random() - 0.8) * spread - mx,
      spreadY: (Math.random() - 0.8) * spread - my,
      rotation: Math.random() * Math.PI * 2,
      rotationSpeed: (Math.random() - 0.8) * 0.2
    };
  };

  const drawTennisBall = (ctx: CanvasRenderingContext2D, particle: TennisBallParticle) => {
    ctx.save();
    ctx.translate(particle.x, particle.y);
    ctx.rotate(particle.rotation);
    
    // Tennis ball base (yellow-green circle)
    ctx.fillStyle = '#39FF14'; // Brighter orange color for better visibility
    ctx.beginPath();
    ctx.arc(0, 0, particle.size, 0, Math.PI * 2);
    ctx.fill();

    // Tennis ball seam pattern
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = particle.size * 0.2;
    ctx.beginPath();
    ctx.arc(0, 0, particle.size * 0.6, -Math.PI / 4, Math.PI / 4);
    ctx.arc(0, 0, particle.size * 0.6, Math.PI * 3/4, Math.PI * 5/4);
    ctx.stroke();

    ctx.restore();
  };

  const updateParticle = (particle: TennisBallParticle) => {
    particle.x += particle.spreadX * particle.speed;
    particle.y += particle.spreadY * particle.speed;
    particle.size -= particle.decay;
    particle.rotation += particle.rotationSpeed;
    return particle.size > 0.1;
  };

  const animate = (time: number) => {
    if (!canvasRef.current) {
      console.error('Canvas ref is null in animation frame');
      return;
    }

    const ctx = canvasRef.current.getContext('2d');
    if (!ctx) {
      console.error('Could not get canvas context');
      return;
    }

    if (previousTimeRef.current === undefined) {
      previousTimeRef.current = time;
    }
    const deltaTime = time - previousTimeRef.current;

    if (deltaTime >= (1000 / 60)) { // 60 FPS cap
      ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
      
      particlesRef.current = particlesRef.current.filter(particle => {
        drawTennisBall(ctx, particle);
        return updateParticle(particle);
      });

      previousTimeRef.current = time;
    }

    requestRef.current = requestAnimationFrame(animate);
  };

  useEffect(() => {
    console.log('Tennis ball particles component mounted');
    const canvas = canvasRef.current;
    if (!canvas) {
      console.error('Canvas ref is null');
      return;
    }

    const handleResize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    const handlePointerMove = (event: PointerEvent) => {
      // Just update pointer position but don't create particles
      const rect = canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
    };

    const handleClick = (event: MouseEvent) => {
      console.log('Click detected', event.clientX, event.clientY);
      const rect = canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      
      console.log('Creating burst of particles on click');
      for (let i = 0; i < 12; i++) { // Slightly increased for better burst effect
        const particle = createParticle(event, Math.random() + 1, Math.random() * 20 + 10);
        if (particle) particlesRef.current.push(particle);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    window.addEventListener('pointermove', handlePointerMove);
    window.addEventListener('click', handleClick);

    requestRef.current = requestAnimationFrame(animate);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('pointermove', handlePointerMove);
      window.removeEventListener('click', handleClick);
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className={className}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        pointerEvents: 'none',
        zIndex: 9999,
        touchAction: 'none'
      }}
    />
  );
};

export default TennisBallParticles;
