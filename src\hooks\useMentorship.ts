import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { createClient } from '../utils/supabase/client';

export interface MentorshipProgram {
  id: string;
  name: string;
  description: string | null;
  duration_months: number;
  price_monthly: number;
  price_upfront: number | null;
  features: any;
  created_at: string;
  updated_at: string;
}

export interface StudentEnrollment {
  id: string;
  student_id: string;
  program_id: string;
  mentor_id: string;
  start_date: string;
  end_date: string;
  payment_type: 'monthly' | 'upfront';
  status: 'active' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
  program?: MentorshipProgram;
  mentor?: any;
  student?: any;
}

// Hook to fetch all mentorship programs
export const useMentorshipPrograms = () => {
  return useQuery({
    queryKey: ['mentorship-programs'],
    queryFn: async (): Promise<MentorshipProgram[]> => {
      const response = await fetch('/api/mentorship/programs');
      
      if (!response.ok) {
        throw new Error('Failed to fetch mentorship programs');
      }
      
      return response.json();
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Hook to fetch student enrollments
export const useStudentEnrollments = (studentId?: string, mentorId?: string) => {
  return useQuery({
    queryKey: ['enrollments', { studentId, mentorId }],
    queryFn: async (): Promise<StudentEnrollment[]> => {
      const params = new URLSearchParams();
      if (studentId) params.append('student_id', studentId);
      if (mentorId) params.append('mentor_id', mentorId);

      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('Authentication required');
      }

      const response = await fetch(`/api/mentorship/enrollments?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
        },
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch enrollments');
      }
      
      return response.json();
    },
    enabled: true, // Always enabled, will show user's own enrollments if no params
  });
};

// Hook to create a new enrollment
export const useCreateEnrollment = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (enrollmentData: {
      student_id: string;
      program_id: string;
      mentor_id: string;
      start_date: string;
      end_date: string;
      payment_type: 'monthly' | 'upfront';
    }): Promise<StudentEnrollment> => {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('Authentication required');
      }

      const response = await fetch('/api/mentorship/enrollments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify(enrollmentData),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create enrollment');
      }
      
      return response.json();
    },
    onSuccess: () => {
      // Invalidate and refetch enrollments
      queryClient.invalidateQueries({ queryKey: ['enrollments'] });
    },
  });
};

// Hook to create a new mentorship program (admin only)
export const useCreateMentorshipProgram = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (programData: {
      name: string;
      description?: string;
      duration_months: number;
      price_monthly: number;
      price_upfront?: number;
      features?: any;
    }): Promise<MentorshipProgram> => {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('Authentication required');
      }

      const response = await fetch('/api/mentorship/programs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify(programData),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create mentorship program');
      }
      
      return response.json();
    },
    onSuccess: () => {
      // Invalidate and refetch programs
      queryClient.invalidateQueries({ queryKey: ['mentorship-programs'] });
    },
  });
};
