import { AlertCircle, CheckCircle, Info } from "lucide-react";

export type Message =
  | { success: string }
  | { error: string }
  | { message: string };

export function FormMessage({ message }: { message: Message }) {
  if (!message || Object.keys(message).length === 0) {
    return null;
  }

  return (
    <div className="w-full">
      {"success" in message && (
        <div className="bg-green-50 dark:bg-green-950/30 text-green-700 dark:text-green-400 rounded-lg p-3 flex items-start gap-2">
          <CheckCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
          <span className="text-sm">{message.success}</span>
        </div>
      )}
      {"error" in message && (
        <div className="bg-red-50 dark:bg-red-950/30 text-red-700 dark:text-red-400 rounded-lg p-3 flex items-start gap-2">
          <AlertCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
          <span className="text-sm">{message.error}</span>
        </div>
      )}
      {"message" in message && (
        <div className="bg-blue-50 dark:bg-blue-950/30 text-blue-700 dark:text-blue-400 rounded-lg p-3 flex items-start gap-2">
          <Info className="w-4 h-4 mt-0.5 flex-shrink-0" />
          <span className="text-sm">{message.message}</span>
        </div>
      )}
    </div>
  );
}
