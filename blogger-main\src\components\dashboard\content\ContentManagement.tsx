import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { InputFile } from "@/components/ui/input-file";
import { DataTable } from "@/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Edit, Trash2, Eye, Plus, Image, FileText } from "lucide-react";

type Article = {
  id: string;
  title: string;
  excerpt: string;
  status: "published" | "draft";
  isPremium: boolean;
  createdAt: Date;
  updatedAt: Date;
};

const articles: Article[] = [
  {
    id: "1",
    title: "10 Tips for Better Web Design",
    excerpt: "Learn how to improve your web design skills with these tips.",
    status: "published",
    isPremium: true,
    createdAt: new Date(2023, 5, 15),
    updatedAt: new Date(2023, 5, 15),
  },
  {
    id: "2",
    title: "Introduction to React Hooks",
    excerpt: "Learn the basics of React Hooks and how to use them.",
    status: "published",
    isPremium: false,
    createdAt: new Date(2023, 5, 10),
    updatedAt: new Date(2023, 5, 12),
  },
  {
    id: "3",
    title: "Advanced CSS Techniques",
    excerpt: "Discover advanced CSS techniques for modern web development.",
    status: "draft",
    isPremium: true,
    createdAt: new Date(2023, 5, 5),
    updatedAt: new Date(2023, 5, 8),
  },
];

export default function ContentManagement() {
  const [activeTab, setActiveTab] = useState("articles");
  const [isPremium, setIsPremium] = useState(false);

  const columns: ColumnDef<Article>[] = [
    {
      accessorKey: "title",
      header: "Title",
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        return (
          <Badge
            className={`${status === "published" ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"}`}
          >
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Badge>
        );
      },
    },
    {
      accessorKey: "isPremium",
      header: "Type",
      cell: ({ row }) => {
        const isPremium = row.getValue("isPremium") as boolean;
        return (
          <Badge
            className={`${isPremium ? "bg-purple-100 text-purple-800" : "bg-blue-100 text-blue-800"}`}
          >
            {isPremium ? "Premium" : "Free"}
          </Badge>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const date = row.getValue("createdAt") as Date;
        return <span>{date.toLocaleDateString()}</span>;
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Eye className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Edit className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
    },
  ];

  return (
    <div className="space-y-6">
      <Card className="bg-white/90 backdrop-blur-sm border border-gray-100 rounded-2xl shadow-sm overflow-hidden">
        <CardHeader className="pb-3">
          <CardTitle className="text-xl font-semibold text-gray-900">
            Content Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs
            defaultValue="articles"
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="articles">Articles</TabsTrigger>
              <TabsTrigger value="create">Create New</TabsTrigger>
            </TabsList>

            <TabsContent value="articles" className="space-y-4">
              <DataTable
                columns={columns}
                data={articles}
                searchKey="title"
                searchPlaceholder="Search articles..."
              />
            </TabsContent>

            <TabsContent value="create" className="space-y-6">
              <div className="grid gap-6">
                <div className="grid gap-3">
                  <Label htmlFor="title">Title</Label>
                  <Input id="title" placeholder="Enter article title" />
                </div>

                <div className="grid gap-3">
                  <Label htmlFor="excerpt">Excerpt</Label>
                  <Textarea
                    id="excerpt"
                    placeholder="Brief summary of the article"
                    className="min-h-[80px]"
                  />
                </div>

                <div className="grid gap-3">
                  <Label htmlFor="content">Content</Label>
                  <Textarea
                    id="content"
                    placeholder="Write your article content here..."
                    className="min-h-[200px]"
                  />
                </div>

                <div className="grid gap-3">
                  <Label>Featured Image</Label>
                  <InputFile
                    accept="image/*"
                    icon={<Image className="h-4 w-4" />}
                  />
                </div>

                <div className="flex items-center gap-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="premium"
                      checked={isPremium}
                      onCheckedChange={setIsPremium}
                    />
                    <Label htmlFor="premium">Premium Content</Label>
                  </div>
                </div>

                <div className="flex justify-end gap-3">
                  <Button variant="outline">Save as Draft</Button>
                  <Button>Publish</Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
