-- Database functions for the blog and ecommerce platform

-- Function to increment article views
CREATE OR REPLACE FUNCTION increment_article_views(article_id uuid)
RETURNS void AS $$
BEGIN
    UPDATE public.articles 
    SET views = views + 1, updated_at = NOW()
    WHERE id = article_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get article count by category
CREATE OR REPLACE FUNCTION get_category_article_count(category_id uuid)
RETURNS integer AS $$
DECLARE
    article_count integer;
BEGIN
    SELECT COUNT(*) INTO article_count
    FROM public.articles
    WHERE category_id = get_category_article_count.category_id
    AND is_published = true;
    
    RETURN article_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get product count by category
CREATE OR REPLACE FUNCTION get_product_category_count(category_id uuid)
RETURNS integer AS $$
DECLARE
    product_count integer;
BEGIN
    SELECT COUNT(*) INTO product_count
    FROM public.products
    WHERE category_id = get_product_category_count.category_id
    AND status = 'active';
    
    RETURN product_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate cart totals
CREATE OR REPLACE FUNCTION calculate_cart_totals(cart_id uuid)
RETURNS void AS $$
DECLARE
    cart_subtotal decimal(10,2);
    cart_tax decimal(10,2);
    cart_shipping decimal(10,2);
    cart_total decimal(10,2);
BEGIN
    -- Calculate subtotal
    SELECT COALESCE(SUM(quantity * price), 0) INTO cart_subtotal
    FROM public.cart_items
    WHERE cart_id = calculate_cart_totals.cart_id;
    
    -- Calculate tax (8%)
    cart_tax := cart_subtotal * 0.08;
    
    -- Calculate shipping (free over $50)
    IF cart_subtotal >= 50 THEN
        cart_shipping := 0;
    ELSE
        cart_shipping := 9.99;
    END IF;
    
    -- Calculate total
    cart_total := cart_subtotal + cart_tax + cart_shipping;
    
    -- Update cart
    UPDATE public.carts
    SET 
        subtotal = cart_subtotal,
        tax = cart_tax,
        shipping = cart_shipping,
        total = cart_total,
        updated_at = NOW()
    WHERE id = calculate_cart_totals.cart_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger function to update cart totals when cart items change
CREATE OR REPLACE FUNCTION trigger_calculate_cart_totals()
RETURNS trigger AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        PERFORM calculate_cart_totals(OLD.cart_id);
        RETURN OLD;
    ELSE
        PERFORM calculate_cart_totals(NEW.cart_id);
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for cart items
DROP TRIGGER IF EXISTS cart_items_totals_trigger ON public.cart_items;
CREATE TRIGGER cart_items_totals_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.cart_items
    FOR EACH ROW EXECUTE FUNCTION trigger_calculate_cart_totals();

-- Function to create or get user cart
CREATE OR REPLACE FUNCTION get_or_create_user_cart(user_id uuid)
RETURNS uuid AS $$
DECLARE
    cart_id uuid;
BEGIN
    -- Try to get existing cart
    SELECT id INTO cart_id
    FROM public.carts
    WHERE user_id = get_or_create_user_cart.user_id
    LIMIT 1;
    
    -- If no cart exists, create one
    IF cart_id IS NULL THEN
        INSERT INTO public.carts (user_id)
        VALUES (get_or_create_user_cart.user_id)
        RETURNING id INTO cart_id;
    END IF;
    
    RETURN cart_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to add item to cart
CREATE OR REPLACE FUNCTION add_to_cart(
    user_id uuid,
    product_id uuid,
    quantity integer DEFAULT 1
)
RETURNS uuid AS $$
DECLARE
    cart_id uuid;
    product_price decimal(10,2);
    existing_quantity integer;
BEGIN
    -- Get or create cart
    cart_id := get_or_create_user_cart(user_id);
    
    -- Get product price
    SELECT price INTO product_price
    FROM public.products
    WHERE id = add_to_cart.product_id
    AND status = 'active';
    
    IF product_price IS NULL THEN
        RAISE EXCEPTION 'Product not found or inactive';
    END IF;
    
    -- Check if item already exists in cart
    SELECT quantity INTO existing_quantity
    FROM public.cart_items
    WHERE cart_id = add_to_cart.cart_id
    AND product_id = add_to_cart.product_id;
    
    IF existing_quantity IS NOT NULL THEN
        -- Update existing item
        UPDATE public.cart_items
        SET quantity = existing_quantity + add_to_cart.quantity,
            updated_at = NOW()
        WHERE cart_id = add_to_cart.cart_id
        AND product_id = add_to_cart.product_id;
    ELSE
        -- Insert new item
        INSERT INTO public.cart_items (cart_id, product_id, quantity, price)
        VALUES (add_to_cart.cart_id, add_to_cart.product_id, add_to_cart.quantity, product_price);
    END IF;
    
    RETURN cart_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to remove item from cart
CREATE OR REPLACE FUNCTION remove_from_cart(
    user_id uuid,
    product_id uuid
)
RETURNS void AS $$
DECLARE
    cart_id uuid;
BEGIN
    -- Get user cart
    SELECT id INTO cart_id
    FROM public.carts
    WHERE user_id = remove_from_cart.user_id;
    
    IF cart_id IS NOT NULL THEN
        DELETE FROM public.cart_items
        WHERE cart_id = remove_from_cart.cart_id
        AND product_id = remove_from_cart.product_id;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update cart item quantity
CREATE OR REPLACE FUNCTION update_cart_item_quantity(
    user_id uuid,
    product_id uuid,
    new_quantity integer
)
RETURNS void AS $$
DECLARE
    cart_id uuid;
BEGIN
    -- Get user cart
    SELECT id INTO cart_id
    FROM public.carts
    WHERE user_id = update_cart_item_quantity.user_id;
    
    IF cart_id IS NOT NULL THEN
        IF new_quantity <= 0 THEN
            -- Remove item if quantity is 0 or negative
            DELETE FROM public.cart_items
            WHERE cart_id = update_cart_item_quantity.cart_id
            AND product_id = update_cart_item_quantity.product_id;
        ELSE
            -- Update quantity
            UPDATE public.cart_items
            SET quantity = new_quantity,
                updated_at = NOW()
            WHERE cart_id = update_cart_item_quantity.cart_id
            AND product_id = update_cart_item_quantity.product_id;
        END IF;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- User Management Functions
CREATE OR REPLACE FUNCTION public.get_users_paginated(
  page_size INTEGER DEFAULT 50,
  page_offset INTEGER DEFAULT 0,
  search_term TEXT DEFAULT NULL,
  role_filter TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  -- This function would need to access auth.users which requires service role
  -- For now, return empty result and handle in application
  SELECT json_build_object(
    'users', '[]'::json,
    'total_count', 0
  ) INTO result;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.get_user_stats()
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  -- Return basic stats
  SELECT json_build_object(
    'total_users', COALESCE((SELECT COUNT(*) FROM public.profiles), 0),
    'admin_users', COALESCE((SELECT COUNT(*) FROM public.profiles WHERE role = 'admin'), 0),
    'new_users_today', 0
  ) INTO result;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.update_user_role(
  target_user_id UUID,
  new_role TEXT
)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  -- Update user role in profiles table
  UPDATE public.profiles
  SET role = new_role, updated_at = NOW()
  WHERE id = target_user_id;

  SELECT json_build_object('success', true) INTO result;
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.delete_user_account(
  target_user_id UUID
)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  -- Delete user profile (auth user deletion requires service role)
  DELETE FROM public.profiles WHERE id = target_user_id;

  SELECT json_build_object('success', true) INTO result;
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.create_favorites_table_if_not_exists()
RETURNS VOID AS $$
BEGIN
  -- Create favorites table if it doesn't exist
  CREATE TABLE IF NOT EXISTS public.favorites (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    article_id UUID REFERENCES public.articles(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, article_id)
  );

  -- Enable RLS
  ALTER TABLE public.favorites ENABLE ROW LEVEL SECURITY;

  -- Create policy
  CREATE POLICY IF NOT EXISTS "Users can manage their own favorites" ON public.favorites
    FOR ALL USING (auth.uid() = user_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle new user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, first_name, last_name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
    'user'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create profile on user signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
