// Advanced Caching System for The Chronicle
// Multi-layer caching with Redis, browser cache, and service worker

export interface CacheConfig {
  ttl: number; // Time to live in seconds
  maxSize?: number; // Maximum cache size
  strategy: 'cache-first' | 'network-first' | 'stale-while-revalidate';
  tags?: string[]; // Cache tags for invalidation
}

export interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
  tags: string[];
  etag?: string;
  lastModified?: string;
}

export interface CacheStats {
  hits: number;
  misses: number;
  size: number;
  hitRate: number;
}

class CacheManager {
  private memoryCache = new Map<string, CacheEntry>();
  private maxMemorySize = 100; // Maximum number of items in memory
  private stats: CacheStats = { hits: 0, misses: 0, size: 0, hitRate: 0 };

  constructor(private config: { maxMemorySize?: number } = {}) {
    this.maxMemorySize = config.maxMemorySize || 100;
    this.setupServiceWorkerCache();
  }

  /**
   * Get item from cache with fallback strategy
   */
  async get<T>(
    key: string,
    fetcher: () => Promise<T>,
    config: CacheConfig
  ): Promise<T> {
    const cacheKey = this.generateKey(key);

    switch (config.strategy) {
      case 'cache-first':
        return this.cacheFirst(cacheKey, fetcher, config);
      case 'network-first':
        return this.networkFirst(cacheKey, fetcher, config);
      case 'stale-while-revalidate':
        return this.staleWhileRevalidate(cacheKey, fetcher, config);
      default:
        return this.cacheFirst(cacheKey, fetcher, config);
    }
  }

  /**
   * Set item in cache
   */
  async set<T>(key: string, data: T, config: CacheConfig): Promise<void> {
    const cacheKey = this.generateKey(key);
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: config.ttl,
      tags: config.tags || [],
    };

    // Store in memory cache
    this.setMemoryCache(cacheKey, entry);

    // Store in browser cache
    await this.setBrowserCache(cacheKey, entry);

    // Store in service worker cache
    await this.setServiceWorkerCache(cacheKey, entry);

    // Store in Redis (if available)
    await this.setRedisCache(cacheKey, entry);
  }

  /**
   * Delete item from cache
   */
  async delete(key: string): Promise<void> {
    const cacheKey = this.generateKey(key);

    // Remove from memory
    this.memoryCache.delete(cacheKey);

    // Remove from browser cache
    await this.deleteBrowserCache(cacheKey);

    // Remove from service worker cache
    await this.deleteServiceWorkerCache(cacheKey);

    // Remove from Redis
    await this.deleteRedisCache(cacheKey);

    this.updateStats();
  }

  /**
   * Invalidate cache by tags
   */
  async invalidateByTags(tags: string[]): Promise<void> {
    const keysToDelete: string[] = [];

    // Find keys with matching tags in memory cache
    for (const [key, entry] of this.memoryCache.entries()) {
      if (entry.tags.some(tag => tags.includes(tag))) {
        keysToDelete.push(key);
      }
    }

    // Delete found keys
    await Promise.all(keysToDelete.map(key => this.delete(key)));
  }

  /**
   * Clear all cache
   */
  async clear(): Promise<void> {
    // Clear memory cache
    this.memoryCache.clear();

    // Clear browser cache
    await this.clearBrowserCache();

    // Clear service worker cache
    await this.clearServiceWorkerCache();

    // Clear Redis cache
    await this.clearRedisCache();

    this.stats = { hits: 0, misses: 0, size: 0, hitRate: 0 };
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    this.updateStats();
    return { ...this.stats };
  }

  // Private methods

  private async cacheFirst<T>(
    key: string,
    fetcher: () => Promise<T>,
    config: CacheConfig
  ): Promise<T> {
    // Try memory cache first
    const memoryEntry = this.getMemoryCache(key);
    if (memoryEntry && !this.isExpired(memoryEntry)) {
      this.stats.hits++;
      return memoryEntry.data;
    }

    // Try browser cache
    const browserEntry = await this.getBrowserCache(key);
    if (browserEntry && !this.isExpired(browserEntry)) {
      this.stats.hits++;
      // Update memory cache
      this.setMemoryCache(key, browserEntry);
      return browserEntry.data;
    }

    // Try service worker cache
    const swEntry = await this.getServiceWorkerCache(key);
    if (swEntry && !this.isExpired(swEntry)) {
      this.stats.hits++;
      // Update memory and browser cache
      this.setMemoryCache(key, swEntry);
      await this.setBrowserCache(key, swEntry);
      return swEntry.data;
    }

    // Try Redis cache
    const redisEntry = await this.getRedisCache(key);
    if (redisEntry && !this.isExpired(redisEntry)) {
      this.stats.hits++;
      // Update all local caches
      this.setMemoryCache(key, redisEntry);
      await this.setBrowserCache(key, redisEntry);
      await this.setServiceWorkerCache(key, redisEntry);
      return redisEntry.data;
    }

    // Cache miss - fetch from network
    this.stats.misses++;
    const data = await fetcher();
    await this.set(key, data, config);
    return data;
  }

  private async networkFirst<T>(
    key: string,
    fetcher: () => Promise<T>,
    config: CacheConfig
  ): Promise<T> {
    try {
      // Try network first
      const data = await fetcher();
      await this.set(key, data, config);
      return data;
    } catch (error) {
      // Network failed, try cache
      const entry = await this.getFromAnyCache(key);
      if (entry && !this.isExpired(entry)) {
        this.stats.hits++;
        return entry.data;
      }
      throw error;
    }
  }

  private async staleWhileRevalidate<T>(
    key: string,
    fetcher: () => Promise<T>,
    config: CacheConfig
  ): Promise<T> {
    const entry = await this.getFromAnyCache(key);

    if (entry) {
      this.stats.hits++;
      
      // Return stale data immediately
      const result = entry.data;

      // Revalidate in background if expired
      if (this.isExpired(entry)) {
        this.revalidateInBackground(key, fetcher, config);
      }

      return result;
    }

    // No cache entry, fetch from network
    this.stats.misses++;
    const data = await fetcher();
    await this.set(key, data, config);
    return data;
  }

  private async revalidateInBackground<T>(
    key: string,
    fetcher: () => Promise<T>,
    config: CacheConfig
  ): Promise<void> {
    try {
      const data = await fetcher();
      await this.set(key, data, config);
    } catch (error) {
      console.warn('Background revalidation failed:', error);
    }
  }

  private async getFromAnyCache(key: string): Promise<CacheEntry | null> {
    // Try memory cache first
    const memoryEntry = this.getMemoryCache(key);
    if (memoryEntry) return memoryEntry;

    // Try browser cache
    const browserEntry = await this.getBrowserCache(key);
    if (browserEntry) {
      this.setMemoryCache(key, browserEntry);
      return browserEntry;
    }

    // Try service worker cache
    const swEntry = await this.getServiceWorkerCache(key);
    if (swEntry) {
      this.setMemoryCache(key, swEntry);
      await this.setBrowserCache(key, swEntry);
      return swEntry;
    }

    // Try Redis cache
    const redisEntry = await this.getRedisCache(key);
    if (redisEntry) {
      this.setMemoryCache(key, redisEntry);
      await this.setBrowserCache(key, redisEntry);
      await this.setServiceWorkerCache(key, redisEntry);
      return redisEntry;
    }

    return null;
  }

  private generateKey(key: string): string {
    return `chronicle:${key}`;
  }

  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > entry.ttl * 1000;
  }

  // Memory cache methods
  private getMemoryCache(key: string): CacheEntry | null {
    return this.memoryCache.get(key) || null;
  }

  private setMemoryCache(key: string, entry: CacheEntry): void {
    // Implement LRU eviction if cache is full
    if (this.memoryCache.size >= this.maxMemorySize) {
      const firstKey = this.memoryCache.keys().next().value;
      this.memoryCache.delete(firstKey);
    }

    this.memoryCache.set(key, entry);
    this.updateStats();
  }

  // Browser cache methods (localStorage/sessionStorage)
  private async getBrowserCache(key: string): Promise<CacheEntry | null> {
    try {
      const stored = localStorage.getItem(key);
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      return null;
    }
  }

  private async setBrowserCache(key: string, entry: CacheEntry): Promise<void> {
    try {
      localStorage.setItem(key, JSON.stringify(entry));
    } catch (error) {
      // Storage quota exceeded or disabled
      console.warn('Browser cache storage failed:', error);
    }
  }

  private async deleteBrowserCache(key: string): Promise<void> {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.warn('Browser cache deletion failed:', error);
    }
  }

  private async clearBrowserCache(): Promise<void> {
    try {
      // Clear only Chronicle cache entries
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('chronicle:')) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.warn('Browser cache clear failed:', error);
    }
  }

  // Service Worker cache methods
  private setupServiceWorkerCache(): void {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js').catch(error => {
        console.warn('Service Worker registration failed:', error);
      });
    }
  }

  private async getServiceWorkerCache(key: string): Promise<CacheEntry | null> {
    if (!('caches' in window)) return null;

    try {
      const cache = await caches.open('chronicle-cache');
      const response = await cache.match(key);
      if (response) {
        const data = await response.json();
        return data;
      }
    } catch (error) {
      console.warn('Service Worker cache get failed:', error);
    }

    return null;
  }

  private async setServiceWorkerCache(key: string, entry: CacheEntry): Promise<void> {
    if (!('caches' in window)) return;

    try {
      const cache = await caches.open('chronicle-cache');
      const response = new Response(JSON.stringify(entry), {
        headers: { 'Content-Type': 'application/json' }
      });
      await cache.put(key, response);
    } catch (error) {
      console.warn('Service Worker cache set failed:', error);
    }
  }

  private async deleteServiceWorkerCache(key: string): Promise<void> {
    if (!('caches' in window)) return;

    try {
      const cache = await caches.open('chronicle-cache');
      await cache.delete(key);
    } catch (error) {
      console.warn('Service Worker cache delete failed:', error);
    }
  }

  private async clearServiceWorkerCache(): Promise<void> {
    if (!('caches' in window)) return;

    try {
      await caches.delete('chronicle-cache');
    } catch (error) {
      console.warn('Service Worker cache clear failed:', error);
    }
  }

  // Redis cache methods (for server-side or edge workers)
  private async getRedisCache(key: string): Promise<CacheEntry | null> {
    // This would be implemented on the server side or edge workers
    // For client-side, this is a no-op
    return null;
  }

  private async setRedisCache(key: string, entry: CacheEntry): Promise<void> {
    // Server-side Redis implementation
  }

  private async deleteRedisCache(key: string): Promise<void> {
    // Server-side Redis implementation
  }

  private async clearRedisCache(): Promise<void> {
    // Server-side Redis implementation
  }

  private updateStats(): void {
    this.stats.size = this.memoryCache.size;
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? this.stats.hits / total : 0;
  }
}

// Global cache instance
export const cacheManager = new CacheManager();

// Predefined cache configurations
export const cacheConfigs = {
  articles: {
    ttl: 300, // 5 minutes
    strategy: 'stale-while-revalidate' as const,
    tags: ['articles'],
  },
  products: {
    ttl: 600, // 10 minutes
    strategy: 'stale-while-revalidate' as const,
    tags: ['products'],
  },
  user: {
    ttl: 60, // 1 minute
    strategy: 'network-first' as const,
    tags: ['user'],
  },
  static: {
    ttl: 3600, // 1 hour
    strategy: 'cache-first' as const,
    tags: ['static'],
  },
} as const;

// Utility functions
export async function getCachedArticles(page: number = 1, category?: string) {
  const key = `articles:${page}:${category || 'all'}`;
  return cacheManager.get(
    key,
    () => fetchArticles(page, category),
    cacheConfigs.articles
  );
}

export async function getCachedProducts(page: number = 1, category?: string) {
  const key = `products:${page}:${category || 'all'}`;
  return cacheManager.get(
    key,
    () => fetchProducts(page, category),
    cacheConfigs.products
  );
}

export async function invalidateArticleCache() {
  await cacheManager.invalidateByTags(['articles']);
}

export async function invalidateProductCache() {
  await cacheManager.invalidateByTags(['products']);
}

// Placeholder fetch functions (to be implemented)
async function fetchArticles(page: number, category?: string): Promise<any> {
  // Implementation would go here
  throw new Error('Not implemented');
}

async function fetchProducts(page: number, category?: string): Promise<any> {
  // Implementation would go here
  throw new Error('Not implemented');
}

export default cacheManager;
