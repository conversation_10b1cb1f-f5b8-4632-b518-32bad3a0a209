import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Eye, Package, Truck, CheckCircle } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

type OrderStatus =
  | "pending"
  | "processing"
  | "shipped"
  | "delivered"
  | "cancelled";

type Order = {
  id: string;
  orderNumber: string;
  customer: string;
  email: string;
  date: Date;
  total: number;
  status: OrderStatus;
  items: Array<{
    id: string;
    name: string;
    quantity: number;
    price: number;
    type: "physical" | "digital";
  }>;
  shippingAddress?: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
};

const orders: Order[] = [
  {
    id: "1",
    orderNumber: "ORD-2023-001",
    customer: "<PERSON>",
    email: "<EMAIL>",
    date: new Date(2023, 5, 15),
    total: 49.98,
    status: "delivered",
    items: [
      {
        id: "1",
        name: "Premium Article Bundle",
        quantity: 1,
        price: 29.99,
        type: "digital",
      },
      {
        id: "3",
        name: "Branded Notebook",
        quantity: 1,
        price: 19.99,
        type: "physical",
      },
    ],
    shippingAddress: {
      street: "123 Main St",
      city: "Boston",
      state: "MA",
      zip: "02108",
      country: "USA",
    },
  },
  {
    id: "2",
    orderNumber: "ORD-2023-002",
    customer: "Jane Smith",
    email: "<EMAIL>",
    date: new Date(2023, 5, 14),
    total: 199.99,
    status: "processing",
    items: [
      {
        id: "2",
        name: "Web Development Course",
        quantity: 1,
        price: 199.99,
        type: "digital",
      },
    ],
  },
  {
    id: "3",
    orderNumber: "ORD-2023-003",
    customer: "Robert Johnson",
    email: "<EMAIL>",
    date: new Date(2023, 5, 12),
    total: 44.98,
    status: "shipped",
    items: [
      {
        id: "3",
        name: "Branded Notebook",
        quantity: 1,
        price: 24.99,
        type: "physical",
      },
      {
        id: "4",
        name: "Developer T-Shirt",
        quantity: 1,
        price: 19.99,
        type: "physical",
      },
    ],
    shippingAddress: {
      street: "456 Oak Ave",
      city: "San Francisco",
      state: "CA",
      zip: "94103",
      country: "USA",
    },
  },
];

export default function OrderManagement() {
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setIsDialogOpen(true);
  };

  const getStatusBadge = (status: OrderStatus) => {
    let badgeClass = "";
    let icon = null;

    switch (status) {
      case "pending":
        badgeClass = "bg-yellow-100 text-yellow-800";
        break;
      case "processing":
        badgeClass = "bg-blue-100 text-blue-800";
        icon = <Package className="h-3 w-3 mr-1" />;
        break;
      case "shipped":
        badgeClass = "bg-purple-100 text-purple-800";
        icon = <Truck className="h-3 w-3 mr-1" />;
        break;
      case "delivered":
        badgeClass = "bg-green-100 text-green-800";
        icon = <CheckCircle className="h-3 w-3 mr-1" />;
        break;
      case "cancelled":
        badgeClass = "bg-red-100 text-red-800";
        break;
    }

    return (
      <Badge className={`${badgeClass} flex items-center gap-1`}>
        {icon}
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const columns: ColumnDef<Order>[] = [
    {
      accessorKey: "orderNumber",
      header: "Order #",
    },
    {
      accessorKey: "customer",
      header: "Customer",
    },
    {
      accessorKey: "date",
      header: "Date",
      cell: ({ row }) => {
        const date = row.getValue("date") as Date;
        return <span>{date.toLocaleDateString()}</span>;
      },
    },
    {
      accessorKey: "total",
      header: "Total",
      cell: ({ row }) => {
        const total = parseFloat(row.getValue("total"));
        const formatted = new Intl.NumberFormat("en-ZA", {
          style: "currency",
          currency: "ZAR",
        }).format(total);
        return <span>{formatted}</span>;
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as OrderStatus;
        return getStatusBadge(status);
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const order = row.original;
        return (
          <Button
            variant="ghost"
            size="sm"
            className="flex items-center gap-1"
            onClick={() => handleViewOrder(order)}
          >
            <Eye className="h-4 w-4" />
            View
          </Button>
        );
      },
    },
  ];

  return (
    <div className="space-y-6">
      <Card className="bg-white/90 backdrop-blur-sm border border-gray-100 rounded-2xl shadow-sm overflow-hidden">
        <CardHeader className="pb-3">
          <CardTitle className="text-xl font-semibold text-gray-900">
            Order Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={orders}
            searchKey="orderNumber"
            searchPlaceholder="Search orders..."
          />
        </CardContent>
      </Card>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Order Details</DialogTitle>
            <DialogDescription>
              {selectedOrder?.orderNumber} -{" "}
              {selectedOrder?.date.toLocaleDateString()}
            </DialogDescription>
          </DialogHeader>

          {selectedOrder && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">
                    Customer Information
                  </h3>
                  <p className="mt-1 font-medium">{selectedOrder.customer}</p>
                  <p className="text-sm text-gray-500">{selectedOrder.email}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">
                    Order Status
                  </h3>
                  <div className="mt-1">
                    {getStatusBadge(selectedOrder.status)}
                  </div>
                </div>
              </div>

              {selectedOrder.shippingAddress && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">
                    Shipping Address
                  </h3>
                  <p className="mt-1 text-sm">
                    {selectedOrder.shippingAddress.street}
                    <br />
                    {selectedOrder.shippingAddress.city},{" "}
                    {selectedOrder.shippingAddress.state}{" "}
                    {selectedOrder.shippingAddress.zip}
                    <br />
                    {selectedOrder.shippingAddress.country}
                  </p>
                </div>
              )}

              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">
                  Order Items
                </h3>
                <div className="border rounded-md overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Item
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Qty
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Price
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {selectedOrder.items.map((item) => (
                        <tr key={item.id}>
                          <td className="px-4 py-3 text-sm">{item.name}</td>
                          <td className="px-4 py-3 text-sm">
                            <Badge
                              className={
                                item.type === "digital"
                                  ? "bg-blue-100 text-blue-800"
                                  : "bg-amber-100 text-amber-800"
                              }
                            >
                              {item.type}
                            </Badge>
                          </td>
                          <td className="px-4 py-3 text-sm">{item.quantity}</td>
                          <td className="px-4 py-3 text-sm">
                            {new Intl.NumberFormat("en-ZA", {
                              style: "currency",
                              currency: "ZAR",
                            }).format(item.price)}
                          </td>
                          <td className="px-4 py-3 text-sm">
                            {new Intl.NumberFormat("en-ZA", {
                              style: "currency",
                              currency: "ZAR",
                            }).format(item.price * item.quantity)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot className="bg-gray-50">
                      <tr>
                        <td
                          colSpan={4}
                          className="px-4 py-3 text-sm font-medium text-right"
                        >
                          Total:
                        </td>
                        <td className="px-4 py-3 text-sm font-medium">
                          {new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency: "USD",
                          }).format(selectedOrder.total)}
                        </td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>

              <div className="flex justify-end gap-3">
                {selectedOrder.status === "processing" && (
                  <Button variant="outline" className="gap-1">
                    <Truck className="h-4 w-4" />
                    Mark as Shipped
                  </Button>
                )}
                {selectedOrder.status === "shipped" && (
                  <Button variant="outline" className="gap-1">
                    <CheckCircle className="h-4 w-4" />
                    Mark as Delivered
                  </Button>
                )}
                <Button>Update Order</Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
