"use client";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Calendar,
  <PERSON><PERSON>hart,
  BookOpen,
  MessageCircle,
  Home,
  Settings,
  LogOut
} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";

const sidebarItems = [
  {
    title: "Overview",
    icon: Home,
    href: "/student-dashboard",
  },
  {
    title: "Progress",
    icon: BarChart,
    href: "/student-dashboard/progress",
  },
  {
    title: "Schedule",
    icon: Calendar,
    href: "/student-dashboard/schedule",
  },
  {
    title: "Resources",
    icon: BookOpen,
    href: "/student-dashboard/resources",
  },
  {
    title: "Chat",
    icon: MessageCircle,
    href: "/student-dashboard/chat",
  },
];

export default function StudentDashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  return (
    <div className="flex min-h-screen">
      {/* Sidebar */}
      <aside className="hidden md:flex w-64 flex-col bg-card border-r">
        <div className="p-6">
          <h2 className="text-2xl text-center font-bold">Tennis Whisperer</h2>
          <p className="text-sm text-center text-muted-foreground">Student Dashboard</p>
        </div>
        <nav className="flex-1 px-4 space-y-2 py-2 p-4 border-t">
          {sidebarItems.map((item) => {
            const isActive = pathname === item.href;
            return (
              <Link key={item.href} href={item.href}>
                <Button
                  variant={isActive ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start gap-2",
                    isActive && "bg-secondary"
                  )}
                >
                  <item.icon className="h-4 w-4" />
                  {item.title}
                </Button>
              </Link>
            );
          })}
        </nav>
        <div className="p-4 border-t">
          <Button variant="ghost" className="w-full justify-start gap-2">
            <Settings className="h-4 w-4" />
            Settings
          </Button>
          <Button variant="ghost" className="w-full justify-start gap-2 text-red-500 hover:text-red-600">
            <LogOut className="h-4 w-4" />
            Sign Out
          </Button>
        </div>
      </aside>

      {/* Main Content */}
      <main className="flex-1 overflow-y-auto">
        {children}
      </main>
    </div>
  );
}
