import { NextRequest, NextResponse } from 'next/server';
import { createClient, createServiceRoleClient } from '@/utils/supabase/server';
import { v4 as uuidv4 } from 'uuid';

export async function POST(request: NextRequest) {
  try {
    console.log('POST /api/upload/product-images - Request received');

    // Check authentication
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (!user) {
      console.log('POST /api/upload/product-images - No authenticated user');
      return NextResponse.json(
        { error: 'Unauthorized', details: authError?.message },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || userData.role !== 'admin') {
      console.log('POST /api/upload/product-images - User is not admin:', userData?.role);
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }

    console.log('POST /api/upload/product-images - Admin access confirmed');

    // Parse the form data
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    console.log('POST /api/upload/product-images - Files to upload:', files.length);

    // Use service role client for storage operations to bypass RLS
    const serviceSupabase = createServiceRoleClient();
    const uploadedUrls: string[] = [];

    for (const file of files) {
      try {
        // Create a unique file name
        const fileExt = file.name.split('.').pop();
        const fileName = `${uuidv4()}.${fileExt}`;
        const filePath = `images/${fileName}`;

        console.log('POST /api/upload/product-images - Uploading file:', fileName);

        // Upload the file using service role client
        const { data, error } = await serviceSupabase.storage
          .from('product-images')
          .upload(filePath, file, {
            cacheControl: '3600',
            upsert: false
          });

        if (error) {
          console.error('POST /api/upload/product-images - Upload error:', error);
          throw error;
        }

        // Get the public URL
        const { data: { publicUrl } } = serviceSupabase.storage
          .from('product-images')
          .getPublicUrl(filePath);

        uploadedUrls.push(publicUrl);
        console.log('POST /api/upload/product-images - File uploaded successfully:', publicUrl);

      } catch (fileError: any) {
        console.error('POST /api/upload/product-images - Error uploading file:', file.name, fileError);
        // Continue with other files, but log the error
      }
    }

    if (uploadedUrls.length === 0) {
      return NextResponse.json(
        { error: 'Failed to upload any files' },
        { status: 500 }
      );
    }

    console.log('POST /api/upload/product-images - Upload completed. URLs:', uploadedUrls.length);

    return NextResponse.json({
      success: true,
      urls: uploadedUrls,
      count: uploadedUrls.length
    });

  } catch (error: any) {
    console.error('POST /api/upload/product-images - General error:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Product Images Upload API',
    method: 'POST',
    usage: 'Send files as FormData with key "files"'
  });
}
