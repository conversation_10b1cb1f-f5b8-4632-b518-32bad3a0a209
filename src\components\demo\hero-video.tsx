﻿"use client";

import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowRight, ShoppingCart, Star, X, Play, Pause, Users, Award } from "lucide-react";
import { useCart } from "@/context/cart-context";
import { toast } from "@/components/ui/use-toast";

// Video configuration with different hotspots for each video
const videoConfig = [
  {
    id: 1,
    src: "/videos/1.mp4",
    title: "Master Your Tennis Game",
    subtitle: "Professional coaching & premium equipment",
    hotspots: [
      {
        id: "6-month-program",
        name: "6-Month Tennis Mastery",
        price: 1599.99,
        type: "mentorship",
        image: "/images/mentor/tc.jpg",
        rating: 4.9,
        reviews: 45,
        position: { x: 30, y: 40 },
        description: "Comprehensive tennis training program",
        features: ["Weekly 1-on-1 sessions", "Video analysis", "Training plans", "Progress tracking"]
      },
      {
        id: 1,
        name: "Pro Tour Racket",
        price: 3599.99,
        type: "product",
        image: "/images/tennis-racket.png",
        rating: 4.9,
        reviews: 128,
        position: { x: 70, y: 50 },
        description: "Professional tournament-grade racket"
      },
      {
        id: 2,
        name: "Competition Tennis Balls",
        price: 269.99,
        type: "product",
        image: "https://images.unsplash.com/photo-1592709823125-a191f07a2a5e?w=400&q=80",
        rating: 4.7,
        reviews: 94,
        position: { x: 25, y: 75 },
        description: "ITF approved tournament balls"
      }
    ]
  },
  {
    id: 2,
    src: "/videos/2.mp4",
    title: "Premium Tennis Equipment",
    subtitle: "Gear used by professionals worldwide",
    hotspots: [
      {
        id: 3,
        name: "Elite Tennis Shoes",
        price: 2349.99,
        type: "product",
        image: "https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=400&q=80",
        rating: 4.9,
        reviews: 87,
        position: { x: 60, y: 65 },
        description: "Professional court shoes"
      },
      {
        id: 4,
        name: "Tennis Polo Shirt",
        price: 899.99,
        type: "product",
        image: "/images/Tennis Polo Shirt.webp",
        rating: 4.6,
        reviews: 156,
        position: { x: 40, y: 35 },
        description: "Premium performance polo"
      },
      {
        id: 5,
        name: "Tennis Wristbands",
        price: 199.99,
        type: "product",
        image: "/images/tennis-wristbands.webp",
        rating: 4.4,
        reviews: 203,
        position: { x: 75, y: 45 },
        description: "Moisture-wicking wristbands"
      }
    ]
  },
  {
    id: 3,
    src: "/videos/3.mp4",
    title: "Tennis Accessories",
    subtitle: "Complete your tennis arsenal",
    hotspots: [
      {
        id: 6,
        name: "Tennis Bag",
        price: 1299.99,
        type: "product",
        image: "/images/accessories.png",
        rating: 4.8,
        reviews: 92,
        position: { x: 50, y: 60 },
        description: "Professional tennis bag"
      },
      {
        id: 7,
        name: "Tennis Dress",
        price: 1199.99,
        type: "product",
        image: "/images/tennis-dress.jpeg",
        rating: 4.7,
        reviews: 134,
        position: { x: 35, y: 40 },
        description: "Performance tennis dress"
      },
      {
        id: 8,
        name: "String Dampener Set",
        price: 149.99,
        type: "product",
        image: "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&q=80",
        rating: 4.5,
        reviews: 78,
        position: { x: 65, y: 30 },
        description: "Vibration dampener set"
      }
    ]
  },
  {
    id: 4,
    src: "/videos/4.mp4",
    title: "Advanced Training",
    subtitle: "Take your game to the next level",
    hotspots: [
      {
        id: "12-month-program",
        name: "12-Month Pro Development",
        price: 4199.99,
        type: "mentorship",
        image: "/images/mentor/tc1.jpg",
        rating: 5.0,
        reviews: 23,
        position: { x: 45, y: 45 },
        description: "Advanced tennis coaching program",
        features: ["Bi-weekly sessions", "Tournament prep", "Mental coaching", "Nutrition guidance"]
      },
      {
        id: 9,
        name: "Training Cones Set",
        price: 299.99,
        type: "product",
        image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&q=80",
        rating: 4.3,
        reviews: 167,
        position: { x: 25, y: 65 },
        description: "Agility training cones"
      },
      {
        id: 10,
        name: "Resistance Bands",
        price: 399.99,
        type: "product",
        image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&q=80",
        rating: 4.6,
        reviews: 89,
        position: { x: 70, y: 35 },
        description: "Tennis-specific resistance bands"
      }
    ]
  }
];

// Hotspot Component
function VideoHotspot({
  hotspot,
  isActive,
  onClick
}: {
  hotspot: any,
  isActive: boolean,
  onClick: () => void
}) {
  const isMentorship = hotspot.type === 'mentorship';

  return (
    <motion.div
      className="absolute cursor-pointer z-10"
      style={{
        left: `${hotspot.position.x}%`,
        top: `${hotspot.position.y}%`,
        transform: 'translate(-50%, -50%)'
      }}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
    >
      {/* Hotspot Indicator */}
      <div className="relative">
        <motion.div
          className={`w-8 h-8 rounded-full flex items-center justify-center shadow-lg ${
            isMentorship
              ? 'bg-blue-600'
              : 'bg-green-600'
          }`}
          animate={{
            scale: isActive ? 1.2 : 1,
            boxShadow: isActive
              ? `0 0 20px ${isMentorship ? 'rgba(37, 99, 235, 0.6)' : 'rgba(34, 197, 94, 0.6)'}`
              : "0 4px 12px rgba(0, 0, 0, 0.3)"
          }}
          transition={{ duration: 0.3 }}
        >
          {isMentorship ? (
            <Users className="w-4 h-4 text-white" />
          ) : (
            <ShoppingCart className="w-4 h-4 text-white" />
          )}
        </motion.div>

        {/* Pulse Animation */}
        <motion.div
          className={`absolute inset-0 rounded-full ${
            isMentorship ? 'bg-blue-600' : 'bg-green-600'
          }`}
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.7, 0, 0.7]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>
    </motion.div>
  );
}

// Product/Mentorship Card Component
function HotspotCard({
  hotspot,
  onClose,
  onAddToCart,
  onEnrollMentorship
}: {
  hotspot: any,
  onClose: () => void,
  onAddToCart: (product: any) => void,
  onEnrollMentorship: (program: any) => void
}) {
  const isMentorship = hotspot.type === 'mentorship';

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8, y: 20 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.8, y: 20 }}
      transition={{ duration: 0.3 }}
      className="absolute z-20"
      style={{
        left: `${Math.min(hotspot.position.x, 70)}%`,
        top: `${Math.max(hotspot.position.y - 10, 10)}%`,
        transform: 'translate(-50%, -100%)'
      }}
    >
      <Card className={`w-80 bg-white/95 backdrop-blur-sm border shadow-xl ${
        isMentorship ? 'border-blue-100' : 'border-green-100'
      }`}>
        <CardContent className="p-4">
          <div className="flex justify-between items-start mb-3">
            <div className="flex items-center gap-2">
              <h3 className="font-semibold text-slate-900 text-lg">{hotspot.name}</h3>
              {isMentorship && (
                <Award className="w-4 h-4 text-blue-600" />
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-6 w-6 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          <div className="flex gap-3 mb-3">
            <img
              src={hotspot.image}
              alt={hotspot.name}
              className="w-16 h-16 object-cover rounded-lg"
            />
            <div className="flex-1">
              <p className="text-sm text-slate-600 mb-2">{hotspot.description}</p>
              <div className="flex items-center gap-2 mb-2">
                <div className="flex items-center gap-1">
                  <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                  <span className="text-xs font-medium">{hotspot.rating}</span>
                </div>
                <span className="text-xs text-slate-500">({hotspot.reviews} reviews)</span>
              </div>
              <p className={`text-lg font-bold ${
                isMentorship ? 'text-blue-600' : 'text-green-600'
              }`}>
                R {hotspot.price.toFixed(2)}
              </p>
            </div>
          </div>

          {/* Features for mentorship programs */}
          {isMentorship && hotspot.features && (
            <div className="mb-3">
              <p className="text-xs font-medium text-slate-700 mb-1">Includes:</p>
              <ul className="text-xs text-slate-600 space-y-1">
                {hotspot.features.slice(0, 3).map((feature: string, index: number) => (
                  <li key={index} className="flex items-center gap-1">
                    <div className="w-1 h-1 bg-blue-600 rounded-full"></div>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          )}

          <Button
            onClick={() => isMentorship ? onEnrollMentorship(hotspot) : onAddToCart(hotspot)}
            className={`w-full text-white ${
              isMentorship
                ? 'bg-blue-600 hover:bg-blue-700'
                : 'bg-green-600 hover:bg-green-700'
            }`}
            size="sm"
          >
            {isMentorship ? (
              <>
                <Users className="w-4 h-4 mr-2" />
                Enroll Now
              </>
            ) : (
              <>
                <ShoppingCart className="w-4 h-4 mr-2" />
                Add to Cart
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
}

// Main HeroVideo Component
export default function HeroVideo() {
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [activeHotspot, setActiveHotspot] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(true);
  const [videoLoaded, setVideoLoaded] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const { addToCart } = useCart();

  const currentVideo = videoConfig[currentVideoIndex];

  useEffect(() => {
    const video = videoRef.current;
    if (video) {
      const handleLoadedData = () => setVideoLoaded(true);
      const handleEnded = () => {
        // Move to next video when current one ends
        setIsTransitioning(true);
        setTimeout(() => {
          setCurrentVideoIndex((prev) => (prev + 1) % videoConfig.length);
          setActiveHotspot(null); // Close any open hotspots
          setIsTransitioning(false);
        }, 500); // 500ms fade transition
      };

      video.addEventListener('loadeddata', handleLoadedData);
      video.addEventListener('ended', handleEnded);

      // Auto-play with error handling
      const playPromise = video.play();
      if (playPromise !== undefined) {
        playPromise.catch(error => {
          console.log("Auto-play prevented:", error);
          setIsPlaying(false);
        });
      }

      return () => {
        video.removeEventListener('loadeddata', handleLoadedData);
        video.removeEventListener('ended', handleEnded);
      };
    }
  }, [currentVideoIndex]);

  const handleHotspotClick = (hotspotId: string | number) => {
    const id = String(hotspotId);
    setActiveHotspot(activeHotspot === id ? null : id);
  };

  const handleAddToCart = (product: any) => {
    addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image
    });
    setActiveHotspot(null);
    toast({
      title: "Added to Cart!",
      description: `${product.name} has been added to your cart.`,
    });
  };

  const handleEnrollMentorship = (program: any) => {
    // For demo purposes, we'll show a toast. In production, this would redirect to enrollment
    toast({
      title: "Mentorship Program",
      description: `Redirecting to ${program.name} enrollment...`,
    });
    setActiveHotspot(null);
    // In production: router.push('/mentorship/enroll?program=' + program.id);
  };

  const togglePlayPause = () => {
    const video = videoRef.current;
    if (video) {
      if (isPlaying) {
        video.pause();
      } else {
        video.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const nextVideo = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      setCurrentVideoIndex((prev) => (prev + 1) % videoConfig.length);
      setActiveHotspot(null);
      setIsTransitioning(false);
    }, 300);
  };

  const prevVideo = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      setCurrentVideoIndex((prev) => (prev - 1 + videoConfig.length) % videoConfig.length);
      setActiveHotspot(null);
      setIsTransitioning(false);
    }, 300);
  };

  return (
    <section className="relative h-screen overflow-hidden">
      {/* Video Background */}
      <div className="absolute inset-0">
        <motion.video
          ref={videoRef}
          key={currentVideoIndex}
          className="w-full h-full object-cover"
          autoPlay
          muted
          playsInline
          poster="https://images.unsplash.com/photo-1554068865-24cecd4e34b8?w=1200&q=80"
          initial={{ opacity: 0 }}
          animate={{ opacity: isTransitioning ? 0 : 1 }}
          transition={{ duration: 0.5 }}
        >
          <source src={currentVideo.src} type="video/mp4" />
        </motion.video>

        {/* Video Overlay */}
        <div className="absolute inset-0 bg-black/40" />
      </div>

      {/* Interactive Hotspots */}
      {videoLoaded && !isTransitioning && currentVideo.hotspots.map((hotspot) => (
        <VideoHotspot
          key={hotspot.id}
          hotspot={hotspot}
          isActive={activeHotspot === String(hotspot.id)}
          onClick={() => handleHotspotClick(hotspot.id)}
        />
      ))}

      {/* Hotspot Cards */}
      <AnimatePresence>
        {activeHotspot && !isTransitioning && (
          <HotspotCard
            hotspot={currentVideo.hotspots.find(h => String(h.id) === activeHotspot)!}
            onClose={() => setActiveHotspot(null)}
            onAddToCart={handleAddToCart}
            onEnrollMentorship={handleEnrollMentorship}
          />
        )}
      </AnimatePresence>

      {/* Video Controls */}
      <div className="absolute bottom-4 left-4 z-10 flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={togglePlayPause}
          className="bg-white/90 backdrop-blur-sm"
        >
          {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
        </Button>
      </div>

      {/* Video Indicators */}
      <div className="absolute bottom-4 right-4 z-10 flex gap-2">
        {videoConfig.map((_, index) => (
          <button
            key={index}
            onClick={() => {
              setIsTransitioning(true);
              setTimeout(() => {
                setCurrentVideoIndex(index);
                setActiveHotspot(null);
                setIsTransitioning(false);
              }, 300);
            }}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentVideoIndex
                ? 'bg-white scale-125'
                : 'bg-white/50 hover:bg-white/75'
            }`}
            aria-label={`Go to video ${index + 1}`}
          />
        ))}
      </div>

      {/* Hero Text Overlay */}
      <div className="absolute inset-0 flex items-center justify-center text-center z-5">
        <motion.div
          key={currentVideoIndex}
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: isTransitioning ? 0 : 1, y: 0 }}
          transition={{ duration: 0.8, delay: isTransitioning ? 0 : 0.5 }}
          className="max-w-4xl px-4"
        >
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 drop-shadow-lg">
            {currentVideo.title}
          </h1>
          <p className="text-xl md:text-2xl text-white/90 mb-8 drop-shadow-md">
            {currentVideo.subtitle}
          </p>

          {/* Instruction Hint */}
          <motion.div
            animate={{ y: [0, -10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 text-white"
          >
            <ShoppingCart className="w-4 h-4" />
            <Users className="w-4 h-4" />
            <span className="text-sm">Click the hotspots to shop & learn</span>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
