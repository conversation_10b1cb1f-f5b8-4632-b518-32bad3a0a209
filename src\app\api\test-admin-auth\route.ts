import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

/**
 * Test API endpoint to verify admin authentication is working correctly
 */
export async function GET() {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'No user found',
        step: 'auth_check'
      });
    }

    // Check if user is admin
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role, email, full_name')
      .eq('id', user.id)
      .single();

    if (userError) {
      return NextResponse.json({
        success: false,
        error: `User data fetch failed: ${userError.message}`,
        step: 'user_data_fetch',
        userId: user.id
      });
    }

    if (!userData || userData.role !== 'admin') {
      return NextResponse.json({
        success: false,
        error: 'User is not admin',
        step: 'admin_check',
        userRole: userData?.role,
        userData
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Admin authentication successful',
      user: {
        id: user.id,
        email: user.email,
        role: userData.role,
        full_name: userData.full_name
      }
    });

  } catch (error: any) {
    console.error('Admin auth test error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      step: 'general_error'
    }, { status: 500 });
  }
}

export async function POST() {
  return NextResponse.json({
    message: 'Use GET method to test admin authentication'
  });
}
