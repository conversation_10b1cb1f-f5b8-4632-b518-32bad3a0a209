-- =====================================================
-- CRITICAL DATABASE SETUP FOR THABO BESTER PROJECT
-- Run this in your Supabase SQL Editor
-- =====================================================

-- 1. CREATE PROFILES TABLE
CREATE TABLE IF NOT EXISTS public.profiles (
    id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    first_name text,
    last_name text,
    email text,
    role text DEFAULT 'user' CHECK (role IN ('user', 'admin')),
    avatar_url text,
    bio text,
    website text,
    location text,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- 2. ENABLE RLS ON PROFILES
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- 3. CREATE PROFILES POLICIES
CREATE POLICY "Users can view their own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Admins can view all profiles" ON public.profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can update all profiles" ON public.profiles
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- 4. CREATE TRIGGER FUNCTION FOR NEW USER SIGNUP
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, first_name, last_name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
    'user'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. CREATE TRIGGER FOR AUTO PROFILE CREATION
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 6. CREATE CATEGORIES TABLE
CREATE TABLE IF NOT EXISTS public.categories (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text NOT NULL,
    slug text UNIQUE NOT NULL,
    description text,
    color text DEFAULT '#3B82F6',
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- 7. ENABLE RLS ON CATEGORIES
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;

-- 8. CREATE CATEGORIES POLICIES
CREATE POLICY "Anyone can view categories" ON public.categories
    FOR SELECT USING (true);

CREATE POLICY "Only admins can manage categories" ON public.categories
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- 9. INSERT DEFAULT CATEGORIES
INSERT INTO public.categories (name, slug, description, color) VALUES
    ('Technology', 'technology', 'Latest tech trends and innovations', '#3B82F6'),
    ('Business', 'business', 'Business insights and strategies', '#10B981'),
    ('Design', 'design', 'Creative design and UX/UI', '#F59E0B'),
    ('Development', 'development', 'Programming and development', '#8B5CF6'),
    ('Marketing', 'marketing', 'Marketing and growth strategies', '#EF4444')
ON CONFLICT (slug) DO NOTHING;

-- 10. CREATE ARTICLES TABLE
CREATE TABLE IF NOT EXISTS public.articles (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    title text NOT NULL,
    slug text UNIQUE NOT NULL,
    excerpt text,
    content text,
    featured_image text,
    is_published boolean DEFAULT false,
    is_premium boolean DEFAULT false,
    category_id uuid REFERENCES public.categories(id),
    author_id uuid REFERENCES public.profiles(id),
    views integer DEFAULT 0,
    likes integer DEFAULT 0,
    comments_count integer DEFAULT 0,
    read_time integer DEFAULT 5,
    published_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- 11. ENABLE RLS ON ARTICLES
ALTER TABLE public.articles ENABLE ROW LEVEL SECURITY;

-- 12. CREATE ARTICLES POLICIES
CREATE POLICY "Anyone can view published articles" ON public.articles
    FOR SELECT USING (is_published = true);

CREATE POLICY "Authors can view their own articles" ON public.articles
    FOR SELECT USING (auth.uid() = author_id);

CREATE POLICY "Admins can view all articles" ON public.articles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Authors can create articles" ON public.articles
    FOR INSERT WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Authors can update their own articles" ON public.articles
    FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Admins can manage all articles" ON public.articles
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- 13. CREATE PRODUCTS TABLE
CREATE TABLE IF NOT EXISTS public.products (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text NOT NULL,
    slug text UNIQUE NOT NULL,
    description text,
    price decimal(10,2) NOT NULL,
    sale_price decimal(10,2),
    image_url text,
    gallery text[],
    type text DEFAULT 'digital',
    status text DEFAULT 'active',
    stock_quantity integer,
    category_id uuid REFERENCES public.categories(id),
    rating decimal(3,2) DEFAULT 0,
    reviews_count integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- 14. ENABLE RLS ON PRODUCTS
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;

-- 15. CREATE PRODUCTS POLICIES
CREATE POLICY "Anyone can view active products" ON public.products
    FOR SELECT USING (status = 'active');

CREATE POLICY "Admins can manage all products" ON public.products
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- 16. CREATE FAVORITES TABLE
CREATE TABLE IF NOT EXISTS public.favorites (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE,
    article_id uuid REFERENCES public.articles(id) ON DELETE CASCADE,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    UNIQUE(user_id, article_id)
);

-- 17. ENABLE RLS ON FAVORITES
ALTER TABLE public.favorites ENABLE ROW LEVEL SECURITY;

-- 18. CREATE FAVORITES POLICIES
CREATE POLICY "Users can manage their own favorites" ON public.favorites
    FOR ALL USING (auth.uid() = user_id);

-- 19. CREATE STORAGE BUCKETS
INSERT INTO storage.buckets (id, name, public) VALUES 
    ('avatars', 'avatars', true),
    ('articles', 'articles', true),
    ('products', 'products', true)
ON CONFLICT (id) DO NOTHING;

-- 20. CREATE STORAGE POLICIES
CREATE POLICY "Avatar images are publicly accessible" ON storage.objects
    FOR SELECT USING (bucket_id = 'avatars');

CREATE POLICY "Users can upload their own avatar" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'avatars' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can update their own avatar" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'avatars' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- 21. CREATE FUNCTION TO UPDATE USER ROLE (ADMIN ONLY)
CREATE OR REPLACE FUNCTION public.update_user_role(
    target_user_id uuid,
    new_role text
)
RETURNS json AS $$
DECLARE
    current_user_role text;
BEGIN
    -- Check if current user is admin
    SELECT role INTO current_user_role 
    FROM public.profiles 
    WHERE id = auth.uid();
    
    IF current_user_role != 'admin' THEN
        RETURN json_build_object('success', false, 'error', 'Unauthorized');
    END IF;
    
    -- Update the target user's role
    UPDATE public.profiles 
    SET role = new_role, updated_at = NOW()
    WHERE id = target_user_id;
    
    IF FOUND THEN
        RETURN json_build_object('success', true);
    ELSE
        RETURN json_build_object('success', false, 'error', 'User not found');
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 22. GRANT NECESSARY PERMISSIONS
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

-- 23. CREATE YOUR ADMIN USER (REPLACE WITH YOUR ACTUAL USER ID)
-- You'll need to run this after creating your account
-- UPDATE public.profiles SET role = 'admin' WHERE email = '<EMAIL>';

SELECT 'Database setup completed successfully!' as status;
