import { NextResponse } from 'next/server';
import yocoConfig, { formatAmount, createPaymentUrl } from '@/lib/yoco';
import { createClient } from '@/utils/supabase/server';

// Define types for cart items
interface CartItem {
  id: number;
  name: string;
  price: number;
  image: string;
  quantity: number;
  category?: string;
}

// Simple function to generate a unique ID
function generateId(prefix = '') {
  return `${prefix}${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { items, shippingDetails, userId, notes, shippingMethod } = body;

    if (!items || items.length === 0) {
      return NextResponse.json(
        { error: 'No items provided' },
        { status: 400 }
      );
    }

    // Calculate subtotal
    const subtotal = items.reduce((sum: number, item: CartItem) => sum + (item.price * item.quantity), 0);
    
    // Add shipping cost if subtotal is less than R1000
    const shippingCost = subtotal < 1000 ? 99.99 : 0;
    
    // Calculate the total amount
    const totalAmount = subtotal + shippingCost;

    // Generate a unique order ID
    const orderId = generateId('TEC-');

    // Create a simplified item summary
    const itemSummary = items.map((item: CartItem) => `${item.quantity}x ${item.name}`).join(', ');
    
    // Create order description
    const orderDescription = `Order ${orderId}: ${itemSummary.substring(0, 100)}${itemSummary.length > 100 ? '...' : ''}`;
    
    // Create metadata for the payment
    const metadata = {
      order_id: orderId,
      user_id: userId || 'guest',
      item_count: items.length.toString(),
      shipping_method: shippingMethod || 'standard'
    };
    
    // Define success and cancel URLs
    const origin = request.headers.get('origin') || process.env.NEXT_PUBLIC_BASE_URL || '';
    const successUrl = `${origin}/order-confirmation?order_id=${orderId}`;
    const cancelUrl = `${origin}/cart`;
    
    // Store order in database first
    if (userId) {
      try {
        const supabase = await createClient();

        console.log("Creating order with ID:", orderId);
        
        // Create order record in database with TEXT ID (not UUID)
        const { error } = await supabase
          .from('orders')
          .insert({
            id: orderId,
            user_id: userId,
            items: items,
            shipping_details: shippingDetails,
            status: 'pending',
            payment_status: 'pending',
            total_amount: totalAmount,
            yoco_payment_id: orderId,
            notes: notes || null,
            shipping_method: shippingMethod || 'standard'
          });

        if (error) {
          console.error('Error creating order in database:', error);
          return NextResponse.json(
            { error: `Failed to create order: ${error.message}` },
            { status: 500 }
          );
        }
      } catch (err) {
        console.error('Error storing order:', err);
        return NextResponse.json(
          { error: 'Failed to create order' },
          { status: 500 }
        );
      }
    }
    
    try {
      // Create a payment URL using the Yoco API
      const paymentUrl = await createPaymentUrl(
        totalAmount,
        'ZAR',
        orderDescription,
        metadata,
        successUrl,
        cancelUrl
      );
      
      // Return the payment URL to the client
      return NextResponse.json({
        orderId,
        url: paymentUrl,
      });
    } catch (paymentError: any) {
      console.error('Error creating Yoco payment:', paymentError);
      return NextResponse.json(
        { error: `Payment gateway error: ${paymentError.message}` },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Error creating checkout session:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
