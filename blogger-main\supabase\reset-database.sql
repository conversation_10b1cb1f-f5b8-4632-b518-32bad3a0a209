-- =====================================================
-- DATABASE RESET SCRIPT FOR THABO BESTER PROJECT
-- Use this to completely reset the database and fix user issues
-- =====================================================

-- WARNING: This will delete ALL data. Use with caution!

-- 1. DISABLE RLS TEMPORARILY
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.articles DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.products DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.categories DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_categories DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.article_likes DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.favorites DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_items DISABLE ROW LEVEL SECURITY;

-- 2. DROP ALL TRIGGERS
DROP TRIGGER IF EXISTS assign_admin_role_trigger ON public.profiles;
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP TRIGGER IF EXISTS handle_new_user ON auth.users;

-- 3. DELETE ALL DATA FROM TABLES
TRUNCATE TABLE public.order_items CASCADE;
TRUNCATE TABLE public.orders CASCADE;
TRUNCATE TABLE public.favorites CASCADE;
TRUNCATE TABLE public.article_likes CASCADE;
TRUNCATE TABLE public.comments CASCADE;
TRUNCATE TABLE public.articles CASCADE;
TRUNCATE TABLE public.products CASCADE;
TRUNCATE TABLE public.product_categories CASCADE;
TRUNCATE TABLE public.categories CASCADE;
TRUNCATE TABLE public.profiles CASCADE;

-- 4. DELETE ALL AUTH USERS (THIS WILL REMOVE ALL EXISTING USERS)
-- WARNING: This will delete all user accounts!
DELETE FROM auth.users;

-- 5. CLEAN UP STORAGE
DELETE FROM storage.objects WHERE bucket_id IN ('avatars', 'articles', 'products', 'media');

-- 6. RESET STORAGE BUCKETS
DELETE FROM storage.buckets WHERE id IN ('avatars', 'articles', 'products', 'media');

-- 7. RE-ENABLE RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.article_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_items ENABLE ROW LEVEL SECURITY;

-- 8. RECREATE ESSENTIAL FUNCTIONS AND TRIGGERS

-- Function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Use INSERT ... ON CONFLICT to handle cases where profile might already exist
    INSERT INTO public.profiles (id, email, first_name, last_name, role)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'first_name', NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
        NEW.raw_user_meta_data->>'last_name',
        CASE
            WHEN (SELECT COUNT(*) FROM auth.users WHERE id != NEW.id) = 0 THEN 'admin'
            ELSE 'user'
        END
    )
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name,
        updated_at = timezone('utc'::text, now());

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update user profile
CREATE OR REPLACE FUNCTION public.update_user_profile()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE public.profiles
    SET 
        email = NEW.email,
        updated_at = NOW()
    WHERE id = NEW.id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for user profile updates
CREATE TRIGGER on_auth_user_updated
    AFTER UPDATE ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.update_user_profile();

-- 9. REFRESH SCHEMA
NOTIFY pgrst, 'reload schema';

-- 10. SUCCESS MESSAGE
DO $$
BEGIN
    RAISE NOTICE 'Database reset completed successfully!';
    RAISE NOTICE 'You can now run the seed.sql file to populate with sample data.';
    RAISE NOTICE 'The first user to sign up will automatically become an admin.';
END $$;
