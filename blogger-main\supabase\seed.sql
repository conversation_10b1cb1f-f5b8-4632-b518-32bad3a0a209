-- =====================================================
-- COMPREHENSIVE SEED FILE FOR THABO BESTER PROJECT
-- This file sets up the complete database with sample data
-- =====================================================

-- 1. CLEAN UP EXISTING DATA (for fresh start)
TRUNCATE TABLE public.favorites CASCADE;
TRUNCATE TABLE public.comments CASCADE;
TRUNCATE TABLE public.article_likes CASCADE;
TRUNCATE TABLE public.orders CASCADE;
TRUNCATE TABLE public.order_items CASCADE;
TRUNCATE TABLE public.products CASCADE;
TRUNCATE TABLE public.product_categories CASCADE;
TRUNCATE TABLE public.articles CASCADE;
TRUNCATE TABLE public.categories CASCADE;
TRUNCATE TABLE public.profiles CASCADE;

-- 2. CREATE STORAGE BUCKETS
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types) VALUES 
    ('avatars', 'avatars', true, 52428800, ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']),
    ('articles', 'articles', true, 52428800, ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'audio/mpeg']),
    ('products', 'products', true, 52428800, ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']),
    ('media', 'media', true, 104857600, ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'audio/mpeg', 'application/pdf'])
ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- 3. CREATE STORAGE POLICIES
-- Avatar policies
DROP POLICY IF EXISTS "Avatar images are publicly accessible" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload their own avatar" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own avatar" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own avatar" ON storage.objects;

CREATE POLICY "Avatar images are publicly accessible" ON storage.objects
    FOR SELECT USING (bucket_id = 'avatars');

CREATE POLICY "Users can upload their own avatar" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'avatars' AND 
        auth.uid() IS NOT NULL
    );

CREATE POLICY "Users can update their own avatar" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'avatars' AND 
        auth.uid() IS NOT NULL
    );

CREATE POLICY "Users can delete their own avatar" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'avatars' AND 
        auth.uid() IS NOT NULL
    );

-- Article media policies
DROP POLICY IF EXISTS "Article images are publicly accessible" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload article media" ON storage.objects;

CREATE POLICY "Article images are publicly accessible" ON storage.objects
    FOR SELECT USING (bucket_id = 'articles');

CREATE POLICY "Authenticated users can upload article media" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'articles' AND 
        auth.uid() IS NOT NULL
    );

-- Product media policies
DROP POLICY IF EXISTS "Product images are publicly accessible" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload product media" ON storage.objects;

CREATE POLICY "Product images are publicly accessible" ON storage.objects
    FOR SELECT USING (bucket_id = 'products');

CREATE POLICY "Authenticated users can upload product media" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'products' AND 
        auth.uid() IS NOT NULL
    );

-- General media policies
DROP POLICY IF EXISTS "Media files are publicly accessible" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload media" ON storage.objects;

CREATE POLICY "Media files are publicly accessible" ON storage.objects
    FOR SELECT USING (bucket_id = 'media');

CREATE POLICY "Authenticated users can upload media" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'media' AND 
        auth.uid() IS NOT NULL
    );

-- 4. INSERT CATEGORIES
INSERT INTO public.categories (id, name, slug, description, color) VALUES
('550e8400-e29b-41d4-a716-************', 'Technology', 'technology', 'Latest tech news and innovations', '#3B82F6'),
('550e8400-e29b-41d4-a716-************', 'Business', 'business', 'Business insights and market analysis', '#10B981'),
('550e8400-e29b-41d4-a716-************', 'Health', 'health', 'Health and wellness articles', '#EF4444'),
('550e8400-e29b-41d4-a716-************', 'Finance', 'finance', 'Financial news and investment advice', '#F59E0B'),
('550e8400-e29b-41d4-a716-************', 'Lifestyle', 'lifestyle', 'Lifestyle and culture content', '#8B5CF6'),
('550e8400-e29b-41d4-a716-************', 'Sports', 'sports', 'Sports news and analysis', '#EF4444'),
('550e8400-e29b-41d4-a716-************', 'Entertainment', 'entertainment', 'Entertainment and celebrity news', '#F59E0B'),
('550e8400-e29b-41d4-a716-************', 'Politics', 'politics', 'Political news and commentary', '#6B7280'),
('550e8400-e29b-41d4-a716-************', 'Science', 'science', 'Scientific discoveries and research', '#10B981'),
('550e8400-e29b-41d4-a716-************', 'Travel', 'travel', 'Travel guides and destination reviews', '#8B5CF6')
ON CONFLICT (id) DO NOTHING;

-- 5. INSERT PRODUCT CATEGORIES
INSERT INTO public.product_categories (id, name, slug, description, is_active) VALUES
('650e8400-e29b-41d4-a716-************', 'Digital Products', 'digital-products', 'Courses, ebooks, and digital downloads', true),
('650e8400-e29b-41d4-a716-************', 'Subscriptions', 'subscriptions', 'Premium memberships and subscriptions', true),
('650e8400-e29b-41d4-a716-************', 'Physical Products', 'physical-products', 'Books, merchandise, and physical items', true),
('650e8400-e29b-41d4-a716-************', 'Courses', 'courses', 'Online courses and educational content', true),
('650e8400-e29b-41d4-a716-************', 'Books', 'books', 'Physical and digital books', true),
('650e8400-e29b-41d4-a716-************', 'Merchandise', 'merchandise', 'Branded merchandise and accessories', true)
ON CONFLICT (id) DO NOTHING;

-- 6. CREATE SAMPLE ADMIN USER PROFILE
-- Note: This will be linked to actual auth users when they sign up
-- The trigger will automatically create profiles for new auth users

-- 7. INSERT SAMPLE PRODUCTS
INSERT INTO public.products (id, name, slug, description, short_description, price, sale_price, type, category_id, image_urls, stock_quantity, status, is_featured, metadata) VALUES
(
    '750e8400-e29b-41d4-a716-************',
    'Premium Membership',
    'premium-membership',
    'Get unlimited access to all premium articles, exclusive content, and member-only features. Join our community of informed readers.',
    'Unlimited access to premium content',
    29.99,
    19.99,
    'subscription',
    '650e8400-e29b-41d4-a716-************',
    '["https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&q=80"]',
    NULL,
    'active',
    true,
    '{"billing_period": "monthly", "features": ["unlimited_articles", "exclusive_content", "ad_free", "early_access"]}'
),
(
    '750e8400-e29b-41d4-a716-************',
    'Digital Marketing Masterclass',
    'digital-marketing-masterclass',
    'Learn the latest digital marketing strategies from industry experts. This comprehensive course covers SEO, social media, content marketing, and more.',
    'Complete digital marketing course',
    199.99,
    149.99,
    'digital',
    '650e8400-e29b-41d4-a716-************',
    '["https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&q=80"]',
    NULL,
    'active',
    true,
    '{"duration": "8 weeks", "modules": 12, "certificate": true, "level": "intermediate"}'
),
(
    '750e8400-e29b-41d4-a716-************',
    'The Chronicle Handbook',
    'chronicle-handbook',
    'A comprehensive guide to modern journalism and media literacy. Written by our editorial team with insights from industry professionals.',
    'Essential guide to modern journalism',
    39.99,
    NULL,
    'physical',
    '650e8400-e29b-41d4-a716-************',
    '["https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=800&q=80"]',
    50,
    'active',
    false,
    '{"pages": 320, "format": "hardcover", "isbn": "978-1234567890", "weight": "1.2kg"}'
)
ON CONFLICT (id) DO NOTHING;

-- 8. SAMPLE ARTICLES WILL BE ADDED AFTER USER CREATION
-- These will be inserted via the application when admin users are created

-- 9. UPDATE THE USER CREATION FUNCTION TO HANDLE CONFLICTS
-- This function is already created in complete-setup.sql, but we ensure it handles conflicts
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Use INSERT ... ON CONFLICT to handle cases where profile might already exist
    INSERT INTO public.profiles (id, email, first_name, last_name, role)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'first_name', NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
        NEW.raw_user_meta_data->>'last_name',
        CASE
            WHEN (SELECT COUNT(*) FROM auth.users WHERE id != NEW.id) = 0 THEN 'admin'
            ELSE 'user'
        END
    )
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name,
        updated_at = timezone('utc'::text, now());

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. REFRESH SCHEMA CACHE
NOTIFY pgrst, 'reload schema';
