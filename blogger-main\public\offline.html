<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>You're Offline - The Chronicle</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #f9f9f9;
      color: #333;
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      min-height: 100vh;
      align-items: center;
      justify-content: center;
      text-align: center;
    }
    
    .container {
      max-width: 600px;
      padding: 2rem;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin: 2rem;
    }
    
    h1 {
      color: #000;
      margin-top: 0;
      font-size: 2rem;
    }
    
    p {
      font-size: 1.1rem;
      line-height: 1.5;
      color: #555;
    }
    
    .logo {
      width: 120px;
      height: auto;
      margin-bottom: 1.5rem;
    }
    
    .button {
      display: inline-block;
      background-color: #FFD700;
      color: #000;
      padding: 0.75rem 1.5rem;
      border-radius: 4px;
      text-decoration: none;
      font-weight: 600;
      margin-top: 1.5rem;
      transition: background-color 0.2s;
    }
    
    .button:hover {
      background-color: #E6C200;
    }
    
    .cached-content {
      margin-top: 2rem;
      border-top: 1px solid #eee;
      padding-top: 1.5rem;
    }
    
    .cached-list {
      list-style: none;
      padding: 0;
      text-align: left;
    }
    
    .cached-list li {
      margin-bottom: 0.5rem;
      padding: 0.5rem;
      background-color: #f5f5f5;
      border-radius: 4px;
    }
    
    .cached-list a {
      color: #333;
      text-decoration: none;
    }
    
    .cached-list a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <div class="container">
    <img src="/assets/images/logo.png" alt="Thabo Bester Logo" class="logo">
    <h1>You're Offline</h1>
    <p>It looks like you've lost your internet connection. Don't worry - you can still access previously viewed content.</p>
    
    <a href="/" class="button">Try Again</a>
    
    <div class="cached-content">
      <h2>Available Offline</h2>
      <p>Here are some articles you've viewed that are available offline:</p>
      <ul class="cached-list" id="cached-articles">
        <li>Loading cached content...</li>
      </ul>
    </div>
  </div>

  <script>
    // Load cached articles when offline
    document.addEventListener('DOMContentLoaded', async () => {
      const cachedListElement = document.getElementById('cached-articles');
      
      try {
        // Try to get cached article URLs
        const cache = await caches.open('chronicle-dynamic-v1.0.0');
        const keys = await cache.keys();
        
        // Filter for article pages
        const articleUrls = keys
          .filter(request => request.url.includes('/articles/'))
          .map(request => request.url);
        
        if (articleUrls.length > 0) {
          // Create list items for each cached article
          cachedListElement.innerHTML = articleUrls
            .map(url => {
              const urlObj = new URL(url);
              const path = urlObj.pathname;
              const title = path.split('/').pop() || 'Article';
              
              return `<li><a href="${path}">${decodeURIComponent(title)}</a></li>`;
            })
            .join('');
        } else {
          cachedListElement.innerHTML