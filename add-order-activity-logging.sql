-- Add automatic order activity logging
-- This script creates triggers to automatically log order activities

-- Function to log new order creation
CREATE OR REPLACE FUNCTION log_new_order()
RETURNS TRIGGER AS $$
DECLARE
    customer_email TEXT;
    admin_user_id UUID;
BEGIN
    -- Get customer email
    SELECT email INTO customer_email
    FROM auth.users
    WHERE id = NEW.user_id;

    -- Get a system admin user ID for logging (use the first admin found)
    SELECT id INTO admin_user_id
    FROM public.users
    WHERE role = 'admin'
    LIMIT 1;

    -- If no admin found, use a system identifier
    IF admin_user_id IS NULL THEN
        admin_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    -- Log the new order activity
    INSERT INTO public.admin_activity_logs (
        admin_id,
        action_type,
        action_description,
        target_id,
        target_type,
        metadata,
        success,
        created_at
    ) VALUES (
        admin_user_id,
        'order_management',
        'New order received from ' || COALESCE(customer_email, 'Unknown Customer') || ' - R' || NEW.total_amount::TEXT,
        NEW.id::TEXT,
        'order',
        jsonb_build_object(
            'customer_email', customer_email,
            'total_amount', NEW.total_amount,
            'status', NEW.status,
            'payment_status', NEW.payment_status,
            'items_count', jsonb_array_length(NEW.items),
            'auto_logged', true
        ),
        true,
        NOW()
    );

    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error but don't fail the order creation
        RAISE WARNING 'Failed to log new order activity: %', SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log order status changes
CREATE OR REPLACE FUNCTION log_order_status_change()
RETURNS TRIGGER AS $$
DECLARE
    customer_email TEXT;
    admin_user_id UUID;
    status_changed BOOLEAN := FALSE;
    payment_status_changed BOOLEAN := FALSE;
BEGIN
    -- Check if status or payment_status changed
    IF OLD.status != NEW.status THEN
        status_changed := TRUE;
    END IF;
    
    IF OLD.payment_status != NEW.payment_status THEN
        payment_status_changed := TRUE;
    END IF;

    -- Only log if something actually changed
    IF NOT (status_changed OR payment_status_changed) THEN
        RETURN NEW;
    END IF;

    -- Get customer email
    SELECT email INTO customer_email
    FROM auth.users
    WHERE id = NEW.user_id;

    -- Get a system admin user ID for logging
    SELECT id INTO admin_user_id
    FROM public.users
    WHERE role = 'admin'
    LIMIT 1;

    -- If no admin found, use a system identifier
    IF admin_user_id IS NULL THEN
        admin_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    -- Log status change
    IF status_changed THEN
        INSERT INTO public.admin_activity_logs (
            admin_id,
            action_type,
            action_description,
            target_id,
            target_type,
            metadata,
            success,
            created_at
        ) VALUES (
            admin_user_id,
            'order_management',
            'Order status changed: ' || OLD.status || ' → ' || NEW.status,
            NEW.id::TEXT,
            'order',
            jsonb_build_object(
                'customer_email', customer_email,
                'old_status', OLD.status,
                'new_status', NEW.status,
                'total_amount', NEW.total_amount,
                'auto_logged', true
            ),
            true,
            NOW()
        );
    END IF;

    -- Log payment status change
    IF payment_status_changed THEN
        INSERT INTO public.admin_activity_logs (
            admin_id,
            action_type,
            action_description,
            target_id,
            target_type,
            metadata,
            success,
            created_at
        ) VALUES (
            admin_user_id,
            'order_management',
            'Payment status changed: ' || OLD.payment_status || ' → ' || NEW.payment_status,
            NEW.id::TEXT,
            'order',
            jsonb_build_object(
                'customer_email', customer_email,
                'old_payment_status', OLD.payment_status,
                'new_payment_status', NEW.payment_status,
                'total_amount', NEW.total_amount,
                'auto_logged', true
            ),
            true,
            NOW()
        );
    END IF;

    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error but don't fail the order update
        RAISE WARNING 'Failed to log order status change: %', SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers
DROP TRIGGER IF EXISTS trigger_log_new_order ON public.orders;
CREATE TRIGGER trigger_log_new_order
    AFTER INSERT ON public.orders
    FOR EACH ROW
    EXECUTE FUNCTION log_new_order();

DROP TRIGGER IF EXISTS trigger_log_order_status_change ON public.orders;
CREATE TRIGGER trigger_log_order_status_change
    AFTER UPDATE ON public.orders
    FOR EACH ROW
    EXECUTE FUNCTION log_order_status_change();

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION log_new_order() TO authenticated;
GRANT EXECUTE ON FUNCTION log_order_status_change() TO authenticated;

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Order activity logging triggers created successfully!';
    RAISE NOTICE 'New orders and status changes will now be automatically logged.';
END
$$;
