-- Fix User Deletion Issues in Supabase
-- This script fixes foreign key constraints that prevent user deletion
-- Run this in your Supabase SQL Editor

-- ============================================================================
-- STEP 1: DROP EXISTING FOREIGN KEY CONSTRAINTS
-- ============================================================================

-- Drop foreign key constraints that don't have CASCADE DELETE
DO $$
DECLARE
    constraint_record RECORD;
BEGIN
    -- Find and drop foreign key constraints that reference auth.users without CASCADE
    FOR constraint_record IN
        SELECT 
            tc.constraint_name,
            tc.table_name,
            tc.table_schema
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu 
            ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage ccu 
            ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND ccu.table_name = 'users'
        AND ccu.table_schema = 'auth'
        AND tc.table_schema = 'public'
    LOOP
        EXECUTE format('ALTER TABLE %I.%I DROP CONSTRAINT IF EXISTS %I CASCADE',
            constraint_record.table_schema,
            constraint_record.table_name,
            constraint_record.constraint_name
        );
        RAISE NOTICE 'Dropped constraint % from table %', 
            constraint_record.constraint_name, 
            constraint_record.table_name;
    END LOOP;
END $$;

-- ============================================================================
-- STEP 2: RECREATE FOREIGN KEY CONSTRAINTS WITH CASCADE DELETE
-- ============================================================================

-- Fix student_enrollments table
DO $$
BEGIN
    -- Add CASCADE DELETE constraint for student_id
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'student_enrollments' AND table_schema = 'public') THEN
        ALTER TABLE public.student_enrollments 
        ADD CONSTRAINT student_enrollments_student_id_fkey 
        FOREIGN KEY (student_id) REFERENCES auth.users(id) ON DELETE CASCADE;
        RAISE NOTICE 'Fixed student_enrollments.student_id foreign key';
    END IF;
EXCEPTION
    WHEN duplicate_object THEN
        RAISE NOTICE 'student_enrollments foreign key already exists';
END $$;

-- Fix mentors table
DO $$
BEGIN
    -- Add CASCADE DELETE constraint for user_id
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mentors' AND table_schema = 'public') THEN
        ALTER TABLE public.mentors 
        ADD CONSTRAINT mentors_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
        RAISE NOTICE 'Fixed mentors.user_id foreign key';
    END IF;
EXCEPTION
    WHEN duplicate_object THEN
        RAISE NOTICE 'mentors foreign key already exists';
END $$;

-- Fix messages table
DO $$
BEGIN
    -- Add CASCADE DELETE constraint for sender_id
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'messages' AND table_schema = 'public') THEN
        ALTER TABLE public.messages 
        ADD CONSTRAINT messages_sender_id_fkey 
        FOREIGN KEY (sender_id) REFERENCES auth.users(id) ON DELETE CASCADE;
        
        -- Add CASCADE DELETE constraint for recipient_id
        ALTER TABLE public.messages 
        ADD CONSTRAINT messages_recipient_id_fkey 
        FOREIGN KEY (recipient_id) REFERENCES auth.users(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Fixed messages foreign keys';
    END IF;
EXCEPTION
    WHEN duplicate_object THEN
        RAISE NOTICE 'messages foreign keys already exist';
END $$;

-- Fix resources table
DO $$
BEGIN
    -- Add CASCADE DELETE constraint for created_by
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'resources' AND table_schema = 'public') THEN
        ALTER TABLE public.resources 
        ADD CONSTRAINT resources_created_by_fkey 
        FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE SET NULL;
        RAISE NOTICE 'Fixed resources.created_by foreign key';
    END IF;
EXCEPTION
    WHEN duplicate_object THEN
        RAISE NOTICE 'resources foreign key already exists';
END $$;

-- Fix orders table (ensure it has CASCADE DELETE)
DO $$
BEGIN
    -- Check if orders table exists and fix foreign key
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'orders' AND table_schema = 'public') THEN
        -- Drop existing constraint if it exists
        ALTER TABLE public.orders DROP CONSTRAINT IF EXISTS orders_user_id_fkey;
        
        -- Add CASCADE DELETE constraint
        ALTER TABLE public.orders 
        ADD CONSTRAINT orders_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
        RAISE NOTICE 'Fixed orders.user_id foreign key';
    END IF;
EXCEPTION
    WHEN duplicate_object THEN
        RAISE NOTICE 'orders foreign key already exists';
END $$;

-- Fix subscriptions table
DO $$
BEGIN
    -- Add CASCADE DELETE constraint for user_id
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'subscriptions' AND table_schema = 'public') THEN
        ALTER TABLE public.subscriptions 
        ADD CONSTRAINT subscriptions_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
        RAISE NOTICE 'Fixed subscriptions.user_id foreign key';
    END IF;
EXCEPTION
    WHEN duplicate_object THEN
        RAISE NOTICE 'subscriptions foreign key already exists';
END $$;

-- ============================================================================
-- STEP 3: FIX PUBLIC.USERS TABLE CONSTRAINT
-- ============================================================================

-- Ensure public.users has proper CASCADE DELETE from auth.users
DO $$
BEGIN
    -- Drop existing constraint if it exists
    ALTER TABLE public.users DROP CONSTRAINT IF EXISTS users_id_fkey;
    
    -- Add CASCADE DELETE constraint
    ALTER TABLE public.users 
    ADD CONSTRAINT users_id_fkey 
    FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;
    RAISE NOTICE 'Fixed public.users.id foreign key';
EXCEPTION
    WHEN duplicate_object THEN
        RAISE NOTICE 'public.users foreign key already exists';
END $$;

-- ============================================================================
-- STEP 4: UPDATE RLS POLICIES FOR ADMIN USER DELETION
-- ============================================================================

-- Update RLS policies to allow admins to delete users
DROP POLICY IF EXISTS "Admins can delete users" ON public.users;
CREATE POLICY "Admins can delete users"
  ON public.users
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM public.users admin_user
      WHERE admin_user.id = auth.uid() 
      AND admin_user.role = 'admin'
    )
  );

-- ============================================================================
-- STEP 5: CREATE HELPER FUNCTION FOR SAFE USER DELETION
-- ============================================================================

-- Create a function to safely delete users with all related data
CREATE OR REPLACE FUNCTION public.delete_user_safely(user_id_to_delete UUID)
RETURNS BOOLEAN AS $$
DECLARE
    current_user_role TEXT;
BEGIN
    -- Check if current user is admin
    SELECT role INTO current_user_role 
    FROM public.users 
    WHERE id = auth.uid();
    
    IF current_user_role != 'admin' THEN
        RAISE EXCEPTION 'Only admin users can delete other users';
    END IF;
    
    -- Delete from auth.users (this will cascade to all related tables)
    DELETE FROM auth.users WHERE id = user_id_to_delete;
    
    -- Return success
    RETURN TRUE;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Failed to delete user: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.delete_user_safely(UUID) TO authenticated;

-- ============================================================================
-- STEP 6: VERIFICATION QUERIES
-- ============================================================================

-- Check all foreign key constraints that reference auth.users
SELECT 
    tc.table_name,
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu 
    ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage ccu 
    ON ccu.constraint_name = tc.constraint_name
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
AND ccu.table_name = 'users'
AND ccu.table_schema = 'auth'
AND tc.table_schema = 'public'
ORDER BY tc.table_name;

-- Final success message
DO $$
BEGIN
    RAISE NOTICE '✅ User deletion constraints have been fixed!';
    RAISE NOTICE '✅ All foreign keys now have CASCADE DELETE or SET NULL';
    RAISE NOTICE '✅ Admin users can now delete users from the dashboard';
    RAISE NOTICE '✅ Use delete_user_safely() function for programmatic deletion';
END $$;
