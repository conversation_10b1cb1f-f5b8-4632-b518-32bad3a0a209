"use client";

import { useCart } from "@/context/cart-context";
import Image from "next/image";
import Link from "next/link";
import { Minus, Plus, Trash2, Truck, ShoppingBag } from "lucide-react";
import { <PERSON><PERSON> } from "./ui/button";
import { Input } from "./ui/input";

export function CartItems() {
  const { cartItems, removeFromCart, updateQuantity, subtotal } = useCart();

  // Calculate shipping and total
  const shipping = subtotal > 1000 ? 0 : 99.99;
  const total = subtotal + shipping;

  if (cartItems.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
          <ShoppingBag className="w-8 h-8 text-muted-foreground" />
        </div>
        <h2 className="text-xl font-semibold text-foreground mb-2">Your cart is empty</h2>
        <p className="text-muted-foreground mb-6">Looks like you haven't added any products to your cart yet.</p>
        <Button asChild>
          <Link href="/shop">
            Start Shopping
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
      {/* Cart Items */}
      <div className="lg:col-span-2">
        <div className="border-b border-border pb-4 mb-6 hidden md:grid md:grid-cols-12 text-sm text-muted-foreground">
          <div className="col-span-6">Product</div>
          <div className="col-span-2 text-center">Price</div>
          <div className="col-span-2 text-center">Quantity</div>
          <div className="col-span-2 text-right">Total</div>
        </div>

        <div className="space-y-6">
          {cartItems.map((item) => (
            <div key={item.id} className="border-b border-border pb-6">
              <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
                {/* Product */}
                <div className="col-span-6 flex items-center gap-4">
                  <div className="relative w-20 h-20 rounded-md overflow-hidden border border-border">
                    <Image
                      src={item.image}
                      alt={item.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div>
                    <h3 className="font-medium text-foreground">
                      <Link href={`/product/${item.id}`} className="hover:text-primary transition-colors">
                        {item.name}
                      </Link>
                    </h3>
                    <button
                      className="text-sm text-muted-foreground hover:text-destructive transition-colors flex items-center gap-1 mt-1"
                      onClick={() => removeFromCart(item.id)}
                    >
                      <Trash2 className="w-3 h-3" />
                      <span>Remove</span>
                    </button>
                  </div>
                </div>

                {/* Price */}
                <div className="md:col-span-2 md:text-center">
                  <div className="flex justify-between md:block">
                    <span className="md:hidden text-muted-foreground">Price:</span>
                    <span className="text-foreground">R {item.price.toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span>
                  </div>
                </div>

                {/* Quantity */}
                <div className="md:col-span-2 md:text-center">
                  <div className="flex justify-between md:justify-center items-center">
                    <span className="md:hidden text-muted-foreground">Quantity:</span>
                    <div className="flex items-center border border-border rounded-md">
                      <button
                        className="px-2 py-1 text-muted-foreground hover:text-primary transition-colors"
                        onClick={() => updateQuantity(item.id, item.quantity - 1)}
                      >
                        <Minus className="w-3 h-3" />
                      </button>
                      <span className="px-3 py-1 text-sm">{item.quantity}</span>
                      <button
                        className="px-2 py-1 text-muted-foreground hover:text-primary transition-colors"
                        onClick={() => updateQuantity(item.id, item.quantity + 1)}
                      >
                        <Plus className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Total */}
                <div className="md:col-span-2 md:text-right">
                  <div className="flex justify-between md:block">
                    <span className="md:hidden text-muted-foreground">Total:</span>
                    <span className="font-medium text-foreground">
                      R {(item.price * item.quantity).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="flex justify-between items-center mt-8">
          <div className="flex gap-2">
            <Input placeholder="Coupon code" className="w-40" />
            <Button variant="outline">Apply</Button>
          </div>

          <Button variant="outline" asChild>
            <Link href="/shop">
              Continue Shopping
            </Link>
          </Button>
        </div>
      </div>

      {/* Order Summary */}
      <div className="lg:col-span-1">
        <div className="bg-muted/30 rounded-2xl p-6 border border-border">
          <h2 className="text-xl font-semibold text-foreground mb-6">Order Summary</h2>

          <div className="space-y-3 mb-6">
            <div className="flex justify-between text-muted-foreground">
              <span>Subtotal</span>
              <span className="text-foreground">R {subtotal.toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span>
            </div>
            <div className="flex justify-between text-muted-foreground">
              <span>Shipping</span>
              <span className="text-foreground">
                {shipping === 0 ? 'Free' : `R ${shipping.toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
              </span>
            </div>
            {shipping > 0 && (
              <div className="text-xs text-muted-foreground pt-1">
                Free shipping on orders over R 1,000.00
              </div>
            )}
          </div>

          <div className="border-t border-border pt-4 mb-6">
            <div className="flex justify-between font-semibold">
              <span>Total</span>
              <span className="text-primary">R {total.toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span>
            </div>
          </div>

          <Button className="w-full mb-4" asChild>
            <Link href="/checkout">
              Proceed to Checkout
            </Link>
          </Button>

          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Truck className="w-4 h-4" />
            <span>Estimated delivery: 3-5 business days</span>
          </div>
        </div>
      </div>
    </div>
  );
}
