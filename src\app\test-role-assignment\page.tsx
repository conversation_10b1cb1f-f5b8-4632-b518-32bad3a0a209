"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertCircle, RefreshCw } from 'lucide-react';

interface TestResult {
  success: boolean;
  timestamp: string;
  tests: {
    enumValues: {
      passed: boolean;
      data: any[];
      error?: string;
    };
    triggerFunction: {
      passed: boolean;
      exists: boolean;
      error?: string;
    };
    roleAssignmentLogic: {
      passed: boolean;
      tests: Array<{
        context: string | null;
        expectedRole: string;
        assignedRole: string;
        passed: boolean;
        description: string;
      }>;
    };
  };
  summary: {
    allTestsPassed: boolean;
    message: string;
  };
}

export default function TestRoleAssignmentPage() {
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const runTests = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/test-role-assignment');
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Test failed');
      }
      
      setTestResult(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    runTests();
  }, []);

  const getStatusIcon = (passed: boolean) => {
    return passed ? (
      <CheckCircle className="h-5 w-5 text-green-500" />
    ) : (
      <XCircle className="h-5 w-5 text-red-500" />
    );
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Role Assignment System Test</h1>
          <p className="text-muted-foreground">
            This page tests the automatic role assignment system for different signup contexts.
          </p>
        </div>

        <div className="mb-6">
          <Button 
            onClick={runTests} 
            disabled={loading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            {loading ? 'Running Tests...' : 'Run Tests'}
          </Button>
        </div>

        {error && (
          <Card className="mb-6 border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2 text-red-700">
                <AlertCircle className="h-5 w-5" />
                <span className="font-medium">Test Error:</span>
                <span>{error}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {testResult && (
          <div className="space-y-6">
            {/* Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {getStatusIcon(testResult.summary.allTestsPassed)}
                  Test Summary
                </CardTitle>
                <CardDescription>
                  Overall system status: {testResult.summary.message}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Badge variant={testResult.summary.allTestsPassed ? "default" : "destructive"}>
                  {testResult.summary.allTestsPassed ? "All Tests Passed" : "Some Tests Failed"}
                </Badge>
                <p className="text-sm text-muted-foreground mt-2">
                  Tested at: {new Date(testResult.timestamp).toLocaleString()}
                </p>
              </CardContent>
            </Card>

            {/* Enum Values Test */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {getStatusIcon(testResult.tests.enumValues.passed)}
                  User Role Enum Values
                </CardTitle>
                <CardDescription>
                  Checks if the user_role enum contains all required values
                </CardDescription>
              </CardHeader>
              <CardContent>
                {testResult.tests.enumValues.passed ? (
                  <div>
                    <p className="text-green-700 mb-2">✓ Enum values are correct</p>
                    <div className="flex gap-2">
                      {testResult.tests.enumValues.data.map((item: any, index: number) => (
                        <Badge key={index} variant="outline">
                          {item.enumlabel || item}
                        </Badge>
                      ))}
                    </div>
                  </div>
                ) : (
                  <p className="text-red-700">✗ {testResult.tests.enumValues.error}</p>
                )}
              </CardContent>
            </Card>

            {/* Trigger Function Test */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {getStatusIcon(testResult.tests.triggerFunction.passed)}
                  Trigger Function
                </CardTitle>
                <CardDescription>
                  Checks if the handle_new_user trigger function exists
                </CardDescription>
              </CardHeader>
              <CardContent>
                {testResult.tests.triggerFunction.passed ? (
                  <p className="text-green-700">✓ Trigger function exists and is properly configured</p>
                ) : (
                  <p className="text-red-700">
                    ✗ {testResult.tests.triggerFunction.error || 'Trigger function not found'}
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Role Assignment Logic Test */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {getStatusIcon(testResult.tests.roleAssignmentLogic.passed)}
                  Role Assignment Logic
                </CardTitle>
                <CardDescription>
                  Tests the role assignment logic for different signup contexts
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {testResult.tests.roleAssignmentLogic.tests.map((test, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{test.description}</p>
                        <p className="text-sm text-muted-foreground">
                          Context: {test.context || 'none'} → Expected: {test.expectedRole}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={test.passed ? "default" : "destructive"}>
                          {test.assignedRole}
                        </Badge>
                        {getStatusIcon(test.passed)}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
