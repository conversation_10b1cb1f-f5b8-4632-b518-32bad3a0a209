-- =====================================================
-- FIX PROFILES TABLE AND AUTHENTICATION ISSUES
-- Run this script in your Supabase SQL Editor to fix all auth issues
-- =====================================================

-- 1. CREATE PROFILES TABLE (if it doesn't exist)
CREATE TABLE IF NOT EXISTS public.profiles (
    id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    first_name text,
    last_name text,
    email text,
    role text DEFAULT 'user' CHECK (role IN ('user', 'admin', 'author')),
    avatar_url text,
    bio text,
    website text,
    location text,
    last_sign_in_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- 2. ENABLE ROW LEVEL SECURITY
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- 3. DROP EXISTING POLICIES
DROP POLICY IF EXISTS "Users can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;

-- 4. CREATE UPDATED RLS POLICIES
CREATE POLICY "Users can view all profiles" ON public.profiles FOR SELECT USING (true);
CREATE POLICY "Users can update own profile" ON public.profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON public.profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- 5. CREATE OR REPLACE USER CREATION FUNCTION
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert profile for new user
    INSERT INTO public.profiles (id, email, first_name, last_name, role, last_sign_in_at)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'first_name', NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
        NEW.raw_user_meta_data->>'last_name',
        CASE
            WHEN (SELECT COUNT(*) FROM auth.users WHERE id != NEW.id) = 0 THEN 'admin'
            ELSE 'user'
        END,
        NEW.last_sign_in_at
    )
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name,
        last_sign_in_at = EXCLUDED.last_sign_in_at,
        updated_at = timezone('utc'::text, now());

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. CREATE TRIGGER FOR NEW USERS
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT OR UPDATE ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 7. CREATE PROFILES FOR EXISTING USERS (if any)
INSERT INTO public.profiles (id, email, first_name, role, created_at, updated_at)
SELECT 
    id,
    email,
    COALESCE(raw_user_meta_data->>'first_name', raw_user_meta_data->>'full_name', split_part(email, '@', 1)),
    CASE
        WHEN ROW_NUMBER() OVER (ORDER BY created_at) = 1 THEN 'admin'
        ELSE 'user'
    END,
    created_at,
    updated_at
FROM auth.users
WHERE id NOT IN (SELECT id FROM public.profiles)
ON CONFLICT (id) DO NOTHING;

-- 8. CREATE INDEXES FOR PERFORMANCE
CREATE INDEX IF NOT EXISTS idx_profiles_role ON public.profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_created_at ON public.profiles(created_at);

-- 9. GRANT PERMISSIONS
GRANT EXECUTE ON FUNCTION public.handle_new_user TO authenticated;

-- 10. REFRESH SCHEMA
NOTIFY pgrst, 'reload schema';

-- Success message
DO $$
BEGIN
    RAISE NOTICE '✅ PROFILES TABLE SETUP COMPLETE!';
    RAISE NOTICE '📋 Profiles table created with proper RLS policies';
    RAISE NOTICE '🔧 User creation trigger configured';
    RAISE NOTICE '👤 Existing users migrated to profiles table';
    RAISE NOTICE '🚀 Authentication should now work correctly';
END $$;
