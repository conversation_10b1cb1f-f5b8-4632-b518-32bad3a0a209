import { supabase } from "../../supabase/supabase";
import { Article, Category, Comment } from "@/types/blog";
import { Product, ProductCategory, Cart, Order } from "@/types/ecommerce";

// Blog Services
export const blogService = {
  // Get published articles with pagination
  async getArticles(page = 1, limit = 10, categoryId?: string) {
    let query = supabase
      .from('articles')
      .select('*')
      .eq('is_published', true)
      .order('published_at', { ascending: false });

    if (categoryId) {
      query = query.eq('category_id', categoryId);
    }

    const { data, error, count } = await query
      .range((page - 1) * limit, page * limit - 1)
      .limit(limit);

    if (error) throw error;

    return {
      articles: data?.map(transformArticle) || [],
      totalCount: count || 0,
      hasMore: (count || 0) > page * limit
    };
  },

  // Get featured article
  async getFeaturedArticle() {
    const { data, error } = await supabase
      .from('articles')
      .select('*')
      .eq('is_published', true)
      .order('views', { ascending: false })
      .limit(1)
      .single();

    if (error) throw error;
    return data ? transformArticle(data) : null;
  },

  // Get trending articles
  async getTrendingArticles(limit = 5) {
    const { data, error } = await supabase
      .from('articles')
      .select('*')
      .eq('is_published', true)
      .order('views', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data?.map(transformArticle) || [];
  },

  // Get article by slug
  async getArticleBySlug(slug: string) {
    const { data, error } = await supabase
      .from('articles')
      .select('*')
      .eq('slug', slug)
      .eq('is_published', true)
      .single();

    if (error) throw error;
    return data ? transformArticle(data) : null;
  },

  // Get categories
  async getCategories() {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .order('name');

    if (error) throw error;
    return data || [];
  },

  // Subscribe to newsletter
  async subscribeToNewsletter(email: string, name?: string) {
    const { data, error } = await supabase
      .from('newsletter_subscribers')
      .upsert({
        email,
        name,
        status: 'active',
        subscribed_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Increment article views
  async incrementViews(articleId: string) {
    const { error } = await supabase.rpc('increment_article_views', {
      article_id: articleId
    });

    if (error) console.error('Error incrementing views:', error);
  }
};

// Ecommerce Services
export const ecommerceService = {
  // Get products with pagination
  async getProducts(page = 1, limit = 12, categoryId?: string, featured?: boolean) {
    let query = supabase
      .from('products')
      .select('*')
      .eq('status', 'active');

    if (categoryId) {
      query = query.eq('category_id', categoryId);
    }

    if (featured !== undefined) {
      query = query.eq('is_featured', featured);
    }

    const { data, error, count } = await query
      .order('created_at', { ascending: false })
      .range((page - 1) * limit, page * limit - 1);

    if (error) throw error;

    return {
      products: data?.map(transformProduct) || [],
      totalCount: count || 0,
      hasMore: (count || 0) > page * limit
    };
  },

  // Get featured products
  async getFeaturedProducts(limit = 6) {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('status', 'active')
      .eq('is_featured', true)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data?.map(transformProduct) || [];
  },

  // Get product by slug
  async getProductBySlug(slug: string) {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('slug', slug)
      .eq('status', 'active')
      .single();

    if (error) throw error;
    return data ? transformProduct(data) : null;
  },

  // Get product categories
  async getProductCategories() {
    const { data, error } = await supabase
      .from('product_categories')
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (error) throw error;
    return data || [];
  }
};

// Transform functions to match our TypeScript interfaces
function transformArticle(data: any): Article {
  return {
    id: data.id,
    title: data.title,
    slug: data.slug,
    excerpt: data.excerpt || '',
    content: data.content || '',
    author: {
      id: data.author_id || '',
      name: 'Author',
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${data.author_id}`
    },
    category: {
      id: data.category_id || '',
      name: 'Technology',
      slug: 'technology'
    },
    tags: [],
    featuredImage: data.featured_image,
    isPremium: data.is_premium || false,
    isPublished: data.is_published || false,
    publishedAt: new Date(data.published_at || data.created_at),
    createdAt: new Date(data.created_at),
    updatedAt: new Date(data.updated_at),
    readTime: data.read_time || 5,
    views: data.views || 0,
    likes: data.likes || 0,
    comments: data.comments_count || 0,
    shares: data.shares || 0,
    seoTitle: data.seo_title,
    seoDescription: data.seo_description
  };
}

function transformProduct(data: any): Product {
  return {
    id: data.id,
    name: data.name,
    slug: data.slug,
    description: data.description || '',
    shortDescription: data.short_description,
    price: parseFloat(data.price),
    comparePrice: data.sale_price ? parseFloat(data.sale_price) : undefined,
    type: data.type as 'digital' | 'physical' | 'subscription',
    category: {
      id: data.category_id || '',
      name: 'Uncategorized',
      slug: 'uncategorized'
    },
    images: Array.isArray(data.image_urls) ? data.image_urls : [data.image_url].filter(Boolean),
    stock: data.stock_quantity || 0,
    status: data.status as 'active' | 'inactive' | 'draft',
    featured: data.is_featured || false,
    tags: [],
    attributes: data.metadata || {},
    downloadUrl: data.download_url,
    createdAt: new Date(data.created_at),
    updatedAt: new Date(data.updated_at),
    seoTitle: data.seo_title,
    seoDescription: data.seo_description
  };
}
