'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { XCircle, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

/**
 * ConsultationCancelPage client component
 * Displayed when a user cancels their consultation payment
 */
export default function ConsultationCancelClient() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const reference = searchParams.get('reference');
  
  return (
    <div className="min-h-screen bg-background py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        <Card className="border border-destructive/20 shadow-lg backdrop-blur-sm bg-black/5">
          <CardHeader className="text-center border-b border-border pb-6">
            <div className="flex justify-center mb-4">
              <XCircle className="h-16 w-16 text-destructive" />
            </div>
            <CardTitle className="text-3xl font-bold">Payment Cancelled</CardTitle>
            <CardDescription className="text-lg mt-2">
              Your tennis consultation booking has been cancelled
            </CardDescription>
          </CardHeader>
          
          <CardContent className="pt-6">
            <div className="space-y-4">
              <p className="text-center">
                You have cancelled the payment process for your consultation booking.
              </p>
              
              {reference && (
                <div className="bg-muted p-4 rounded-md text-center">
                  <p className="text-sm text-muted-foreground mb-1">Reference Number</p>
                  <p className="font-medium">{reference}</p>
                </div>
              )}
              
              <div className="bg-amber-50 border border-amber-200 p-4 rounded-md">
                <p className="text-amber-800 text-sm">
                  No charges have been made to your account. You can try booking again or contact us if you have any questions.
                </p>
              </div>
            </div>
          </CardContent>
          
          <CardFooter className="flex flex-col sm:flex-row gap-4 pt-6">
            <Button 
              variant="outline" 
              className="w-full sm:w-auto"
              onClick={() => router.push('/')}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Return to Home
            </Button>
            
            <Button 
              className="w-full sm:w-auto"
              onClick={() => router.push('/consultation/booking')}
            >
              Try Again
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
