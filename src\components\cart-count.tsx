"use client";

import { useCart } from "@/context/cart-context";

export function CartCount() {
  const { totalItems } = useCart();
  
  if (totalItems === 0) {
    return null;
  }
  
  return (
    <span className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs rounded-full w-5 h-5 flex items-center justify-center">
      {totalItems > 99 ? "99+" : totalItems}
    </span>
  );
}
