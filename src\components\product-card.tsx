"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Star, Heart, Eye, ShoppingCart, Plus, ShoppingBag } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { AddToCartButton } from "./add-to-cart-button";
import { Product as SupabaseProduct } from "@/utils/supabase/products";
import { useCart } from "@/context/cart-context";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";

// Extended product type that includes optional rating and reviews
// and allows both string and number IDs for compatibility
interface ProductWithRating extends Omit<SupabaseProduct, 'id' | 'image' | 'description'> {
  id: string | number;
  image?: string | null; // Handle both undefined and null from database
  description?: string | null; // Handle both undefined and null from database
  rating?: number;
  reviews?: number;
}

interface ProductCardProps {
  product: ProductWithRating;
}

export function ProductCard({ product }: ProductCardProps) {
  const { addToCart, cartItems } = useCart();
  const router = useRouter();

  // Get the product image (handle null, undefined, and empty string)
  const productImage = product.image || '/placeholder-product.jpg';

  // Default values for rating and reviews
  const rating = product.rating || 4.5;
  const reviews = product.reviews || 0;

  // Get normalized product ID for consistent comparison
  const getNormalizedId = (id: string | number) => {
    return typeof id === 'string' ? parseInt(id, 10) : id;
  };

  // Check if product is in cart
  const productId = getNormalizedId(product.id);
  const isInCart = cartItems.some(item => item.id === productId);

  // Handle add to cart
  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (isInCart) {
      // If already in cart, navigate to cart
      router.push('/cart');
    } else {
      // Add to cart
      addToCart({
        id: productId,
        name: product.name,
        price: product.price,
        image: productImage
      });
    }
  };

  // Handle view product
  const handleViewProduct = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    router.push(`/product/${product.id}`);
  };
  
  return (
    <Card className="overflow-hidden border-0 product-card neo-shadow-light group focus-ring">
      <Link href={`/product/${product.id}`} className="block overflow-hidden relative focus-ring rounded-2xl">
        <div className="aspect-square overflow-hidden rounded-t-2xl relative">
          <Image
            src={productImage}
            alt={product.name}
            width={400}
            height={400}
            className="object-cover w-full h-full transition-all duration-500 group-hover:scale-110"
            priority={false}
            loading="lazy"
          />

          {/* Enhanced overlay gradient */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

          {/* Category badge with enhanced styling */}
          <div className="absolute top-3 left-3 px-3 py-1.5 rounded-full glass-effect-subtle">
            <span className="text-xs font-semibold text-foreground/90">{product.category}</span>
          </div>

          {/* Quick action button */}
          <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
            <button className="w-8 h-8 rounded-full glass-effect-dark flex items-center justify-center neo-shadow-light hover:neo-shadow transition-neo">
              <Heart className="h-4 w-4 text-foreground/70 hover:text-red-400 transition-colors" />
            </button>
          </div>
        </div>
      </Link>

      <CardContent className="p-5 space-y-3">
        <Link href={`/product/${product.id}`} className="block group/title">
          <h3 className="font-semibold mb-2 group-hover/title:text-primary transition-colors text-sm md:text-base line-clamp-2 leading-relaxed">
            {product.name}
          </h3>
        </Link>

        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <div className="font-bold text-lg text-gradient">R{product.price.toFixed(0)}</div>
            {product.price > 1000 && (
              <div className="text-xs text-muted-foreground">Free shipping</div>
            )}
          </div>

          <div className="flex items-center gap-1.5 px-2 py-1 rounded-full bg-yellow-400/10">
            <Star className="h-3.5 w-3.5 fill-yellow-400 text-yellow-400" />
            <span className="text-sm font-semibold text-yellow-600 dark:text-yellow-400">{rating}</span>
            <span className="text-xs text-muted-foreground">({reviews || 0})</span>
          </div>
        </div>

        {/* Always visible description */}
        <div className="pt-2 border-t border-border/50">
          <div className="text-xs text-muted-foreground line-clamp-2 mb-3">
            {product.description || "Premium tennis equipment for enhanced performance"}
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex items-center justify-between gap-3">
          {/* View Product Button */}
          <button
            onClick={handleViewProduct}
            className="flex-1 flex items-center justify-center gap-2 px-4 py-2.5 rounded-xl bg-slate-100 hover:bg-slate-200 dark:bg-slate-800 dark:hover:bg-slate-700 transition-all duration-300 neo-shadow-light hover:neo-shadow focus-ring min-h-[44px]"
            aria-label="View product details"
          >
            <Eye className="h-4 w-4 text-slate-600 dark:text-slate-400" />
            <span className="text-sm font-medium text-slate-700 dark:text-slate-300">View</span>
          </button>

          {/* Add to Cart / View Cart Button */}
          <button
            onClick={handleAddToCart}
            className={cn(
              "flex-1 flex items-center justify-center gap-2 px-4 py-2.5 rounded-xl transition-all duration-300 neo-shadow-light hover:neo-shadow focus-ring min-h-[44px]",
              isInCart
                ? "bg-green-100 hover:bg-green-200 dark:bg-green-900 dark:hover:bg-green-800"
                : "bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800"
            )}
            aria-label={isInCart ? "View cart" : "Add to cart"}
          >
            {isInCart ? (
              <>
                <ShoppingBag className="h-4 w-4 text-green-600 dark:text-green-400" />
                <span className="text-sm font-medium text-green-700 dark:text-green-300">Cart</span>
              </>
            ) : (
              <>
                <div className="relative">
                  <ShoppingCart className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  <Plus className="h-2.5 w-2.5 text-blue-600 dark:text-blue-400 absolute -top-1 -right-1" />
                </div>
                <span className="text-sm font-medium text-blue-700 dark:text-blue-300">Add</span>
              </>
            )}
          </button>
        </div>
      </CardContent>
    </Card>
  );
}
