"use client";

import { useEffect, useState, useRef } from 'react';
import { usePathname } from 'next/navigation';
import Image from 'next/image';
import { cn } from '@/lib/utils';

interface PreloaderProps {
  className?: string;
}

export default function Preloader({ className }: PreloaderProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [isVisible, setIsVisible] = useState(true);
  const [logoLoaded, setLogoLoaded] = useState(false);
  const [fadeOut, setFadeOut] = useState(false);
  const [hasShownOnce, setHasShownOnce] = useState(false);
  const pathname = usePathname();
  const timeoutsRef = useRef<NodeJS.Timeout[]>([]);

  // Helper function to add timeout to ref for cleanup
  const addTimeout = (timeout: NodeJS.Timeout) => {
    timeoutsRef.current.push(timeout);
    return timeout;
  };

  // Helper function to clear all timeouts
  const clearAllTimeouts = () => {
    timeoutsRef.current.forEach(clearTimeout);
    timeoutsRef.current = [];
  };

  useEffect(() => {
    // Only show preloader on initial load, not on navigation
    if (hasShownOnce) {
      setIsVisible(false);
      return;
    }

    const hidePreloader = () => {
      setFadeOut(true);
      addTimeout(setTimeout(() => {
        setIsVisible(false);
        setHasShownOnce(true);
      }, 800));
    };

    // Handle initial page load with VirTra-style timing
    const handleInitialLoad = () => {
      const minLoadTime = 2000; // VirTra-style longer initial display
      const maxLoadTime = 4000;
      const startTime = Date.now();

      // Logo appears with delay (VirTra style)
      addTimeout(setTimeout(() => {
        setLogoLoaded(true);
      }, 300));

      const checkPageLoad = () => {
        const isPageReady = document.readyState === 'complete';
        const elapsedTime = Date.now() - startTime;

        if (isPageReady && elapsedTime >= minLoadTime) {
          // VirTra-style: Wait for logo to be fully visible before hiding
          addTimeout(setTimeout(hidePreloader, 500));
        } else if (elapsedTime >= maxLoadTime) {
          // Force hide after max time
          addTimeout(setTimeout(hidePreloader, 100));
        } else {
          // Continue checking
          addTimeout(setTimeout(checkPageLoad, 100));
        }
      };

      // Start checking after logo appears
      addTimeout(setTimeout(checkPageLoad, 800));
    };

    // Only run on initial load
    if (isLoading && !hasShownOnce) {
      handleInitialLoad();
    }

    // Cleanup
    return () => {
      clearAllTimeouts();
    };
  }, [isLoading, hasShownOnce]);

  // Handle pathname changes (navigation)
  useEffect(() => {
    // If we've already shown the preloader once, don't show it again
    if (hasShownOnce) {
      setIsVisible(false);
    }
  }, [pathname, hasShownOnce]);

  // Handle reduced motion preference
  const prefersReducedMotion = typeof window !== 'undefined' 
    ? window.matchMedia('(prefers-reduced-motion: reduce)').matches 
    : false;

  if (!isVisible) return null;

  return (
    <div
      className={cn(
        "fixed inset-0 z-[9999] flex items-center justify-center",
        "bg-background",
        "transition-all duration-800 ease-out",
        fadeOut ? "opacity-0 scale-105" : "opacity-100 scale-100",
        className
      )}
      role="progressbar"
      aria-label="Loading page content"
      aria-live="polite"
    >
      {/* Clean background - VirTra style */}
      <div className="absolute inset-0 bg-background"></div>

      {/* Logo container - VirTra style */}
      <div className="relative flex flex-col items-center justify-center">
        {/* Logo with VirTra-style animation */}
        <div
          className={cn(
            "relative transition-all duration-1000 ease-out",
            logoLoaded
              ? "opacity-100 scale-100 translate-y-0"
              : "opacity-0 scale-95 translate-y-4",
            !prefersReducedMotion && logoLoaded && "animate-virtra-logo"
          )}
        >
          <div className="relative w-32 h-32 md:w-40 md:h-40">
            <Image
              src="/logo.svg"
              alt="Tennis Whisperer"
              fill
              className="object-contain filter drop-shadow-lg"
              priority
              sizes="(max-width: 768px) 128px, 160px"
            />
          </div>
        </div>

        {/* VirTra-style minimal loading indicator */}
        {logoLoaded && (
          <div
            className={cn(
              "mt-12 transition-all duration-700 ease-out",
              logoLoaded
                ? "opacity-100 translate-y-0"
                : "opacity-0 translate-y-4"
            )}
          >
            {/* Simple loading dots - VirTra style */}
            {!prefersReducedMotion ? (
              <div className="flex justify-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full animate-virtra-dot"></div>
                <div className="w-2 h-2 bg-primary rounded-full animate-virtra-dot" style={{ animationDelay: '0.2s' }}></div>
                <div className="w-2 h-2 bg-primary rounded-full animate-virtra-dot" style={{ animationDelay: '0.4s' }}></div>
              </div>
            ) : (
              <div className="flex justify-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <div className="w-2 h-2 bg-primary rounded-full"></div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Screen reader content */}
      <div className="sr-only">
        {isLoading ? 'Loading content, please wait...' : 'Content loaded successfully'}
      </div>
    </div>
  );
}
