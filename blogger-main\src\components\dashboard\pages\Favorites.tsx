import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import {
  Heart,
  Search,
  Eye,
  Calendar,
  User,
  X,
  BookOpen,
  TrendingUp,
} from 'lucide-react';
import { useAuth } from '../../../../supabase/auth';
import { supabase } from '../../../../supabase/supabase';

interface FavoriteArticle {
  id: string;
  article_id: string;
  created_at: string;
  article: {
    id: string;
    title: string;
    slug: string;
    excerpt: string;
    featured_image: string | null;
    views: number;
    likes: number;
    read_time: number;
    published_at: string;
    author: {
      email: string;
    };
    category: {
      name: string;
      color: string;
    } | null;
  };
}

export function Favorites() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [favorites, setFavorites] = useState<FavoriteArticle[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (user) {
      loadFavorites();
    }
  }, [user]);

  const loadFavorites = async () => {
    try {
      setIsLoading(true);

      const { data, error } = await supabase
        .from('favorites')
        .select(`
          id,
          article_id,
          created_at,
          article:articles (
            id,
            title,
            slug,
            excerpt,
            featured_image,
            views,
            likes,
            read_time,
            published_at,
            author:profiles (
              email,
              first_name,
              last_name
            ),
            category:categories (
              name,
              color
            )
          )
        `)
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      setFavorites(data || []);
    } catch (error) {
      console.error('Error loading favorites:', error);
      toast({
        title: 'Error',
        description: 'Failed to load your favorites',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveFavorite = async (articleId: string) => {
    try {
      const { error } = await supabase
        .from('favorites')
        .delete()
        .eq('user_id', user?.id)
        .eq('article_id', articleId);

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'Article removed from favorites',
      });

      loadFavorites();
    } catch (error) {
      console.error('Error removing favorite:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove from favorites',
        variant: 'destructive',
      });
    }
  };

  const filteredFavorites = favorites.filter(favorite =>
    favorite.article?.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    favorite.article?.excerpt?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-64 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <Heart className="h-8 w-8 text-red-600" />
            Favorites
          </h1>
          <p className="text-gray-600 mt-1">
            Articles you've saved for later reading
          </p>
        </div>
        <div className="text-sm text-gray-500">
          {filteredFavorites.length} article{filteredFavorites.length !== 1 ? 's' : ''}
        </div>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search your favorites..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Favorites Grid */}
      {filteredFavorites.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredFavorites.map((favorite) => (
            <Card key={favorite.id} className="group hover:shadow-lg transition-shadow">
              <div className="relative">
                {favorite.article?.featured_image && (
                  <img
                    src={favorite.article.featured_image}
                    alt={favorite.article.title}
                    className="w-full h-48 object-cover rounded-t-lg"
                  />
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-2 right-2 bg-white/80 hover:bg-white"
                  onClick={() => handleRemoveFavorite(favorite.article_id)}
                >
                  <X className="h-4 w-4 text-red-600" />
                </Button>
              </div>
              
              <CardContent className="p-4">
                <div className="space-y-3">
                  {/* Category */}
                  {favorite.article?.category && (
                    <Badge 
                      variant="outline" 
                      style={{ borderColor: favorite.article.category.color }}
                      className="text-xs"
                    >
                      {favorite.article.category.name}
                    </Badge>
                  )}

                  {/* Title */}
                  <h3 className="font-semibold text-lg line-clamp-2 group-hover:text-blue-600 transition-colors">
                    {favorite.article?.title}
                  </h3>

                  {/* Excerpt */}
                  <p className="text-gray-600 text-sm line-clamp-3">
                    {favorite.article?.excerpt}
                  </p>

                  {/* Meta Info */}
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center gap-4">
                      <span className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {favorite.article?.author?.email?.split('@')[0]}
                      </span>
                      <span className="flex items-center gap-1">
                        <BookOpen className="h-3 w-3" />
                        {favorite.article?.read_time || 5} min
                      </span>
                    </div>
                    <span className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {new Date(favorite.article?.published_at || '').toLocaleDateString()}
                    </span>
                  </div>

                  {/* Stats */}
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center gap-4">
                      <span className="flex items-center gap-1">
                        <Eye className="h-3 w-3" />
                        {favorite.article?.views || 0}
                      </span>
                      <span className="flex items-center gap-1">
                        <Heart className="h-3 w-3" />
                        {favorite.article?.likes || 0}
                      </span>
                    </div>
                    <span className="text-gray-400">
                      Saved {new Date(favorite.created_at).toLocaleDateString()}
                    </span>
                  </div>

                  {/* Read Button */}
                  <Link to={`/articles/${favorite.article?.slug}`} className="block">
                    <Button className="w-full mt-3" variant="outline">
                      Read Article
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="p-12 text-center">
            <Heart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">
              {searchTerm ? 'No matching favorites' : 'No favorites yet'}
            </h3>
            <p className="text-gray-500 mb-6 max-w-md mx-auto">
              {searchTerm 
                ? 'Try adjusting your search terms to find what you\'re looking for.'
                : 'Start exploring articles and save your favorites by clicking the heart icon on any article.'
              }
            </p>
            {!searchTerm && (
              <Link to="/articles">
                <Button>
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Explore Articles
                </Button>
              </Link>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}


