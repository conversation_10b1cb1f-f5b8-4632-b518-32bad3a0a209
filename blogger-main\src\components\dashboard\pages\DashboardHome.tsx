import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { UserAvatar } from '@/components/ui/UserAvatar';
import { Progress } from '@/components/ui/progress';
import { Link } from 'react-router-dom';
import {
  FileText,
  TrendingUp,
  Users,
  Eye,
  Heart,
  MessageSquare,
  PlusCircle,
  Calendar,
  Target,
  Award,
  Clock,
  BarChart3,
  DollarSign,
  ShoppingCart,
} from 'lucide-react';
import { useAuth } from '../../../../supabase/auth';
import { blogService } from '@/lib/supabase-services';
import { forceRefreshUserData, clearBrowserCache } from '../../../utils/forceRefresh';

interface DashboardStats {
  totalArticles: number;
  totalViews: number;
  totalLikes: number;
  totalComments: number;
  publishedArticles: number;
  draftArticles: number;
  premiumArticles: number;
}

export function DashboardHome() {
  const { user, isAdmin, refreshUserRole } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalArticles: 0,
    totalViews: 0,
    totalLikes: 0,
    totalComments: 0,
    publishedArticles: 0,
    draftArticles: 0,
    premiumArticles: 0,
  });
  const [recentArticles, setRecentArticles] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      // In a real app, you would fetch user-specific data
      const articles = await blogService.getArticles(1, 10);

      // Calculate stats from articles
      const totalViews = articles.articles.reduce((sum, article) => sum + article.views, 0);
      const totalLikes = articles.articles.reduce((sum, article) => sum + article.likes, 0);
      const totalComments = articles.articles.reduce((sum, article) => sum + article.comments, 0);
      const publishedArticles = articles.articles.filter(article => article.isPublished).length;
      const premiumArticles = articles.articles.filter(article => article.isPremium).length;

      setStats({
        totalArticles: articles.articles.length,
        totalViews,
        totalLikes,
        totalComments,
        publishedArticles,
        draftArticles: articles.articles.length - publishedArticles,
        premiumArticles,
      });

      setRecentArticles(articles.articles.slice(0, 5));
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleForceRefresh = async () => {
    console.log('🔄 Force refreshing user data...');
    await refreshUserRole();
    await forceRefreshUserData();
    window.location.reload();
  };

  const statCards = [
    {
      title: 'Total Articles',
      value: stats.totalArticles,
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+12%',
      changeType: 'positive' as const,
    },
    {
      title: 'Total Views',
      value: stats.totalViews.toLocaleString(),
      icon: Eye,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+23%',
      changeType: 'positive' as const,
    },
    {
      title: 'Total Likes',
      value: stats.totalLikes.toLocaleString(),
      icon: Heart,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      change: '+8%',
      changeType: 'positive' as const,
    },
    {
      title: 'Comments',
      value: stats.totalComments.toLocaleString(),
      icon: MessageSquare,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+15%',
      changeType: 'positive' as const,
    },
  ];

  const quickActions = isAdmin ? [
    {
      title: 'Create Article',
      description: 'Write a new blog post',
      icon: PlusCircle,
      href: '/dashboard/articles/create',
      color: 'bg-blue-600 hover:bg-blue-700',
    },
    {
      title: 'View Analytics',
      description: 'Check your performance',
      icon: BarChart3,
      href: '/dashboard/analytics',
      color: 'bg-green-600 hover:bg-green-700',
    },
    {
      title: 'Manage Content',
      description: 'Edit existing articles',
      icon: FileText,
      href: '/dashboard/articles',
      color: 'bg-purple-600 hover:bg-purple-700',
    },
    {
      title: 'Settings',
      description: 'Update your profile',
      icon: Users,
      href: '/dashboard/settings',
      color: 'bg-gray-600 hover:bg-gray-700',
    },
  ] : [
    {
      title: 'View Analytics',
      description: 'Check your reading stats',
      icon: BarChart3,
      href: '/dashboard/analytics',
      color: 'bg-green-600 hover:bg-green-700',
    },
    {
      title: 'My Favorites',
      description: 'View saved articles',
      icon: Heart,
      href: '/dashboard/favorites',
      color: 'bg-red-600 hover:bg-red-700',
    },
    {
      title: 'My Comments',
      description: 'Manage your comments',
      icon: MessageSquare,
      href: '/dashboard/comments',
      color: 'bg-purple-600 hover:bg-purple-700',
    },
    {
      title: 'Settings',
      description: 'Update your profile',
      icon: Users,
      href: '/dashboard/settings',
      color: 'bg-gray-600 hover:bg-gray-700',
    },
  ];

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {user?.email?.split('@')[0]}!
          </h1>
          <p className="text-gray-600 mt-1">
            Here's what's happening with your content today.
          </p>
          <div className="flex items-center space-x-2 mt-2">
            <Button
              onClick={handleForceRefresh}
              variant="outline"
              size="sm"
              className="text-xs"
            >
              🔄 Refresh Role
            </Button>
            <Button
              onClick={clearBrowserCache}
              variant="outline"
              size="sm"
              className="text-xs"
            >
              🧹 Clear Cache
            </Button>
            <Badge variant={isAdmin ? "default" : "secondary"}>
              Role: {isAdmin ? "Admin" : "User"}
            </Badge>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <UserAvatar size="lg" />
          {isAdmin && (
            <Badge variant="secondary" className="bg-purple-100 text-purple-700">
              Admin
            </Badge>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  <p className={`text-sm ${stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'}`}>
                    {stat.change} from last month
                  </p>
                </div>
                <div className={`p-3 rounded-full ${stat.bgColor}`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => (
              <Link key={index} to={action.href}>
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2 hover:shadow-md transition-all"
                >
                  <div className={`p-2 rounded-full ${action.color} text-white`}>
                    <action.icon className="h-5 w-5" />
                  </div>
                  <div className="text-center">
                    <p className="font-medium">{action.title}</p>
                    <p className="text-xs text-gray-500">{action.description}</p>
                  </div>
                </Button>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Content Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Article Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Content Overview
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Published Articles</span>
                <span className="text-sm text-gray-600">{stats.publishedArticles}</span>
              </div>
              <Progress value={(stats.publishedArticles / stats.totalArticles) * 100} className="h-2" />
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Draft Articles</span>
                <span className="text-sm text-gray-600">{stats.draftArticles}</span>
              </div>
              <Progress value={(stats.draftArticles / stats.totalArticles) * 100} className="h-2" />
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Premium Content</span>
                <span className="text-sm text-gray-600">{stats.premiumArticles}</span>
              </div>
              <Progress value={(stats.premiumArticles / stats.totalArticles) * 100} className="h-2" />
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Recent Articles
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentArticles.length > 0 ? (
                recentArticles.map((article: any, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {article.title}
                      </p>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge variant={article.isPublished ? "default" : "secondary"} className="text-xs">
                          {article.isPublished ? "Published" : "Draft"}
                        </Badge>
                        {article.isPremium && (
                          <Badge variant="outline" className="text-xs">
                            Premium
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <Eye className="h-4 w-4" />
                      <span>{article.views}</span>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-6">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-500">No articles yet</p>
                  <Link to="/dashboard/articles/create">
                    <Button className="mt-2" size="sm">
                      Create your first article
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
