/**
 * Admin Activity Logger Utility
 * 
 * This utility provides functions to log admin activities throughout the application.
 * It automatically captures admin actions and stores them in the database for monitoring and auditing.
 */

interface ActivityLogData {
  action_type: 'order_management' | 'product_management' | 'user_management' | 'system_config' | 'consultation_management';
  action_description: string;
  target_id?: string;
  target_type?: 'order' | 'product' | 'user' | 'system' | 'consultation';
  metadata?: Record<string, any>;
  success?: boolean;
  error_message?: string;
}

/**
 * Log an admin activity to the database
 */
export async function logAdminActivity(data: ActivityLogData): Promise<boolean> {
  try {
    // Don't log if we're in development and don't have proper auth
    if (typeof window === 'undefined') {
      return false;
    }

    const response = await fetch('/api/admin/activity', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        action_type: data.action_type,
        action_description: data.action_description,
        target_id: data.target_id || null,
        target_type: data.target_type || null,
        metadata: data.metadata || null,
        success: data.success !== undefined ? data.success : true,
        error_message: data.error_message || null,
      }),
    });

    if (!response.ok) {
      // Don't log errors for permission issues (non-admin users)
      if (response.status !== 403) {
        console.error('Failed to log admin activity:', await response.text());
      }
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error logging admin activity:', error);
    return false;
  }
}

/**
 * Log product management activities
 */
export const ProductActivityLogger = {
  created: (productId: string, productName: string, metadata?: Record<string, any>) =>
    logAdminActivity({
      action_type: 'product_management',
      action_description: `Created product: ${productName}`,
      target_id: productId,
      target_type: 'product',
      metadata: { product_name: productName, ...metadata },
    }),

  updated: (productId: string, productName: string, changes: Record<string, any>) =>
    logAdminActivity({
      action_type: 'product_management',
      action_description: `Updated product: ${productName}`,
      target_id: productId,
      target_type: 'product',
      metadata: { product_name: productName, changes },
    }),

  deleted: (productId: string, productName: string) =>
    logAdminActivity({
      action_type: 'product_management',
      action_description: `Deleted product: ${productName}`,
      target_id: productId,
      target_type: 'product',
      metadata: { product_name: productName },
    }),

  stockUpdated: (productId: string, productName: string, oldStock: number, newStock: number) =>
    logAdminActivity({
      action_type: 'product_management',
      action_description: `Updated stock for ${productName}: ${oldStock} → ${newStock}`,
      target_id: productId,
      target_type: 'product',
      metadata: { product_name: productName, old_stock: oldStock, new_stock: newStock },
    }),

  priceUpdated: (productId: string, productName: string, oldPrice: number, newPrice: number) =>
    logAdminActivity({
      action_type: 'product_management',
      action_description: `Updated price for ${productName}: $${oldPrice} → $${newPrice}`,
      target_id: productId,
      target_type: 'product',
      metadata: { product_name: productName, old_price: oldPrice, new_price: newPrice },
    }),
};

/**
 * Log order management activities
 */
export const OrderActivityLogger = {
  created: (orderId: string, customerEmail: string, totalAmount: number, metadata?: Record<string, any>) =>
    logAdminActivity({
      action_type: 'order_management',
      action_description: `New order received from ${customerEmail} - R${totalAmount.toFixed(2)}`,
      target_id: orderId,
      target_type: 'order',
      metadata: { customer_email: customerEmail, total_amount: totalAmount, ...metadata },
    }),

  statusChanged: (orderId: string, oldStatus: string, newStatus: string, customerEmail?: string) =>
    logAdminActivity({
      action_type: 'order_management',
      action_description: `Changed order status: ${oldStatus} → ${newStatus}`,
      target_id: orderId,
      target_type: 'order',
      metadata: { old_status: oldStatus, new_status: newStatus, customer_email: customerEmail },
    }),

  paymentStatusChanged: (orderId: string, oldStatus: string, newStatus: string) =>
    logAdminActivity({
      action_type: 'order_management',
      action_description: `Changed payment status: ${oldStatus} → ${newStatus}`,
      target_id: orderId,
      target_type: 'order',
      metadata: { old_payment_status: oldStatus, new_payment_status: newStatus },
    }),

  refunded: (orderId: string, amount: number, reason?: string) =>
    logAdminActivity({
      action_type: 'order_management',
      action_description: `Refunded order: R${amount.toFixed(2)}`,
      target_id: orderId,
      target_type: 'order',
      metadata: { refund_amount: amount, reason },
    }),

  cancelled: (orderId: string, reason?: string) =>
    logAdminActivity({
      action_type: 'order_management',
      action_description: `Cancelled order`,
      target_id: orderId,
      target_type: 'order',
      metadata: { reason },
    }),

  shipped: (orderId: string, trackingNumber?: string, carrier?: string) =>
    logAdminActivity({
      action_type: 'order_management',
      action_description: `Marked order as shipped`,
      target_id: orderId,
      target_type: 'order',
      metadata: { tracking_number: trackingNumber, carrier },
    }),

  viewed: (orderId: string, customerEmail?: string) =>
    logAdminActivity({
      action_type: 'order_management',
      action_description: `Viewed order details`,
      target_id: orderId,
      target_type: 'order',
      metadata: { customer_email: customerEmail, action: 'view' },
    }),

  deleted: (orderId: string, customerEmail?: string, reason?: string) =>
    logAdminActivity({
      action_type: 'order_management',
      action_description: `Deleted order`,
      target_id: orderId,
      target_type: 'order',
      metadata: { customer_email: customerEmail, reason },
    }),
};

/**
 * Log user management activities
 */
export const UserActivityLogger = {
  roleChanged: (userId: string, userEmail: string, oldRole: string, newRole: string) =>
    logAdminActivity({
      action_type: 'user_management',
      action_description: `Changed user role: ${oldRole} → ${newRole}`,
      target_id: userId,
      target_type: 'user',
      metadata: { user_email: userEmail, old_role: oldRole, new_role: newRole },
    }),

  adminRoleChanged: (userId: string, userEmail: string, oldRole: string, newRole: string) =>
    logAdminActivity({
      action_type: 'user_management',
      action_description: `Changed admin role: ${oldRole} → ${newRole}`,
      target_id: userId,
      target_type: 'user',
      metadata: { user_email: userEmail, old_admin_role: oldRole, new_admin_role: newRole },
    }),

  suspended: (userId: string, userEmail: string, reason?: string) =>
    logAdminActivity({
      action_type: 'user_management',
      action_description: `Suspended user: ${userEmail}`,
      target_id: userId,
      target_type: 'user',
      metadata: { user_email: userEmail, reason },
    }),

  reactivated: (userId: string, userEmail: string) =>
    logAdminActivity({
      action_type: 'user_management',
      action_description: `Reactivated user: ${userEmail}`,
      target_id: userId,
      target_type: 'user',
      metadata: { user_email: userEmail },
    }),

  deleted: (userId: string, userEmail: string) =>
    logAdminActivity({
      action_type: 'user_management',
      action_description: `Deleted user: ${userEmail}`,
      target_id: userId,
      target_type: 'user',
      metadata: { user_email: userEmail },
    }),
};

/**
 * Log consultation management activities
 */
export const ConsultationActivityLogger = {
  created: (consultationId: string, customerName: string, customerEmail: string, scheduledDate: string, metadata?: Record<string, any>) =>
    logAdminActivity({
      action_type: 'consultation_management',
      action_description: `New consultation booked for ${customerName} (${customerEmail}) on ${scheduledDate}`,
      target_id: consultationId,
      target_type: 'consultation',
      metadata: { customer_name: customerName, customer_email: customerEmail, scheduled_date: scheduledDate, ...metadata },
    }),

  statusChanged: (consultationId: string, oldStatus: string, newStatus: string, customerName?: string) =>
    logAdminActivity({
      action_type: 'consultation_management',
      action_description: `Changed consultation status: ${oldStatus} → ${newStatus}${customerName ? ` for ${customerName}` : ''}`,
      target_id: consultationId,
      target_type: 'consultation',
      metadata: { old_status: oldStatus, new_status: newStatus, customer_name: customerName },
    }),

  rescheduled: (consultationId: string, oldDate: string, newDate: string, customerName?: string) =>
    logAdminActivity({
      action_type: 'consultation_management',
      action_description: `Rescheduled consultation from ${oldDate} to ${newDate}${customerName ? ` for ${customerName}` : ''}`,
      target_id: consultationId,
      target_type: 'consultation',
      metadata: { old_date: oldDate, new_date: newDate, customer_name: customerName },
    }),

  cancelled: (consultationId: string, reason?: string, customerName?: string) =>
    logAdminActivity({
      action_type: 'consultation_management',
      action_description: `Cancelled consultation${customerName ? ` for ${customerName}` : ''}`,
      target_id: consultationId,
      target_type: 'consultation',
      metadata: { reason, customer_name: customerName },
    }),

  completed: (consultationId: string, customerName?: string, duration?: number) =>
    logAdminActivity({
      action_type: 'consultation_management',
      action_description: `Marked consultation as completed${customerName ? ` for ${customerName}` : ''}`,
      target_id: consultationId,
      target_type: 'consultation',
      metadata: { customer_name: customerName, duration },
    }),

  paymentStatusChanged: (consultationId: string, oldStatus: string, newStatus: string, amount?: number) =>
    logAdminActivity({
      action_type: 'consultation_management',
      action_description: `Changed payment status: ${oldStatus} → ${newStatus}${amount ? ` (R${amount.toFixed(2)})` : ''}`,
      target_id: consultationId,
      target_type: 'consultation',
      metadata: { old_payment_status: oldStatus, new_payment_status: newStatus, amount },
    }),

  deleted: (consultationId: string, customerName?: string, reason?: string) =>
    logAdminActivity({
      action_type: 'consultation_management',
      action_description: `Deleted consultation${customerName ? ` for ${customerName}` : ''}`,
      target_id: consultationId,
      target_type: 'consultation',
      metadata: { customer_name: customerName, reason },
    }),

  bulkAction: (action: string, consultationIds: string[], count: number) =>
    logAdminActivity({
      action_type: 'consultation_management',
      action_description: `Performed bulk action: ${action} on ${count} consultations`,
      target_type: 'consultation',
      metadata: { bulk_action: action, consultation_ids: consultationIds, count },
    }),

  viewed: (consultationId: string, customerName?: string) =>
    logAdminActivity({
      action_type: 'consultation_management',
      action_description: `Viewed consultation details${customerName ? ` for ${customerName}` : ''}`,
      target_id: consultationId,
      target_type: 'consultation',
      metadata: { customer_name: customerName, action: 'view' },
    }),
};

/**
 * Log system configuration activities
 */
export const SystemActivityLogger = {
  reportGenerated: (reportType: string, reportName: string, fileFormat: string) =>
    logAdminActivity({
      action_type: 'system_config',
      action_description: `Generated ${reportType} report: ${reportName}`,
      target_type: 'system',
      metadata: { report_type: reportType, report_name: reportName, file_format: fileFormat },
    }),

  reportDeleted: (reportId: string, reportName: string) =>
    logAdminActivity({
      action_type: 'system_config',
      action_description: `Deleted report: ${reportName}`,
      target_id: reportId,
      target_type: 'system',
      metadata: { report_name: reportName },
    }),

  settingsChanged: (settingName: string, oldValue: any, newValue: any) =>
    logAdminActivity({
      action_type: 'system_config',
      action_description: `Changed system setting: ${settingName}`,
      target_type: 'system',
      metadata: { setting_name: settingName, old_value: oldValue, new_value: newValue },
    }),

  backupCreated: (backupType: string) =>
    logAdminActivity({
      action_type: 'system_config',
      action_description: `Created ${backupType} backup`,
      target_type: 'system',
      metadata: { backup_type: backupType },
    }),

  maintenanceMode: (enabled: boolean) =>
    logAdminActivity({
      action_type: 'system_config',
      action_description: `${enabled ? 'Enabled' : 'Disabled'} maintenance mode`,
      target_type: 'system',
      metadata: { maintenance_enabled: enabled },
    }),

  // Consultation management convenience methods
  consultationUpdated: (consultationId: string, description: string, adminId?: string) =>
    logAdminActivity({
      action_type: 'consultation_management',
      action_description: description,
      target_id: consultationId,
      target_type: 'consultation',
      metadata: { admin_id: adminId },
    }),

  consultationDeleted: (consultationId: string, description: string, adminId?: string) =>
    logAdminActivity({
      action_type: 'consultation_management',
      action_description: description,
      target_id: consultationId,
      target_type: 'consultation',
      metadata: { admin_id: adminId, action: 'delete' },
    }),
};

/**
 * Log failed activities with error details
 */
export function logFailedActivity(
  actionType: ActivityLogData['action_type'],
  actionDescription: string,
  error: Error | string,
  metadata?: Record<string, any>
): Promise<boolean> {
  return logAdminActivity({
    action_type: actionType,
    action_description: actionDescription,
    success: false,
    error_message: error instanceof Error ? error.message : error,
    metadata,
  });
}

/**
 * Wrapper function to automatically log activity for async operations
 */
export async function withActivityLogging<T>(
  operation: () => Promise<T>,
  activityData: Omit<ActivityLogData, 'success' | 'error_message'>
): Promise<T> {
  try {
    const result = await operation();
    await logAdminActivity({ ...activityData, success: true });
    return result;
  } catch (error) {
    await logAdminActivity({
      ...activityData,
      success: false,
      error_message: error instanceof Error ? error.message : String(error),
    });
    throw error;
  }
}
