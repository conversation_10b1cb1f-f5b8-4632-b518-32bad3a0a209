import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import {
  Shield,
  Lock,
  Eye,
  Download,
  Trash2,
  AlertTriangle,
  CheckCircle,
  FileText,
  Users,
  Database,
  Globe,
  Cookie,
  Mail
} from 'lucide-react';

interface PrivacySettings {
  cookieConsent: boolean;
  dataRetention: number;
  anonymizeIPs: boolean;
  gdprCompliance: boolean;
  ccpaCompliance: boolean;
  dataExportEnabled: boolean;
  rightToBeForgotten: boolean;
  consentLogging: boolean;
}

interface DataRequest {
  id: string;
  type: 'export' | 'deletion';
  userEmail: string;
  status: 'pending' | 'processing' | 'completed' | 'rejected';
  requestDate: string;
  completedDate?: string;
}

export function Privacy() {
  const { toast } = useToast();
  const [settings, setSettings] = useState<PrivacySettings>({
    cookieConsent: true,
    dataRetention: 365,
    anonymizeIPs: true,
    gdprCompliance: true,
    ccpaCompliance: true,
    dataExportEnabled: true,
    rightToBeForgotten: true,
    consentLogging: true,
  });

  const [dataRequests] = useState<DataRequest[]>([
    {
      id: '1',
      type: 'export',
      userEmail: '<EMAIL>',
      status: 'pending',
      requestDate: '2024-01-15',
    },
    {
      id: '2',
      type: 'deletion',
      userEmail: '<EMAIL>',
      status: 'processing',
      requestDate: '2024-01-14',
    },
    {
      id: '3',
      type: 'export',
      userEmail: '<EMAIL>',
      status: 'completed',
      requestDate: '2024-01-13',
      completedDate: '2024-01-14',
    },
  ]);

  const [privacyPolicy, setPrivacyPolicy] = useState(`
# Privacy Policy

## Information We Collect
We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us.

## How We Use Your Information
We use the information we collect to provide, maintain, and improve our services.

## Information Sharing
We do not sell, trade, or otherwise transfer your personal information to third parties.

## Data Security
We implement appropriate security measures to protect your personal information.

## Your Rights
You have the right to access, update, or delete your personal information.

## Contact Us
If you have any questions about this Privacy Policy, please contact us.
  `.trim());

  const handleSettingChange = (key: keyof PrivacySettings, value: boolean | number) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleSaveSettings = () => {
    // Here you would save settings to your backend
    toast({
      title: 'Success',
      description: 'Privacy settings updated successfully',
    });
  };

  const handleSavePrivacyPolicy = () => {
    // Here you would save the privacy policy to your backend
    toast({
      title: 'Success',
      description: 'Privacy policy updated successfully',
    });
  };

  const handleDataRequest = (requestId: string, action: 'approve' | 'reject') => {
    // Here you would handle the data request
    toast({
      title: 'Success',
      description: `Data request ${action}d successfully`,
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'processing':
        return <Database className="w-4 h-4" />;
      case 'pending':
        return <AlertTriangle className="w-4 h-4" />;
      case 'rejected':
        return <Trash2 className="w-4 h-4" />;
      default:
        return <AlertTriangle className="w-4 h-4" />;
    }
  };

  const complianceScore = Object.values(settings).filter(Boolean).length / Object.keys(settings).length * 100;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Privacy & Compliance</h1>
          <p className="text-gray-600">Manage GDPR, CCPA compliance and privacy settings</p>
        </div>
        <div className="flex items-center space-x-3">
          <Badge className={complianceScore >= 80 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
            <Shield className="w-4 h-4 mr-1" />
            {Math.round(complianceScore)}% Compliant
          </Badge>
        </div>
      </div>

      {/* Compliance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">GDPR Compliance</p>
                <p className="text-2xl font-bold text-green-600">Active</p>
              </div>
              <Shield className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">CCPA Compliance</p>
                <p className="text-2xl font-bold text-green-600">Active</p>
              </div>
              <Lock className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Data Requests</p>
                <p className="text-2xl font-bold text-gray-900">{dataRequests.length}</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Cookie Consent</p>
                <p className="text-2xl font-bold text-green-600">Enabled</p>
              </div>
              <Cookie className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Privacy Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Shield className="w-5 h-5 mr-2" />
            Privacy Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="font-medium">Cookie Consent Banner</label>
                  <p className="text-sm text-gray-600">Show cookie consent banner to users</p>
                </div>
                <Switch
                  checked={settings.cookieConsent}
                  onCheckedChange={(checked) => handleSettingChange('cookieConsent', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="font-medium">Anonymize IP Addresses</label>
                  <p className="text-sm text-gray-600">Anonymize user IP addresses in analytics</p>
                </div>
                <Switch
                  checked={settings.anonymizeIPs}
                  onCheckedChange={(checked) => handleSettingChange('anonymizeIPs', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="font-medium">GDPR Compliance</label>
                  <p className="text-sm text-gray-600">Enable GDPR compliance features</p>
                </div>
                <Switch
                  checked={settings.gdprCompliance}
                  onCheckedChange={(checked) => handleSettingChange('gdprCompliance', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="font-medium">CCPA Compliance</label>
                  <p className="text-sm text-gray-600">Enable CCPA compliance features</p>
                </div>
                <Switch
                  checked={settings.ccpaCompliance}
                  onCheckedChange={(checked) => handleSettingChange('ccpaCompliance', checked)}
                />
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="font-medium">Data Export</label>
                  <p className="text-sm text-gray-600">Allow users to export their data</p>
                </div>
                <Switch
                  checked={settings.dataExportEnabled}
                  onCheckedChange={(checked) => handleSettingChange('dataExportEnabled', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="font-medium">Right to be Forgotten</label>
                  <p className="text-sm text-gray-600">Allow users to request data deletion</p>
                </div>
                <Switch
                  checked={settings.rightToBeForgotten}
                  onCheckedChange={(checked) => handleSettingChange('rightToBeForgotten', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="font-medium">Consent Logging</label>
                  <p className="text-sm text-gray-600">Log user consent decisions</p>
                </div>
                <Switch
                  checked={settings.consentLogging}
                  onCheckedChange={(checked) => handleSettingChange('consentLogging', checked)}
                />
              </div>

              <div>
                <label className="font-medium block mb-2">Data Retention Period (days)</label>
                <Input
                  type="number"
                  value={settings.dataRetention}
                  onChange={(e) => handleSettingChange('dataRetention', parseInt(e.target.value))}
                  className="w-full"
                />
                <p className="text-sm text-gray-600 mt-1">How long to retain user data</p>
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <Button onClick={handleSaveSettings}>
              Save Settings
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Data Requests */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Database className="w-5 h-5 mr-2" />
            Data Requests
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {dataRequests.map((request) => (
              <div key={request.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="text-gray-400">
                    {request.type === 'export' ? <Download className="w-5 h-5" /> : <Trash2 className="w-5 h-5" />}
                  </div>
                  <div>
                    <h3 className="font-medium">
                      {request.type === 'export' ? 'Data Export' : 'Data Deletion'} Request
                    </h3>
                    <p className="text-sm text-gray-600">{request.userEmail}</p>
                    <p className="text-sm text-gray-500">
                      Requested: {new Date(request.requestDate).toLocaleDateString()}
                      {request.completedDate && (
                        <span> • Completed: {new Date(request.completedDate).toLocaleDateString()}</span>
                      )}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Badge className={`${getStatusColor(request.status)} flex items-center space-x-1`}>
                    {getStatusIcon(request.status)}
                    <span>{request.status}</span>
                  </Badge>
                  {request.status === 'pending' && (
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDataRequest(request.id, 'approve')}
                      >
                        Approve
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDataRequest(request.id, 'reject')}
                      >
                        Reject
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Privacy Policy Editor */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="w-5 h-5 mr-2" />
            Privacy Policy
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Textarea
            value={privacyPolicy}
            onChange={(e) => setPrivacyPolicy(e.target.value)}
            rows={15}
            className="font-mono text-sm"
          />
          <div className="flex justify-between items-center">
            <p className="text-sm text-gray-600">
              Last updated: {new Date().toLocaleDateString()}
            </p>
            <div className="flex space-x-3">
              <Button variant="outline">
                <Eye className="w-4 h-4 mr-2" />
                Preview
              </Button>
              <Button onClick={handleSavePrivacyPolicy}>
                Save Policy
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
