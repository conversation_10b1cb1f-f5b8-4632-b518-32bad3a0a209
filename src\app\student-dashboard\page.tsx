"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  Video,
  BookOpen,
  MessageCircle,
  Clock,
  Users,
  BarChart,
  CalendarIcon,
  Plus,
  Loader2,
  AlertCircle,
  Database
} from "lucide-react";
import { redirect } from "next/navigation";
import { createBrowserClient } from '@supabase/ssr';
import Link from "next/link";
import { getStudentDashboardData, requestSession, getStudentEnrollments } from "@/utils/supabase/mentorship-utils";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";

interface DashboardData {
  enrollments: any[];
  upcomingSessions: any[];
  progress: any;
  nextSession: any;
}

export default function StudentDashboard() {
  const [showWelcome, setShowWelcome] = useState(true);
  const [studentName, setStudentName] = useState("");
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Session booking state
  const [isBookingDialogOpen, setIsBookingDialogOpen] = useState(false);
  const [enrollments, setEnrollments] = useState<any[]>([]);
  const [submitting, setSubmitting] = useState(false);
  const [sessionType, setSessionType] = useState("");
  const [preferredDate, setPreferredDate] = useState("");
  const [preferredTime, setPreferredTime] = useState("");
  const [selectedEnrollment, setSelectedEnrollment] = useState("");
  const [notes, setNotes] = useState("");

  const { toast } = useToast();

  useEffect(() => {
    async function loadDashboardData() {
      try {
        setLoading(true);
        setError(null);

        const supabase = createBrowserClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
        );

        const { data: { user }, error: authError } = await supabase.auth.getUser();

        if (authError) {
          throw new Error('Authentication error');
        }

        if (!user) {
          redirect('/auth/sign-in');
          return;
        }

        // Get user's data
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('full_name')
          .eq('id', user.id)
          .single();

        if (userError) {
          throw new Error('Error fetching user data');
        }

        setStudentName(userData?.full_name || user.email?.split('@')[0] || 'Student');

        // Get dashboard data with detailed error handling
        const { data: dashData, error: dashError } = await getStudentDashboardData(user.id);

        if (dashError) {
          console.error('Dashboard data error:', dashError);

          // Check if it's a table missing error
          const errorMessage = (dashError as any)?.message || dashError?.toString() || 'Unknown error';
          if (errorMessage.includes('relation') && errorMessage.includes('does not exist')) {
            setError('Database tables are not set up. Please visit /database-setup to initialize the database.');
            return;
          } else if (errorMessage.includes('permission denied') || errorMessage.includes('RLS')) {
            setError('Permission denied. Please ensure you are properly authenticated and have the correct permissions.');
            return;
          } else {
            console.warn('Dashboard data error (continuing with empty state):', errorMessage);
          }
        }

        setDashboardData(dashData);

        // Load enrollments for session booking with detailed error handling
        const { data: enrollmentData, error: enrollmentError } = await getStudentEnrollments(user.id);
        if (enrollmentError) {
          console.error('Enrollment data error:', enrollmentError);

          const enrollmentErrorMessage = (enrollmentError as any)?.message || enrollmentError?.toString() || 'Unknown error';
          if (enrollmentErrorMessage.includes('relation') && enrollmentErrorMessage.includes('does not exist')) {
            // Table doesn't exist, but don't override main error if already set
            if (!error) {
              setError('Enrollment system is not set up. Please visit /database-setup to initialize the database.');
              return;
            }
          } else {
            console.warn('Failed to load enrollments (continuing with empty state):', enrollmentErrorMessage);
          }
        } else if (enrollmentData) {
          setEnrollments(enrollmentData);
        }
      } catch (error: any) {
        console.error('Error loading dashboard data:', error);
        setError(error.message || 'Failed to load dashboard data');
        setStudentName('Student');
      } finally {
        setLoading(false);
      }
    }

    loadDashboardData();

    // Hide welcome message after 3 seconds
    const timer = setTimeout(() => {
      setShowWelcome(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  const handleSessionRequest = async () => {
    if (!sessionType || !preferredDate || !preferredTime || !selectedEnrollment) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      setSubmitting(true);

      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        redirect("/auth/sign-in");
        return;
      }

      const { data, error } = await requestSession({
        student_id: user.id,
        enrollment_id: selectedEnrollment,
        preferred_date: preferredDate,
        preferred_time: preferredTime,
        session_type: sessionType,
        notes: notes
      });

      if (error) {
        throw new Error('Failed to request session');
      }

      toast({
        title: "Session Requested",
        description: "Your session request has been submitted successfully",
      });

      // Reset form and close dialog
      setSessionType("");
      setPreferredDate("");
      setPreferredTime("");
      setSelectedEnrollment("");
      setNotes("");
      setIsBookingDialogOpen(false);

      // Reload dashboard data
      window.location.reload();
    } catch (error: any) {
      console.error('Error requesting session:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to request session",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 space-y-4">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>

        {error.includes('database-setup') && (
          <div className="flex gap-4">
            <Link href="/database-setup">
              <Button>
                <Database className="h-4 w-4 mr-2" />
                Setup Database
              </Button>
            </Link>
            <Link href="/test-database">
              <Button variant="outline">
                <AlertCircle className="h-4 w-4 mr-2" />
                Test Database
              </Button>
            </Link>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="relative h-12">
        <h1
          className={`text-3xl font-bold absolute transition-all duration-1000 ${
            showWelcome
              ? 'opacity-100 transform-none'
              : 'opacity-0 -translate-y-4'
          }`}
        >
          Hello {studentName}, Welcome to your dashboard
        </h1>
        <h1
          className={`text-3xl font-bold absolute transition-all duration-1000 ${
            showWelcome
              ? 'opacity-0 translate-y-4'
              : 'opacity-100 transform-none'
          }`}
        >
          {studentName}&apos;s Dashboard
        </h1>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Link href="/student-dashboard/schedule">
          <Card className="p-6 bg-blue-500 text-white hover:bg-blue-600 cursor-pointer transition-colors">
            <Video className="h-8 w-8 mb-2" />
            <h3 className="text-lg font-semibold">Join Session</h3>
            <p className="text-sm opacity-90">Connect with your mentor</p>
          </Card>
        </Link>
        <Link href="/student-dashboard/schedule">
          <Card className="p-6 bg-purple-500 text-white hover:bg-purple-600 cursor-pointer transition-colors">
            <CalendarIcon className="h-8 w-8 mb-2" />
            <h3 className="text-lg font-semibold">Schedule Meeting</h3>
            <p className="text-sm opacity-90">Book your next session</p>
          </Card>
        </Link>
        <Link href="/student-dashboard/resources">
          <Card className="p-6 bg-green-500 text-white hover:bg-green-600 cursor-pointer transition-colors">
            <BookOpen className="h-8 w-8 mb-2" />
            <h3 className="text-lg font-semibold">Resources</h3>
            <p className="text-sm opacity-90">Access learning materials</p>
          </Card>
        </Link>
        <Link href="/student-dashboard/chat">
          <Card className="p-6 bg-orange-500 text-white hover:bg-orange-600 cursor-pointer transition-colors">
            <MessageCircle className="h-8 w-8 mb-2" />
            <h3 className="text-lg font-semibold">Chat</h3>
            <p className="text-sm opacity-90">Message your mentor</p>
          </Card>
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Calendar and Schedule */}
        <Card className="col-span-2 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-semibold">Schedule</h2>
            <Dialog open={isBookingDialogOpen} onOpenChange={setIsBookingDialogOpen}>
              <DialogTrigger asChild>
                <Button disabled={enrollments.length === 0}>
                  <Plus className="h-4 w-4 mr-2" />
                  Book Session
                </Button>
              </DialogTrigger>
              <DialogContent className="bg-card max-w-md">
                <DialogHeader>
                  <DialogTitle>Book New Session</DialogTitle>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  {enrollments.length === 0 ? (
                    <div className="text-center py-4 text-muted-foreground">
                      <CalendarIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No active enrollments found</p>
                      <p className="text-sm">You need to be enrolled in a program to book sessions</p>
                    </div>
                  ) : (
                    <>
                      <div className="space-y-2">
                        <Label>Program/Mentor</Label>
                        <Select value={selectedEnrollment} onValueChange={setSelectedEnrollment}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select program" />
                          </SelectTrigger>
                          <SelectContent>
                            {enrollments.map((enrollment: any) => (
                              <SelectItem key={enrollment.id} value={enrollment.id}>
                                {enrollment.program?.name || 'Mentorship Program'} - {enrollment.mentor?.user?.full_name || 'Mentor'}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Session Type</Label>
                        <Select value={sessionType} onValueChange={setSessionType}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="training">Training Session</SelectItem>
                            <SelectItem value="review">Progress Review</SelectItem>
                            <SelectItem value="consultation">Consultation</SelectItem>
                            <SelectItem value="practice">Practice Session</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Preferred Date</Label>
                        <Input
                          type="date"
                          value={preferredDate}
                          onChange={(e) => setPreferredDate(e.target.value)}
                          min={new Date().toISOString().split('T')[0]}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>Preferred Time</Label>
                        <Input
                          type="time"
                          value={preferredTime}
                          onChange={(e) => setPreferredTime(e.target.value)}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>Notes (Optional)</Label>
                        <Textarea
                          placeholder="Any specific focus areas or requests"
                          value={notes}
                          onChange={(e) => setNotes(e.target.value)}
                          rows={3}
                        />
                      </div>

                      <Button
                        className="w-full"
                        onClick={handleSessionRequest}
                        disabled={submitting}
                      >
                        {submitting ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Booking...
                          </>
                        ) : (
                          'Book Session'
                        )}
                      </Button>
                    </>
                  )}
                </div>
              </DialogContent>
            </Dialog>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Calendar
              mode="single"
              className="rounded-md border"
            />
            <ScrollArea className="h-[300px]">
              <div className="space-y-4">
                {dashboardData?.upcomingSessions && dashboardData.upcomingSessions.length > 0 ? (
                  dashboardData.upcomingSessions.slice(0, 3).map((session: any) => (
                    <Card key={session.id} className="p-4">
                      <div className="flex items-start justify-between">
                        <div>
                          <h4 className="font-semibold">
                            {session.notes || 'Mentorship Session'}
                          </h4>
                          <div className="text-sm text-muted-foreground">
                            <p className="flex items-center">
                              <Clock className="h-4 w-4 mr-1" />
                              {new Date(session.scheduled_at).toLocaleDateString()} at{' '}
                              {new Date(session.scheduled_at).toLocaleTimeString([], {
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </p>
                            <p className="flex items-center">
                              <Users className="h-4 w-4 mr-1" />
                              {session.enrollment?.mentor?.user?.full_name || 'Mentor'}
                            </p>
                          </div>
                        </div>
                        <Button variant="outline" size="sm">Join</Button>
                      </div>
                    </Card>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <CalendarIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No upcoming sessions scheduled</p>
                    <p className="text-sm">Book a session with your mentor</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        </Card>

        {/* Progress Overview */}
        <Card className="p-6">
          <h2 className="text-2xl font-semibold mb-6">Progress Overview</h2>
          <div className="space-y-6">
            {dashboardData?.progress ? (
              <>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Overall Progress</span>
                    <span>{dashboardData.progress.completionPercentage}%</span>
                  </div>
                  <div className="h-2 bg-muted rounded-full overflow-hidden">
                    <div
                      className="h-full bg-blue-500 rounded-full transition-all duration-500"
                      style={{ width: `${dashboardData.progress.completionPercentage}%` }}
                    />
                  </div>
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Sessions Completed</span>
                    <span>{dashboardData.progress.completedSessions}/{dashboardData.progress.totalSessions}</span>
                  </div>
                  <div className="h-2 bg-muted rounded-full overflow-hidden">
                    <div
                      className="h-full bg-green-500 rounded-full transition-all duration-500"
                      style={{
                        width: `${dashboardData.progress.totalSessions > 0
                          ? (dashboardData.progress.completedSessions / dashboardData.progress.totalSessions) * 100
                          : 0}%`
                      }}
                    />
                  </div>
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Active Enrollments</span>
                    <span>{dashboardData.progress.activeEnrollments}</span>
                  </div>
                  <div className="h-2 bg-muted rounded-full overflow-hidden">
                    <div
                      className="h-full bg-purple-500 rounded-full transition-all duration-500"
                      style={{ width: `${Math.min(dashboardData.progress.activeEnrollments * 50, 100)}%` }}
                    />
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                <BarChart className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No progress data available</p>
                <p className="text-sm">Complete sessions to track progress</p>
              </div>
            )}
            <Link href="/student-dashboard/progress">
              <Button className="w-full" variant="outline">
                <BarChart className="h-4 w-4 mr-2" />
                View Full Progress Report
              </Button>
            </Link>
          </div>
        </Card>
      </div>
    </div>
  );
}