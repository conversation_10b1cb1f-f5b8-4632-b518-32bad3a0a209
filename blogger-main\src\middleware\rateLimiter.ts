// Rate Limiting Middleware for API Protection
// Implements sliding window rate limiting with Redis-like functionality

interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  message?: string; // Custom error message
  skipSuccessfulRequests?: boolean; // Don't count successful requests
  skipFailedRequests?: boolean; // Don't count failed requests
  keyGenerator?: (req: any) => string; // Custom key generator
}

interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
    requests: number[];
  };
}

class RateLimiter {
  private store: RateLimitStore = {};
  private config: RateLimitConfig;

  constructor(config: RateLimitConfig) {
    this.config = {
      windowMs: 15 * 60 * 1000, // 15 minutes default
      maxRequests: 100, // 100 requests default
      message: 'Too many requests, please try again later.',
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
      keyGenerator: (req) => req.ip || 'anonymous',
      ...config,
    };

    // Clean up expired entries every minute
    setInterval(() => this.cleanup(), 60 * 1000);
  }

  // Main rate limiting function
  limit = (req: any, res: any, next: any) => {
    const key = this.config.keyGenerator!(req);
    const now = Date.now();
    const windowStart = now - this.config.windowMs;

    // Initialize or get existing record
    if (!this.store[key]) {
      this.store[key] = {
        count: 0,
        resetTime: now + this.config.windowMs,
        requests: [],
      };
    }

    const record = this.store[key];

    // Remove old requests outside the window
    record.requests = record.requests.filter(time => time > windowStart);
    record.count = record.requests.length;

    // Check if limit exceeded
    if (record.count >= this.config.maxRequests) {
      const resetTime = Math.ceil((record.resetTime - now) / 1000);
      
      // Set rate limit headers
      res.setHeader('X-RateLimit-Limit', this.config.maxRequests);
      res.setHeader('X-RateLimit-Remaining', 0);
      res.setHeader('X-RateLimit-Reset', resetTime);
      res.setHeader('Retry-After', resetTime);

      return res.status(429).json({
        error: 'Rate limit exceeded',
        message: this.config.message,
        retryAfter: resetTime,
      });
    }

    // Add current request
    record.requests.push(now);
    record.count++;

    // Update reset time if needed
    if (now > record.resetTime) {
      record.resetTime = now + this.config.windowMs;
    }

    // Set rate limit headers
    res.setHeader('X-RateLimit-Limit', this.config.maxRequests);
    res.setHeader('X-RateLimit-Remaining', this.config.maxRequests - record.count);
    res.setHeader('X-RateLimit-Reset', Math.ceil((record.resetTime - now) / 1000));

    next();
  };

  // Clean up expired entries
  private cleanup() {
    const now = Date.now();
    Object.keys(this.store).forEach(key => {
      const record = this.store[key];
      if (record.resetTime < now && record.requests.length === 0) {
        delete this.store[key];
      }
    });
  }

  // Get current stats for a key
  getStats(key: string) {
    const record = this.store[key];
    if (!record) return null;

    const now = Date.now();
    const windowStart = now - this.config.windowMs;
    const validRequests = record.requests.filter(time => time > windowStart);

    return {
      count: validRequests.length,
      remaining: this.config.maxRequests - validRequests.length,
      resetTime: record.resetTime,
      resetIn: Math.max(0, record.resetTime - now),
    };
  }

  // Reset rate limit for a key
  reset(key: string) {
    delete this.store[key];
  }
}

// Predefined rate limiters for different endpoints
export const rateLimiters = {
  // General API rate limiter
  general: new RateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 1000, // 1000 requests per 15 minutes
    message: 'Too many API requests, please try again later.',
  }),

  // Authentication rate limiter (stricter)
  auth: new RateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 login attempts per 15 minutes
    message: 'Too many login attempts, please try again later.',
    keyGenerator: (req) => `auth:${req.ip}:${req.body?.email || 'anonymous'}`,
  }),

  // Password reset rate limiter
  passwordReset: new RateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3, // 3 password reset attempts per hour
    message: 'Too many password reset requests, please try again later.',
    keyGenerator: (req) => `reset:${req.body?.email || req.ip}`,
  }),

  // Search rate limiter
  search: new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30, // 30 searches per minute
    message: 'Too many search requests, please slow down.',
  }),

  // Upload rate limiter
  upload: new RateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 50, // 50 uploads per hour
    message: 'Upload limit exceeded, please try again later.',
  }),

  // Comment/posting rate limiter
  posting: new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 5, // 5 posts per minute
    message: 'Posting too frequently, please wait before posting again.',
    keyGenerator: (req) => `post:${req.user?.id || req.ip}`,
  }),

  // Newsletter signup rate limiter
  newsletter: new RateLimiter({
    windowMs: 24 * 60 * 60 * 1000, // 24 hours
    maxRequests: 3, // 3 newsletter signups per day
    message: 'Newsletter signup limit reached, please try again tomorrow.',
    keyGenerator: (req) => `newsletter:${req.body?.email || req.ip}`,
  }),
};

// Middleware factory for custom rate limiting
export function createRateLimiter(config: RateLimitConfig) {
  return new RateLimiter(config);
}

// Express middleware wrapper
export function rateLimitMiddleware(limiterName: keyof typeof rateLimiters) {
  return rateLimiters[limiterName].limit;
}

// Advanced rate limiting with different tiers
export class TieredRateLimiter {
  private limiters: Map<string, RateLimiter> = new Map();

  constructor(private tiers: Record<string, RateLimitConfig>) {
    Object.entries(tiers).forEach(([tier, config]) => {
      this.limiters.set(tier, new RateLimiter(config));
    });
  }

  // Get appropriate limiter based on user tier
  getLimiter(userTier: string = 'free'): RateLimiter {
    return this.limiters.get(userTier) || this.limiters.get('free')!;
  }

  // Middleware that checks user tier
  middleware = (req: any, res: any, next: any) => {
    const userTier = req.user?.subscription_tier || 'free';
    const limiter = this.getLimiter(userTier);
    return limiter.limit(req, res, next);
  };
}

// User tier-based rate limiting
export const tieredRateLimiter = new TieredRateLimiter({
  free: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100, // 100 requests per 15 minutes
    message: 'Rate limit exceeded. Upgrade to premium for higher limits.',
  },
  premium: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 1000, // 1000 requests per 15 minutes
    message: 'Premium rate limit exceeded.',
  },
  admin: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 10000, // 10000 requests per 15 minutes
    message: 'Admin rate limit exceeded.',
  },
});

// Rate limiting utilities
export const rateLimitUtils = {
  // Check if IP is whitelisted
  isWhitelisted: (ip: string): boolean => {
    const whitelist = [
      '127.0.0.1',
      '::1',
      'localhost',
      // Add your server IPs here
    ];
    return whitelist.includes(ip);
  },

  // Get client IP from request
  getClientIP: (req: any): string => {
    return (
      req.headers['x-forwarded-for']?.split(',')[0] ||
      req.headers['x-real-ip'] ||
      req.connection?.remoteAddress ||
      req.socket?.remoteAddress ||
      req.ip ||
      'unknown'
    );
  },

  // Create custom key generator
  createKeyGenerator: (prefix: string, fields: string[]) => {
    return (req: any) => {
      const values = fields.map(field => {
        if (field === 'ip') return rateLimitUtils.getClientIP(req);
        if (field.startsWith('body.')) return req.body?.[field.slice(5)];
        if (field.startsWith('user.')) return req.user?.[field.slice(5)];
        if (field.startsWith('headers.')) return req.headers?.[field.slice(8)];
        return req[field];
      }).filter(Boolean);
      
      return `${prefix}:${values.join(':')}`;
    };
  },
};

export default RateLimiter;
