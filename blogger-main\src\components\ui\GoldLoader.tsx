// 🎨 GOLD-THEMED LOADER COMPONENT
// Thabo Bester Project - Professional Gold & Black Loader

import React from 'react';
import { motion } from 'framer-motion';
import { theme } from '../../config/theme';

interface GoldLoaderProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'spinner' | 'typewriter' | 'pulse' | 'dots';
  text?: string;
  showText?: boolean;
  className?: string;
}

export const GoldLoader: React.FC<GoldLoaderProps> = ({
  size = 'md',
  variant = 'typewriter',
  text = 'Thabo Bester',
  showText = true,
  className = '',
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24',
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
  };

  // Spinner Variant
  const SpinnerLoader = () => (
    <div className={`relative ${sizeClasses[size]} ${className}`}>
      <motion.div
        className="absolute inset-0 rounded-full border-4 border-gray-200"
        style={{ borderTopColor: theme.colors.primary[400] }}
        animate={{ rotate: 360 }}
        transition={{
          duration: 1,
          repeat: Infinity,
          ease: 'linear',
        }}
      />
      <motion.div
        className="absolute inset-2 rounded-full"
        style={{ backgroundColor: theme.colors.primary[400] }}
        animate={{
          scale: [0.8, 1.2, 0.8],
          opacity: [0.5, 1, 0.5],
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      />
    </div>
  );

  // Typewriter Variant
  const TypewriterLoader = () => {
    const letters = text.split('');
    
    return (
      <div className={`flex items-center space-x-1 ${className}`}>
        {letters.map((letter, index) => (
          <motion.span
            key={index}
            className={`font-bold ${textSizeClasses[size]}`}
            style={{ color: theme.colors.primary[400] }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              duration: 0.5,
              delay: index * 0.1,
              repeat: Infinity,
              repeatDelay: 2,
            }}
          >
            {letter === ' ' ? '\u00A0' : letter}
          </motion.span>
        ))}
        <motion.span
          className={`font-bold ${textSizeClasses[size]}`}
          style={{ color: theme.colors.primary[400] }}
          animate={{ opacity: [1, 0, 1] }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        >
          |
        </motion.span>
      </div>
    );
  };

  // Pulse Variant
  const PulseLoader = () => (
    <div className={`${sizeClasses[size]} ${className}`}>
      <motion.div
        className="w-full h-full rounded-full"
        style={{ backgroundColor: theme.colors.primary[400] }}
        animate={{
          scale: [1, 1.5, 1],
          opacity: [1, 0.5, 1],
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      />
    </div>
  );

  // Dots Variant
  const DotsLoader = () => (
    <div className={`flex space-x-2 ${className}`}>
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          className={`w-3 h-3 rounded-full`}
          style={{ backgroundColor: theme.colors.primary[400] }}
          animate={{
            y: [0, -10, 0],
            opacity: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 0.8,
            repeat: Infinity,
            delay: index * 0.2,
            ease: 'easeInOut',
          }}
        />
      ))}
    </div>
  );

  const renderLoader = () => {
    switch (variant) {
      case 'spinner':
        return <SpinnerLoader />;
      case 'typewriter':
        return <TypewriterLoader />;
      case 'pulse':
        return <PulseLoader />;
      case 'dots':
        return <DotsLoader />;
      default:
        return <TypewriterLoader />;
    }
  };

  return (
    <div className="flex flex-col items-center justify-center space-y-4">
      {renderLoader()}
      {showText && variant !== 'typewriter' && (
        <motion.p
          className={`font-medium ${textSizeClasses[size]}`}
          style={{ color: theme.colors.text.primary }}
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        >
          {text}
        </motion.p>
      )}
    </div>
  );
};

// Full-screen loader overlay
export const GoldLoaderOverlay: React.FC<{
  isLoading: boolean;
  text?: string;
  variant?: GoldLoaderProps['variant'];
}> = ({ isLoading, text = 'Loading...', variant = 'typewriter' }) => {
  if (!isLoading) return null;

  return (
    <motion.div
      className="fixed inset-0 z-50 flex items-center justify-center"
      style={{ backgroundColor: 'rgba(255, 255, 255, 0.95)' }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="text-center">
        <GoldLoader
          size="xl"
          variant={variant}
          text={text}
          showText={true}
        />
        <motion.div
          className="mt-8 w-64 h-1 bg-gray-200 rounded-full overflow-hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <motion.div
            className="h-full rounded-full"
            style={{ backgroundColor: theme.colors.primary[400] }}
            animate={{
              x: ['-100%', '100%'],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          />
        </motion.div>
      </div>
    </motion.div>
  );
};

// Inline loader for buttons and small components
export const GoldInlineLoader: React.FC<{
  size?: 'xs' | 'sm' | 'md';
  className?: string;
}> = ({ size = 'sm', className = '' }) => {
  const sizeMap = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
  };

  return (
    <motion.div
      className={`${sizeMap[size]} border-2 border-gray-200 rounded-full ${className}`}
      style={{ borderTopColor: theme.colors.primary[400] }}
      animate={{ rotate: 360 }}
      transition={{
        duration: 1,
        repeat: Infinity,
        ease: 'linear',
      }}
    />
  );
};

// Skeleton loader with gold theme
export const GoldSkeleton: React.FC<{
  className?: string;
  variant?: 'text' | 'rectangular' | 'circular';
}> = ({ className = '', variant = 'text' }) => {
  const baseClasses = 'animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200';
  
  const variantClasses = {
    text: 'h-4 rounded',
    rectangular: 'rounded-lg',
    circular: 'rounded-full',
  };

  return (
    <div
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      style={{
        backgroundImage: `linear-gradient(90deg, #f3f4f6 25%, ${theme.colors.primary[100]} 50%, #f3f4f6 75%)`,
        backgroundSize: '200% 100%',
        animation: 'shimmer 2s infinite',
      }}
    />
  );
};

// Add shimmer animation to global styles
const shimmerStyles = `
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = shimmerStyles;
  document.head.appendChild(style);
}

export default GoldLoader;
