#!/bin/bash

# 🐳 DOCKER SETUP SCRIPT FOR THABO BESTER PROJECT
# Sets up local development environment with Supabase and all services

set -e

echo "🚀 Setting up Thabo Bester Project with Docker..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    print_warning ".env file not found. Creating from .env.example..."
    if [ -f .env.example ]; then
        cp .env.example .env
        print_success ".env file created from .env.example"
        print_warning "Please update the .env file with your actual values before continuing."
        exit 1
    else
        print_error ".env.example file not found. Cannot create .env file."
        exit 1
    fi
fi

# Load environment variables
source .env

# Validate required environment variables
required_vars=(
    "VITE_SUPABASE_URL"
    "VITE_SUPABASE_ANON_KEY"
    "JWT_SECRET"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        print_error "Required environment variable $var is not set in .env file"
        exit 1
    fi
done

print_status "Environment variables validated successfully"

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p logs
mkdir -p data/postgres
mkdir -p data/redis
mkdir -p data/supabase
mkdir -p reports

# Stop any existing containers
print_status "Stopping any existing containers..."
docker-compose down --remove-orphans

# Pull latest images
print_status "Pulling latest Docker images..."
docker-compose pull

# Build the application
print_status "Building application container..."
docker-compose build --no-cache

# Start the services
print_status "Starting all services..."
docker-compose up -d

# Wait for services to be ready
print_status "Waiting for services to be ready..."

# Wait for PostgreSQL
print_status "Waiting for PostgreSQL to be ready..."
timeout=60
counter=0
while ! docker-compose exec -T supabase-db pg_isready -U postgres > /dev/null 2>&1; do
    if [ $counter -ge $timeout ]; then
        print_error "PostgreSQL failed to start within $timeout seconds"
        exit 1
    fi
    sleep 1
    counter=$((counter + 1))
done
print_success "PostgreSQL is ready"

# Wait for Supabase REST API
print_status "Waiting for Supabase REST API to be ready..."
timeout=60
counter=0
while ! curl -f http://localhost:54326/rest/v1/ > /dev/null 2>&1; do
    if [ $counter -ge $timeout ]; then
        print_error "Supabase REST API failed to start within $timeout seconds"
        exit 1
    fi
    sleep 1
    counter=$((counter + 1))
done
print_success "Supabase REST API is ready"

# Run database migrations
print_status "Running database migrations..."
if [ -d "supabase/migrations" ]; then
    for migration in supabase/migrations/*.sql; do
        if [ -f "$migration" ]; then
            print_status "Running migration: $(basename $migration)"
            docker-compose exec -T supabase-db psql -U postgres -d postgres -f "/docker-entrypoint-initdb.d/$(basename $migration)"
        fi
    done
    print_success "Database migrations completed"
else
    print_warning "No migrations directory found"
fi

# Run database setup scripts
print_status "Running database setup scripts..."
if [ -f "supabase/complete-setup.sql" ]; then
    print_status "Running complete setup script..."
    docker-compose exec -T supabase-db psql -U postgres -d postgres -f "/docker-entrypoint-initdb.d/complete-setup.sql"
    print_success "Complete setup script executed"
fi

if [ -f "supabase/create-missing-tables.sql" ]; then
    print_status "Running missing tables script..."
    docker-compose exec -T supabase-db psql -U postgres -d postgres -f "/docker-entrypoint-initdb.d/create-missing-tables.sql"
    print_success "Missing tables script executed"
fi

if [ -f "supabase/complete-admin-sync.sql" ]; then
    print_status "Running admin sync script..."
    docker-compose exec -T supabase-db psql -U postgres -d postgres -f "/docker-entrypoint-initdb.d/complete-admin-sync.sql"
    print_success "Admin sync script executed"
fi

# Check service health
print_status "Checking service health..."

services=(
    "app:5173"
    "supabase-db:54321"
    "supabase-rest:54326"
    "supabase-realtime:54327"
    "redis:6379"
)

for service in "${services[@]}"; do
    service_name=$(echo $service | cut -d: -f1)
    port=$(echo $service | cut -d: -f2)
    
    if docker-compose ps $service_name | grep -q "Up"; then
        print_success "$service_name is running on port $port"
    else
        print_error "$service_name is not running"
    fi
done

# Display service URLs
print_success "🎉 Docker setup completed successfully!"
echo ""
echo "📋 Service URLs:"
echo "   🌐 Application:        http://localhost:5173"
echo "   🗄️  Supabase REST API:  http://localhost:54326"
echo "   ⚡ Supabase Realtime:  ws://localhost:54327"
echo "   🐘 PostgreSQL:        localhost:54321"
echo "   🔴 Redis:             localhost:6379"
echo ""
echo "📊 Monitoring:"
echo "   📈 Prometheus:        http://localhost:9090"
echo "   📊 Grafana:          http://localhost:3001"
echo ""
echo "🔧 Management Commands:"
echo "   📜 View logs:         docker-compose logs -f"
echo "   🛑 Stop services:     docker-compose down"
echo "   🔄 Restart services:  docker-compose restart"
echo "   🧹 Clean up:         docker-compose down -v --remove-orphans"
echo ""
echo "🎯 Next Steps:"
echo "   1. Open http://localhost:5173 in your browser"
echo "   2. Check that all services are working correctly"
echo "   3. Run tests: npm run test"
echo "   4. Start developing! 🚀"
echo ""

# Create a status report
cat > reports/docker-setup-report.md << EOF
# Docker Setup Report

**Date:** $(date)
**Status:** ✅ Success

## Services Started

$(docker-compose ps)

## Environment Configuration

- **Database URL:** postgresql://postgres:postgres@localhost:54321/postgres
- **Supabase URL:** http://localhost:54326
- **Realtime URL:** ws://localhost:54327
- **Application URL:** http://localhost:5173

## Database Setup

- ✅ PostgreSQL started successfully
- ✅ Supabase REST API started successfully
- ✅ Database migrations executed
- ✅ Setup scripts executed
- ✅ Admin user sync completed

## Next Steps

1. Verify all services are accessible
2. Run application tests
3. Check real-time functionality
4. Validate payment integration (Yoco)
5. Test WebSocket connections

## Troubleshooting

If you encounter issues:

1. Check logs: \`docker-compose logs -f\`
2. Restart services: \`docker-compose restart\`
3. Clean restart: \`docker-compose down && docker-compose up -d\`

EOF

print_success "Setup report saved to reports/docker-setup-report.md"
