-- Fix User Deletion Issues in Supabase (SAFE VERSION)
-- This script safely fixes foreign key constraints and cleans up orphaned data
-- Run this in your Supabase SQL Editor

-- ============================================================================
-- STEP 1: DATA CLEANUP - REMOVE ORPHANED RECORDS
-- ============================================================================

-- First, let's identify and clean up orphaned records
DO $$
DECLARE
    orphaned_count INTEGER;
BEGIN
    -- Count orphaned records in public.users (records that don't exist in auth.users)
    SELECT COUNT(*) INTO orphaned_count
    FROM public.users pu
    WHERE NOT EXISTS (
        SELECT 1 FROM auth.users au WHERE au.id = pu.id
    );
    
    RAISE NOTICE 'Found % orphaned records in public.users', orphaned_count;
    
    -- Remove orphaned records from public.users
    DELETE FROM public.users 
    WHERE id NOT IN (
        SELECT id FROM auth.users
    );
    
    RAISE NOTICE 'Cleaned up % orphaned records from public.users', orphaned_count;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error during cleanup: %', SQLERRM;
END $$;

-- Clean up orphaned records in other tables that reference auth.users
DO $$
BEGIN
    -- Clean up student_enrollments
    DELETE FROM public.student_enrollments 
    WHERE student_id NOT IN (SELECT id FROM auth.users);
    
    -- Clean up mentors
    DELETE FROM public.mentors 
    WHERE user_id NOT IN (SELECT id FROM auth.users);
    
    -- Clean up messages (sender_id)
    DELETE FROM public.messages 
    WHERE sender_id NOT IN (SELECT id FROM auth.users);
    
    -- Clean up messages (recipient_id)
    DELETE FROM public.messages 
    WHERE recipient_id NOT IN (SELECT id FROM auth.users);
    
    -- Clean up orders
    DELETE FROM public.orders 
    WHERE user_id NOT IN (SELECT id FROM auth.users);
    
    -- Clean up subscriptions (if table exists)
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'subscriptions' AND table_schema = 'public') THEN
        DELETE FROM public.subscriptions 
        WHERE user_id NOT IN (SELECT id FROM auth.users);
    END IF;
    
    -- Set created_by to NULL in resources for non-existent users
    UPDATE public.resources 
    SET created_by = NULL 
    WHERE created_by IS NOT NULL 
    AND created_by NOT IN (SELECT id FROM auth.users);
    
    RAISE NOTICE 'Cleaned up orphaned records from all related tables';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error during related table cleanup: %', SQLERRM;
END $$;

-- ============================================================================
-- STEP 2: DROP EXISTING FOREIGN KEY CONSTRAINTS SAFELY
-- ============================================================================

-- Drop foreign key constraints that don't have CASCADE DELETE
DO $$
DECLARE
    constraint_record RECORD;
BEGIN
    -- Find and drop foreign key constraints that reference auth.users
    FOR constraint_record IN
        SELECT 
            tc.constraint_name,
            tc.table_name,
            tc.table_schema
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu 
            ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage ccu 
            ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND ccu.table_name = 'users'
        AND ccu.table_schema = 'auth'
        AND tc.table_schema = 'public'
    LOOP
        EXECUTE format('ALTER TABLE %I.%I DROP CONSTRAINT IF EXISTS %I',
            constraint_record.table_schema,
            constraint_record.table_name,
            constraint_record.constraint_name
        );
        RAISE NOTICE 'Dropped constraint % from table %', 
            constraint_record.constraint_name, 
            constraint_record.table_name;
    END LOOP;
END $$;

-- ============================================================================
-- STEP 3: RECREATE FOREIGN KEY CONSTRAINTS WITH CASCADE DELETE
-- ============================================================================

-- Fix public.users table first (most important)
DO $$
BEGIN
    -- Add CASCADE DELETE constraint for public.users.id
    ALTER TABLE public.users 
    ADD CONSTRAINT users_id_fkey 
    FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;
    RAISE NOTICE 'Fixed public.users.id foreign key with CASCADE DELETE';
EXCEPTION
    WHEN duplicate_object THEN
        RAISE NOTICE 'public.users foreign key already exists';
    WHEN OTHERS THEN
        RAISE NOTICE 'Error fixing public.users foreign key: %', SQLERRM;
END $$;

-- Fix student_enrollments table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'student_enrollments' AND table_schema = 'public') THEN
        ALTER TABLE public.student_enrollments 
        ADD CONSTRAINT student_enrollments_student_id_fkey 
        FOREIGN KEY (student_id) REFERENCES auth.users(id) ON DELETE CASCADE;
        RAISE NOTICE 'Fixed student_enrollments.student_id foreign key';
    END IF;
EXCEPTION
    WHEN duplicate_object THEN
        RAISE NOTICE 'student_enrollments foreign key already exists';
    WHEN OTHERS THEN
        RAISE NOTICE 'Error fixing student_enrollments foreign key: %', SQLERRM;
END $$;

-- Fix mentors table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mentors' AND table_schema = 'public') THEN
        ALTER TABLE public.mentors 
        ADD CONSTRAINT mentors_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
        RAISE NOTICE 'Fixed mentors.user_id foreign key';
    END IF;
EXCEPTION
    WHEN duplicate_object THEN
        RAISE NOTICE 'mentors foreign key already exists';
    WHEN OTHERS THEN
        RAISE NOTICE 'Error fixing mentors foreign key: %', SQLERRM;
END $$;

-- Fix messages table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'messages' AND table_schema = 'public') THEN
        ALTER TABLE public.messages 
        ADD CONSTRAINT messages_sender_id_fkey 
        FOREIGN KEY (sender_id) REFERENCES auth.users(id) ON DELETE CASCADE;
        
        ALTER TABLE public.messages 
        ADD CONSTRAINT messages_recipient_id_fkey 
        FOREIGN KEY (recipient_id) REFERENCES auth.users(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Fixed messages foreign keys';
    END IF;
EXCEPTION
    WHEN duplicate_object THEN
        RAISE NOTICE 'messages foreign keys already exist';
    WHEN OTHERS THEN
        RAISE NOTICE 'Error fixing messages foreign keys: %', SQLERRM;
END $$;

-- Fix resources table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'resources' AND table_schema = 'public') THEN
        ALTER TABLE public.resources 
        ADD CONSTRAINT resources_created_by_fkey 
        FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE SET NULL;
        RAISE NOTICE 'Fixed resources.created_by foreign key';
    END IF;
EXCEPTION
    WHEN duplicate_object THEN
        RAISE NOTICE 'resources foreign key already exists';
    WHEN OTHERS THEN
        RAISE NOTICE 'Error fixing resources foreign key: %', SQLERRM;
END $$;

-- Fix orders table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'orders' AND table_schema = 'public') THEN
        ALTER TABLE public.orders 
        ADD CONSTRAINT orders_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
        RAISE NOTICE 'Fixed orders.user_id foreign key';
    END IF;
EXCEPTION
    WHEN duplicate_object THEN
        RAISE NOTICE 'orders foreign key already exists';
    WHEN OTHERS THEN
        RAISE NOTICE 'Error fixing orders foreign key: %', SQLERRM;
END $$;

-- Fix subscriptions table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'subscriptions' AND table_schema = 'public') THEN
        ALTER TABLE public.subscriptions 
        ADD CONSTRAINT subscriptions_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
        RAISE NOTICE 'Fixed subscriptions.user_id foreign key';
    END IF;
EXCEPTION
    WHEN duplicate_object THEN
        RAISE NOTICE 'subscriptions foreign key already exists';
    WHEN OTHERS THEN
        RAISE NOTICE 'Error fixing subscriptions foreign key: %', SQLERRM;
END $$;

-- ============================================================================
-- STEP 4: UPDATE RLS POLICIES FOR ADMIN USER DELETION
-- ============================================================================

-- Update RLS policies to allow admins to delete users
DROP POLICY IF EXISTS "Admins can delete users" ON public.users;
CREATE POLICY "Admins can delete users"
  ON public.users
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM public.users admin_user
      WHERE admin_user.id = auth.uid() 
      AND admin_user.role IN ('admin')
    )
  );

-- ============================================================================
-- STEP 5: CREATE HELPER FUNCTION FOR SAFE USER DELETION
-- ============================================================================

-- Create a function to safely delete users with all related data
CREATE OR REPLACE FUNCTION public.delete_user_safely(user_id_to_delete UUID)
RETURNS BOOLEAN AS $$
DECLARE
    current_user_role TEXT;
BEGIN
    -- Check if current user is admin
    SELECT role INTO current_user_role 
    FROM public.users 
    WHERE id = auth.uid();
    
    IF current_user_role NOT IN ('admin') THEN
        RAISE EXCEPTION 'Only admin users can delete other users';
    END IF;
    
    -- Delete from auth.users (this will cascade to all related tables)
    DELETE FROM auth.users WHERE id = user_id_to_delete;
    
    -- Return success
    RETURN TRUE;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Failed to delete user: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.delete_user_safely(UUID) TO authenticated;

-- ============================================================================
-- STEP 6: FINAL VERIFICATION
-- ============================================================================

-- Final success message
DO $$
BEGIN
    RAISE NOTICE '==========================================';
    RAISE NOTICE '✅ User deletion constraints have been fixed!';
    RAISE NOTICE '✅ Orphaned data has been cleaned up';
    RAISE NOTICE '✅ All foreign keys now have CASCADE DELETE or SET NULL';
    RAISE NOTICE '✅ Admin users can now delete users from the dashboard';
    RAISE NOTICE '✅ Use delete_user_safely() function for programmatic deletion';
    RAISE NOTICE '==========================================';
END $$;
