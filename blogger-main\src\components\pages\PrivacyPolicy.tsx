
import { Navbar } from '@/components/layout/Navbar';
import { Link } from 'react-router-dom';
import { ArrowLeft, Shield, Eye, Lock, Database, Users } from 'lucide-react';

export default function PrivacyPolicy() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="mb-8">
          <Link 
            to="/" 
            className="inline-flex items-center text-blue-600 hover:text-blue-700 mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Link>
          
          <div className="flex items-center gap-3 mb-4">
            <Shield className="h-8 w-8 text-blue-600" />
            <h1 className="text-4xl font-bold text-gray-900">Privacy Policy</h1>
          </div>
          
          <p className="text-lg text-gray-600">
            Last updated: {new Date().toLocaleDateString()}
          </p>
        </div>

        {/* Content */}
        <div className="bg-white rounded-lg shadow-sm p-8 space-y-8">
          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">1. Information We Collect</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              We collect information you provide directly to us, such as when you create an account, subscribe to our newsletter, or contact us for support.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <Users className="h-5 w-5 text-blue-600 mr-2" />
                  <h4 className="font-medium text-blue-800">Personal Information</h4>
                </div>
                <ul className="text-blue-700 text-sm space-y-1">
                  <li>• Name and email address</li>
                  <li>• Profile information</li>
                  <li>• Payment information</li>
                  <li>• Communication preferences</li>
                </ul>
              </div>
              
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <Eye className="h-5 w-5 text-green-600 mr-2" />
                  <h4 className="font-medium text-green-800">Usage Information</h4>
                </div>
                <ul className="text-green-700 text-sm space-y-1">
                  <li>• Pages visited and time spent</li>
                  <li>• Articles read and interactions</li>
                  <li>• Device and browser information</li>
                  <li>• IP address and location data</li>
                </ul>
              </div>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">2. How We Use Your Information</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you.
            </p>
            <ul className="list-disc list-inside text-gray-700 space-y-2 ml-4">
              <li>Provide and deliver the services you request</li>
              <li>Process transactions and send related information</li>
              <li>Send you technical notices and support messages</li>
              <li>Communicate about products, services, and events</li>
              <li>Monitor and analyze trends and usage</li>
              <li>Personalize and improve the user experience</li>
            </ul>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">3. Information Sharing</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.
            </p>
            
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-800 mb-2">We may share information in these situations:</h4>
              <ul className="text-yellow-700 text-sm space-y-1">
                <li>• With service providers who assist in our operations</li>
                <li>• To comply with legal obligations</li>
                <li>• To protect our rights and safety</li>
                <li>• In connection with a business transfer</li>
              </ul>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">4. Data Security</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.
            </p>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <Lock className="h-5 w-5 text-gray-600 mr-2" />
                <h4 className="font-medium text-gray-800">Security Measures</h4>
              </div>
              <ul className="text-gray-700 text-sm space-y-1">
                <li>• SSL encryption for data transmission</li>
                <li>• Secure data storage and access controls</li>
                <li>• Regular security audits and updates</li>
                <li>• Employee training on data protection</li>
              </ul>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">5. Your Rights and Choices</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              You have certain rights regarding your personal information, including the right to access, update, or delete your data.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-800 mb-2">Access & Update</h4>
                <p className="text-gray-600 text-sm">
                  You can access and update your account information through your profile settings.
                </p>
              </div>
              
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-800 mb-2">Data Deletion</h4>
                <p className="text-gray-600 text-sm">
                  You can request deletion of your account and associated data by contacting us.
                </p>
              </div>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">6. Cookies and Tracking</h2>
            <p className="text-gray-700 leading-relaxed">
              We use cookies and similar tracking technologies to collect and use personal information about you. For more information about our use of cookies, please see our Cookie Policy.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">7. Third-Party Services</h2>
            <p className="text-gray-700 leading-relaxed">
              Our service may contain links to third-party websites or services. We are not responsible for the privacy practices of these third parties and encourage you to read their privacy policies.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">8. Children's Privacy</h2>
            <p className="text-gray-700 leading-relaxed">
              Our service is not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">9. Changes to This Policy</h2>
            <p className="text-gray-700 leading-relaxed">
              We may update this privacy policy from time to time. We will notify you of any changes by posting the new policy on this page and updating the "Last updated" date.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">10. Contact Us</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              If you have any questions about this Privacy Policy, please contact us:
            </p>
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-gray-700">
                <strong>Email:</strong> <EMAIL><br />
                <strong>Address:</strong> 123 News Street, Media City, MC 12345<br />
                <strong>Phone:</strong> (*************
              </p>
            </div>
          </section>
        </div>

        {/* Related Links */}
        <div className="mt-12 bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Documents</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link 
              to="/terms-of-service" 
              className="flex items-center p-3 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors"
            >
              <Database className="h-5 w-5 text-blue-600 mr-3" />
              <span className="text-gray-700">Terms of Service</span>
            </Link>
            <Link 
              to="/cookie-policy" 
              className="flex items-center p-3 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors"
            >
              <Eye className="h-5 w-5 text-blue-600 mr-3" />
              <span className="text-gray-700">Cookie Policy</span>
            </Link>
            <Link 
              to="/contact" 
              className="flex items-center p-3 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors"
            >
              <Users className="h-5 w-5 text-blue-600 mr-3" />
              <span className="text-gray-700">Contact Us</span>
            </Link>
          </div>
        </div>
      </main>
    </div>
  );
}
