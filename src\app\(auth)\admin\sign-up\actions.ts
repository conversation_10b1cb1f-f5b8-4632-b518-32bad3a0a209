"use server";

import { encodedRedirect } from "@/utils/utils";
import { createClient } from "@/utils/supabase/server";
import { headers } from "next/headers";

// Admin access codes - in production, these should be environment variables
const ADMIN_ACCESS_CODES = {
  MAIN_ADMIN: process.env.MAIN_ADMIN_ACCESS_CODE || "TENNIS_MAIN_ADMIN_2024",
  SENIOR_ADMIN: process.env.SENIOR_ADMIN_ACCESS_CODE || "TENNIS_SENIOR_ADMIN_2024",
  JUNIOR_ADMIN: process.env.JUNIOR_ADMIN_ACCESS_CODE || "TENNIS_JUNIOR_ADMIN_2024"
};

// Legacy support for existing admin code
const ADMIN_ACCESS_CODE = process.env.ADMIN_ACCESS_CODE || "TENNIS_ADMIN_2024";

export const adminSignUpAction = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const password = formData.get("password")?.toString();
  const fullName = formData.get("full_name")?.toString() || '';
  const adminCode = formData.get("admin_code")?.toString();
  const supabase = await createClient();
  const origin = headers().get("origin");

  if (!email || !password) {
    return encodedRedirect(
      "error",
      "/admin/sign-up",
      "Email and password are required",
    );
  }

  if (!fullName) {
    return encodedRedirect(
      "error",
      "/admin/sign-up",
      "Full name is required",
    );
  }

  if (!adminCode) {
    return encodedRedirect(
      "error",
      "/admin/sign-up",
      "Admin access code is required",
    );
  }

  // Verify admin access code and determine admin role
  let adminRole = 'admin'; // default

  if (adminCode === ADMIN_ACCESS_CODES.MAIN_ADMIN) {
    adminRole = 'main_admin';
  } else if (adminCode === ADMIN_ACCESS_CODES.SENIOR_ADMIN) {
    adminRole = 'senior_admin';
  } else if (adminCode === ADMIN_ACCESS_CODES.JUNIOR_ADMIN) {
    adminRole = 'junior_admin';
  } else if (adminCode === ADMIN_ACCESS_CODE) {
    adminRole = 'admin'; // legacy support
  } else {
    return encodedRedirect(
      "error",
      "/admin/sign-up",
      "Invalid admin access code",
    );
  }

  // Check if email domain is allowed for admin (optional security measure)
  const allowedDomains = process.env.ADMIN_EMAIL_DOMAINS?.split(',') || [];
  if (allowedDomains.length > 0) {
    const emailDomain = email.split('@')[1];
    if (!allowedDomains.includes(emailDomain)) {
      return encodedRedirect(
        "error",
        "/admin/sign-up",
        "Email domain not authorized for admin access",
      );
    }
  }

  console.log("Attempting admin sign-up with data:", {
    email,
    fullName,
    role: 'admin'
  });

  // Simple check - let Supabase handle duplicate email errors naturally
  console.log("Attempting to create admin user with email:", email);

  const { data: { user }, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${origin}/auth/callback?redirect_to=/admin`,
      data: {
        full_name: fullName,
        email: email,
        role: 'admin', // Set role as admin
        admin_role: adminRole, // Set admin_role based on access code used
      }
    },
  });

  if (error) {
    console.error("Admin sign up error:", error);
    console.error("Error details:", {
      code: error.code,
      message: error.message,
      status: error.status,
      name: error.name
    });

    // Provide more specific error messages
    let errorMessage = "Sign-up failed";
    if (error.message.includes("already registered")) {
      errorMessage = "This email is already registered. Please use a different email or try signing in.";
    } else if (error.message.includes("Database error")) {
      errorMessage = "Database error during sign-up. Please try again.";
    } else if (error.message.includes("Invalid")) {
      errorMessage = "Invalid sign-up data. Please check your information.";
    } else {
      errorMessage = `Sign-up failed: ${error.message}`;
    }

    return encodedRedirect("error", "/admin/sign-up", errorMessage);
  }

  if (user) {
    console.log("User created in auth.users:", user.id);
    console.log("User metadata:", user.user_metadata);

    // The trigger will handle creating the user profile automatically
    // Just log success and redirect
    console.log("Admin account created successfully. Trigger will create profile.");
  }

  return encodedRedirect(
    "success",
    "/admin/sign-up",
    "Thanks for signing up! you can access the admin dashboard when you sign in.",
  );
};
