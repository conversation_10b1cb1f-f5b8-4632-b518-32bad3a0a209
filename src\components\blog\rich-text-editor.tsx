"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { ProductSelector } from '@/components/blog/product-selector';
import {
  Bold,
  Italic,
  Underline,
  Link,
  Image,
  List,
  ListOrdered,
  Quote,
  Code,
  Heading1,
  Heading2,
  Heading3,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Upload,
  X,
  Plus,
  Eye,
  Save,
  Send
} from 'lucide-react';

interface ArticleData {
  title: string;
  excerpt: string;
  content: string;
  tags: string[];
  category: string;
  isFeatured: boolean;
  featuredImage?: string;
  seoTitle?: string;
  seoDescription?: string;
  productReferences?: Array<{
    product: {
      id: string;
      name: string;
      price: number;
      image_url: string;
      slug: string;
    };
    reference_type: 'featured' | 'mentioned' | 'related';
    position: number;
  }>;
}

interface RichTextEditorProps {
  initialContent?: string;
  initialTitle?: string;
  initialExcerpt?: string;
  initialTags?: string[];
  initialCategory?: string;
  initialIsFeatured?: boolean;
  onSave?: (data: ArticleData) => void;
  onPublish?: (data: ArticleData) => void;
  categories?: Array<{ id: string; name: string; }>;
  isLoading?: boolean;
}

export function RichTextEditor({
  initialContent = '',
  initialTitle = '',
  initialExcerpt = '',
  initialTags = [],
  initialCategory = '',
  initialIsFeatured = false,
  onSave,
  onPublish,
  categories = [],
  isLoading = false
}: RichTextEditorProps) {
  const [title, setTitle] = useState(initialTitle);
  const [excerpt, setExcerpt] = useState(initialExcerpt);
  const [content, setContent] = useState(initialContent);
  const [tags, setTags] = useState<string[]>(initialTags);
  const [newTag, setNewTag] = useState('');
  const [category, setCategory] = useState(initialCategory);
  const [isFeatured, setIsFeatured] = useState(initialIsFeatured);
  const [featuredImage, setFeaturedImage] = useState<string>('');
  const [seoTitle, setSeoTitle] = useState('');
  const [seoDescription, setSeoDescription] = useState('');
  const [showSeoFields, setShowSeoFields] = useState(false);
  const [productReferences, setProductReferences] = useState<any[]>([]);

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleSave = () => {
    const articleData: ArticleData = {
      title,
      excerpt,
      content,
      tags,
      category,
      isFeatured,
      featuredImage,
      seoTitle,
      seoDescription,
      productReferences,
    };
    onSave?.(articleData);
  };

  const handlePublish = () => {
    const articleData: ArticleData = {
      title,
      excerpt,
      content,
      tags,
      category,
      isFeatured,
      featuredImage,
      seoTitle,
      seoDescription,
      productReferences,
    };
    onPublish?.(articleData);
  };

  return (
    <div className="space-y-6">
      {/* Article Header */}
      <Card className="glass-effect border border-white/10 neo-shadow">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-foreground">Article Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title" className="text-sm font-medium text-foreground">Title</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter article title..."
              className="glass-effect border-white/20 text-foreground placeholder:text-muted-foreground"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="excerpt" className="text-sm font-medium text-foreground">Excerpt</Label>
            <Textarea
              id="excerpt"
              value={excerpt}
              onChange={(e) => setExcerpt(e.target.value)}
              placeholder="Brief summary of the article..."
              className="glass-effect border-white/20 text-foreground placeholder:text-muted-foreground min-h-[80px]"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category" className="text-sm font-medium text-foreground">Category</Label>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger className="glass-effect border-white/20 text-foreground">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent className="glass-effect border-white/20">
                  {categories.map((cat) => (
                    <SelectItem key={cat.id} value={cat.id}>
                      {cat.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="featured-image" className="text-sm font-medium text-foreground">Featured Image URL</Label>
              <Input
                id="featured-image"
                value={featuredImage}
                onChange={(e) => setFeaturedImage(e.target.value)}
                placeholder="https://example.com/image.jpg"
                className="glass-effect border-white/20 text-foreground placeholder:text-muted-foreground"
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="featured"
              checked={isFeatured}
              onCheckedChange={setIsFeatured}
            />
            <Label htmlFor="featured" className="text-sm font-medium text-foreground">Featured Article</Label>
          </div>
        </CardContent>
      </Card>

      {/* Content Editor */}
      <Card className="glass-effect border border-white/10 neo-shadow">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-foreground">Content</CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Write your article content here..."
            className="glass-effect border-white/20 text-foreground placeholder:text-muted-foreground min-h-[400px]"
          />
        </CardContent>
      </Card>

      {/* Tags */}
      <Card className="glass-effect border border-white/10 neo-shadow">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-foreground">Tags</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              placeholder="Add a tag..."
              className="glass-effect border-white/20 text-foreground placeholder:text-muted-foreground"
              onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
            />
            <Button
              onClick={handleAddTag}
              variant="outline"
              size="icon"
              className="glass-effect border-white/20 hover:glass-effect-subtle min-h-[44px] min-w-[44px]"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {tags.map((tag) => (
              <Badge
                key={tag}
                variant="secondary"
                className="glass-effect border border-white/20 text-foreground"
              >
                {tag}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveTag(tag)}
                  className="ml-1 h-4 w-4 p-0 hover:bg-destructive/20"
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Product References */}
      <Card className="glass-effect border border-white/10 neo-shadow">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-foreground">Product Integration</CardTitle>
          <CardDescription>Reference Tennis Whisperer products in your article</CardDescription>
        </CardHeader>
        <CardContent>
          <ProductSelector
            onReferencesChange={setProductReferences}
            className="w-full"
          />
        </CardContent>
      </Card>

      {/* SEO Settings */}
      <Card className="glass-effect border border-white/10 neo-shadow">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl font-semibold text-foreground">SEO Settings</CardTitle>
            <Button
              variant="ghost"
              onClick={() => setShowSeoFields(!showSeoFields)}
              className="text-muted-foreground hover:text-foreground"
            >
              {showSeoFields ? 'Hide' : 'Show'} SEO Fields
            </Button>
          </div>
        </CardHeader>
        {showSeoFields && (
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="seo-title" className="text-sm font-medium text-foreground">SEO Title</Label>
              <Input
                id="seo-title"
                value={seoTitle}
                onChange={(e) => setSeoTitle(e.target.value)}
                placeholder="SEO optimized title..."
                className="glass-effect border-white/20 text-foreground placeholder:text-muted-foreground"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="seo-description" className="text-sm font-medium text-foreground">SEO Description</Label>
              <Textarea
                id="seo-description"
                value={seoDescription}
                onChange={(e) => setSeoDescription(e.target.value)}
                placeholder="SEO meta description..."
                className="glass-effect border-white/20 text-foreground placeholder:text-muted-foreground min-h-[80px]"
              />
            </div>
          </CardContent>
        )}
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-end gap-4">
        <Button
          onClick={handleSave}
          variant="outline"
          disabled={isLoading}
          className="glass-effect border-white/20 hover:glass-effect-subtle text-foreground min-h-[44px] px-6"
        >
          <Save className="h-4 w-4 mr-2" />
          Save Draft
        </Button>
        <Button
          onClick={handlePublish}
          disabled={isLoading || !title || !content || !excerpt}
          className="bg-primary hover:bg-primary/90 text-primary-foreground min-h-[44px] px-6"
        >
          <Send className="h-4 w-4 mr-2" />
          Publish
        </Button>
      </div>
    </div>
  );
}
