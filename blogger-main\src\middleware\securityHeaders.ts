// Security Headers Middleware
// Implements comprehensive security headers for enterprise-grade protection

export interface SecurityHeadersConfig {
  contentSecurityPolicy?: {
    directives?: Record<string, string[]>;
    reportOnly?: boolean;
    reportUri?: string;
  };
  hsts?: {
    maxAge?: number;
    includeSubDomains?: boolean;
    preload?: boolean;
  };
  frameOptions?: 'DENY' | 'SAMEORIGIN' | string;
  contentTypeOptions?: boolean;
  referrerPolicy?: string;
  permissionsPolicy?: Record<string, string[]>;
  crossOriginEmbedderPolicy?: 'require-corp' | 'unsafe-none';
  crossOriginOpenerPolicy?: 'same-origin' | 'same-origin-allow-popups' | 'unsafe-none';
  crossOriginResourcePolicy?: 'same-site' | 'same-origin' | 'cross-origin';
}

const defaultConfig: SecurityHeadersConfig = {
  contentSecurityPolicy: {
    directives: {
      'default-src': ["'self'"],
      'script-src': [
        "'self'",
        "'unsafe-inline'", // Required for React
        "'unsafe-eval'", // Required for development
        'https://www.googletagmanager.com',
        'https://www.google-analytics.com',
        'https://js.yoco.com',
        'https://checkout.yoco.com',
        'https://online.yoco.com',
      ],
      'style-src': [
        "'self'",
        "'unsafe-inline'", // Required for styled-components and CSS-in-JS
        'https://fonts.googleapis.com',
      ],
      'img-src': [
        "'self'",
        'data:',
        'blob:',
        'https:',
        'https://images.unsplash.com',
        'https://via.placeholder.com',
        'https://www.google-analytics.com',
      ],
      'font-src': [
        "'self'",
        'https://fonts.gstatic.com',
      ],
      'connect-src': [
        "'self'",
        'https://online.yoco.com',
        'https://api.yoco.com',
        'https://www.google-analytics.com',
        'https://analytics.google.com',
        'wss://realtime.supabase.co',
        'https://*.supabase.co',
      ],
      'frame-src': [
        "'self'",
        'https://js.yoco.com',
        'https://checkout.yoco.com',
      ],
      'worker-src': [
        "'self'",
        'blob:',
      ],
      'manifest-src': ["'self'"],
      'media-src': ["'self'", 'https:'],
      'object-src': ["'none'"],
      'base-uri': ["'self'"],
      'form-action': ["'self'"],
      'frame-ancestors': ["'none'"],
      'upgrade-insecure-requests': [],
    },
    reportOnly: false,
  },
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true,
  },
  frameOptions: 'DENY',
  contentTypeOptions: true,
  referrerPolicy: 'strict-origin-when-cross-origin',
  permissionsPolicy: {
    'accelerometer': ["'none'"],
    'ambient-light-sensor': ["'none'"],
    'autoplay': ["'self'"],
    'battery': ["'none'"],
    'camera': ["'none'"],
    'cross-origin-isolated': ["'none'"],
    'display-capture': ["'none'"],
    'document-domain': ["'none'"],
    'encrypted-media': ["'none'"],
    'execution-while-not-rendered': ["'none'"],
    'execution-while-out-of-viewport': ["'none'"],
    'fullscreen': ["'self'"],
    'geolocation': ["'none'"],
    'gyroscope': ["'none'"],
    'keyboard-map': ["'none'"],
    'magnetometer': ["'none'"],
    'microphone': ["'none'"],
    'midi': ["'none'"],
    'navigation-override': ["'none'"],
    'payment': ["'self'"],
    'picture-in-picture': ["'none'"],
    'publickey-credentials-get': ["'self'"],
    'screen-wake-lock': ["'none'"],
    'sync-xhr': ["'none'"],
    'usb': ["'none'"],
    'web-share': ["'self'"],
    'xr-spatial-tracking': ["'none'"],
  },
  crossOriginEmbedderPolicy: 'unsafe-none',
  crossOriginOpenerPolicy: 'same-origin-allow-popups',
  crossOriginResourcePolicy: 'cross-origin',
};

export class SecurityHeaders {
  private config: SecurityHeadersConfig;

  constructor(config: SecurityHeadersConfig = {}) {
    this.config = { ...defaultConfig, ...config };
  }

  // Generate Content Security Policy header value
  private generateCSP(): string {
    if (!this.config.contentSecurityPolicy?.directives) return '';

    const directives = Object.entries(this.config.contentSecurityPolicy.directives)
      .map(([directive, sources]) => {
        if (sources.length === 0) return directive;
        return `${directive} ${sources.join(' ')}`;
      })
      .join('; ');

    return directives;
  }

  // Generate Permissions Policy header value
  private generatePermissionsPolicy(): string {
    if (!this.config.permissionsPolicy) return '';

    return Object.entries(this.config.permissionsPolicy)
      .map(([directive, allowlist]) => {
        if (allowlist.length === 0) return `${directive}=()`;
        return `${directive}=(${allowlist.join(' ')})`;
      })
      .join(', ');
  }

  // Generate HSTS header value
  private generateHSTS(): string {
    if (!this.config.hsts) return '';

    let value = `max-age=${this.config.hsts.maxAge || 31536000}`;
    
    if (this.config.hsts.includeSubDomains) {
      value += '; includeSubDomains';
    }
    
    if (this.config.hsts.preload) {
      value += '; preload';
    }

    return value;
  }

  // Main middleware function
  middleware = (req: any, res: any, next: any) => {
    // Content Security Policy
    const csp = this.generateCSP();
    if (csp) {
      const headerName = this.config.contentSecurityPolicy?.reportOnly 
        ? 'Content-Security-Policy-Report-Only' 
        : 'Content-Security-Policy';
      res.setHeader(headerName, csp);
    }

    // HTTP Strict Transport Security
    const hsts = this.generateHSTS();
    if (hsts && req.secure) {
      res.setHeader('Strict-Transport-Security', hsts);
    }

    // X-Frame-Options
    if (this.config.frameOptions) {
      res.setHeader('X-Frame-Options', this.config.frameOptions);
    }

    // X-Content-Type-Options
    if (this.config.contentTypeOptions) {
      res.setHeader('X-Content-Type-Options', 'nosniff');
    }

    // Referrer Policy
    if (this.config.referrerPolicy) {
      res.setHeader('Referrer-Policy', this.config.referrerPolicy);
    }

    // Permissions Policy
    const permissionsPolicy = this.generatePermissionsPolicy();
    if (permissionsPolicy) {
      res.setHeader('Permissions-Policy', permissionsPolicy);
    }

    // Cross-Origin Embedder Policy
    if (this.config.crossOriginEmbedderPolicy) {
      res.setHeader('Cross-Origin-Embedder-Policy', this.config.crossOriginEmbedderPolicy);
    }

    // Cross-Origin Opener Policy
    if (this.config.crossOriginOpenerPolicy) {
      res.setHeader('Cross-Origin-Opener-Policy', this.config.crossOriginOpenerPolicy);
    }

    // Cross-Origin Resource Policy
    if (this.config.crossOriginResourcePolicy) {
      res.setHeader('Cross-Origin-Resource-Policy', this.config.crossOriginResourcePolicy);
    }

    // Additional security headers
    res.setHeader('X-DNS-Prefetch-Control', 'off');
    res.setHeader('X-Download-Options', 'noopen');
    res.setHeader('X-Permitted-Cross-Domain-Policies', 'none');
    res.setHeader('X-XSS-Protection', '0'); // Disabled as CSP is more effective

    // Remove server information
    res.removeHeader('X-Powered-By');
    res.removeHeader('Server');

    next();
  };

  // Update CSP for development environment
  enableDevelopmentMode() {
    if (this.config.contentSecurityPolicy?.directives) {
      // Allow unsafe-eval and unsafe-inline for development
      this.config.contentSecurityPolicy.directives['script-src'] = [
        "'self'",
        "'unsafe-inline'",
        "'unsafe-eval'",
        'localhost:*',
        '127.0.0.1:*',
        'ws://localhost:*',
        'ws://127.0.0.1:*',
        ...this.config.contentSecurityPolicy.directives['script-src'].filter(
          src => !src.includes('localhost') && !src.includes('127.0.0.1')
        ),
      ];

      this.config.contentSecurityPolicy.directives['connect-src'] = [
        "'self'",
        'localhost:*',
        '127.0.0.1:*',
        'ws://localhost:*',
        'ws://127.0.0.1:*',
        'wss://localhost:*',
        'wss://127.0.0.1:*',
        ...this.config.contentSecurityPolicy.directives['connect-src'].filter(
          src => !src.includes('localhost') && !src.includes('127.0.0.1')
        ),
      ];
    }
  }

  // Add nonce support for inline scripts
  addNonce(req: any, res: any, next: any) {
    const nonce = Buffer.from(Math.random().toString()).toString('base64');
    res.locals.nonce = nonce;
    
    // Update CSP with nonce
    if (this.config.contentSecurityPolicy?.directives?.['script-src']) {
      const scriptSrc = this.config.contentSecurityPolicy.directives['script-src'];
      if (!scriptSrc.includes(`'nonce-${nonce}'`)) {
        scriptSrc.push(`'nonce-${nonce}'`);
      }
    }

    next();
  }
}

// Predefined security configurations
export const securityConfigs = {
  // Strict security for production
  strict: new SecurityHeaders({
    contentSecurityPolicy: {
      directives: {
        'default-src': ["'none'"],
        'script-src': ["'self'"],
        'style-src': ["'self'"],
        'img-src': ["'self'", 'data:', 'https:'],
        'font-src': ["'self'"],
        'connect-src': ["'self'"],
        'frame-src': ["'none'"],
        'object-src': ["'none'"],
        'base-uri': ["'self'"],
        'form-action': ["'self'"],
        'frame-ancestors': ["'none'"],
        'upgrade-insecure-requests': [],
      },
    },
    frameOptions: 'DENY',
    crossOriginEmbedderPolicy: 'require-corp',
  }),

  // Balanced security for most applications
  balanced: new SecurityHeaders(defaultConfig),

  // Relaxed security for development
  development: new SecurityHeaders({
    ...defaultConfig,
    contentSecurityPolicy: {
      ...defaultConfig.contentSecurityPolicy,
      reportOnly: true,
    },
  }),
};

// Initialize development mode if needed
if (process.env.NODE_ENV === 'development') {
  securityConfigs.development.enableDevelopmentMode();
  securityConfigs.balanced.enableDevelopmentMode();
}

export default SecurityHeaders;
