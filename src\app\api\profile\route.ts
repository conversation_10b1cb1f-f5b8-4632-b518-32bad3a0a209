import { NextResponse } from 'next/server';
import { createClient } from '../../../utils/supabase/server';

export async function GET(request: Request) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { data: profile, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (error) {
      // If profile doesn't exist, return null instead of error
      if (error.code === 'PGRST116') {
        return NextResponse.json(null);
      }
      console.error('Error fetching profile:', error);
      return NextResponse.json(
        { error: 'Failed to fetch profile' },
        { status: 500 }
      );
    }

    return NextResponse.json(profile);
  } catch (error: any) {
    console.error('Error in profile GET API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { avatar_url, phone_number, address, city, state, postal_code, country, date_of_birth, bio, preferences } = body;

    // Handle empty date_of_birth - convert empty string to null
    const processedDateOfBirth = date_of_birth && date_of_birth.trim() !== '' ? date_of_birth : null;

    // Process other fields to handle empty strings
    const processedPhoneNumber = phone_number && phone_number.trim() !== '' ? phone_number : null;
    const processedAddress = address && address.trim() !== '' ? address : null;
    const processedCity = city && city.trim() !== '' ? city : null;
    const processedState = state && state.trim() !== '' ? state : null;
    const processedPostalCode = postal_code && postal_code.trim() !== '' ? postal_code : null;
    const processedCountry = country && country.trim() !== '' ? country : null;
    const processedBio = bio && bio.trim() !== '' ? bio : null;

    // Check if profile already exists
    const { data: existingProfile } = await supabase
      .from('user_profiles')
      .select('id')
      .eq('user_id', user.id)
      .single();

    let profile;
    if (existingProfile) {
      // Update existing profile
      const updateData: any = {};
      if (avatar_url !== undefined) updateData.avatar_url = avatar_url;
      if (phone_number !== undefined) updateData.phone_number = processedPhoneNumber;
      if (address !== undefined) updateData.address = processedAddress;
      if (city !== undefined) updateData.city = processedCity;
      if (state !== undefined) updateData.state = processedState;
      if (postal_code !== undefined) updateData.postal_code = processedPostalCode;
      if (country !== undefined) updateData.country = processedCountry;
      if (date_of_birth !== undefined) updateData.date_of_birth = processedDateOfBirth;
      if (bio !== undefined) updateData.bio = processedBio;
      if (preferences !== undefined) updateData.preferences = preferences;
      
      updateData.updated_at = new Date().toISOString();

      const { data, error } = await supabase
        .from('user_profiles')
        .update(updateData)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating profile:', error);
        return NextResponse.json(
          { error: 'Failed to update profile' },
          { status: 500 }
        );
      }
      profile = data;
    } else {
      // Create new profile
      const { data, error } = await supabase
        .from('user_profiles')
        .insert({
          user_id: user.id,
          avatar_url,
          phone_number: processedPhoneNumber,
          address: processedAddress,
          city: processedCity,
          state: processedState,
          postal_code: processedPostalCode,
          country: processedCountry,
          date_of_birth: processedDateOfBirth,
          bio: processedBio,
          preferences
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating profile:', error);
        return NextResponse.json(
          { error: 'Failed to create profile' },
          { status: 500 }
        );
      }
      profile = data;
    }

    return NextResponse.json(profile);
  } catch (error: any) {
    console.error('Error in profile POST API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
