"use client";

import { useEffect, useState } from "react";
import { Package, Eye, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { useToast } from "@/hooks/use-toast";
import { formatCurrency } from "@/lib/format";
import { createClient } from "@/utils/supabase/client";
import { getUserOrdersClient } from "@/utils/supabase/orders-client";
import { Order } from "@/utils/supabase/orders";

export function UserOrders() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        
        // Get the current user
        const supabase = createClient();
        const { data: userData } = await supabase.auth.getUser();
        
        if (!userData?.user) {
          throw new Error('You must be logged in to view orders');
        }
        
        // Fetch orders from Supabase
        const ordersData = await getUserOrdersClient(userData.user.id);
        setOrders(ordersData);
      } catch (err: any) {
        console.error('Error fetching orders:', err);
        setError(err.message || 'Failed to load orders');
        toast({
          title: "Error",
          description: err.message || 'Failed to load orders',
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchOrders();
  }, [toast]);

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Loading your orders...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-destructive/10 mb-4">
          <Package className="h-8 w-8 text-destructive" />
        </div>
        <h3 className="text-lg font-medium mb-2">Error loading orders</h3>
        <p className="text-muted-foreground mb-6">{error}</p>
        <Button onClick={() => window.location.reload()}>Try Again</Button>
      </div>
    );
  }

  if (orders.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
          <Package className="h-8 w-8 text-muted-foreground" />
        </div>
        <h3 className="text-lg font-medium mb-2">No orders yet</h3>
        <p className="text-muted-foreground mb-6">You haven't placed any orders yet.</p>
        <Button asChild>
          <Link href="/shop">Start Shopping</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {orders.map((order) => {
        const orderDate = new Date(order.created_at || Date.now()).toLocaleDateString("en-ZA", {
          year: "numeric",
          month: "long",
          day: "numeric",
        });

        return (
          <div key={order.id} className="border border-border rounded-lg overflow-hidden">
            <div className="bg-muted/30 p-4 flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div>
                <div className="flex items-center gap-2">
                  <h3 className="font-medium">Order #{order.id.substring(0, 8)}</h3>
                  <Badge variant={order.status === "delivered" ? "outline" : "default"} className="capitalize">
                    {order.status}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">{orderDate}</p>
              </div>
              <div className="flex items-center gap-4">
                <p className="font-medium">
                  {formatCurrency(order.total_amount)}
                </p>
                <Button variant="outline" size="sm" asChild>
                  <Link href={`/account/orders/${order.id}`}>
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </Link>
                </Button>
              </div>
            </div>
            <div className="p-4 border-t border-border">
              <h4 className="text-sm font-medium mb-2">Items</h4>
              <ul className="space-y-1">
                {order.items.map((item, index) => (
                  <li key={index} className="text-sm text-muted-foreground">
                    {item.quantity} x {item.name}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        );
      })}
    </div>
  );
}
