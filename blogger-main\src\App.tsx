import { Suspense } from "react";
import { Navigate, Route, Routes, useRoutes } from "react-router-dom";
import LoginForm from "./components/auth/LoginForm";
import SignUpForm from "./components/auth/SignUpForm";
import { AuthCallback } from "./components/auth/SocialAuth";
import Dashboard from "./components/pages/dashboard";
import Home from "./components/pages/home";
import SeedPage from "./components/pages/seed";
import { Articles } from "./components/pages/Articles";
import { ArticleView } from "./components/pages/ArticleView";
import { Products } from "./components/pages/Products";
import { ProductView } from "./components/pages/ProductView";
import { CheckoutForm } from "./components/payment/CheckoutForm";
import { SuccessPage } from "./components/payment/SuccessPage";
import { About } from "./components/pages/About";
import { Contact } from "./components/pages/Contact";
import { TestPage } from "./components/pages/TestPage";
import { Cart } from "./components/pages/Cart";
import TermsOfService from "./components/pages/TermsOfService";
import PrivacyPolicy from "./components/pages/PrivacyPolicy";
import CookiePolicy from "./components/pages/CookiePolicy";
import { AuthProvider, useAuth } from "../supabase/auth";
import { CartProvider } from "./contexts/CartContext";
import { AvatarProvider } from "./contexts/AvatarContext";
import { NotificationProvider } from "./contexts/NotificationContext";
import { Toaster } from "./components/ui/toaster";
import { LoadingScreen } from "./components/ui/loading-spinner";
import ErrorBoundary from "./components/ErrorBoundary";
import "./styles/responsive-optimizations.css";

// Tempo routes - fallback to empty array if not available
const routes: any = [];

function PrivateRoute({
  children,
  requireAdmin = false,
}: {
  children: React.ReactNode;
  requireAdmin?: boolean;
}) {
  const { user, loading, isAdmin } = useAuth();

  if (loading) {
    return <LoadingScreen text="Authenticating..." />;
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  if (requireAdmin && !isAdmin) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
}

function AppRoutes() {
  const tempoRoutes = useRoutes(import.meta.env.VITE_TEMPO === "true" ? routes : []);

  return (
    <>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/articles" element={<Articles />} />
        <Route path="/articles/:slug" element={<ArticleView />} />
        <Route path="/products" element={<Products />} />
        <Route path="/products/:slug" element={<ProductView />} />
        <Route path="/checkout" element={<CheckoutForm />} />
        <Route path="/success" element={<SuccessPage />} />
        <Route path="/about" element={<About />} />
        <Route path="/contact" element={<Contact />} />
        <Route path="/test" element={<TestPage />} />
        <Route path="/cart" element={<Cart />} />
        <Route path="/terms-of-service" element={<TermsOfService />} />
        <Route path="/privacy-policy" element={<PrivacyPolicy />} />
        <Route path="/cookie-policy" element={<CookiePolicy />} />
        <Route path="/login" element={<LoginForm />} />
        <Route path="/signup" element={<SignUpForm />} />
        <Route path="/auth/callback" element={<AuthCallback />} />
        <Route path="/seed" element={<SeedPage />} />
        <Route
          path="/dashboard/*"
          element={
            <PrivateRoute>
              <Dashboard />
            </PrivateRoute>
          }
        />
        <Route path="*" element={<Navigate to="/" />} />
      </Routes>
      {tempoRoutes}
    </>
  );
}

function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <AvatarProvider>
          <NotificationProvider>
            <CartProvider>
              <Suspense fallback={<LoadingScreen text="Loading Thabo Bester..." />}>
                <AppRoutes />
              </Suspense>
              <Toaster />
            </CartProvider>
          </NotificationProvider>
        </AvatarProvider>
      </AuthProvider>
    </ErrorBoundary>
  );
}

export default App;
