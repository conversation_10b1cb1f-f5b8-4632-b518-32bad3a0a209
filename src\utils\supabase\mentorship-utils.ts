import supabase from './supabase-client';
import { Tables, InsertTables, UpdateTables } from './database.types';
import { v4 as uuidv4 } from 'uuid';

/**
 * Mentorship program utilities for Tennis-Gear application
 * These functions handle mentorship program operations with Supabase
 */

/**
 * Get all mentorship programs
 * @returns List of mentorship programs or error
 */
export async function getMentorshipPrograms() {
  const { data, error } = await supabase
    .from('mentorship_programs')
    .select('*')
    .order('duration_months', { ascending: true });
  
  return { data, error };
}

/**
 * Get a single mentorship program by ID
 * @param programId Program ID
 * @returns Program data or error
 */
export async function getMentorshipProgram(programId: string) {
  const { data, error } = await supabase
    .from('mentorship_programs')
    .select('*')
    .eq('id', programId)
    .single();
  
  return { data, error };
}

/**
 * Create a new mentorship program (admin function)
 * @param program Program data
 * @returns Created program or error
 */
export async function createMentorshipProgram(program: {
  name: string;
  description: string;
  duration_months: number;
  price_monthly: number;
  price_upfront?: number;
  features?: any;
}) {
  const programData: InsertTables<'mentorship_programs'> = {
    ...program,
    id: uuidv4(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  const { data, error } = await supabase
    .from('mentorship_programs')
    .insert(programData)
    .select()
    .single();
  
  return { data, error };
}

/**
 * Update a mentorship program (admin function)
 * @param programId Program ID
 * @param updates Program data to update
 * @returns Updated program or error
 */
export async function updateMentorshipProgram(programId: string, updates: {
  name?: string;
  description?: string;
  duration_months?: number;
  price_monthly?: number;
  price_upfront?: number;
  features?: any;
}) {
  const updateData: UpdateTables<'mentorship_programs'> = {
    ...updates,
    updated_at: new Date().toISOString()
  };
  
  const { data, error } = await supabase
    .from('mentorship_programs')
    .update(updateData)
    .eq('id', programId)
    .select()
    .single();
  
  return { data, error };
}

/**
 * Delete a mentorship program (admin function)
 * @param programId Program ID
 * @returns Success or error
 */
export async function deleteMentorshipProgram(programId: string) {
  const { error } = await supabase
    .from('mentorship_programs')
    .delete()
    .eq('id', programId);
  
  return { error };
}

/**
 * Get all mentors
 * @returns List of mentors or error
 */
export async function getMentors() {
  const { data, error } = await supabase
    .from('mentors')
    .select(`
      *,
      user:user_id (
        id,
        full_name,
        email,
        avatar_url
      )
    `);
  
  return { data, error };
}

/**
 * Get a single mentor by ID
 * @param mentorId Mentor ID
 * @returns Mentor data or error
 */
export async function getMentor(mentorId: string) {
  const { data, error } = await supabase
    .from('mentors')
    .select(`
      *,
      user:user_id (
        id,
        full_name,
        email,
        avatar_url
      )
    `)
    .eq('id', mentorId)
    .single();
  
  return { data, error };
}

/**
 * Create a mentor profile
 * @param mentorData Mentor data
 * @returns Created mentor profile or error
 */
export async function createMentorProfile(mentorData: {
  user_id: string;
  bio: string;
  specialties: string[];
  experience_years: number;
  availability?: any;
}) {
  const profileData: InsertTables<'mentors'> = {
    ...mentorData,
    id: uuidv4(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  const { data, error } = await supabase
    .from('mentors')
    .insert(profileData)
    .select()
    .single();
  
  return { data, error };
}

/**
 * Update mentor profile
 * @param mentorId Mentor ID
 * @param updates Mentor data to update
 * @returns Updated mentor profile or error
 */
export async function updateMentorProfile(mentorId: string, updates: {
  bio?: string;
  specialties?: string[];
  experience_years?: number;
  availability?: any;
}) {
  const updateData: UpdateTables<'mentors'> = {
    ...updates,
    updated_at: new Date().toISOString()
  };
  
  const { data, error } = await supabase
    .from('mentors')
    .update(updateData)
    .eq('id', mentorId)
    .select()
    .single();
  
  return { data, error };
}

/**
 * Enroll a student in a mentorship program
 * @param enrollment Enrollment data
 * @returns Created enrollment or error
 */
export async function enrollStudent(enrollment: {
  student_id: string;
  program_id: string;
  mentor_id: string;
  start_date: string;
  end_date: string;
  payment_type: 'monthly' | 'upfront';
}) {
  const enrollmentData: InsertTables<'student_enrollments'> = {
    ...enrollment,
    id: uuidv4(),
    status: 'active',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  const { data, error } = await supabase
    .from('student_enrollments')
    .insert(enrollmentData)
    .select()
    .single();
  
  return { data, error };
}

/**
 * Get all enrollments for a student
 * @param studentId Student ID
 * @returns List of enrollments or error
 */
export async function getStudentEnrollments(studentId: string) {
  const { data, error } = await supabase
    .from('student_enrollments')
    .select(`
      *,
      program:program_id (*),
      mentor:mentor_id (
        *,
        user:user_id (
          id,
          full_name,
          email,
          avatar_url
        )
      )
    `)
    .eq('student_id', studentId)
    .order('created_at', { ascending: false });
  
  return { data, error };
}

/**
 * Get all students for a mentor
 * @param mentorId Mentor ID
 * @returns List of students or error
 */
export async function getMentorStudents(mentorId: string) {
  const { data, error } = await supabase
    .from('student_enrollments')
    .select(`
      *,
      student:student_id (*),
      program:program_id (*)
    `)
    .eq('mentor_id', mentorId)
    .eq('status', 'active')
    .order('created_at', { ascending: false });
  
  return { data, error };
}

/**
 * Update enrollment status
 * @param enrollmentId Enrollment ID
 * @param status New status
 * @returns Updated enrollment or error
 */
export async function updateEnrollmentStatus(enrollmentId: string, status: 'active' | 'completed' | 'cancelled') {
  const updateData: UpdateTables<'student_enrollments'> = {
    status,
    updated_at: new Date().toISOString()
  };
  
  const { data, error } = await supabase
    .from('student_enrollments')
    .update(updateData)
    .eq('id', enrollmentId)
    .select()
    .single();
  
  return { data, error };
}

/**
 * Schedule a mentorship session
 * @param session Session data
 * @returns Created session or error
 */
export async function scheduleSession(session: {
  enrollment_id: string;
  scheduled_at: string;
  duration_minutes: number;
  notes?: string;
}) {
  const sessionData: InsertTables<'mentorship_sessions'> = {
    ...session,
    id: uuidv4(),
    status: 'scheduled',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  const { data, error } = await supabase
    .from('mentorship_sessions')
    .insert(sessionData)
    .select()
    .single();
  
  return { data, error };
}

/**
 * Get all sessions for an enrollment
 * @param enrollmentId Enrollment ID
 * @returns List of sessions or error
 */
export async function getEnrollmentSessions(enrollmentId: string) {
  const { data, error } = await supabase
    .from('mentorship_sessions')
    .select('*')
    .eq('enrollment_id', enrollmentId)
    .order('scheduled_at', { ascending: true });
  
  return { data, error };
}

/**
 * Get all upcoming sessions for a mentor
 * @param mentorId Mentor ID
 * @returns List of upcoming sessions or error
 */
export async function getMentorUpcomingSessions(mentorId: string) {
  const now = new Date().toISOString();

  // First get the enrollment IDs for the mentor's active students
  const { data: enrollmentIds, error: enrollmentError } = await supabase
    .from('student_enrollments')
    .select('id')
    .eq('mentor_id', mentorId)
    .eq('status', 'active');

  if (enrollmentError || !enrollmentIds) {
    return { data: null, error: enrollmentError };
  }

  // Then get the sessions for those enrollments
  const { data, error } = await supabase
    .from('mentorship_sessions')
    .select(`
      *,
      enrollment:enrollment_id (
        *,
        student:student_id (*)
      )
    `)
    .gte('scheduled_at', now)
    .eq('status', 'scheduled')
    .in('enrollment_id', enrollmentIds.map(e => e.id))
    .order('scheduled_at', { ascending: true });

  return { data, error };
}

/**
 * Get all upcoming sessions for a student
 * @param studentId Student ID
 * @returns List of upcoming sessions or error
 */
export async function getStudentUpcomingSessions(studentId: string) {
  const now = new Date().toISOString();

  // First get the enrollment IDs for the student's active enrollments
  const { data: enrollmentIds, error: enrollmentError } = await supabase
    .from('student_enrollments')
    .select('id')
    .eq('student_id', studentId)
    .eq('status', 'active');

  if (enrollmentError || !enrollmentIds) {
    return { data: null, error: enrollmentError };
  }

  // Then get the sessions for those enrollments
  const { data, error } = await supabase
    .from('mentorship_sessions')
    .select(`
      *,
      enrollment:enrollment_id (
        *,
        mentor:mentor_id (
          *,
          user:user_id (
            id,
            full_name,
            email,
            avatar_url
          )
        ),
        program:program_id (*)
      )
    `)
    .gte('scheduled_at', now)
    .eq('status', 'scheduled')
    .in('enrollment_id', enrollmentIds.map(e => e.id))
    .order('scheduled_at', { ascending: true });

  return { data, error };
}

/**
 * Get all sessions for a student (past and upcoming)
 * @param studentId Student ID
 * @returns List of all sessions or error
 */
export async function getStudentAllSessions(studentId: string) {
  // First get the enrollment IDs for the student's enrollments
  const { data: enrollmentIds, error: enrollmentError } = await supabase
    .from('student_enrollments')
    .select('id')
    .eq('student_id', studentId);

  if (enrollmentError || !enrollmentIds) {
    return { data: null, error: enrollmentError };
  }

  // Then get all sessions for those enrollments
  const { data, error } = await supabase
    .from('mentorship_sessions')
    .select(`
      *,
      enrollment:enrollment_id (
        *,
        mentor:mentor_id (
          *,
          user:user_id (
            id,
            full_name,
            email,
            avatar_url
          )
        ),
        program:program_id (*)
      )
    `)
    .in('enrollment_id', enrollmentIds.map(e => e.id))
    .order('scheduled_at', { ascending: false });

  return { data, error };
}

/**
 * Get student progress data
 * @param studentId Student ID
 * @returns Progress data or error
 */
export async function getStudentProgress(studentId: string) {
  try {
    // Get student enrollments
    const { data: enrollments, error: enrollmentError } = await getStudentEnrollments(studentId);

    if (enrollmentError || !enrollments) {
      return { data: null, error: enrollmentError };
    }

    // Get all sessions for the student
    const { data: sessions, error: sessionError } = await getStudentAllSessions(studentId);

    if (sessionError) {
      return { data: null, error: sessionError };
    }

    // Calculate progress metrics
    const totalSessions = sessions?.length || 0;
    const completedSessions = sessions?.filter(s => s.status === 'completed').length || 0;
    const upcomingSessions = sessions?.filter(s => s.status === 'scheduled' && new Date(s.scheduled_at) > new Date()).length || 0;

    // Calculate program progress (assuming each program has a standard number of sessions)
    const activeEnrollments = enrollments.filter(e => e.status === 'active');
    const totalProgramSessions = activeEnrollments.reduce((total, enrollment) => {
      // Estimate sessions based on program duration (e.g., 1 session per week)
      const durationMonths = enrollment.program?.duration_months || 6;
      return total + (durationMonths * 4); // 4 sessions per month
    }, 0);

    const progressData = {
      totalEnrollments: enrollments.length,
      activeEnrollments: activeEnrollments.length,
      totalSessions,
      completedSessions,
      upcomingSessions,
      totalProgramSessions,
      completionPercentage: totalProgramSessions > 0 ? Math.round((completedSessions / totalProgramSessions) * 100) : 0,
      enrollments: activeEnrollments,
      recentSessions: sessions?.slice(0, 5) || []
    };

    return { data: progressData, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Update session status
 * @param sessionId Session ID
 * @param status New status
 * @param notes Optional session notes
 * @returns Updated session or error
 */
export async function updateSessionStatus(
  sessionId: string, 
  status: 'scheduled' | 'completed' | 'cancelled',
  notes?: string
) {
  const updates: UpdateTables<'mentorship_sessions'> = {
    status,
    updated_at: new Date().toISOString()
  };
  
  if (notes !== undefined) {
    updates.notes = notes;
  }
  
  const { data, error } = await supabase
    .from('mentorship_sessions')
    .update(updates)
    .eq('id', sessionId)
    .select()
    .single();
  
  return { data, error };
}

/**
 * Request a new session for a student
 * @param sessionRequest Session request data
 * @returns Created session request or error
 */
export async function requestSession(sessionRequest: {
  student_id: string;
  enrollment_id: string;
  preferred_date: string;
  preferred_time: string;
  session_type: string;
  notes?: string;
}) {
  // For now, we'll create a session directly
  // In a real app, this might create a "session request" that mentors can approve
  const sessionData: InsertTables<'mentorship_sessions'> = {
    id: uuidv4(),
    enrollment_id: sessionRequest.enrollment_id,
    scheduled_at: `${sessionRequest.preferred_date}T${sessionRequest.preferred_time}:00.000Z`,
    duration_minutes: 60, // Default duration
    status: 'scheduled',
    notes: sessionRequest.notes || `${sessionRequest.session_type} session requested by student`,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  const { data, error } = await supabase
    .from('mentorship_sessions')
    .insert(sessionData)
    .select(`
      *,
      enrollment:enrollment_id (
        *,
        mentor:mentor_id (
          *,
          user:user_id (
            id,
            full_name,
            email,
            avatar_url
          )
        ),
        program:program_id (*)
      )
    `)
    .single();

  return { data, error };
}

/**
 * Get resources available to a student based on their enrollments
 * @param studentId Student ID
 * @returns List of available resources or error
 */
export async function getStudentResources(studentId: string) {
  // Get student enrollments to determine what resources they have access to
  const { data: enrollments, error: enrollmentError } = await getStudentEnrollments(studentId);

  if (enrollmentError || !enrollments) {
    return { data: null, error: enrollmentError };
  }

  // For now, get all resources (in a real app, you might filter by program/level)
  const { data, error } = await supabase
    .from('resources')
    .select('*')
    .order('created_at', { ascending: false });

  return { data, error };
}

/**
 * Get student dashboard summary data
 * @param studentId Student ID
 * @returns Dashboard summary data or error
 */
export async function getStudentDashboardData(studentId: string) {
  try {
    // Get all necessary data in parallel
    const [
      enrollmentsResult,
      upcomingSessionsResult,
      progressResult
    ] = await Promise.all([
      getStudentEnrollments(studentId),
      getStudentUpcomingSessions(studentId),
      getStudentProgress(studentId)
    ]);

    if (enrollmentsResult.error) {
      return { data: null, error: enrollmentsResult.error };
    }

    if (upcomingSessionsResult.error) {
      return { data: null, error: upcomingSessionsResult.error };
    }

    if (progressResult.error) {
      return { data: null, error: progressResult.error };
    }

    const dashboardData = {
      enrollments: enrollmentsResult.data || [],
      upcomingSessions: upcomingSessionsResult.data || [],
      progress: progressResult.data,
      nextSession: upcomingSessionsResult.data?.[0] || null
    };

    return { data: dashboardData, error: null };
  } catch (error) {
    return { data: null, error };
  }
}
