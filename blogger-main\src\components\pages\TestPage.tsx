import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Navbar } from '@/components/layout/Navbar';
import { Footer } from '@/components/layout/Footer';
import { ScrollToTop } from '@/components/ui/scroll-to-top';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '../../../supabase/auth';
import { supabase } from '../../../supabase/supabase';
import { formatCurrency } from '@/lib/currency';
import {
  CheckCircle,
  XCircle,
  AlertCircle,
  Info,
  Database,
  Users,
  ShoppingCart,
  Package,
  Church,
  MessageSquare,
  CreditCard,
  Settings,
  TestTube,
  Play,
  Zap,
  Eye,
  Heart,
  Star,
  Upload,
  Download,
  Mail,
  Phone,
  MapPin,
  Clock,
  Shield,
  LogIn,
  LogOut,
  Plus,
  Edit,
  Trash2,
} from 'lucide-react';

interface TestResult {
  name: string;
  status: 'success' | 'error' | 'warning' | 'info';
  message: string;
  details?: string;
}

export function TestPage() {
  const { user, isAdmin, userRole } = useAuth();
  const { toast } = useToast();
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [testData, setTestData] = useState({
    productName: 'Test Product',
    productPrice: 99.99,
    articleTitle: 'Test Article',
    prayerTitle: 'Test Prayer',
    contactMessage: 'Test contact message',
  });
  const [showCrudTests, setShowCrudTests] = useState(false);
  const [adminLoginData, setAdminLoginData] = useState({
    email: '<EMAIL>',
    password: 'admin123'
  });

  const addTestResult = (result: TestResult) => {
    setTestResults(prev => [...prev, result]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  // Test Database Connectivity
  const testDatabaseConnection = async () => {
    try {
      const { data, error } = await supabase.from('profiles').select('count').single();
      if (error) throw error;
      addTestResult({
        name: 'Database Connection',
        status: 'success',
        message: 'Successfully connected to Supabase database',
      });
    } catch (error) {
      addTestResult({
        name: 'Database Connection',
        status: 'error',
        message: 'Failed to connect to database',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Test Authentication
  const testAuthentication = async () => {
    if (!user) {
      addTestResult({
        name: 'Authentication',
        status: 'error',
        message: 'User not authenticated',
      });
      return;
    }

    addTestResult({
      name: 'Authentication',
      status: 'success',
      message: `User authenticated as ${user.email}`,
      details: `Role: ${userRole}, Admin: ${isAdmin}`,
    });
  };

  // Test User Management
  const testUserManagement = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, email, first_name, last_name, role')
        .limit(5);

      if (error) throw error;

      addTestResult({
        name: 'User Management',
        status: 'success',
        message: `Found ${data?.length || 0} users in database`,
        details: data?.map(u => `${u.email} (${u.role})`).join(', '),
      });
    } catch (error) {
      addTestResult({
        name: 'User Management',
        status: 'error',
        message: 'Failed to fetch users',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Test Product Management
  const testProductManagement = async () => {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('id, name, price, status')
        .limit(5);

      if (error) throw error;

      addTestResult({
        name: 'Product Management',
        status: 'success',
        message: `Found ${data?.length || 0} products in database`,
        details: data?.map(p => `${p.name} - ${formatCurrency(p.price)}`).join(', '),
      });
    } catch (error) {
      addTestResult({
        name: 'Product Management',
        status: 'error',
        message: 'Failed to fetch products',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Test Order Management
  const testOrderManagement = async () => {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select('id, total_amount, status')
        .limit(5);

      if (error) throw error;

      addTestResult({
        name: 'Order Management',
        status: 'success',
        message: `Found ${data?.length || 0} orders in database`,
        details: data?.map(o => `Order ${o.id.slice(0, 8)} - ${formatCurrency(o.total_amount)} (${o.status})`).join(', '),
      });
    } catch (error) {
      addTestResult({
        name: 'Order Management',
        status: 'error',
        message: 'Failed to fetch orders',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Test Prayer Room
  const testPrayerRoom = async () => {
    try {
      const { data, error } = await supabase
        .from('prayers')
        .select('id, title, type')
        .limit(5);

      if (error) {
        // Prayer table might not exist yet
        addTestResult({
          name: 'Prayer Room',
          status: 'warning',
          message: 'Prayer system not set up yet',
          details: 'Run the prayer-system-setup.sql script in Supabase',
        });
        return;
      }

      addTestResult({
        name: 'Prayer Room',
        status: 'success',
        message: `Found ${data?.length || 0} prayers in database`,
        details: data?.map(p => `${p.title} (${p.type})`).join(', '),
      });
    } catch (error) {
      addTestResult({
        name: 'Prayer Room',
        status: 'error',
        message: 'Failed to test prayer room',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Test Currency Formatting
  const testCurrencyFormatting = () => {
    const testAmounts = [0, 99.99, 1234.56, 10000];
    const results = testAmounts.map(amount => formatCurrency(amount));
    
    addTestResult({
      name: 'Currency Formatting',
      status: 'success',
      message: 'Currency formatting working correctly',
      details: `Test amounts: ${results.join(', ')}`,
    });
  };

  // Test Contact Information
  const testContactInformation = () => {
    const contactInfo = {
      email: '<EMAIL>',
      phone: '+27 11 234 5678',
      address: 'Morningside, Sandton, 2057, South Africa',
      hours: 'Mon-Fri: 8AM-5PM SAST',
    };

    addTestResult({
      name: 'Contact Information',
      status: 'success',
      message: 'Contact information updated to Morningside Sandton',
      details: `${contactInfo.email}, ${contactInfo.phone}, ${contactInfo.address}`,
    });
  };

  // Test Admin Permissions
  const testAdminPermissions = async () => {
    if (!isAdmin) {
      addTestResult({
        name: 'Admin Permissions',
        status: 'warning',
        message: 'Current user is not an admin',
        details: 'Login as admin to test admin features',
      });
      return;
    }

    addTestResult({
      name: 'Admin Permissions',
      status: 'success',
      message: 'Admin permissions verified',
      details: 'User has admin access to all features',
    });
  };

  // Admin Login Function
  const loginAsAdmin = async () => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: adminLoginData.email,
        password: adminLoginData.password,
      });

      if (error) throw error;

      addTestResult({
        name: 'Admin Login',
        status: 'success',
        message: 'Successfully logged in as admin',
        details: `Logged in as ${adminLoginData.email}`,
      });

      toast({
        title: 'Admin Login Successful',
        description: 'You are now logged in as admin',
      });
    } catch (error) {
      addTestResult({
        name: 'Admin Login',
        status: 'error',
        message: 'Failed to login as admin',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Test Contact Form Submission
  const testContactFormSubmission = async () => {
    try {
      const { data, error } = await supabase
        .from('messages')
        .insert({
          name: 'Test User',
          email: '<EMAIL>',
          subject: 'Test Message from Test Page',
          inquiry_type: 'general',
          message: testData.contactMessage,
          status: 'unread',
          priority: 'normal'
        })
        .select();

      if (error) throw error;

      addTestResult({
        name: 'Contact Form Submission',
        status: 'success',
        message: 'Contact form submission successful',
        details: `Message ID: ${data[0]?.id}`,
      });
    } catch (error) {
      addTestResult({
        name: 'Contact Form Submission',
        status: 'error',
        message: 'Failed to submit contact form',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Test Article CRUD Operations
  const testArticleCrud = async () => {
    if (!isAdmin) {
      addTestResult({
        name: 'Article CRUD',
        status: 'warning',
        message: 'Admin access required for article CRUD operations',
      });
      return;
    }

    try {
      // Create
      const { data: createData, error: createError } = await supabase
        .from('articles')
        .insert({
          title: testData.articleTitle,
          content: 'This is a test article content',
          excerpt: 'Test excerpt',
          slug: 'test-article-' + Date.now(),
          status: 'draft',
          author_id: user?.id,
        })
        .select()
        .single();

      if (createError) throw createError;

      // Update
      const { error: updateError } = await supabase
        .from('articles')
        .update({ title: testData.articleTitle + ' (Updated)' })
        .eq('id', createData.id);

      if (updateError) throw updateError;

      // Delete
      const { error: deleteError } = await supabase
        .from('articles')
        .delete()
        .eq('id', createData.id);

      if (deleteError) throw deleteError;

      addTestResult({
        name: 'Article CRUD',
        status: 'success',
        message: 'Article CRUD operations successful',
        details: 'Created, updated, and deleted test article',
      });
    } catch (error) {
      addTestResult({
        name: 'Article CRUD',
        status: 'error',
        message: 'Failed article CRUD operations',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Test Product CRUD Operations
  const testProductCrud = async () => {
    if (!isAdmin) {
      addTestResult({
        name: 'Product CRUD',
        status: 'warning',
        message: 'Admin access required for product CRUD operations',
      });
      return;
    }

    try {
      // Create
      const { data: createData, error: createError } = await supabase
        .from('products')
        .insert({
          name: testData.productName,
          description: 'This is a test product',
          price: testData.productPrice,
          slug: 'test-product-' + Date.now(),
          status: 'draft',
          category_id: null,
          type: 'physical',
        })
        .select()
        .single();

      if (createError) throw createError;

      // Update
      const { error: updateError } = await supabase
        .from('products')
        .update({ name: testData.productName + ' (Updated)' })
        .eq('id', createData.id);

      if (updateError) throw updateError;

      // Delete
      const { error: deleteError } = await supabase
        .from('products')
        .delete()
        .eq('id', createData.id);

      if (deleteError) throw deleteError;

      addTestResult({
        name: 'Product CRUD',
        status: 'success',
        message: 'Product CRUD operations successful',
        details: 'Created, updated, and deleted test product',
      });
    } catch (error) {
      addTestResult({
        name: 'Product CRUD',
        status: 'error',
        message: 'Failed product CRUD operations',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Test Comment System
  const testCommentSystem = async () => {
    try {
      // Get a test article
      const { data: articles, error: articlesError } = await supabase
        .from('articles')
        .select('id')
        .limit(1);

      if (articlesError || !articles?.length) {
        addTestResult({
          name: 'Comment System',
          status: 'warning',
          message: 'No articles found to test comments',
        });
        return;
      }

      // Test comment creation
      const { data: commentData, error: commentError } = await supabase
        .from('comments')
        .insert({
          article_id: articles[0].id,
          user_id: user?.id,
          content: 'This is a test comment from the test page',
          is_approved: true,
        })
        .select()
        .single();

      if (commentError) throw commentError;

      // Test comment deletion
      const { error: deleteError } = await supabase
        .from('comments')
        .delete()
        .eq('id', commentData.id);

      if (deleteError) throw deleteError;

      addTestResult({
        name: 'Comment System',
        status: 'success',
        message: 'Comment system working correctly',
        details: 'Created and deleted test comment',
      });
    } catch (error) {
      addTestResult({
        name: 'Comment System',
        status: 'error',
        message: 'Failed to test comment system',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Run All Tests
  const runAllTests = async () => {
    setIsRunningTests(true);
    clearResults();

    const tests = [
      testDatabaseConnection,
      testAuthentication,
      testUserManagement,
      testProductManagement,
      testOrderManagement,
      testPrayerRoom,
      testCurrencyFormatting,
      testContactInformation,
      testAdminPermissions,
      testContactFormSubmission,
    ];

    for (const test of tests) {
      await test();
      // Small delay between tests for better UX
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    setIsRunningTests(false);
    toast({
      title: 'Tests Completed',
      description: 'All system tests have been executed',
    });
  };

  // Run CRUD Tests
  const runCrudTests = async () => {
    setIsRunningTests(true);
    clearResults();

    const crudTests = [
      testArticleCrud,
      testProductCrud,
      testCommentSystem,
    ];

    for (const test of crudTests) {
      await test();
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    setIsRunningTests(false);
    toast({
      title: 'CRUD Tests Completed',
      description: 'All CRUD operations have been tested',
    });
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error': return <XCircle className="h-5 w-5 text-red-600" />;
      case 'warning': return <AlertCircle className="h-5 w-5 text-yellow-600" />;
      case 'info': return <Info className="h-5 w-5 text-blue-600" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return 'border-green-200 bg-green-50';
      case 'error': return 'border-red-200 bg-red-50';
      case 'warning': return 'border-yellow-200 bg-yellow-50';
      case 'info': return 'border-blue-200 bg-blue-50';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4 flex items-center justify-center">
            <TestTube className="h-10 w-10 mr-3 text-blue-600" />
            System Test Page
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Comprehensive testing suite for all Thabo Bester platform features
          </p>
        </div>

        {/* User Info */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Current User Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-gray-600">Email</p>
                <p className="font-medium">{user?.email || 'Not logged in'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Role</p>
                <Badge variant={isAdmin ? 'default' : 'secondary'}>
                  {userRole || 'Unknown'}
                </Badge>
              </div>
              <div>
                <p className="text-sm text-gray-600">Admin Status</p>
                <Badge variant={isAdmin ? 'default' : 'outline'}>
                  {isAdmin ? 'Admin' : 'Regular User'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Admin Login */}
        {!isAdmin && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 mr-2" />
                Admin Login
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                <div>
                  <Label htmlFor="admin-email">Admin Email</Label>
                  <Input
                    id="admin-email"
                    type="email"
                    value={adminLoginData.email}
                    onChange={(e) => setAdminLoginData(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="admin-password">Admin Password</Label>
                  <Input
                    id="admin-password"
                    type="password"
                    value={adminLoginData.password}
                    onChange={(e) => setAdminLoginData(prev => ({ ...prev, password: e.target.value }))}
                    placeholder="admin123"
                  />
                </div>
                <Button onClick={loginAsAdmin} className="flex items-center">
                  <Shield className="h-4 w-4 mr-2" />
                  Login as Admin
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Test Controls */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Play className="h-5 w-5 mr-2" />
              Test Controls
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              <Button
                onClick={runAllTests}
                disabled={isRunningTests}
                className="flex items-center"
              >
                {isRunningTests ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Running Tests...
                  </>
                ) : (
                  <>
                    <Zap className="h-4 w-4 mr-2" />
                    Run All Tests
                  </>
                )}
              </Button>

              <Button
                onClick={runCrudTests}
                disabled={isRunningTests}
                variant="secondary"
                className="flex items-center"
              >
                <Database className="h-4 w-4 mr-2" />
                Run CRUD Tests
              </Button>

              <Button
                onClick={testContactFormSubmission}
                disabled={isRunningTests}
                variant="outline"
                className="flex items-center"
              >
                <Mail className="h-4 w-4 mr-2" />
                Test Contact Form
              </Button>

              <Button variant="outline" onClick={clearResults}>
                Clear Results
              </Button>

              <Button
                variant="outline"
                onClick={() => setShowCrudTests(!showCrudTests)}
                className="flex items-center"
              >
                <Eye className="h-4 w-4 mr-2" />
                {showCrudTests ? 'Hide' : 'Show'} CRUD Options
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        {testResults.length > 0 && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="h-5 w-5 mr-2" />
                Test Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {testResults.map((result, index) => (
                  <div
                    key={index}
                    className={`p-4 rounded-lg border ${getStatusColor(result.status)}`}
                  >
                    <div className="flex items-start space-x-3">
                      {getStatusIcon(result.status)}
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900">{result.name}</h3>
                        <p className="text-sm text-gray-600 mt-1">{result.message}</p>
                        {result.details && (
                          <p className="text-xs text-gray-500 mt-2 font-mono bg-white/50 p-2 rounded">
                            {result.details}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Feature Test Links */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center text-lg">
                <Package className="h-5 w-5 mr-2" />
                Product Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Test product creation, editing, and image uploads
              </p>
              <div className="space-y-2">
                <Button variant="outline" size="sm" className="w-full" asChild>
                  <a href="/products">View Products</a>
                </Button>
                {isAdmin && (
                  <Button variant="outline" size="sm" className="w-full" asChild>
                    <a href="/dashboard/products/create">Create Product</a>
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center text-lg">
                <ShoppingCart className="h-5 w-5 mr-2" />
                Order Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Test order creation, status updates, and CRUD operations
              </p>
              <div className="space-y-2">
                <Button variant="outline" size="sm" className="w-full" asChild>
                  <a href="/dashboard/orders">View Orders</a>
                </Button>
                <Button variant="outline" size="sm" className="w-full" asChild>
                  <a href="/cart">Shopping Cart</a>
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center text-lg">
                <Church className="h-5 w-5 mr-2" />
                Prayer Room
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Test prayer creation, multimedia support, and user interactions
              </p>
              <div className="space-y-2">
                <Button variant="outline" size="sm" className="w-full" asChild>
                  <a href="/dashboard/prayers">Prayer Room</a>
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center text-lg">
                <Users className="h-5 w-5 mr-2" />
                User Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Test user display, role management, and admin functions
              </p>
              <div className="space-y-2">
                {isAdmin && (
                  <Button variant="outline" size="sm" className="w-full" asChild>
                    <a href="/dashboard/users">Manage Users</a>
                  </Button>
                )}
                <Button variant="outline" size="sm" className="w-full" asChild>
                  <a href="/dashboard">User Dashboard</a>
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center text-lg">
                <MessageSquare className="h-5 w-5 mr-2" />
                Contact System
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Test contact forms and Morningside Sandton information
              </p>
              <div className="space-y-2">
                <Button variant="outline" size="sm" className="w-full" asChild>
                  <a href="/contact">Contact Page</a>
                </Button>
                {isAdmin && (
                  <Button variant="outline" size="sm" className="w-full" asChild>
                    <a href="/dashboard/messages">View Messages</a>
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center text-lg">
                <CreditCard className="h-5 w-5 mr-2" />
                Currency & Payments
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Test ZAR currency formatting and payment flows
              </p>
              <div className="space-y-2">
                <div className="text-sm">
                  <p>Test amounts:</p>
                  <p>{formatCurrency(99.99)}</p>
                  <p>{formatCurrency(1234.56)}</p>
                  <p>{formatCurrency(10000)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Button variant="outline" asChild>
                <a href="/dashboard">Dashboard</a>
              </Button>
              <Button variant="outline" asChild>
                <a href="/articles">Articles</a>
              </Button>
              <Button variant="outline" asChild>
                <a href="/products">Products</a>
              </Button>
              <Button variant="outline" asChild>
                <a href="/about">About</a>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <Footer />
      <ScrollToTop />
    </div>
  );
}
