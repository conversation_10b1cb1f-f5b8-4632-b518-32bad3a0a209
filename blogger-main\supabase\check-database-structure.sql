-- =====================================================
-- CHECK DATABASE STRUCTURE - Compare Online vs Local
-- This script checks all tables, columns, and relationships
-- =====================================================

-- STEP 1: LIST ALL TABLES IN PUBLIC SCHEMA
-- =====================================================

SELECT 
    'PUBLIC TABLES' as check_type,
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public'
ORDER BY table_name;

-- STEP 2: CHECK SPECIFIC TABLES THAT SHOULD EXIST
-- =====================================================

-- Check for each expected table
SELECT 
    'TABLE EXISTENCE CHECK' as check_type,
    'profiles' as table_name,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles' AND table_schema = 'public')
        THEN '✅ EXISTS'
        ELSE '❌ MISSING'
    END as status
UNION ALL
SELECT 
    'TABLE EXISTENCE CHECK' as check_type,
    'articles' as table_name,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'articles' AND table_schema = 'public')
        THEN '✅ EXISTS'
        ELSE '❌ MISSING'
    END as status
UNION ALL
SELECT 
    'TABLE EXISTENCE CHECK' as check_type,
    'products' as table_name,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'products' AND table_schema = 'public')
        THEN '✅ EXISTS'
        ELSE '❌ MISSING'
    END as status
UNION ALL
SELECT 
    'TABLE EXISTENCE CHECK' as check_type,
    'categories' as table_name,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'categories' AND table_schema = 'public')
        THEN '✅ EXISTS'
        ELSE '❌ MISSING'
    END as status
UNION ALL
SELECT 
    'TABLE EXISTENCE CHECK' as check_type,
    'contact_messages' as table_name,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'contact_messages' AND table_schema = 'public')
        THEN '✅ EXISTS'
        ELSE '❌ MISSING'
    END as status
UNION ALL
SELECT 
    'TABLE EXISTENCE CHECK' as check_type,
    'user_subscriptions' as table_name,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_subscriptions' AND table_schema = 'public')
        THEN '✅ EXISTS'
        ELSE '❌ MISSING'
    END as status
UNION ALL
SELECT 
    'TABLE EXISTENCE CHECK' as check_type,
    'orders' as table_name,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'orders' AND table_schema = 'public')
        THEN '✅ EXISTS'
        ELSE '❌ MISSING'
    END as status
UNION ALL
SELECT 
    'TABLE EXISTENCE CHECK' as check_type,
    'order_items' as table_name,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'order_items' AND table_schema = 'public')
        THEN '✅ EXISTS'
        ELSE '❌ MISSING'
    END as status
UNION ALL
SELECT 
    'TABLE EXISTENCE CHECK' as check_type,
    'comments' as table_name,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'comments' AND table_schema = 'public')
        THEN '✅ EXISTS'
        ELSE '❌ MISSING'
    END as status
UNION ALL
SELECT 
    'TABLE EXISTENCE CHECK' as check_type,
    'likes' as table_name,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'likes' AND table_schema = 'public')
        THEN '✅ EXISTS'
        ELSE '❌ MISSING'
    END as status;

-- STEP 3: CHECK STORAGE BUCKETS
-- =====================================================

SELECT 
    'STORAGE BUCKETS' as check_type,
    id as bucket_name,
    public,
    file_size_limit,
    allowed_mime_types
FROM storage.buckets
ORDER BY id;

-- STEP 4: CHECK AUTH USERS AND PROFILES
-- =====================================================

-- Count auth users
SELECT 
    'AUTH USERS COUNT' as check_type,
    COUNT(*) as total_users
FROM auth.users;

-- Count profiles (if table exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles' AND table_schema = 'public') THEN
        RAISE NOTICE 'PROFILES COUNT: %', (SELECT COUNT(*) FROM public.profiles);
        RAISE NOTICE 'ADMIN USERS: %', (SELECT COUNT(*) FROM public.profiles WHERE role = 'admin');
    ELSE
        RAISE NOTICE 'PROFILES TABLE DOES NOT EXIST';
    END IF;
END $$;

-- STEP 5: CHECK SPECIFIC MISSING TABLE (contact_messages)
-- =====================================================

-- Check if contact_messages table exists and show its structure if it does
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'contact_messages' AND table_schema = 'public') THEN
        RAISE NOTICE '✅ CONTACT_MESSAGES TABLE EXISTS';
    ELSE
        RAISE NOTICE '❌ CONTACT_MESSAGES TABLE MISSING - This is causing the 404 error';
        RAISE NOTICE '🔧 SOLUTION: Need to run complete database setup';
    END IF;
END $$;

-- STEP 6: SHOW TABLE COLUMNS FOR EXISTING TABLES
-- =====================================================

-- Show columns for profiles table (if exists)
SELECT 
    'PROFILES COLUMNS' as check_type,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'profiles' AND table_schema = 'public'
ORDER BY ordinal_position;

-- STEP 7: SUMMARY AND RECOMMENDATIONS
-- =====================================================

DO $$
DECLARE
    table_count INTEGER;
    expected_tables TEXT[] := ARRAY['profiles', 'articles', 'products', 'categories', 'contact_messages', 'user_subscriptions', 'orders', 'order_items', 'comments', 'likes'];
    missing_tables TEXT := '';
    table_name TEXT;
BEGIN
    -- Count existing tables
    SELECT COUNT(*) INTO table_count 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = ANY(expected_tables);
    
    -- Find missing tables
    FOREACH table_name IN ARRAY expected_tables
    LOOP
        IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = table_name AND table_schema = 'public') THEN
            missing_tables := missing_tables || table_name || ', ';
        END IF;
    END LOOP;
    
    RAISE NOTICE '🔍 DATABASE STRUCTURE ANALYSIS';
    RAISE NOTICE '==========================================';
    RAISE NOTICE 'Expected tables: %', array_length(expected_tables, 1);
    RAISE NOTICE 'Existing tables: %', table_count;
    RAISE NOTICE 'Missing tables: %', TRIM(TRAILING ', ' FROM missing_tables);
    RAISE NOTICE '==========================================';
    
    IF table_count < array_length(expected_tables, 1) THEN
        RAISE NOTICE '❌ DATABASE INCOMPLETE';
        RAISE NOTICE '🔧 RECOMMENDED ACTIONS:';
        RAISE NOTICE '1. Run: npm run db:setup (complete setup)';
        RAISE NOTICE '2. Or run: npm run db:force-sync (recreate all)';
        RAISE NOTICE '3. Then run: npm run db:seed (add sample data)';
        RAISE NOTICE '4. Verify all tables exist';
    ELSE
        RAISE NOTICE '✅ DATABASE COMPLETE';
        RAISE NOTICE '🎉 All expected tables exist';
    END IF;
    
    RAISE NOTICE '==========================================';
END $$;
