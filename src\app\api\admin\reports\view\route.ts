import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { createServiceClient } from '@/utils/supabase/service';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions
    const { data: adminData } = await supabase
      .from('users')
      .select('role, admin_role')
      .eq('id', session.user.id)
      .single();

    if (!adminData || adminData.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const reportId = searchParams.get('id');
    let reportType = searchParams.get('type');

    if (!reportId && !reportType) {
      return NextResponse.json({ error: 'Report ID or type required' }, { status: 400 });
    }

    // Use service role client for database operations
    const serviceSupabase = createServiceClient();

    let reportData: any = {};
    let reportTitle = '';
    let reportFilters = {};

    if (reportId) {
      // Get specific report details
      const { data: report } = await serviceSupabase
        .from('admin_reports')
        .select('*')
        .eq('id', reportId)
        .single();

      if (!report) {
        return NextResponse.json({ error: 'Report not found' }, { status: 404 });
      }

      reportTitle = report.report_name;
      reportFilters = report.filters || {};
      reportType = report.report_type;
    }

    // Fetch data based on report type
    let activities: any[] = [];
    let metrics: any = {};

    switch (reportType) {
      case 'activity_summary':
        if (!reportTitle) reportTitle = 'Activity Summary Report';
        
        // Get activity logs with admin details
        const { data: activityData } = await serviceSupabase
          .from('admin_activity_logs')
          .select(`
            id,
            action_type,
            action_description,
            target_type,
            success,
            error_message,
            created_at,
            admin:users!admin_id(email, full_name, admin_role)
          `)
          .order('created_at', { ascending: false })
          .limit(100);

        activities = activityData || [];

        // Get summary metrics
        const { data: summaryMetrics } = await serviceSupabase
          .rpc('get_activity_summary_metrics');
        
        metrics = summaryMetrics?.[0] || {
          total_activities: 0,
          success_rate: 0,
          activities_today: 0,
          activities_this_week: 0,
          most_active_admin: 'N/A'
        };
        break;

      case 'performance_metrics':
        if (!reportTitle) reportTitle = 'Performance Metrics Report';
        
        // Get performance data by admin
        const { data: performanceData } = await serviceSupabase
          .rpc('get_admin_performance_metrics');
        
        activities = performanceData || [];
        
        // Calculate overall metrics
        const totalActions = activities.reduce((sum, admin) => sum + (admin.total_actions || 0), 0);
        const avgSuccessRate = activities.length > 0 
          ? activities.reduce((sum, admin) => sum + (admin.success_rate || 0), 0) / activities.length 
          : 0;

        metrics = {
          total_admins: activities.length,
          total_actions: totalActions,
          average_success_rate: Math.round(avgSuccessRate * 100) / 100,
          most_active_admin: activities[0]?.admin_name || 'N/A'
        };
        break;

      case 'audit_trail':
        if (!reportTitle) reportTitle = 'Audit Trail Report';
        
        // Get failed activities for audit
        const { data: auditData } = await serviceSupabase
          .from('admin_activity_logs')
          .select(`
            id,
            action_type,
            action_description,
            target_type,
            error_message,
            ip_address,
            user_agent,
            created_at,
            admin:users!admin_id(email, full_name, admin_role)
          `)
          .eq('success', false)
          .order('created_at', { ascending: false })
          .limit(100);

        activities = auditData || [];

        // Get audit metrics
        const { data: auditMetrics } = await serviceSupabase
          .rpc('get_audit_trail_metrics');
        
        metrics = auditMetrics?.[0] || {
          total_failures: 0,
          failure_rate: 0,
          most_common_error: 'N/A',
          failures_today: 0
        };
        break;

      default:
        return NextResponse.json({ error: 'Invalid report type' }, { status: 400 });
    }

    // Format the response
    const reportView = {
      id: reportId,
      title: reportTitle,
      type: reportType,
      filters: reportFilters,
      generated_at: new Date().toISOString(),
      metrics,
      data: activities,
      summary: {
        total_records: activities.length,
        date_range: {
          from: activities.length > 0 ? activities[activities.length - 1]?.created_at : null,
          to: activities.length > 0 ? activities[0]?.created_at : null
        }
      }
    };

    return NextResponse.json(reportView);

  } catch (error) {
    console.error('View report error:', error);
    return NextResponse.json({ error: 'Failed to load report' }, { status: 500 });
  }
}

// Helper function to apply filters to data
function applyFilters(data: any[], filters: any): any[] {
  let filteredData = [...data];

  if (filters.date_from) {
    filteredData = filteredData.filter(item => 
      new Date(item.created_at) >= new Date(filters.date_from)
    );
  }

  if (filters.date_to) {
    filteredData = filteredData.filter(item => 
      new Date(item.created_at) <= new Date(filters.date_to)
    );
  }

  if (filters.admin_roles && filters.admin_roles.length > 0) {
    filteredData = filteredData.filter(item => 
      filters.admin_roles.includes(item.admin?.admin_role)
    );
  }

  if (filters.activity_types && filters.activity_types.length > 0) {
    filteredData = filteredData.filter(item => 
      filters.activity_types.includes(item.action_type)
    );
  }

  if (filters.success_only !== undefined) {
    filteredData = filteredData.filter(item => 
      item.success === filters.success_only
    );
  }

  return filteredData;
}
