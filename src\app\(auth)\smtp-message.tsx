// import { ArrowUpRight, InfoIcon } from "lucide-react";
// import Link from "next/link";

// export function SmtpMessage() {
//   return (
//     <div className="bg-muted/30 px-5 py-4 border border-border rounded-xl flex gap-3">
//       <InfoIcon size={16} className="text-primary mt-0.5 flex-shrink-0" />
//       <div className="flex flex-col gap-1">
//         <small className="text-sm text-muted-foreground">
//           <strong className="font-medium text-foreground">Note:</strong> Emails are rate limited. Enable Custom SMTP to
//           increase the rate limit.
//         </small>
//         <div>
//           <Link
//             href="https://supabase.com/docs/guides/auth/auth-smtp"
//             target="_blank"
//             className="text-primary hover:text-primary/80 flex items-center text-xs gap-1 transition-colors"
//           >
//             Learn more <ArrowUpRight size={12} />
//           </Link>
//         </div>
//       </div>
//     </div>
//   );
// }
