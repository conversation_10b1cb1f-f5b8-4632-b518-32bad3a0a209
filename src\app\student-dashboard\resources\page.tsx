"use client";

import { useState, useEffect } from "react";
import { createBrowserClient } from '@supabase/ssr';
import { redirect } from "next/navigation";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  FileText,
  Video,
  BookOpen,
  Download,
  Search,
  Loader2,
  AlertCircle,
  File,
  ExternalLink
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { getStudentResources } from "@/utils/supabase/mentorship-utils";

export default function ResourcesPage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [resources, setResources] = useState<any[]>([]);
  const [filteredResources, setFilteredResources] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedType, setSelectedType] = useState<string>("all");

  useEffect(() => {
    loadResources();
  }, []);

  useEffect(() => {
    filterResources();
  }, [resources, searchTerm, selectedType]);

  const loadResources = async () => {
    try {
      setLoading(true);
      setError(null);

      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        redirect("/auth/sign-in");
        return;
      }

      const { data, error } = await getStudentResources(user.id);

      if (error) {
        throw new Error('Failed to load resources');
      }

      setResources(data || []);
    } catch (error: any) {
      console.error('Error loading resources:', error);
      setError(error.message || 'Failed to load resources');
    } finally {
      setLoading(false);
    }
  };

  const filterResources = () => {
    let filtered = resources;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(resource =>
        resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        resource.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        resource.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by type
    if (selectedType !== "all") {
      filtered = filtered.filter(resource => resource.type === selectedType);
    }

    setFilteredResources(filtered);
  };

  const getIcon = (type: string) => {
    switch (type) {
      case "video":
        return <Video className="h-5 w-5 text-blue-500" />;
      case "document":
        return <FileText className="h-5 w-5 text-red-500" />;
      case "training":
        return <BookOpen className="h-5 w-5 text-green-500" />;
      case "progress":
        return <File className="h-5 w-5 text-purple-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "video":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "document":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case "training":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "progress":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading resources...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="p-4 md:p-6 space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <h1 className="text-2xl md:text-3xl font-bold">Resource Library</h1>
        <div className="flex gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search resources..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-full md:w-64"
            />
          </div>
        </div>
      </div>

      {/* Filter Buttons */}
      <div className="flex flex-wrap gap-2">
        <Button
          variant={selectedType === "all" ? "default" : "outline"}
          size="sm"
          onClick={() => setSelectedType("all")}
        >
          All Resources
        </Button>
        <Button
          variant={selectedType === "video" ? "default" : "outline"}
          size="sm"
          onClick={() => setSelectedType("video")}
        >
          <Video className="h-4 w-4 mr-1" />
          Videos
        </Button>
        <Button
          variant={selectedType === "document" ? "default" : "outline"}
          size="sm"
          onClick={() => setSelectedType("document")}
        >
          <FileText className="h-4 w-4 mr-1" />
          Documents
        </Button>
        <Button
          variant={selectedType === "training" ? "default" : "outline"}
          size="sm"
          onClick={() => setSelectedType("training")}
        >
          <BookOpen className="h-4 w-4 mr-1" />
          Training
        </Button>
      </div>

      {/* Resources Grid */}
      <div className="grid gap-4 md:gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        {filteredResources.length > 0 ? (
          filteredResources.map((resource: any) => (
            <Card key={resource.id} className="p-6 hover:bg-accent transition-colors cursor-pointer group">
              <div className="space-y-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      {getIcon(resource.type)}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-sm md:text-base line-clamp-2">
                        {resource.title}
                      </h3>
                      <Badge className={`text-xs ${getTypeColor(resource.type)}`}>
                        {resource.type}
                      </Badge>
                    </div>
                  </div>
                </div>

                {resource.description && (
                  <p className="text-sm text-muted-foreground line-clamp-3">
                    {resource.description}
                  </p>
                )}

                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>Category: {resource.category}</span>
                  <span>{resource.download_count || 0} downloads</span>
                </div>

                <div className="flex gap-2">
                  <Button size="sm" className="flex-1">
                    <ExternalLink className="h-4 w-4 mr-1" />
                    View
                  </Button>
                  <Button size="sm" variant="outline">
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          ))
        ) : (
          <div className="col-span-full text-center py-12 text-muted-foreground">
            <BookOpen className="h-16 w-16 mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-medium mb-2">No resources found</h3>
            <p className="text-sm">
              {searchTerm || selectedType !== "all"
                ? "Try adjusting your search or filters"
                : "Resources will appear here once they're added to your program"
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
