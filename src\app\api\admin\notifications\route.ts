import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { createServiceClient } from '@/utils/supabase/service';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions - only main admins can view notifications
    const { data: adminData } = await supabase
      .from('users')
      .select('role, admin_role')
      .eq('id', session.user.id)
      .single();

    if (!adminData || adminData.role !== 'admin' || adminData.admin_role !== 'admin') {
      return NextResponse.json({ error: 'Main admin access required' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const since = searchParams.get('since'); // ISO timestamp for real-time updates

    // Use service role client for database operations
    const serviceSupabase = createServiceClient();

    // Build query for recent activities
    let query = serviceSupabase
      .from('admin_activity_logs')
      .select(`
        id,
        action_type,
        action_description,
        target_type,
        success,
        created_at,
        admin:users!admin_id(email, full_name, admin_role)
      `)
      .order('created_at', { ascending: false })
      .limit(limit);

    // If 'since' parameter is provided, only get activities after that timestamp
    if (since) {
      query = query.gt('created_at', since);
    }

    const { data: activities, error } = await query;

    if (error) {
      console.error('Notifications fetch error:', error);
      return NextResponse.json({ error: 'Failed to fetch notifications' }, { status: 500 });
    }

    // Transform activities into notification format
    const notifications = (activities || []).map(activity => {
      // Handle the admin relationship - it could be an object or null
      const admin = Array.isArray(activity.admin) ? activity.admin[0] : activity.admin;

      return {
        id: activity.id,
        type: getNotificationType(activity.action_type, activity.success),
        title: getNotificationTitle(activity.action_type, activity.success),
        message: activity.action_description,
        admin: admin?.full_name || 'Unknown Admin',
        admin_role: admin?.admin_role || 'unknown',
        timestamp: activity.created_at,
        success: activity.success,
        target_type: activity.target_type,
        read: false // All notifications start as unread
      };
    });

    return NextResponse.json({
      notifications,
      count: notifications.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Notifications API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Helper function to determine notification type based on activity
function getNotificationType(actionType: string, success: boolean): string {
  if (!success) return 'error';
  
  switch (actionType) {
    case 'order_management':
      return 'order';
    case 'product_management':
      return 'product';
    case 'user_management':
      return 'user';
    case 'system_config':
      return 'system';
    default:
      return 'info';
  }
}

// Helper function to generate notification titles
function getNotificationTitle(actionType: string, success: boolean): string {
  if (!success) return 'Action Failed';
  
  switch (actionType) {
    case 'order_management':
      return 'Order Updated';
    case 'product_management':
      return 'Product Modified';
    case 'user_management':
      return 'User Account Changed';
    case 'system_config':
      return 'System Configuration';
    default:
      return 'Admin Activity';
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions
    const { data: adminData } = await supabase
      .from('users')
      .select('role, admin_role')
      .eq('id', session.user.id)
      .single();

    if (!adminData || adminData.role !== 'admin' || adminData.admin_role !== 'admin') {
      return NextResponse.json({ error: 'Main admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { action } = body;

    if (action === 'mark_all_read') {
      // In a real implementation, you would update a notifications_read table
      // For now, we'll just return success since we don't persist read status
      return NextResponse.json({ success: true, message: 'All notifications marked as read' });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });

  } catch (error) {
    console.error('Notifications POST error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
