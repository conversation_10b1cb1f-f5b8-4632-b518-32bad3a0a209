import { render, screen, waitFor, act } from '@testing-library/react';
import { usePathname } from 'next/navigation';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import Preloader from '../preloader';

// Mock Next.js navigation
vi.mock('next/navigation', () => ({
  usePathname: vi.fn(),
}));

// Mock Next.js Image component
vi.mock('next/image', () => ({
  default: ({ src, alt, ...props }: any) => (
    <img src={src} alt={alt} {...props} />
  ),
}));

// Mock window.matchMedia for reduced motion
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

describe('Preloader Component', () => {
  const mockUsePathname = usePathname as ReturnType<typeof vi.fn>;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
    mockUsePathname.mockReturnValue('/');

    // Mock document.readyState
    Object.defineProperty(document, 'readyState', {
      writable: true,
      value: 'loading'
    });
  });

  afterEach(() => {
    vi.runOnlyPendingTimers();
    vi.useRealTimers();
  });

  it('renders preloader on initial load', () => {
    render(<Preloader />);
    
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(screen.getByLabelText('Loading page content')).toBeInTheDocument();
    expect(screen.getByAltText('Tennis Whisperer')).toBeInTheDocument();
  });

  it('shows logo after delay', async () => {
    render(<Preloader />);

    // Initially logo should be hidden (opacity-0)
    const logoContainer = screen.getByAltText('Tennis Whisperer').parentElement?.parentElement;
    expect(logoContainer).toHaveClass('opacity-0');

    // After 300ms, logo should appear
    act(() => {
      vi.advanceTimersByTime(300);
    });

    await waitFor(() => {
      expect(logoContainer).toHaveClass('opacity-100');
    });
  });

  it('hides preloader after minimum load time when page is ready', async () => {
    render(<Preloader />);

    // Simulate page ready after 1 second
    act(() => {
      vi.advanceTimersByTime(1000);
      Object.defineProperty(document, 'readyState', {
        writable: true,
        value: 'complete'
      });
    });

    // Advance to minimum load time (2000ms) + check delay (800ms) + hide delay (500ms) + fade delay (800ms)
    act(() => {
      vi.advanceTimersByTime(4100);
    });

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 10000 });
  });

  it('forces hide after maximum load time', async () => {
    render(<Preloader />);

    // Keep document in loading state
    Object.defineProperty(document, 'readyState', {
      writable: true,
      value: 'loading'
    });

    // Advance to maximum load time (4000ms) + check delay (800ms) + hide delay (100ms) + fade delay (800ms)
    act(() => {
      vi.advanceTimersByTime(5700);
    });

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 10000 });
  });

  it('does not show preloader on subsequent navigation', async () => {
    const { rerender } = render(<Preloader />);

    // Simulate initial load completion
    act(() => {
      vi.advanceTimersByTime(3000);
      Object.defineProperty(document, 'readyState', {
        writable: true,
        value: 'complete'
      });
    });

    // Wait for preloader to hide completely
    act(() => {
      vi.advanceTimersByTime(2000);
    });

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    // Navigate to different page
    mockUsePathname.mockReturnValue('/shop');
    rerender(<Preloader />);

    // Preloader should not be visible
    expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
  });

  it('respects reduced motion preferences', () => {
    // Mock reduced motion preference
    window.matchMedia = vi.fn().mockImplementation(query => ({
      matches: query === '(prefers-reduced-motion: reduce)',
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    }));

    render(<Preloader />);

    // Logo should appear after delay
    act(() => {
      vi.advanceTimersByTime(300);
    });

    // Check that animation classes are not applied
    const logoContainer = screen.getByAltText('Tennis Whisperer').closest('div');
    expect(logoContainer).not.toHaveClass('animate-virtra-logo');
  });

  it('provides proper accessibility attributes', () => {
    render(<Preloader />);
    
    const preloader = screen.getByRole('progressbar');
    expect(preloader).toHaveAttribute('aria-label', 'Loading page content');
    expect(preloader).toHaveAttribute('aria-live', 'polite');
    
    // Check screen reader content
    expect(screen.getByText('Loading content, please wait...')).toBeInTheDocument();
  });

  it('cleans up timeouts on unmount', () => {
    const clearTimeoutSpy = vi.spyOn(global, 'clearTimeout');

    const { unmount } = render(<Preloader />);

    // Advance time to create some timeouts
    act(() => {
      vi.advanceTimersByTime(500);
    });

    unmount();

    // Verify that clearTimeout was called
    expect(clearTimeoutSpy).toHaveBeenCalled();

    clearTimeoutSpy.mockRestore();
  });
});
