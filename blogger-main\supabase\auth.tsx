import { createContext, useContext, useEffect, useState } from "react";
import { User } from "@supabase/supabase-js";
import { supabase } from "./supabase";
import { webSocketService, WebSocketEventType } from "../src/services/websocket";

// Simple logger for development
const logger = {
  info: (message: string, extra?: any) => console.log(`ℹ️ ${message}`, extra),
  warn: (message: string, extra?: any) => console.warn(`⚠️ ${message}`, extra),
  error: (message: string, error?: any) => console.error(`❌ ${message}`, error),
  debug: (message: string, extra?: any) => console.debug(`🐛 ${message}`, extra),
};


type UserWithRole = User & { role?: string };

type AuthContextType = {
  user: UserWithRole | null;
  userRole: string;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<User | null>;
  signUp: (email: string, password: string, fullName: string) => Promise<void>;
  signOut: () => Promise<void>;
  updateUserRole: (userId: string, role: string) => Promise<void>;
  refreshUserRole: () => Promise<void>;
  isAdmin: boolean;
  isConnected: boolean;
  connectionState: string;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<UserWithRole | null>(null);
  const [userRole, setUserRole] = useState<string>('user');
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionState, setConnectionState] = useState('disconnected');

  // Function to fetch user role from the database
  const fetchUserRole = async (userId: string) => {
    try {
      // First check if user exists in profiles table
      const { data: profileData, error: profileError } = await supabase
        .from("profiles")
        .select("role, email, first_name, last_name")
        .eq("id", userId)
        .single();

      if (profileError) {
        console.log("Profile error:", profileError);

        // If profiles table doesn't exist or profile doesn't exist
        if (profileError.code === 'PGRST116' || profileError.code === '42P01') {
          console.log("No profile found, checking if user should be admin...");

          // Check if this user should be admin by counting total profiles
          const { count, error: countError } = await supabase
            .from("profiles")
            .select("*", { count: 'exact', head: true });

          if (countError) {
            console.log("Could not count profiles, defaulting to user role");
            return "user";
          }

          // If no profiles exist, this user should be admin
          const shouldBeAdmin = count === 0;
          const role = shouldBeAdmin ? "admin" : "user";

          console.log(`User should be ${role} (profile count: ${count})`);
          return role;
        }

        return "user";
      }

      const userRole = profileData?.role || "user";
      console.log("Profile found with role:", userRole);
      return userRole;
    } catch (error) {
      console.error("Error fetching user role:", error);
      return "user";
    }
  };

  // Update user with role information
  const updateUserWithRole = async (authUser: User | null) => {
    if (!authUser) {
      setUser(null);
      setUserRole('user');
      setIsAdmin(false);
      setLoading(false);
      return;
    }

    try {
      const role = await fetchUserRole(authUser.id);
      const userWithRole = { ...authUser, role };
      setUser(userWithRole);
      setUserRole(role);
      setIsAdmin(role === "admin");

      // Debug logging
      console.log("User role updated:", {
        userId: authUser.id,
        email: authUser.email,
        role,
        isAdmin: role === "admin"
      });
    } catch (error) {
      console.error("Error updating user role:", error);
      // Set user without role if there's an error
      setUser(authUser);
      setUserRole('user');
      setIsAdmin(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Check active sessions and sets the user
    supabase.auth.getSession().then(({ data: { session } }) => {
      updateUserWithRole(session?.user ?? null);
    });

    // Listen for changes on auth state (signed in, signed out, etc.)
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      updateUserWithRole(session?.user ?? null);

      // Notify WebSocket of auth state change
      if (session?.user) {
        webSocketService.notifyUserCreated({
          id: session.user.id,
          email: session.user.email,
          created_at: session.user.created_at,
        });
      }
    });

    // Setup WebSocket event listeners
    const handleAuthStateChange = (message: any) => {
      setIsConnected(message.payload.connected);
      setConnectionState(webSocketService.getConnectionState());
      logger.info('WebSocket auth state changed', message.payload);
    };

    const handleUserCreated = (message: any) => {
      logger.info('Real-time user created event', message.payload);
      // Refresh user role if it's the current user
      if (message.payload.id === user?.id) {
        refreshUserRole();
      }
    };

    const handleProfileUpdated = (message: any) => {
      logger.info('Real-time profile updated event', message.payload);
      // Refresh user role if it's the current user
      if (message.payload.id === user?.id) {
        refreshUserRole();
      }
    };

    // Add WebSocket event listeners
    webSocketService.addEventListener(WebSocketEventType.AUTH_STATE_CHANGED, handleAuthStateChange);
    webSocketService.addEventListener(WebSocketEventType.USER_CREATED, handleUserCreated);
    webSocketService.addEventListener(WebSocketEventType.PROFILE_UPDATED, handleProfileUpdated);

    // Update connection state
    setIsConnected(webSocketService.isConnected());
    setConnectionState(webSocketService.getConnectionState());

    return () => {
      subscription.unsubscribe();

      // Remove WebSocket event listeners
      webSocketService.removeEventListener(WebSocketEventType.AUTH_STATE_CHANGED, handleAuthStateChange);
      webSocketService.removeEventListener(WebSocketEventType.USER_CREATED, handleUserCreated);
      webSocketService.removeEventListener(WebSocketEventType.PROFILE_UPDATED, handleProfileUpdated);
    };
  }, [user?.id]);

  const signUp = async (email: string, password: string, fullName: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            first_name: fullName.split(' ')[0],
            last_name: fullName.split(' ').slice(1).join(' '),
          },
        },
      });

      if (error) throw error;

      // If user was created successfully, notify WebSocket
      if (data.user) {
        logger.info('User created successfully', {
          userId: data.user.id,
          email: data.user.email
        });

        // Notify WebSocket of new user creation
        webSocketService.notifyUserCreated({
          id: data.user.id,
          email: data.user.email,
          full_name: fullName,
          created_at: data.user.created_at,
          email_confirmed: data.user.email_confirmed_at !== null,
        });

        // Create profile record with optimistic UI
        const profileData = {
          id: data.user.id,
          email: data.user.email,
          first_name: fullName.split(' ')[0],
          last_name: fullName.split(' ').slice(1).join(' '),
          role: 'user',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

        // Insert profile with error handling
        const { error: profileError } = await supabase
          .from('profiles')
          .insert(profileData);

        if (profileError) {
          logger.warn('Profile creation failed, will be handled by trigger', profileError);
        } else {
          logger.info('Profile created successfully', { userId: data.user.id });

          // Notify WebSocket of profile creation
          webSocketService.notifyProfileUpdated(profileData);
        }
      }
    } catch (error) {
      logger.error('Sign up failed', error);
      throw error;
    }
  };

  const signIn = async (email: string, password: string) => {
    console.log('🔐 Attempting to sign in user:', email);

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      console.error('❌ Sign in error:', error);

      // Provide more specific error messages
      if (error.message.includes('Invalid login credentials')) {
        throw new Error('Invalid email or password. Please check your credentials and try again.');
      } else if (error.message.includes('Email not confirmed')) {
        throw new Error('Please check your email and click the confirmation link before signing in.');
      } else if (error.message.includes('Too many requests')) {
        throw new Error('Too many login attempts. Please wait a few minutes and try again.');
      } else {
        throw new Error(error.message || 'Login failed. Please try again.');
      }
    }

    // Wait for the auth state to update and return user info
    if (data.user) {
      console.log('✅ Sign in successful for user:', data.user.email);

      // Update last sign in time in profiles table
      try {
        await supabase
          .from('profiles')
          .update({ last_sign_in_at: new Date().toISOString() })
          .eq('id', data.user.id);
      } catch (profileError) {
        console.warn('⚠️ Could not update last sign in time:', profileError);
        // Don't throw error here, just log it
      }

      await updateUserWithRole(data.user);
      return data.user;
    }
    return null;
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  };

  // Function to refresh current user's role
  const refreshUserRole = async () => {
    if (!user) return;

    console.log('🔄 Refreshing user role...');
    const role = await fetchUserRole(user.id);
    const userWithRole = { ...user, role };
    setUser(userWithRole);
    setUserRole(role);
    setIsAdmin(role === "admin");

    console.log('✅ Role refreshed:', {
      userId: user.id,
      email: user.email,
      newRole: role,
      isAdmin: role === "admin"
    });
  };

  // Function to update a user's role (admin only)
  const updateUserRole = async (userId: string, role: string) => {
    if (!isAdmin) {
      throw new Error("Only admins can update user roles");
    }

    const { error } = await supabase
      .from("profiles")
      .update({ role, updated_at: new Date().toISOString() })
      .eq("id", userId);

    if (error) throw error;

    // If updating current user's role, refresh it
    if (userId === user?.id) {
      await refreshUserRole();
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        userRole,
        loading,
        signIn,
        signUp,
        signOut,
        updateUserRole,
        refreshUserRole,
        isAdmin,
        isConnected,
        connectionState,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
