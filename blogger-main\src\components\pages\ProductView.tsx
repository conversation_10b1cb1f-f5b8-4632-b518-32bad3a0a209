import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { useSEO } from '@/hooks/useSEO';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Navbar } from '@/components/layout/Navbar';
import { Footer } from '@/components/layout/Footer';
import { ScrollToTop } from '@/components/ui/scroll-to-top';
import { useToast } from '@/components/ui/use-toast';
import { useCart } from '@/contexts/CartContext';
import {
  Star,
  ShoppingCart,
  Heart,
  Share2,
  Package,
  Truck,
  Shield,
  ArrowLeft,
  Plus,
  Minus,
  Check,
  Info,
  Bookmark,
} from 'lucide-react';
import { supabase } from '../../../supabase/supabase';
import { formatCurrency } from '@/lib/currency';

interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  price: number;
  sale_price: number | null;
  image_url: string | null;
  gallery: string[] | null;
  type: 'physical' | 'digital' | 'subscription';
  status: 'active' | 'inactive' | 'out_of_stock';
  stock_quantity: number | null;
  rating: number;
  reviews_count: number;
  features: string[] | null;
  specifications: Record<string, string> | null;
  category: {
    name: string;
    color: string;
  } | null;
}

export function ProductView() {
  const { slug } = useParams<{ slug: string }>();
  const { toast } = useToast();
  const { addItem } = useCart();
  const [product, setProduct] = useState<Product | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedImage, setSelectedImage] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [isInWishlist, setIsInWishlist] = useState(false);
  const [isLiked, setIsLiked] = useState(false);

  // SEO for product page - must be called before any conditional returns
  useSEO({
    title: product?.name || 'Product',
    description: product?.description || 'Product details',
    keywords: [
      product?.name || 'product',
      product?.category?.name || 'product',
      product?.type || 'product',
      'ecommerce',
      'shopping',
      'premium',
      ...(product?.features || [])
    ],
    image: product?.image_url || undefined,
    type: 'product',
  });

  useEffect(() => {
    if (slug) {
      loadProduct();
    }
  }, [slug]);

  useEffect(() => {
    if (product && user) {
      loadUserEngagement();
    }
  }, [product, user]);

  const loadProduct = async () => {
    try {
      setIsLoading(true);
      
      // Try to fetch product with category relationship first
      let { data, error } = await supabase
        .from('products')
        .select(`
          *,
          category:categories(name, color)
        `)
        .eq('slug', slug)
        .single();

      // If the relationship doesn't exist, fetch without category
      if (error && error.code === 'PGRST200') {
        const { data: productData, error: productError } = await supabase
          .from('products')
          .select('*')
          .eq('slug', slug)
          .single();

        data = productData;
        error = productError;
      }

      if (error) throw error;

      if (data) {
        // Add fallback category and ensure all required properties exist
        const productWithDefaults = {
          ...data,
          category: data.category || { name: 'Electronics', color: '#3B82F6' },
          rating: data.rating || 4.5,
          reviews_count: data.reviews_count || 0,
          price: data.price || 0,
          sale_price: data.sale_price || null,
          stock_quantity: data.stock_quantity || 0,
          type: data.type || 'physical',
          status: data.status || 'active',
          features: data.features || [],
          specifications: data.specifications || {},
          gallery: data.gallery || []
        };
        setProduct(productWithDefaults);
      } else {
        // Mock product data for demo
        setProduct({
          id: '1',
          name: 'Premium Wireless Headphones',
          slug: slug || '',
          description: 'Experience crystal-clear audio with our premium wireless headphones. Featuring advanced noise cancellation, 30-hour battery life, and premium comfort design.',
          price: 299.99,
          sale_price: 249.99,
          image_url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500',
          gallery: [
            'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500',
            'https://images.unsplash.com/photo-1484704849700-f032a568e944?w=500',
            'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=500',
          ],
          type: 'physical',
          status: 'active',
          stock_quantity: 25,
          rating: 4.8,
          reviews_count: 342,
          features: [
            'Active Noise Cancellation',
            '30-hour battery life',
            'Quick charge (5 min = 3 hours)',
            'Premium comfort design',
            'Wireless & wired connectivity',
            'Built-in microphone'
          ],
          specifications: {
            'Driver Size': '40mm',
            'Frequency Response': '20Hz - 20kHz',
            'Impedance': '32 ohms',
            'Weight': '250g',
            'Connectivity': 'Bluetooth 5.0, 3.5mm jack',
            'Battery': '30 hours wireless, 40 hours wired'
          },
          category: { name: 'Electronics', color: '#3B82F6' }
        });
      }
    } catch (error) {
      console.error('Error loading product:', error);
      toast({
        title: 'Error',
        description: 'Failed to load product details',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadUserEngagement = async () => {
    if (!user || !product) return;

    try {
      const { data, error } = await supabase
        .from('product_engagement')
        .select('liked, bookmarked')
        .eq('user_id', user.id)
        .eq('product_id', product.id)
        .single();

      if (data) {
        setIsLiked(data.liked || false);
        setIsInWishlist(data.bookmarked || false);
      }
    } catch (error) {
      // User hasn't engaged with this product yet
    }
  };

  const handleLike = async () => {
    if (!user || !product) {
      toast({
        title: 'Please sign in',
        description: 'You need to be signed in to like products',
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('product_engagement')
        .upsert({
          user_id: user.id,
          product_id: product.id,
          liked: !isLiked,
          bookmarked: isInWishlist,
        });

      if (error) throw error;

      setIsLiked(!isLiked);
      toast({
        title: isLiked ? 'Removed from likes' : 'Added to likes',
        description: isLiked
          ? 'Product removed from your likes'
          : 'Product added to your likes',
      });
    } catch (error) {
      console.error('Error updating like:', error);
      toast({
        title: 'Error',
        description: 'Failed to update like',
        variant: 'destructive',
      });
    }
  };

  const handleWishlist = async () => {
    if (!user || !product) {
      toast({
        title: 'Please sign in',
        description: 'You need to be signed in to add to wishlist',
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('product_engagement')
        .upsert({
          user_id: user.id,
          product_id: product.id,
          liked: isLiked,
          bookmarked: !isInWishlist,
        });

      if (error) throw error;

      setIsInWishlist(!isInWishlist);
      toast({
        title: isInWishlist ? 'Removed from wishlist' : 'Added to wishlist',
        description: isInWishlist
          ? 'Product removed from your wishlist'
          : 'Product added to your wishlist',
      });
    } catch (error) {
      console.error('Error updating wishlist:', error);
      toast({
        title: 'Error',
        description: 'Failed to update wishlist',
        variant: 'destructive',
      });
    }
  };

  const handleAddToCart = async () => {
    if (!product) return;
    
    try {
      setIsAddingToCart(true);
      
      await addItem({
        id: product.id,
        name: product.name,
        price: product.sale_price || product.price,
        images: [product.image_url || ''],
        stock: product.stock_quantity || 0,
        featured: false,
        tags: [],
        createdAt: new Date(),
        updatedAt: new Date()
      }, quantity);

      toast({
        title: 'Added to cart!',
        description: `${quantity} ${product.name} added to your cart`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to add item to cart',
        variant: 'destructive',
      });
    } finally {
      setIsAddingToCart(false);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: product?.name,
          text: product?.description,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      toast({
        title: 'Link copied!',
        description: 'Product link copied to clipboard',
      });
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="h-96 bg-gray-200 rounded-lg"></div>
                <div className="flex gap-2">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="h-20 w-20 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </div>
              <div className="space-y-4">
                <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                <div className="h-20 bg-gray-200 rounded"></div>
                <div className="h-12 bg-gray-200 rounded w-1/3"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-7xl mx-auto px-4 py-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
          <p className="text-gray-600 mb-6">The product you're looking for doesn't exist.</p>
          <Link to="/products">
            <Button>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Products
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  // Add safety checks for product properties
  const currentPrice = product.sale_price || product.price || 0;
  const originalPrice = product.sale_price ? product.price : null;
  const discount = originalPrice ? Math.round(((originalPrice - currentPrice) / originalPrice) * 100) : 0;
  const images = product.gallery || [product.image_url].filter(Boolean);
  const rating = product.rating || 4.5;
  const reviewsCount = product.reviews_count || 0;



  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-6">
          <Link to="/" className="hover:text-blue-600">Home</Link>
          <span>/</span>
          <Link to="/products" className="hover:text-blue-600">Products</Link>
          <span>/</span>
          <span className="text-gray-900">{product.name}</span>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="relative overflow-hidden rounded-lg bg-white">
              <img
                src={images[selectedImage] || product.image_url || ''}
                alt={product.name}
                className="w-full h-96 object-cover"
              />
              {discount > 0 && (
                <Badge className="absolute top-4 left-4 bg-red-600 text-white">
                  -{discount}%
                </Badge>
              )}
            </div>
            
            {images.length > 1 && (
              <div className="flex gap-2 overflow-x-auto">
                {images.map((image, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={() => setSelectedImage(index)}
                    className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${
                      selectedImage === index ? 'border-blue-600' : 'border-gray-200'
                    }`}
                  >
                    <img
                      src={image || ''}
                      alt={`${product.name} ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Details */}
          <div className="space-y-6">
            {/* Category */}
            {product.category && (
              <Badge variant="outline" style={{ borderColor: product.category.color }}>
                {product.category.name}
              </Badge>
            )}

            {/* Title */}
            <h1 className="text-3xl font-bold text-gray-900">{product.name}</h1>

            {/* Rating */}
            <div className="flex items-center gap-4">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-5 w-5 ${
                      i < Math.floor(rating)
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-gray-600">
                {rating.toFixed(1)} ({reviewsCount} reviews)
              </span>
            </div>

            {/* Price */}
            <div className="flex items-center gap-3">
              <span className="text-3xl font-bold text-gray-900">
                {formatCurrency(currentPrice)}
              </span>
              {originalPrice && (
                <span className="text-xl text-gray-500 line-through">
                  {formatCurrency(originalPrice)}
                </span>
              )}
            </div>

            {/* Description */}
            <p className="text-gray-600 leading-relaxed">{product.description}</p>

            {/* Stock Status */}
            <div className="flex items-center gap-2">
              {product.type === 'physical' && product.stock_quantity !== null && (
                <>
                  {product.stock_quantity > 0 ? (
                    <>
                      <Check className="h-4 w-4 text-green-600" />
                      <span className="text-green-600 font-medium">
                        In Stock ({product.stock_quantity} available)
                      </span>
                    </>
                  ) : (
                    <>
                      <Info className="h-4 w-4 text-red-600" />
                      <span className="text-red-600 font-medium">Out of Stock</span>
                    </>
                  )}
                </>
              )}
              {product.type === 'digital' && (
                <>
                  <Check className="h-4 w-4 text-green-600" />
                  <span className="text-green-600 font-medium">Instant Download</span>
                </>
              )}
            </div>

            {/* Quantity Selector */}
            {product.type === 'physical' && product.stock_quantity && product.stock_quantity > 0 && (
              <div className="flex items-center gap-4">
                <span className="font-medium">Quantity:</span>
                <div className="flex items-center border rounded-lg">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    disabled={quantity <= 1}
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                  <span className="px-4 py-2 font-medium">{quantity}</span>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setQuantity(Math.min(product.stock_quantity || 1, quantity + 1))}
                    disabled={quantity >= (product.stock_quantity || 1)}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-4">
              <Button
                onClick={handleAddToCart}
                disabled={isAddingToCart || (product.type === 'physical' && product.stock_quantity === 0)}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                size="lg"
              >
                {isAddingToCart ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Adding...
                  </>
                ) : (
                  <>
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    Add to Cart
                  </>
                )}
              </Button>
              
              <Button
                variant="outline"
                size="lg"
                onClick={handleLike}
                className={isLiked ? 'text-red-600 border-red-600' : ''}
              >
                <Heart className={`h-4 w-4 ${isLiked ? 'fill-current text-red-600' : ''}`} />
              </Button>

              <Button
                variant="outline"
                size="lg"
                onClick={handleWishlist}
                className={isInWishlist ? 'text-blue-600 border-blue-600' : ''}
              >
                <Bookmark className={`h-4 w-4 ${isInWishlist ? 'fill-current text-blue-600' : ''}`} />
              </Button>
              
              <Button variant="outline" size="lg" onClick={handleShare}>
                <Share2 className="h-4 w-4" />
              </Button>
            </div>

            {/* Features */}
            {product.features && product.features.length > 0 && (
              <Card>
                <CardContent className="p-4">
                  <h3 className="font-semibold mb-3">Key Features</h3>
                  <ul className="space-y-2">
                    {product.features.map((feature, index) => (
                      <li key={index} className="flex items-center gap-2 text-sm">
                        <Check className="h-4 w-4 text-green-600 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Shipping & Security Info */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Truck className="h-4 w-4 text-blue-600" />
                <span>Free shipping over $50</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Shield className="h-4 w-4 text-green-600" />
                <span>Secure checkout</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Package className="h-4 w-4 text-purple-600" />
                <span>30-day returns</span>
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="mt-16">
          <Tabs defaultValue="description" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="description">Description</TabsTrigger>
              <TabsTrigger value="specifications">Specifications</TabsTrigger>
              <TabsTrigger value="reviews">Reviews</TabsTrigger>
            </TabsList>

            <TabsContent value="description" className="mt-6">
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold mb-4">Product Description</h3>
                  <div className="prose prose-gray max-w-none">
                    <p className="text-gray-700 leading-relaxed mb-4">
                      {product.description}
                    </p>
                    <p className="text-gray-700 leading-relaxed">
                      Our premium products are designed with the highest quality materials and cutting-edge technology.
                      Each item undergoes rigorous testing to ensure it meets our strict quality standards and provides
                      exceptional value to our customers.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="specifications" className="mt-6">
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold mb-4">Technical Specifications</h3>
                  {product.specifications ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {Object.entries(product.specifications).map(([key, value]) => (
                        <div key={key} className="flex justify-between py-2 border-b border-gray-100">
                          <span className="font-medium text-gray-900">{key}:</span>
                          <span className="text-gray-600">{value}</span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-600">No specifications available for this product.</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="reviews" className="mt-6">
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold mb-4">Customer Reviews</h3>
                  <div className="space-y-6">
                    {/* Review Summary */}
                    <div className="flex items-center gap-6 pb-6 border-b">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-gray-900">{rating.toFixed(1)}</div>
                        <div className="flex items-center justify-center mt-1">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${
                                i < Math.floor(rating)
                                  ? 'text-yellow-400 fill-current'
                                  : 'text-gray-300'
                              }`}
                            />
                          ))}
                        </div>
                        <div className="text-sm text-gray-600 mt-1">
                          {reviewsCount} reviews
                        </div>
                      </div>

                      <div className="flex-1">
                        {[5, 4, 3, 2, 1].map((rating) => (
                          <div key={rating} className="flex items-center gap-2 mb-1">
                            <span className="text-sm w-8">{rating}★</span>
                            <div className="flex-1 bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-yellow-400 h-2 rounded-full"
                                style={{ width: `${Math.random() * 80 + 10}%` }}
                              ></div>
                            </div>
                            <span className="text-sm text-gray-600 w-8">
                              {Math.floor(Math.random() * 50)}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Sample Reviews */}
                    <div className="space-y-4">
                      {[
                        {
                          name: "Sarah Johnson",
                          rating: 5,
                          date: "2024-01-15",
                          comment: "Excellent quality and fast shipping. Highly recommended!"
                        },
                        {
                          name: "Mike Chen",
                          rating: 4,
                          date: "2024-01-10",
                          comment: "Great product, exactly as described. Very satisfied with my purchase."
                        },
                        {
                          name: "Emily Davis",
                          rating: 5,
                          date: "2024-01-08",
                          comment: "Outstanding customer service and product quality. Will buy again!"
                        }
                      ].map((review, index) => (
                        <div key={index} className="border-b border-gray-100 pb-4">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{review.name}</span>
                              <div className="flex items-center">
                                {[...Array(5)].map((_, i) => (
                                  <Star
                                    key={i}
                                    className={`h-3 w-3 ${
                                      i < review.rating
                                        ? 'text-yellow-400 fill-current'
                                        : 'text-gray-300'
                                    }`}
                                  />
                                ))}
                              </div>
                            </div>
                            <span className="text-sm text-gray-500">{review.date}</span>
                          </div>
                          <p className="text-gray-700">{review.comment}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      <Footer />
      <ScrollToTop />
    </div>
  );
}
