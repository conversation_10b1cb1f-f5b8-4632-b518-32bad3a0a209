import { FormMessage, Message } from "@/components/form-message";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
//import { SmtpMessage } from "../smtp-message";
import { signUpAction, checkoutSignUpAction, mentorshipSignUpAction } from "@/app/actions";
import Navbar from "@/components/navbar";
import { UrlProvider } from "@/components/url-provider";
import Footer from "@/components/footer";
import { FaRegUser, FaRegEnvelope, FaLock } from "react-icons/fa";
import Image from "next/image";

export default async function Signup(props: {
  searchParams: Promise<Message & { redirect?: string }>;
}) {
  const searchParams = await props.searchParams;
  if ("message" in searchParams) {
    return (
      <div className="flex h-screen w-full flex-1 items-center justify-center p-4 sm:max-w-md">
        <FormMessage message={searchParams} />
      </div>
    );
  }

  // Detect signup context based on redirect URL
  const redirectTo = searchParams.redirect;
  const isCheckoutSignup = redirectTo === '/checkout';
  const isMentorshipSignup = redirectTo?.includes('/mentorship') || redirectTo?.includes('program=');

  // Choose appropriate signup action based on context
  let signupAction = signUpAction;
  if (isCheckoutSignup) {
    signupAction = checkoutSignUpAction;
  } else if (isMentorshipSignup) {
    signupAction = mentorshipSignUpAction;
  }

  return (
    <>
      {/* Desktop Navbar */}
      <div className="hidden md:block">
        {/* <Navbar /> */}
      </div>

      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-background via-muted/20 to-background px-4 py-8 relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 overflow-hidden -z-10">
          <div className="absolute h-[400px] w-[400px] rounded-full gradient-purple top-[-200px] left-[-200px] blur-[100px] opacity-30 animate-pulse"></div>
          <div className="absolute h-[300px] w-[300px] rounded-full gradient-blue bottom-[-150px] right-[-150px] blur-[80px] opacity-30 animate-pulse"></div>
        </div>

        <div className="w-full max-w-md">
          {/* Logo Avatar */}
          <div className="text-center mb-8">
            <div className="relative mx-auto w-28 h-28 mb-6">
              <div className="w-full h-full rounded-full p-1 neo-shadow">
                <div className="w-full h-full rounded-full bg-blur flex items-center justify-center">
                  <Link
                    href="/"
                    className="text-center"
                  >
                  <Image
                    src="/logo.svg"
                    alt="Logo"
                    width={70}
                    height={70}
                    className=" object-contain"
                  />
                  </Link>
                </div>
              </div>
            </div>
            <h1 className="text-2xl font-bold tracking-tight text-foreground mb-2">CREATE ACCOUNT</h1>
            <p className="text-muted-foreground text-sm">
              Join Tennis Whisperer
            </p>
          </div>

          <div className="glass-effect-dark rounded-3xl p-8 neo-shadow">
            <UrlProvider>
              <form className="flex flex-col space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="relative">
                      <Input
                        id="full_name"
                        name="full_name"
                        type="text"
                        placeholder="full name"
                        required
                        className="w-full h-14 pl-12 rounded-2xl bg-muted/30 border-0 neo-shadow-inset placeholder:text-muted-foreground/70"
                      />
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                                              <span className="text-sm"><FaRegUser /></span>
                                          </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="relative">
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        placeholder="email"
                        required
                        className="w-full h-14 pl-12 rounded-2xl bg-muted/30 border-0 neo-shadow-inset placeholder:text-muted-foreground/70"
                      />
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                          <span className="text-sm"><FaRegEnvelope /></span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="relative">
                      <Input
                        id="password"
                        type="password"
                        name="password"
                        placeholder="password"
                        minLength={6}
                        required
                        className="w-full h-14 pl-12 rounded-2xl bg-muted/30 border-0 neo-shadow-inset placeholder:text-muted-foreground/70"
                      />
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                          
                          <span className="text-sm"><FaLock /></span>
                      </div>
                    </div>
                  </div>
                </div>

                <SubmitButton
                  formAction={signupAction}
                  pendingText="Creating account..."
                  className="w-full h-14 rounded-2xl gradient-blue text-white font-semibold text-lg neo-shadow hover:neo-shadow-light transition-neo border-0"
                >
                  Create Account
                </SubmitButton>

                <FormMessage message={searchParams} />

                <div className="text-center text-sm text-muted-foreground">
                  Already have an account?{" "}
                  <Link
                    className="text-primary font-medium hover:underline transition-colors"
                    href="/sign-in"
                  >
                    Sign in
                  </Link>
                </div>
              </form>
            </UrlProvider>
          </div>

          <div className="mt-6">
            {/* <SmtpMessage /> */}
          </div>
        </div>
      </div>
     {/* <Footer /> */}
    </>
  );
}
