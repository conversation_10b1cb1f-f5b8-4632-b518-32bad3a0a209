import React from 'react';
import { Link } from 'react-router-dom';
import { Heart, ShoppingCart, Star, Eye, Download, Package } from 'lucide-react';
import { NeomorphismCard, NeomorphismButton, NeomorphismBadge, NeomorphismToggle } from './neomorphism-card';
import { formatCurrency } from '@/lib/currency';
import { cn } from '@/lib/utils';

interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  price: number;
  sale_price?: number;
  image_url?: string;
  type: 'physical' | 'digital';
  status: 'active' | 'inactive' | 'draft';
  is_featured?: boolean;
  stock_quantity?: number;
  category?: {
    name: string;
    color: string;
  };
}

interface ProductCardProps {
  product: Product;
  onAddToCart?: (product: Product) => void;
  onToggleFavorite?: (productId: string) => void;
  onToggleFeatured?: (productId: string, featured: boolean) => void;
  isFavorited?: boolean;
  isAdmin?: boolean;
  className?: string;
  variant?: 'default' | 'compact' | 'featured';
}

export function ProductCard({
  product,
  onAddToCart,
  onToggleFavorite,
  onToggleFeatured,
  isFavorited = false,
  isAdmin = false,
  className,
  variant = 'default',
}: ProductCardProps) {
  const currentPrice = product.sale_price || product.price;
  const originalPrice = product.sale_price ? product.price : null;
  const discountPercentage = originalPrice 
    ? Math.round(((originalPrice - currentPrice) / originalPrice) * 100)
    : 0;

  const isOutOfStock = product.type === 'physical' && (product.stock_quantity || 0) <= 0;

  const cardSizes = {
    default: 'h-[420px]',
    compact: 'h-[360px]',
    featured: 'h-[480px]',
  };

  const imageSizes = {
    default: 'h-48',
    compact: 'h-40',
    featured: 'h-56',
  };

  return (
    <NeomorphismCard
      className={cn(
        'group overflow-hidden transition-all duration-300 hover:scale-[1.02]',
        cardSizes[variant],
        className
      )}
      variant="default"
      size="md"
      interactive
    >
      <div className="relative h-full flex flex-col">
        {/* Image Section */}
        <div className={cn('relative overflow-hidden rounded-lg mb-4', imageSizes[variant])}>
          <img
            src={product.image_url || '/api/placeholder/300/200'}
            alt={product.name}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
          />
          
          {/* Overlay Badges */}
          <div className="absolute top-2 left-2 flex flex-col gap-1">
            {product.is_featured && (
              <NeomorphismBadge variant="warning" className="text-xs">
                ⭐ Featured
              </NeomorphismBadge>
            )}
            {product.type === 'digital' && (
              <NeomorphismBadge variant="info" className="text-xs">
                <Download className="h-3 w-3 mr-1" />
                Digital
              </NeomorphismBadge>
            )}
            {discountPercentage > 0 && (
              <NeomorphismBadge variant="danger" className="text-xs">
                -{discountPercentage}%
              </NeomorphismBadge>
            )}
            {isOutOfStock && (
              <NeomorphismBadge variant="default" className="text-xs bg-gray-200">
                Out of Stock
              </NeomorphismBadge>
            )}
          </div>

          {/* Action Buttons */}
          <div className="absolute top-2 right-2 flex flex-col gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <NeomorphismButton
              size="sm"
              variant="secondary"
              onClick={() => onToggleFavorite?.(product.id)}
              className="p-2 w-8 h-8"
            >
              <Heart className={cn('h-3 w-3', isFavorited && 'fill-red-500 text-red-500')} />
            </NeomorphismButton>
            <Link to={`/products/${product.slug}`}>
              <NeomorphismButton
                size="sm"
                variant="secondary"
                className="p-2 w-8 h-8"
              >
                <Eye className="h-3 w-3" />
              </NeomorphismButton>
            </Link>
          </div>

          {/* Stock Indicator for Physical Products */}
          {product.type === 'physical' && product.stock_quantity !== undefined && (
            <div className="absolute bottom-2 left-2">
              <NeomorphismBadge 
                variant={product.stock_quantity > 10 ? 'success' : product.stock_quantity > 0 ? 'warning' : 'danger'}
                className="text-xs"
              >
                <Package className="h-3 w-3 mr-1" />
                {product.stock_quantity > 0 ? `${product.stock_quantity} left` : 'Out of stock'}
              </NeomorphismBadge>
            </div>
          )}
        </div>

        {/* Content Section */}
        <div className="flex-1 flex flex-col">
          {/* Category */}
          {product.category && (
            <NeomorphismBadge 
              variant="default" 
              className="text-xs mb-2 w-fit"
              style={{ backgroundColor: `${product.category.color}20` }}
            >
              {product.category.name}
            </NeomorphismBadge>
          )}

          {/* Title */}
          <Link to={`/products/${product.slug}`}>
            <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 hover:text-blue-600 transition-colors">
              {product.name}
            </h3>
          </Link>

          {/* Description */}
          <p className="text-sm text-gray-600 mb-3 line-clamp-2 flex-1">
            {product.description}
          </p>

          {/* Price Section */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <span className="text-lg font-bold text-gray-900">
                {formatCurrency(currentPrice)}
              </span>
              {originalPrice && (
                <span className="text-sm text-gray-500 line-through">
                  {formatCurrency(originalPrice)}
                </span>
              )}
            </div>
            
            {/* Rating (placeholder) */}
            <div className="flex items-center gap-1">
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
              <span className="text-sm text-gray-600">4.5</span>
            </div>
          </div>

          {/* Admin Controls */}
          {isAdmin && (
            <div className="flex items-center justify-between mb-3 p-2 bg-gray-50 rounded-lg">
              <span className="text-xs text-gray-600">Featured:</span>
              <NeomorphismToggle
                checked={product.is_featured || false}
                onChange={(checked) => onToggleFeatured?.(product.id, checked)}
              />
            </div>
          )}

          {/* Action Button */}
          <NeomorphismButton
            variant="primary"
            className="w-full"
            onClick={() => onAddToCart?.(product)}
            disabled={isOutOfStock}
          >
            <ShoppingCart className="h-4 w-4 mr-2" />
            {isOutOfStock ? 'Out of Stock' : 'Add to Cart'}
          </NeomorphismButton>
        </div>
      </div>
    </NeomorphismCard>
  );
}

interface ProductGridProps {
  products: Product[];
  onAddToCart?: (product: Product) => void;
  onToggleFavorite?: (productId: string) => void;
  onToggleFeatured?: (productId: string, featured: boolean) => void;
  favoriteIds?: string[];
  isAdmin?: boolean;
  className?: string;
  variant?: 'default' | 'compact' | 'featured';
}

export function ProductGrid({
  products,
  onAddToCart,
  onToggleFavorite,
  onToggleFeatured,
  favoriteIds = [],
  isAdmin = false,
  className,
  variant = 'default',
}: ProductGridProps) {
  const gridCols = {
    default: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
    compact: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5',
    featured: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
  };

  return (
    <div className={cn('grid gap-6', gridCols[variant], className)}>
      {products.map((product) => (
        <ProductCard
          key={product.id}
          product={product}
          onAddToCart={onAddToCart}
          onToggleFavorite={onToggleFavorite}
          onToggleFeatured={onToggleFeatured}
          isFavorited={favoriteIds.includes(product.id)}
          isAdmin={isAdmin}
          variant={variant}
        />
      ))}
    </div>
  );
}
