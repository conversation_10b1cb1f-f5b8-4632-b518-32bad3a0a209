import { NextRequest, NextResponse } from 'next/server';
import { createClient, createServiceRoleClient } from '@/utils/supabase/server';

/**
 * GET /api/admin/users/stats
 * Get user statistics for dashboard
 */
export async function GET(request: NextRequest) {
  try {
    console.log('GET /api/admin/users/stats - Request received');

    // Check authentication
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin role
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || userData.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 });
    }

    console.log('GET /api/admin/users/stats - Admin access confirmed');

    // Use service role client for database operations
    const serviceSupabase = createServiceRoleClient();

    // Get user statistics
    const { data: users, error, count } = await serviceSupabase
      .from('users')
      .select('id, role, created_at', { count: 'exact' });

    if (error) {
      console.error('GET /api/admin/users/stats - Database error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch user statistics', details: error.message },
        { status: 500 }
      );
    }

    // Calculate role distribution
    const totalUsers = count || 0;
    const roleDistribution = users?.reduce((acc, user) => {
      acc[user.role] = (acc[user.role] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    // Calculate recent signups
    const today = new Date();
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
    
    const recentUsers = users?.filter(user => 
      new Date(user.created_at) >= lastMonth
    ).length || 0;

    const stats = {
      total: totalUsers,
      admins: roleDistribution.admin || 0,
      users: roleDistribution.user || 0,
      students: roleDistribution.student || 0,
      recent: recentUsers,
      growth: totalUsers > 0 ? ((recentUsers / totalUsers) * 100).toFixed(1) : '0.0'
    };

    console.log('GET /api/admin/users/stats - Success:', stats);

    return NextResponse.json(stats);

  } catch (error: any) {
    console.error('GET /api/admin/users/stats - General error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
