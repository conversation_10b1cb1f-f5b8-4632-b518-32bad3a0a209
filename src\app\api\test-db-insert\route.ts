import { createClient } from "@/utils/supabase/server";
import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    // Prevent automatic calls during development/compilation
    const userAgent = request.headers.get('user-agent') || '';
    if (userAgent.includes('Next.js') || userAgent.includes('webpack')) {
      return NextResponse.json({
        success: false,
        error: "Test endpoint not available during compilation"
      }, { status: 503 });
    }

    // Validate request has proper content type
    const contentType = request.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      return NextResponse.json({
        success: false,
        error: "Content-Type must be application/json"
      }, { status: 400 });
    }

    const body = await request.json();
    const { email, fullName, role } = body;

    // Validate required parameters
    if (!email || !fullName) {
      return NextResponse.json({
        success: false,
        error: "Missing required parameters: email, fullName"
      }, { status: 400 });
    }

    const supabase = await createClient();

    console.log("Testing database insert with:", { email, fullName, role });

    // Generate a test UUID
    const testId = crypto.randomUUID();

    // Try to insert a test user
    const { data, error } = await supabase
      .from('users')
      .insert([
        {
          id: testId,
          email: email,
          full_name: fullName,
          name: fullName,
          role: role || 'admin',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
      ])
      .select()
      .single();

    if (error) {
      console.error("Database insert error:", error);
      return NextResponse.json({
        success: false,
        error: {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        }
      }, { status: 400 });
    }

    // Clean up the test user
    await supabase
      .from('users')
      .delete()
      .eq('id', testId);

    return NextResponse.json({
      success: true,
      message: "Database insert test successful",
      data: data
    });

  } catch (error: any) {
    console.error("API error:", error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    const supabase = await createClient();
    
    // Test basic database connection
    const { data, error } = await supabase
      .from('users')
      .select('id, email, role')
      .limit(1);

    if (error) {
      return NextResponse.json({
        success: false,
        error: {
          code: error.code,
          message: error.message
        }
      }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      message: "Database connection successful",
      userCount: data?.length || 0
    });

  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
