-- Admin Authentication Setup for <PERSON> Whisperer
-- Run this in your Supabase SQL Editor to ensure proper admin authentication

-- 1. Check if user_role enum exists and has correct values
DO $$
BEGIN
    -- Check if user_role enum exists
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        -- Create user_role enum if it doesn't exist
        CREATE TYPE user_role AS ENUM ('user', 'admin');
        RAISE NOTICE 'Created user_role enum';
    ELSE
        -- Check if 'admin' value exists in the enum
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum 
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role') 
            AND enumlabel = 'admin'
        ) THEN
            -- Add 'admin' to existing enum
            ALTER TYPE user_role ADD VALUE 'admin';
            RAISE NOTICE 'Added admin value to user_role enum';
        END IF;
    END IF;
END $$;

-- 2. Ensure users table has correct structure
-- Check if users table exists and has required columns
DO $$
BEGIN
    -- Check if token_identifier column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'users' 
        AND column_name = 'token_identifier'
    ) THEN
        -- Add token_identifier column if missing
        ALTER TABLE public.users ADD COLUMN token_identifier TEXT;
        RAISE NOTICE 'Added token_identifier column to users table';
    END IF;

    -- Check if role column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'users' 
        AND column_name = 'role'
    ) THEN
        -- Add role column if missing
        ALTER TABLE public.users ADD COLUMN role user_role NOT NULL DEFAULT 'user';
        RAISE NOTICE 'Added role column to users table';
    END IF;

    -- Check if full_name column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'users' 
        AND column_name = 'full_name'
    ) THEN
        -- Add full_name column if missing
        ALTER TABLE public.users ADD COLUMN full_name TEXT;
        RAISE NOTICE 'Added full_name column to users table';
    END IF;

    -- Check if name column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'users' 
        AND column_name = 'name'
    ) THEN
        -- Add name column if missing
        ALTER TABLE public.users ADD COLUMN name TEXT;
        RAISE NOTICE 'Added name column to users table';
    END IF;
END $$;

-- 3. Update token_identifier to be nullable if it's currently NOT NULL
-- This prevents issues when creating new users
ALTER TABLE public.users ALTER COLUMN token_identifier DROP NOT NULL;

-- 4. Create or update RLS policies for admin access
-- Enable RLS on users table
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Admins can view all users" ON public.users;
DROP POLICY IF EXISTS "Admins can manage all users" ON public.users;

-- Create new RLS policies
-- Users can view their own profile
CREATE POLICY "Users can view own profile" 
  ON public.users 
  FOR SELECT 
  USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" 
  ON public.users 
  FOR UPDATE 
  USING (auth.uid() = id);

-- Admins can view all users
CREATE POLICY "Admins can view all users" 
  ON public.users 
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Admins can manage all users
CREATE POLICY "Admins can manage all users" 
  ON public.users 
  FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Allow authenticated users to insert their own profile
CREATE POLICY "Users can insert own profile" 
  ON public.users 
  FOR INSERT 
  WITH CHECK (auth.uid() = id);

-- 5. Create a function to automatically create user profiles
-- This ensures that when someone signs up, their profile is created
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, full_name, name, role, token_identifier, created_at, updated_at)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE((NEW.raw_user_meta_data->>'role')::user_role, 'user'),
    NEW.id,
    NOW(),
    NOW()
  )
  ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = COALESCE(EXCLUDED.full_name, public.users.full_name),
    name = COALESCE(EXCLUDED.name, public.users.name),
    role = COALESCE(EXCLUDED.role, public.users.role),
    updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for automatic user profile creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 6. Grant necessary permissions
-- Grant usage on the user_role type
GRANT USAGE ON TYPE user_role TO authenticated;
GRANT USAGE ON TYPE user_role TO anon;

-- Grant permissions on users table
GRANT SELECT, INSERT, UPDATE ON public.users TO authenticated;
GRANT SELECT ON public.users TO anon;

-- 7. Verification queries
-- Check the current structure
SELECT 
  'users table structure' as info,
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'users'
ORDER BY ordinal_position;

-- Check user_role enum values
SELECT 
  'user_role enum values' as info,
  enumlabel as role_value
FROM pg_enum 
WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')
ORDER BY enumsortorder;

-- Check RLS policies
SELECT
  'RLS policies' as info,
  policyname,
  cmd,
  permissive
FROM pg_policies
WHERE tablename = 'users'
AND schemaname = 'public';

-- Final completion messages
DO $$
BEGIN
    RAISE NOTICE 'Admin authentication setup completed successfully!';
    RAISE NOTICE 'You can now use the admin sign-up form with the access code.';
    RAISE NOTICE 'Default admin access code: TENNIS_ADMIN_2024';
END $$;
