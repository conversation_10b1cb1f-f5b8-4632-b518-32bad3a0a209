"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import { ProductCard } from "@/components/product-card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Filter, Grid3X3, List, SlidersHorizontal } from "lucide-react";
import { searchProductsClient, getCategoriesClient } from "@/utils/supabase/products-client";
import { Product } from "@/utils/supabase/products";

/**
 * SearchPageClient component
 * Handles client-side search functionality for products
 */
export default function SearchPageClient() {
  const searchParams = useSearchParams();
  const query = searchParams.get("q") || "";
  
  const [searchQuery, setSearchQuery] = useState(query);
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<{name: string, value: string}[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [priceRange, setPriceRange] = useState<{min: string, max: string}>({min: "", max: ""});
  const [loading, setLoading] = useState(true);
  const [showFilters, setShowFilters] = useState(false);
  
  // Fetch products based on search query
  useEffect(() => {
    async function fetchSearchResults() {
      setLoading(true);
      try {
        if (query) {
          const results = await searchProductsClient(query);
          setProducts(results);
        } else {
          setProducts([]);
        }
        
        // Fetch categories for filters
        const categoriesData = await getCategoriesClient();
        if (categoriesData.length > 0) {
          const formattedCategories = categoriesData.map(cat => ({
            name: cat.name,
            value: cat.name.toLowerCase()
          }));
          setCategories(formattedCategories);
        }
      } catch (error) {
        console.error('Error fetching search results:', error);
      } finally {
        setLoading(false);
      }
    }
    
    fetchSearchResults();
  }, [query]);
  
  // Handle category selection
  const handleCategoryToggle = (category: string) => {
    setSelectedCategories(prev => {
      if (prev.includes(category)) {
        return prev.filter(cat => cat !== category);
      } else {
        return [...prev, category];
      }
    });
  };
  
  // Handle price range input
  const handlePriceChange = (type: 'min' | 'max', value: string) => {
    setPriceRange(prev => ({
      ...prev,
      [type]: value
    }));
  };
  
  // Apply filters to search results
  const applyFilters = () => {
    let filtered = [...products];
    
    // Filter by selected categories
    if (selectedCategories.length > 0) {
      filtered = filtered.filter(product => 
        selectedCategories.includes(product.category.toLowerCase())
      );
    }
    
    // Filter by price range
    if (priceRange.min) {
      filtered = filtered.filter(product => 
        product.price >= parseFloat(priceRange.min)
      );
    }
    
    if (priceRange.max) {
      filtered = filtered.filter(product => 
        product.price <= parseFloat(priceRange.max)
      );
    }
    
    return filtered;
  };
  
  // Get filtered products
  const filteredProducts = applyFilters();
  
  // Handle new search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Update URL with search query
      const url = new URL(window.location.href);
      url.searchParams.set('q', searchQuery);
      window.history.pushState({}, '', url.toString());
      
      // Fetch results
      setLoading(true);
      searchProductsClient(searchQuery)
        .then(results => {
          setProducts(results);
        })
        .catch(error => {
          console.error('Error searching products:', error);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };
  
  return (
    <div className="flex flex-col min-h-screen">
      {/* Desktop Navbar */}
      <div className="hidden md:block">
        <Navbar />
      </div>

      <main className="flex-grow container mx-auto px-4 pt-4 md:pt-8 pb-24 md:pb-8">
        {/* Mobile Header */}
        <div className="md:hidden mb-6">
          <h1 className="text-2xl font-bold text-center mb-4">Search</h1>
        </div>

        {/* Search header */}
        <div className="mb-8">
          <div className="hidden md:block">
            <h1 className="text-3xl font-bold mb-4">
              {query ? `Search results for "${query}"` : 'Search Products'}
            </h1>
          </div>

          <form onSubmit={handleSearch} className="max-w-2xl mx-auto md:max-w-lg md:mx-0">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search for products..."
                className="pl-12 pr-16 h-14 rounded-2xl bg-muted/30 border-0 neo-shadow-light text-lg md:h-10 md:text-base md:pl-10 md:pr-4 md:rounded-md"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <Button
                type="submit"
                size="sm"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-10 px-4 rounded-xl bg-primary hover:bg-primary/90 md:relative md:ml-2 md:transform-none"
              >
                Search
              </Button>
            </div>
          </form>
        </div>
        
        {/* Main content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters sidebar */}
          <div className={`lg:block ${showFilters ? 'block' : 'hidden'}`}>
            <div className="bg-card rounded-xl border border-border p-6 sticky top-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="font-semibold text-lg flex items-center gap-2">
                  <Filter className="w-4 h-4" />
                  Filters
                </h2>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="lg:hidden"
                  onClick={() => setShowFilters(false)}
                >
                  ×
                </Button>
              </div>
              
              {/* Categories */}
              {categories.length > 0 && (
                <div className="mb-6">
                  <h3 className="font-medium mb-3">Categories</h3>
                  <div className="space-y-2">
                    {categories.map((category) => (
                      <div key={category.value} className="flex items-center">
                        <input
                          type="checkbox"
                          id={`category-${category.value}`}
                          checked={selectedCategories.includes(category.value)}
                          onChange={() => handleCategoryToggle(category.value)}
                          className="mr-2 h-4 w-4 rounded border-border text-primary focus:ring-primary"
                        />
                        <label
                          htmlFor={`category-${category.value}`}
                          className="text-sm text-foreground"
                        >
                          {category.name}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Price Range */}
              <div className="mb-6">
                <h3 className="font-medium mb-3">Price Range</h3>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Input
                      type="number"
                      placeholder="Min"
                      value={priceRange.min}
                      onChange={(e) => handlePriceChange('min', e.target.value)}
                    />
                  </div>
                  <div>
                    <Input
                      type="number"
                      placeholder="Max"
                      value={priceRange.max}
                      onChange={(e) => handlePriceChange('max', e.target.value)}
                    />
                  </div>
                </div>
              </div>
              
              {/* Reset filters */}
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full"
                onClick={() => {
                  setSelectedCategories([]);
                  setPriceRange({min: "", max: ""});
                }}
              >
                Reset Filters
              </Button>
            </div>
          </div>
          
          {/* Products grid */}
          <div className="lg:col-span-3">
            <div className="flex justify-between items-center mb-6">
              <div>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="lg:hidden flex items-center gap-1"
                  onClick={() => setShowFilters(!showFilters)}
                >
                  <SlidersHorizontal className="h-4 w-4" />
                  {showFilters ? 'Hide Filters' : 'Show Filters'}
                </Button>
              </div>
              
              <div className="text-sm text-muted-foreground">
                {loading ? 'Searching...' : `${filteredProducts.length} results found`}
              </div>
              
              <div className="flex border border-border rounded-md">
                <Button variant="ghost" size="icon" className="rounded-r-none">
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon" className="rounded-l-none">
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="bg-muted animate-pulse rounded-xl h-80"></div>
                ))}
              </div>
            ) : filteredProducts.length === 0 ? (
              <div className="text-center py-12 bg-muted/30 rounded-xl">
                <Search className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No products found</h3>
                <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                  {query 
                    ? `We couldn't find any products matching "${query}". Try a different search term or browse our categories.`
                    : 'Enter a search term to find products.'}
                </p>
                <Button onClick={() => {
                  setSearchQuery('');
                  setSelectedCategories([]);
                  setPriceRange({min: "", max: ""});
                }}>
                  Clear Search
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {filteredProducts.map((product) => (
                  <ProductCard key={product.id} product={product} />
                ))}
              </div>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
