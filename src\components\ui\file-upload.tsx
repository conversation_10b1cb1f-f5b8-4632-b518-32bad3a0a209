'use client';

import * as React from "react";
import { UploadCloud, X, Image as ImageIcon, Link, Plus } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface FileUploadProps extends React.InputHTMLAttributes<HTMLInputElement> {
  onFilesSelected: (files: File[]) => void;
  onUrlsAdded?: (urls: string[]) => void;
  onFileRemoved?: (index: number) => void;
  onExistingImageRemoved?: (index: number) => void;
  previewUrls?: string[];
  maxFiles?: number;
  accept?: string;
  className?: string;
  label?: string;
  allowUrls?: boolean;
}

export function FileUpload({
  onFilesSelected,
  onUrlsAdded,
  onFileRemoved,
  onExistingImageRemoved,
  previewUrls = [],
  maxFiles = 5,
  accept = "image/*",
  className,
  label = "Upload images",
  allowUrls = true,
  ...props
}: FileUploadProps) {
  const [newFilePreviews, setNewFilePreviews] = React.useState<string[]>([]);
  const [dragActive, setDragActive] = React.useState(false);
  const [urlInput, setUrlInput] = React.useState('');
  const [urlPreviews, setUrlPreviews] = React.useState<string[]>([]);
  const inputRef = React.useRef<HTMLInputElement>(null);

  // Combine existing images, new file previews, and URL previews
  const allPreviews = [...previewUrls, ...newFilePreviews, ...urlPreviews];
  const existingImageCount = previewUrls.length;
  const newFileCount = newFilePreviews.length;
  const urlCount = urlPreviews.length;

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);
      const validFiles = newFiles.slice(0, maxFiles - allPreviews.length);

      if (validFiles.length > 0) {
        // Generate previews for the new files
        const newPreviews = validFiles.map(file => URL.createObjectURL(file));
        setNewFilePreviews([...newFilePreviews, ...newPreviews]);
        onFilesSelected(validFiles);
      }
    }
  };

  // Handle drag events
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  // Handle drop event
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const newFiles = Array.from(e.dataTransfer.files);
      const validFiles = newFiles
        .filter(file => file.type.startsWith('image/'))
        .slice(0, maxFiles - allPreviews.length);
      
      if (validFiles.length > 0) {
        // Generate previews for the new files
        const newPreviews = validFiles.map(file => URL.createObjectURL(file));
        setNewFilePreviews([...newFilePreviews, ...newPreviews]);
        onFilesSelected(validFiles);
      }
    }
  };

  // Handle URL addition
  const handleAddUrl = () => {
    if (!urlInput.trim()) return;

    const url = urlInput.trim();
    // Basic URL validation
    try {
      new URL(url);
      if (allPreviews.length < maxFiles) {
        setUrlPreviews([...urlPreviews, url]);
        onUrlsAdded?.([url]);
        setUrlInput('');
      }
    } catch (error) {
      alert('Please enter a valid URL');
    }
  };

  // Handle URL removal
  const handleUrlRemoved = (index: number) => {
    const urlIndex = index - existingImageCount - newFileCount;
    if (urlIndex >= 0 && urlIndex < urlPreviews.length) {
      const newUrls = [...urlPreviews];
      newUrls.splice(urlIndex, 1);
      setUrlPreviews(newUrls);
    }
  };

  // Enhanced remove handler
  const handleRemovePreview = (index: number) => {
    if (index < existingImageCount) {
      // Existing image
      onExistingImageRemoved?.(index);
    } else if (index < existingImageCount + newFileCount) {
      // New file
      const newFileIndex = index - existingImageCount;
      const updatedNewPreviews = [...newFilePreviews];
      updatedNewPreviews.splice(newFileIndex, 1);
      setNewFilePreviews(updatedNewPreviews);
      onFileRemoved?.(newFileIndex);
    } else {
      // URL image
      handleUrlRemoved(index);
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      {allowUrls ? (
        <Tabs defaultValue="upload" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="upload">Upload Files</TabsTrigger>
            <TabsTrigger value="url">Add from URL</TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-2">
            <div
              className={cn(
                "border-2 border-dashed rounded-lg p-6 transition-colors",
                "flex flex-col items-center justify-center gap-2",
                dragActive ? "border-primary/70 bg-primary/5" : "border-border",
                allPreviews.length >= maxFiles ? "opacity-50 cursor-not-allowed" : "cursor-pointer"
              )}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
              onClick={() => allPreviews.length < maxFiles && inputRef.current?.click()}
            >
              <UploadCloud className="h-10 w-10 text-muted-foreground" />
              <p className="text-sm font-medium">{label}</p>
              <p className="text-xs text-muted-foreground">
                Drag & drop or click to upload ({allPreviews.length}/{maxFiles})
              </p>
              <input
                type="file"
                ref={inputRef}
                className="hidden"
                onChange={handleFileChange}
                accept={accept}
                multiple={maxFiles > 1}
                disabled={allPreviews.length >= maxFiles}
                {...props}
              />
            </div>
          </TabsContent>

          <TabsContent value="url" className="space-y-2">
            <div className="border rounded-lg p-4 space-y-3">
              <div className="flex items-center gap-2 text-muted-foreground">
                <Link className="h-4 w-4" />
                <span className="text-sm font-medium">Add Image from URL</span>
              </div>
              <div className="flex gap-2">
                <Input
                  placeholder="https://example.com/image.jpg"
                  value={urlInput}
                  onChange={(e) => setUrlInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleAddUrl()}
                  disabled={allPreviews.length >= maxFiles}
                />
                <Button
                  type="button"
                  onClick={handleAddUrl}
                  disabled={!urlInput.trim() || allPreviews.length >= maxFiles}
                  size="sm"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                Enter a direct link to an image ({allPreviews.length}/{maxFiles})
              </p>
            </div>
          </TabsContent>
        </Tabs>
      ) : (
        <div
          className={cn(
            "border-2 border-dashed rounded-lg p-6 transition-colors",
            "flex flex-col items-center justify-center gap-2",
            dragActive ? "border-primary/70 bg-primary/5" : "border-border",
            allPreviews.length >= maxFiles ? "opacity-50 cursor-not-allowed" : "cursor-pointer"
          )}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
          onClick={() => allPreviews.length < maxFiles && inputRef.current?.click()}
        >
          <UploadCloud className="h-10 w-10 text-muted-foreground" />
          <p className="text-sm font-medium">{label}</p>
          <p className="text-xs text-muted-foreground">
            Drag & drop or click to upload ({allPreviews.length}/{maxFiles})
          </p>
          <input
            type="file"
            ref={inputRef}
            className="hidden"
            onChange={handleFileChange}
            accept={accept}
            multiple={maxFiles > 1}
            disabled={allPreviews.length >= maxFiles}
            {...props}
          />
        </div>
      )}

      {allPreviews.length > 0 && (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 mt-4">
          {allPreviews.map((preview, index) => (
            <div key={index} className="relative group">
              <div className="aspect-square rounded-md overflow-hidden border bg-muted">
                <img
                  src={preview}
                  alt={`Preview ${index + 1}`}
                  className="w-full h-full object-cover"
                />
              </div>
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  handleRemovePreview(index);
                }}
                className="absolute -top-2 -right-2 bg-destructive text-destructive-foreground rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <X className="h-3 w-3" />
              </button>
              {/* Add badges to distinguish different image types */}
              {index < existingImageCount && (
                <div className="absolute top-1 left-1 bg-blue-500 text-white text-xs px-1 py-0.5 rounded">
                  Current
                </div>
              )}
              {index >= existingImageCount && index < existingImageCount + newFileCount && (
                <div className="absolute top-1 left-1 bg-green-500 text-white text-xs px-1 py-0.5 rounded">
                  New File
                </div>
              )}
              {index >= existingImageCount + newFileCount && (
                <div className="absolute top-1 left-1 bg-purple-500 text-white text-xs px-1 py-0.5 rounded">
                  URL
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
