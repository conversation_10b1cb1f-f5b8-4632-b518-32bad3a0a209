"use client";

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import Link from 'next/link';
import { createClient } from '@/utils/supabase/client';
import { RichTextEditor } from '@/components/blog/rich-text-editor';

interface ArticleData {
  title: string;
  excerpt: string;
  content: string;
  tags: string[];
  category: string;
  isFeatured: boolean;
  featuredImage?: string;
  seoTitle?: string;
  seoDescription?: string;
}

interface Category {
  id: string;
  name: string;
  color: string;
}

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  tags: string[];
  category_id: string;
  is_featured: boolean;
  featured_image: string | null;
  seo_title: string | null;
  seo_description: string | null;
  is_published: boolean;
}

export default function EditBlogPostPage() {
  const router = useRouter();
  const params = useParams();
  const { toast } = useToast();
  const [categories, setCategories] = useState<Category[]>([]);
  const [post, setPost] = useState<BlogPost | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoadingPost, setIsLoadingPost] = useState(true);
  const supabase = createClient();

  const postId = params.id as string;

  useEffect(() => {
    fetchCategories();
    fetchPost();
  }, [postId]);

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('blog_categories')
        .select('id, name, color')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      setCategories(data || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast({
        title: "Error",
        description: "Failed to load categories",
        variant: "destructive",
      });
    }
  };

  const fetchPost = async () => {
    try {
      setIsLoadingPost(true);
      const { data, error } = await supabase
        .from('blog_articles')
        .select('*')
        .eq('id', postId)
        .single();

      if (error) throw error;
      
      if (!data) {
        toast({
          title: "Error",
          description: "Post not found",
          variant: "destructive",
        });
        router.push('/admin/blog/posts');
        return;
      }

      setPost(data);
    } catch (error) {
      console.error('Error fetching post:', error);
      toast({
        title: "Error",
        description: "Failed to load post",
        variant: "destructive",
      });
      router.push('/admin/blog/posts');
    } finally {
      setIsLoadingPost(false);
    }
  };

  const generateSlug = (title: string): string => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const calculateReadTime = (content: string): number => {
    const wordsPerMinute = 200;
    const wordCount = content.split(/\s+/).length;
    return Math.ceil(wordCount / wordsPerMinute);
  };

  const handleSave = async (data: ArticleData) => {
    if (!data.title || !data.content || !data.excerpt) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSaving(true);

      const { error } = await supabase
        .from('blog_articles')
        .update({
          title: data.title,
          slug: generateSlug(data.title),
          excerpt: data.excerpt,
          content: data.content,
          category_id: data.category || null,
          featured_image: data.featuredImage || null,
          is_featured: data.isFeatured,
          seo_title: data.seoTitle || null,
          seo_description: data.seoDescription || null,
          read_time: calculateReadTime(data.content),
          tags: data.tags,
          updated_at: new Date().toISOString(),
        })
        .eq('id', postId);

      if (error) throw error;

      toast({
        title: "Success!",
        description: "Article updated successfully",
      });

      router.push('/admin/blog/posts');
    } catch (error) {
      console.error('Error updating article:', error);
      toast({
        title: "Error",
        description: "Failed to update article",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handlePublish = async (data: ArticleData) => {
    if (!data.title || !data.content || !data.excerpt) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);

      const updateData: any = {
        title: data.title,
        slug: generateSlug(data.title),
        excerpt: data.excerpt,
        content: data.content,
        category_id: data.category || null,
        featured_image: data.featuredImage || null,
        is_featured: data.isFeatured,
        seo_title: data.seoTitle || null,
        seo_description: data.seoDescription || null,
        read_time: calculateReadTime(data.content),
        tags: data.tags,
        updated_at: new Date().toISOString(),
      };

      // If not already published, set publish status and date
      if (!post?.is_published) {
        updateData.is_published = true;
        updateData.published_at = new Date().toISOString();
      }

      const { error } = await supabase
        .from('blog_articles')
        .update(updateData)
        .eq('id', postId);

      if (error) throw error;

      toast({
        title: "Success!",
        description: post?.is_published ? "Article updated successfully" : "Article published successfully",
      });

      router.push('/admin/blog/posts');
    } catch (error) {
      console.error('Error publishing article:', error);
      toast({
        title: "Error",
        description: "Failed to publish article",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingPost) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <div className="w-10 h-10 bg-white/20 rounded-xl animate-pulse"></div>
          <div>
            <div className="h-8 w-48 bg-white/20 rounded animate-pulse mb-2"></div>
            <div className="h-4 w-64 bg-white/10 rounded animate-pulse"></div>
          </div>
        </div>
        <div className="space-y-6">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="glass-effect border border-white/10 neo-shadow p-6 rounded-2xl animate-pulse">
              <div className="h-6 w-32 bg-white/20 rounded mb-4"></div>
              <div className="space-y-3">
                <div className="h-4 bg-white/10 rounded"></div>
                <div className="h-4 bg-white/10 rounded w-3/4"></div>
                <div className="h-4 bg-white/10 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <h3 className="text-lg font-semibold text-foreground mb-2">Post not found</h3>
          <p className="text-muted-foreground mb-4">The blog post you're looking for doesn't exist</p>
          <Button asChild className="bg-primary hover:bg-primary/90 text-primary-foreground min-h-[44px]">
            <Link href="/admin/blog/posts">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Posts
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="icon"
            asChild
            className="glass-effect border border-white/20 hover:glass-effect-subtle min-h-[44px] min-w-[44px]"
          >
            <Link href="/admin/blog/posts">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Edit Post</h1>
            <p className="text-muted-foreground">Update your blog content</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {post.is_published && (
            <div className="px-3 py-1 bg-green-500/20 text-green-400 border border-green-500/30 rounded-full text-sm">
              Published
            </div>
          )}
          {!post.is_published && (
            <div className="px-3 py-1 bg-yellow-500/20 text-yellow-400 border border-yellow-500/30 rounded-full text-sm">
              Draft
            </div>
          )}
        </div>
      </div>

      {/* Editor */}
      <RichTextEditor
        initialTitle={post.title}
        initialExcerpt={post.excerpt}
        initialContent={post.content}
        initialTags={post.tags || []}
        initialCategory={post.category_id}
        initialIsFeatured={post.is_featured}
        categories={categories}
        onSave={handleSave}
        onPublish={handlePublish}
        isLoading={isLoading || isSaving}
      />

      {/* Post Info */}
      <div className="glass-effect border border-white/10 neo-shadow p-6 rounded-2xl">
        <h3 className="text-lg font-semibold text-foreground mb-4">Post Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground">Post ID</p>
            <p className="text-foreground font-mono">{post.id}</p>
          </div>
          <div>
            <p className="text-muted-foreground">Status</p>
            <p className="text-foreground">{post.is_published ? 'Published' : 'Draft'}</p>
          </div>
          <div>
            <p className="text-muted-foreground">Featured</p>
            <p className="text-foreground">{post.is_featured ? 'Yes' : 'No'}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
