import { createServerClient } from '@supabase/ssr'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()

  // Check if Supabase environment variables are set
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  // Skip Supabase authentication if environment variables are not set
  if (!supabaseUrl || !supabaseKey) {
    console.warn('Supabase environment variables are not set. Skipping authentication.')
    return res
  }

  try {
    const supabase = createServerClient(
      supabaseUrl,
      supabaseKey,
      {
        cookies: {
          getAll() {
            return req.cookies.getAll().map(({ name, value }) => ({
              name,
              value,
            }))
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              req.cookies.set(name, value)
              res.cookies.set(name, value, options)
            })
          },
        },
      }
    )

    // Refresh session if expired - required for Server Components
    const { data: { session }, error } = await supabase.auth.getSession()

    if (error) {
      console.error('Auth session error:', error)
    }

    // Protected routes that require authentication
    const protectedRoutes = ['/checkout', '/account', '/account/orders']
    const adminRoutes = ['/admin']
    const adminAuthRoutes = ['/admin/sign-in', '/admin/sign-up']

    const isProtectedRoute = protectedRoutes.some(route => req.nextUrl.pathname.startsWith(route))
    const isAdminRoute = adminRoutes.some(route => req.nextUrl.pathname.startsWith(route))
    const isAdminAuthRoute = adminAuthRoutes.some(route => req.nextUrl.pathname.startsWith(route))

    // If trying to access a protected route without being logged in, redirect to sign-in
    if (isProtectedRoute && !session) {
      // Store the original URL to redirect back after login
      const redirectUrl = req.nextUrl.pathname + req.nextUrl.search
      const signInUrl = new URL('/sign-in', req.url)
      signInUrl.searchParams.set('redirect_to', redirectUrl)

      return NextResponse.redirect(signInUrl)
    }

    // If trying to access admin routes without being logged in, redirect to admin sign-in
    if (isAdminRoute && !isAdminAuthRoute && !session) {
      // Store the original URL to redirect back after login
      const redirectUrl = req.nextUrl.pathname + req.nextUrl.search
      const adminSignInUrl = new URL('/admin/sign-in', req.url)
      adminSignInUrl.searchParams.set('redirect_to', redirectUrl)

      return NextResponse.redirect(adminSignInUrl)
    }

    // If accessing admin routes with a session, verify admin role
    if (isAdminRoute && !isAdminAuthRoute && session) {
      try {
        const { data: userData } = await supabase
          .from('users')
          .select('role')
          .eq('id', session.user.id)
          .single()

        if (!userData || userData.role !== 'admin') {
          // User is not an admin, redirect to admin sign-in
          const adminSignInUrl = new URL('/admin/sign-in', req.url)
          adminSignInUrl.searchParams.set('error', 'admin_access_required')
          return NextResponse.redirect(adminSignInUrl)
        }
      } catch (error) {
        console.error('Error checking admin role:', error)
        // On error, redirect to admin sign-in
        const adminSignInUrl = new URL('/admin/sign-in', req.url)
        adminSignInUrl.searchParams.set('error', 'auth_error')
        return NextResponse.redirect(adminSignInUrl)
      }
    }
  } catch (error) {
    console.error('Error in middleware:', error)
  }

  return res
}

// Ensure the middleware is only called for relevant paths
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public (public files)
     */
    '/((?!_next/static|_next/image|favicon.ico|public|api).*)',
  ],
}
