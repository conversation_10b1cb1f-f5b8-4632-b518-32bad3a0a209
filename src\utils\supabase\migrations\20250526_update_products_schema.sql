-- Ensure the products table has the correct structure
-- This migration ensures the schema matches our TypeScript definitions

-- First, check if 'images' column exists and drop it if it does
DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'products'
        AND column_name = 'images'
    ) THEN
        ALTER TABLE public.products DROP COLUMN images;
    END IF;
END $$;

-- Ensure the image column exists and has the correct type
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'products'
        AND column_name = 'image'
    ) THEN
        ALTER TABLE public.products ADD COLUMN image TEXT;
    END IF;
END $$;

-- Ensure the stock column exists and has the correct type
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'products'
        AND column_name = 'stock'
    ) THEN
        ALTER TABLE public.products ADD COLUMN stock INTEGER NOT NULL DEFAULT 0;
    END IF;
END $$;

-- Ensure the status column exists and has the correct type
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'products'
        AND column_name = 'status'
    ) THEN
        ALTER TABLE public.products ADD COLUMN status TEXT NOT NULL DEFAULT 'In Stock';
    END IF;
END $$;

-- Comment to force schema cache refresh
COMMENT ON TABLE public.products IS 'Product catalog with updated schema';
