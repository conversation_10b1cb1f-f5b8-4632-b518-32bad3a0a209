"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { createClient } from "../utils/supabase/client";
import { GlowingButton } from "@/components/ui/glowing-button";
import { ExpandableTabs } from "./ui/expandable-tabs";
import { Home, ShoppingCart, Menu, Loader2, Search, Shirt, Gift, ShoppingBag, Store } from "lucide-react";
import UserProfile from "./user-profile";
import ThemeSwitcher from "./theme-switcher";
import { CartCount } from "./cart-count";
import { SearchBar } from "./search-bar";
import { Dialog, DialogContent, DialogTrigger } from "./ui/dialog";
import { cn } from "@/lib/utils";

export default function Navbar() {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const router = useRouter();
  const searchRef = useRef<HTMLDivElement>(null);
  
  // Navigation tabs configuration
  const navTabs = [
    {
      title: "Home",
      icon: Home,
      href: "/"
    },
    {
      title: "Shop",
      icon: Store,
      href: "/shop"
    },
    {
      title: "Rackets",
      icon: ShoppingBag,
      href: "/shop?category=rackets"
    },
    {
      title: "Apparel",
      icon: Shirt,
      href: "/shop?category=apparel"
    },
    {
      title: "Accessories",
      icon: Gift,
      href: "/shop?category=accessories"
    }
  ];



  // Handle scroll to collapse/expand tabs
  useEffect(() => {
    const handleScroll = () => {
      // Expand on hero section, collapse when scrolling down
      setIsScrolled(window.scrollY > 100);
      
      // Also close search when scrolling
      if (showSearch) {
        setShowSearch(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [showSearch]);

  // Handle clicking outside search to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSearch(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Fetch user data
  useEffect(() => {
    async function getUser() {
      const supabase = createClient();
      const { data } = await supabase.auth.getUser();
      setUser(data.user);
      setLoading(false);
    }

    getUser();
  }, []);

  const handleTabClick = (href: string) => {
    router.push(href);
  };

  const toggleSearch = () => {
    setShowSearch(!showSearch);
  };

  return (
    <header className="w-full py-4 px-2 z-50 absolute top-0 left-0 right-0">
      <div className="container mx-auto px-4 flex items-center justify-between relative">
        {/* Logo */}
        <Link href="/" prefetch className="text-xl font-bold text-primary flex items-center gap-2 z-20">
          <img src="/logo.svg" alt="Tennis Whisperer Logo" className="h-14 w-auto" />
        </Link>
        
        {/* Enhanced floating navigation pill - hidden on mobile */}
        <div className={cn(
          "fixed left-1/2 transform -translate-x-1/2 transition-all duration-500 z-10 hidden md:block py-3",
          isScrolled ? "top-3 scale-95" : "top-3 scale-100"
        )}>
          <div className="relative">
            {/* Enhanced background with glassmorphism */}
            <div className="absolute inset-0 nav-pill rounded-full blur-sm"></div>
            <ExpandableTabs
              tabs={navTabs}
              onTabClick={handleTabClick}
              className="relative nav-pill neo-shadow-light hover:neo-shadow transition-all duration-300 border-primary/20"
              activeColor="text-primary font-semibold"
            />
          </div>
        </div>
        
        {/* Enhanced right side elements */}
        <div className="flex gap-2 items-center z-20">
          {/* Enhanced search with glassmorphism */}
          <div ref={searchRef} className="relative">
            {showSearch ? (
              <div className="absolute right-0 top-0 w-60 transition-all duration-300 ease-in-out">
                <SearchBar />
              </div>
            ) : (
              <button
                onClick={toggleSearch}
                className="text-foreground/80 hover:text-primary transition-all duration-300 p-2.5 rounded-full glass-effect-subtle neo-shadow-light hover:neo-shadow focus-ring"
                aria-label="Search products"
              >
                <Search className="h-5 w-5" />
              </button>
            )}
          </div>

          <ThemeSwitcher />

          {/* Enhanced cart button */}
          <Link
            href="/cart"
            className="text-foreground/80 hover:text-primary transition-all duration-300 p-2.5 rounded-full glass-effect-subtle neo-shadow-light hover:neo-shadow focus-ring relative group"
            aria-label="Shopping cart"
          >
            <ShoppingCart className="h-5 w-5 group-hover:scale-110 transition-transform duration-200" />
            <CartCount />
          </Link>

          {loading ? (
            <div className="p-2">
              <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
            </div>
          ) : user ? (
            <UserProfile />
          ) : (
            <div className="hidden md:flex items-center gap-3">
              {/* <Link href="/admin/sign-in" className="text-xs text-muted-foreground hover:text-primary transition-colors px-3 py-2 rounded-full glass-effect-subtle">
                Admin
              </Link> */}
              <Link href="/sign-in">
                <GlowingButton className="neo-shadow-light hover:neo-shadow transition-neo">
                  Get Started
                </GlowingButton>
              </Link>
            </div>
          )}

          {/* Enhanced Mobile Menu Button */}
          <Dialog open={isMenuOpen} onOpenChange={setIsMenuOpen}>
            <DialogTrigger asChild>
              <button className="md:hidden text-foreground/80 hover:text-primary transition-all duration-300 p-2.5 rounded-full glass-effect-subtle neo-shadow-light hover:neo-shadow focus-ring">
                <Menu className="h-5 w-5" />
              </button>
            </DialogTrigger>
            <DialogContent className="p-0 border-none max-w-[350px] bg-transparent shadow-none">
              <div className="relative w-full h-full">
                {/* Enhanced background circles for glassmorphism effect */}
                <div className="absolute inset-0 overflow-hidden -z-10">
                  <div className="absolute h-[400px] w-[400px] rounded-full gradient-blue top-[-150px] left-[-200px] blur-[80px] opacity-30 animate-pulse"></div>
                  <div className="absolute h-[300px] w-[300px] rounded-full gradient-purple bottom-[-100px] right-[-150px] blur-[70px] opacity-35 animate-pulse"></div>
                  <div className="absolute h-[200px] w-[200px] rounded-full gradient-pink top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 blur-[60px] opacity-20 animate-pulse"></div>
                </div>

                {/* Enhanced mobile menu card */}
                <div className="w-full rounded-3xl glass-effect-dark neo-shadow overflow-hidden">
                  {/* Header with logo and close button */}
                  <div className="flex items-center justify-between p-4 border-b border-border/30">
                    <div className="text-xl font-bold text-primary">Menu</div>
                    {/* <button 
                      onClick={() => setIsMenuOpen(false)}
                      className="text-foreground/80 hover:text-primary transition-colors p-1 rounded-full"
                    >
                      <X className="h-5 w-5" />
                    </button> */}
                  </div>

                  {/* Menu items */}
                  <div className="p-6 flex flex-col items-center text-center">
                    <nav className="flex flex-col space-y-5 w-full">
                      {navTabs.map((tab, i) => (
                        <Link
                          key={i}
                          href={tab.href || "#"}
                          onClick={() => setIsMenuOpen(false)}
                          className="text-foreground hover:text-primary transition-colors font-medium"
                        >
                          {tab.title}
                        </Link>
                      ))}
                      <Link 
                        href="/about" 
                        className="text-foreground hover:text-primary transition-colors font-medium" 
                        onClick={() => setIsMenuOpen(false)}
                      >
                        About
                      </Link>
                      <Link
                        href="/contact"
                        className="text-foreground hover:text-primary transition-colors font-medium"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Contact
                      </Link>
                      <Link
                        href="/demo"
                        className="text-foreground hover:text-primary transition-colors font-medium"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Preloader Demo
                      </Link>

                      {/* Hero Demos */}
                      <div className="border-t border-border/30 pt-4 mt-4">
                        <div className="text-sm text-muted-foreground mb-3 font-semibold">Hero Demos</div>
                        <Link
                          href="/demo/hero-3d"
                          className="text-foreground hover:text-primary transition-colors font-medium block mb-2"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          3D Interactive
                        </Link>
                        <Link
                          href="/demo/hero-video"
                          className="text-foreground hover:text-primary transition-colors font-medium block mb-2"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          Video Background
                        </Link>
                        <Link
                          href="/demo/hero-gamified"
                          className="text-foreground hover:text-primary transition-colors font-medium block"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          Gamified Experience
                        </Link>
                      </div>
                    </nav>

                    {/* Action button */}
                    {!user && !loading && (
                      <div className="mt-8 w-full">
                        <Link href="/sign-up" onClick={() => setIsMenuOpen(false)}>
                          <GlowingButton className="w-full">Get Started</GlowingButton>
                        </Link>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </header>
  );
}
