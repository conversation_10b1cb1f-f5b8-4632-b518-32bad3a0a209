-- Create helper functions for debugging

-- Function to get table columns information
CREATE OR REPLACE FUNCTION get_table_columns(table_name TEXT)
RETURNS TABLE (
    column_name TEXT,
    data_type TEXT,
    is_nullable TEXT,
    column_default TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.column_name::TEXT,
        c.data_type::TEXT,
        c.is_nullable::TEXT,
        c.column_default::TEXT
    FROM information_schema.columns c
    WHERE c.table_schema = 'public' 
    AND c.table_name = get_table_columns.table_name
    ORDER BY c.ordinal_position;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_table_columns(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_table_columns(TEXT) TO service_role;

-- Function to get order statistics for admin dashboard
CREATE OR REPLACE FUNCTION get_order_stats()
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_orders', (SELECT COUNT(*) FROM public.orders),
        'pending_orders', (SELECT COUNT(*) FROM public.orders WHERE status = 'pending'),
        'processing_orders', (SELECT COUNT(*) FROM public.orders WHERE status = 'processing'),
        'delivered_orders', (SELECT COUNT(*) FROM public.orders WHERE status = 'delivered'),
        'total_revenue', (SELECT COALESCE(SUM(total_amount), 0) FROM public.orders WHERE payment_status = 'paid'),
        'monthly_growth', 0.0 -- Placeholder for now
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_order_stats() TO authenticated;
GRANT EXECUTE ON FUNCTION get_order_stats() TO service_role;

-- Test the functions
SELECT 'Testing get_table_columns function:' as test;
SELECT * FROM get_table_columns('orders') LIMIT 5;

SELECT 'Testing get_order_stats function:' as test;
SELECT get_order_stats();

-- Verify orders table structure
SELECT 'Current orders table structure:' as info;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'orders'
ORDER BY ordinal_position;
