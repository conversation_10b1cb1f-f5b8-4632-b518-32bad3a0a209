-- Verification Script for Admin Authentication Setup
-- Run this AFTER running admin-auth-setup.sql to verify everything is working

-- 1. Verify user_role enum exists and has correct values
SELECT 
  'user_role enum verification' as test_name,
  enumlabel as role_value,
  CASE 
    WHEN enumlabel IN ('user', 'admin') THEN '✅ PASS'
    ELSE '❌ FAIL'
  END as status
FROM pg_enum 
WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')
ORDER BY enumsortorder;

-- 2. Verify users table structure
SELECT 
  'users table structure verification' as test_name,
  column_name,
  data_type,
  is_nullable,
  column_default,
  CASE 
    WHEN column_name = 'id' AND data_type = 'uuid' THEN '✅ PASS'
    WHEN column_name = 'email' AND data_type = 'text' THEN '✅ PASS'
    WHEN column_name = 'role' AND data_type = 'USER-DEFINED' THEN '✅ PASS'
    WHEN column_name = 'token_identifier' AND is_nullable = 'YES' THEN '✅ PASS'
    WHEN column_name IN ('full_name', 'name') AND data_type = 'text' THEN '✅ PASS'
    WHEN column_name IN ('created_at', 'updated_at') AND data_type = 'timestamp with time zone' THEN '✅ PASS'
    ELSE '✅ OK'
  END as status
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'users'
ORDER BY ordinal_position;

-- 3. Verify RLS is enabled on users table
SELECT 
  'RLS verification' as test_name,
  schemaname,
  tablename,
  rowsecurity,
  CASE 
    WHEN rowsecurity = true THEN '✅ PASS - RLS Enabled'
    ELSE '❌ FAIL - RLS Not Enabled'
  END as status
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename = 'users';

-- 4. Verify RLS policies exist
SELECT 
  'RLS policies verification' as test_name,
  policyname,
  cmd,
  permissive,
  CASE 
    WHEN policyname LIKE '%admin%' OR policyname LIKE '%own%' THEN '✅ PASS'
    ELSE '✅ OK'
  END as status
FROM pg_policies 
WHERE tablename = 'users' 
AND schemaname = 'public'
ORDER BY policyname;

-- 5. Verify trigger function exists
SELECT 
  'trigger function verification' as test_name,
  routine_name,
  routine_type,
  CASE 
    WHEN routine_name = 'handle_new_user' AND routine_type = 'FUNCTION' THEN '✅ PASS'
    ELSE '❌ FAIL'
  END as status
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name = 'handle_new_user';

-- 6. Verify trigger exists
SELECT 
  'trigger verification' as test_name,
  trigger_name,
  event_manipulation,
  action_timing,
  CASE 
    WHEN trigger_name = 'on_auth_user_created' 
         AND event_manipulation = 'INSERT' 
         AND action_timing = 'AFTER' THEN '✅ PASS'
    ELSE '❌ FAIL'
  END as status
FROM information_schema.triggers 
WHERE trigger_schema = 'public' 
AND trigger_name = 'on_auth_user_created';

-- 7. Test permissions (this will show if current user has proper access)
SELECT 
  'permissions verification' as test_name,
  'Testing SELECT permission on users table' as test_description,
  CASE 
    WHEN has_table_privilege('public.users', 'SELECT') THEN '✅ PASS - SELECT allowed'
    ELSE '❌ FAIL - SELECT not allowed'
  END as status
UNION ALL
SELECT 
  'permissions verification' as test_name,
  'Testing INSERT permission on users table' as test_description,
  CASE 
    WHEN has_table_privilege('public.users', 'INSERT') THEN '✅ PASS - INSERT allowed'
    ELSE '❌ FAIL - INSERT not allowed'
  END as status
UNION ALL
SELECT 
  'permissions verification' as test_name,
  'Testing UPDATE permission on users table' as test_description,
  CASE 
    WHEN has_table_privilege('public.users', 'UPDATE') THEN '✅ PASS - UPDATE allowed'
    ELSE '❌ FAIL - UPDATE not allowed'
  END as status;

-- 8. Summary report
DO $$
BEGIN
    RAISE NOTICE '=== ADMIN AUTHENTICATION SETUP VERIFICATION COMPLETE ===';
    RAISE NOTICE 'Review the results above to ensure all tests show ✅ PASS status';
    RAISE NOTICE 'If any tests show ❌ FAIL, re-run the admin-auth-setup.sql script';
    RAISE NOTICE 'Admin access code: TENNIS_ADMIN_2024';
    RAISE NOTICE 'Admin sign-up URL: /admin/sign-up';
    RAISE NOTICE 'Admin sign-in URL: /admin/sign-in';
END $$;
