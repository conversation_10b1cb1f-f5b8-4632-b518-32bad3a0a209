'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Play, Pause, ShoppingCart, Users, ChevronLeft, ChevronRight, Award } from 'lucide-react';

// Video configuration for horizontal carousel
const videoConfig = [
  {
    id: 1,
    src: '/videos/1.mp4',
    title: 'Premium Equipment',
    subtitle: 'Professional tennis gear',
    category: 'Equipment',
    thumbnail: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600&q=80',
    hotspots: [
      {
        id: 1,
        name: 'Pro Tour Racket',
        price: 3599.99,
        type: 'product' as const,
        position: { x: 70, y: 50 },
        image: '/images/tennis-racket.png'
      },
      {
        id: 2,
        name: '6-Month Program',
        price: 1599.99,
        type: 'mentorship' as const,
        position: { x: 30, y: 40 },
        image: '/images/mentor/tc.jpg'
      }
    ]
  },
  {
    id: 2,
    src: '/videos/2.mp4',
    title: 'Training Programs',
    subtitle: 'Expert coaching sessions',
    category: 'Training',
    thumbnail: 'https://images.unsplash.com/photo-1622279457486-62dcc4a431d6?w=600&q=80',
    hotspots: [
      {
        id: 3,
        name: 'Elite Tennis Shoes',
        price: 2349.99,
        type: 'product' as const,
        position: { x: 60, y: 65 },
        image: 'https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=400&q=80'
      },
      {
        id: 4,
        name: '12-Month Program',
        price: 2999.99,
        type: 'mentorship' as const,
        position: { x: 40, y: 35 },
        image: '/images/mentor/tc.jpg'
      }
    ]
  },
  {
    id: 3,
    src: '/videos/3.mp4',
    title: 'Accessories',
    subtitle: 'Complete your setup',
    category: 'Accessories',
    thumbnail: 'https://images.unsplash.com/photo-1554068865-24cecd4e34b8?w=600&q=80',
    hotspots: [
      {
        id: 5,
        name: 'Tennis Wristbands',
        price: 199.99,
        type: 'product' as const,
        position: { x: 75, y: 45 },
        image: '/images/tennis-wristbands.webp'
      }
    ]
  },
  {
    id: 4,
    src: '/videos/4.mp4',
    title: 'Match Strategies',
    subtitle: 'Winning techniques',
    category: 'Strategy',
    thumbnail: 'https://images.unsplash.com/photo-1544717684-7ba720c2b5ea?w=600&q=80',
    hotspots: [
      {
        id: 6,
        name: 'Court Bag',
        price: 799.99,
        type: 'product' as const,
        position: { x: 20, y: 60 },
        image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&q=80'
      }
    ]
  }
];

interface VideoCarouselProps {
  onAddToCart?: (item: any) => void;
  onEnrollMentorship?: (program: any) => void;
}

export default function VideoCarousel({ onAddToCart, onEnrollMentorship }: VideoCarouselProps) {
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [activeHotspot, setActiveHotspot] = useState<string | null>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const currentVideo = videoConfig[currentVideoIndex];

  // Auto-advance videos
  useEffect(() => {
    const interval = setInterval(() => {
      nextVideo();
    }, 8000);

    return () => clearInterval(interval);
  }, [currentVideoIndex]);

  const nextVideo = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      setCurrentVideoIndex((prev) => (prev + 1) % videoConfig.length);
      setActiveHotspot(null);
      setIsTransitioning(false);
    }, 300);
  };

  const prevVideo = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      setCurrentVideoIndex((prev) => (prev - 1 + videoConfig.length) % videoConfig.length);
      setActiveHotspot(null);
      setIsTransitioning(false);
    }, 300);
  };

  const scrollToVideo = (index: number) => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const cardWidth = container.scrollWidth / videoConfig.length;
      container.scrollTo({
        left: cardWidth * index,
        behavior: 'smooth'
      });
    }
  };

  const selectVideo = (index: number) => {
    setIsTransitioning(true);
    setTimeout(() => {
      setCurrentVideoIndex(index);
      setActiveHotspot(null);
      setIsTransitioning(false);
      scrollToVideo(index);
    }, 300);
  };

  const togglePlayPause = () => {
    const video = videoRef.current;
    if (video) {
      if (isPlaying) {
        video.pause();
      } else {
        video.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleHotspotClick = (hotspotId: number) => {
    setActiveHotspot(String(hotspotId));
  };

  const handleAddToCart = (item: any) => {
    onAddToCart?.(item);
    setActiveHotspot(null);
  };

  const handleEnrollMentorship = (program: any) => {
    onEnrollMentorship?.(program);
    setActiveHotspot(null);
  };

  return (
    <section className="relative bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[radial-gradient(#10b981_0.5px,transparent_0.5px)] [background-size:24px_24px] opacity-5" />
      
      {/* Header */}
      <div className="relative z-10 pt-20 pb-8">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <motion.h1 
              className="text-4xl md:text-6xl font-bold text-white mb-4"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              Tennis Whisperer
              <span className="block text-green-400">Video Collection</span>
            </motion.h1>
            <motion.p 
              className="text-xl text-white/80 max-w-2xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              Explore our premium tennis equipment and training programs
            </motion.p>
          </div>
        </div>
      </div>

      {/* Video Carousel Header */}
      <div className="container mx-auto px-4 mb-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl md:text-3xl font-bold text-white">
            Shop by Category
          </h2>
          <div className="flex items-center gap-4">
            <span className="text-white/60 text-sm hidden md:block">
              {currentVideoIndex + 1} of {videoConfig.length}
            </span>
            <div className="flex gap-2">
              <button
                onClick={prevVideo}
                className="w-10 h-10 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 flex items-center justify-center text-white hover:bg-white/20 transition-all"
                aria-label="Previous video"
              >
                <ChevronLeft className="w-5 h-5" />
              </button>
              <button
                onClick={nextVideo}
                className="w-10 h-10 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 flex items-center justify-center text-white hover:bg-white/20 transition-all"
                aria-label="Next video"
              >
                <ChevronRight className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Horizontal Video Carousel */}
      <div className="relative">
        <div
          ref={scrollContainerRef}
          className="flex gap-6 px-4 overflow-x-auto snap-x snap-mandatory"
          style={{
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
            WebkitScrollbar: 'none'
          } as React.CSSProperties}
        >
          {videoConfig.map((video, index) => (
            <motion.div
              key={video.id}
              className="flex-shrink-0 w-80 md:w-96 snap-center"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.3 }}
            >
              {/* Video Card */}
              <div className={`relative bg-white/5 backdrop-blur-sm rounded-2xl overflow-hidden border shadow-2xl transition-all duration-300 ${
                index === currentVideoIndex
                  ? 'border-green-400/50 shadow-green-400/20'
                  : 'border-white/10 hover:border-white/20'
              }`}>
                {/* Video Container */}
                <div className="relative aspect-video">
                  {index === currentVideoIndex ? (
                    <>
                      <video
                        ref={videoRef}
                        className="w-full h-full object-cover"
                        autoPlay
                        muted
                        playsInline
                        poster={video.thumbnail}
                      >
                        <source src={video.src} type="video/mp4" />
                      </video>

                      {/* Video Overlay */}
                      <div className="absolute inset-0 bg-black/20" />

                      {/* Interactive Hotspots */}
                      {video.hotspots.map((hotspot) => (
                        <motion.button
                          key={hotspot.id}
                          className="absolute w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white shadow-lg hover:scale-110 transition-transform"
                          style={{
                            left: `${hotspot.position.x}%`,
                            top: `${hotspot.position.y}%`,
                            transform: 'translate(-50%, -50%)'
                          }}
                          onClick={() => handleHotspotClick(hotspot.id)}
                          whileHover={{ scale: 1.2 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          {hotspot.type === 'mentorship' ? '🎓' : '🎾'}
                        </motion.button>
                      ))}

                      {/* Play/Pause Button */}
                      <div className="absolute bottom-4 left-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={togglePlayPause}
                          className="bg-white/90 backdrop-blur-sm hover:bg-white"
                        >
                          {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                        </Button>
                      </div>

                      {/* Category Badge */}
                      <div className="absolute top-4 left-4">
                        <span className="px-3 py-1 bg-green-600/90 backdrop-blur-sm text-white text-sm font-medium rounded-full">
                          {video.category}
                        </span>
                      </div>
                    </>
                  ) : (
                    <div
                      className="w-full h-full bg-gradient-to-br from-slate-700 to-slate-800 flex flex-col items-center justify-center cursor-pointer group"
                      onClick={() => selectVideo(index)}
                    >
                      <img
                        src={video.thumbnail}
                        alt={video.title}
                        className="w-full h-full object-cover opacity-60 group-hover:opacity-80 transition-opacity"
                      />
                      <div className="absolute inset-0 bg-black/40 group-hover:bg-black/20 transition-colors" />
                      <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
                        <Play className="w-16 h-16 mb-4 opacity-80 group-hover:opacity-100 group-hover:scale-110 transition-all" />
                        <span className="px-3 py-1 bg-green-600/90 backdrop-blur-sm text-sm font-medium rounded-full mb-2">
                          {video.category}
                        </span>
                        <p className="text-lg font-semibold text-center px-4">{video.title}</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Video Info */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-white mb-2">
                    {video.title}
                  </h3>
                  <p className="text-white/70 mb-4">
                    {video.subtitle}
                  </p>

                  {/* Hotspot Preview */}
                  <div className="flex gap-2 flex-wrap">
                    {video.hotspots.slice(0, 2).map((hotspot) => (
                      <span
                        key={hotspot.id}
                        className={`px-3 py-1 rounded-full text-xs font-medium ${
                          hotspot.type === 'mentorship'
                            ? 'bg-blue-600/20 text-blue-300 border border-blue-500/30'
                            : 'bg-green-600/20 text-green-300 border border-green-500/30'
                        }`}
                      >
                        {hotspot.type === 'mentorship' ? '🎓' : '🎾'} R{hotspot.price}
                      </span>
                    ))}
                    {video.hotspots.length > 2 && (
                      <span className="px-3 py-1 rounded-full text-xs font-medium bg-white/10 text-white/70 border border-white/20">
                        +{video.hotspots.length - 2} more
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Scroll Indicators */}
        <div className="flex justify-center mt-8 gap-2">
          {videoConfig.map((_, index) => (
            <button
              key={index}
              onClick={() => selectVideo(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentVideoIndex
                  ? 'bg-green-400 scale-125'
                  : 'bg-white/30 hover:bg-white/50'
              }`}
              aria-label={`Go to video ${index + 1}`}
            />
          ))}
        </div>
      </div>

      {/* Bottom CTA Section */}
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="max-w-3xl mx-auto"
          >
            <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
              Ready to Elevate Your Tennis Game?
            </h3>
            <p className="text-white/70 mb-8 text-lg">
              Join thousands of players who have transformed their skills with our premium equipment and expert mentorship programs.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="bg-green-600 hover:bg-green-700 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 min-h-[56px]"
              >
                <ShoppingCart className="w-5 h-5 mr-2" />
                Shop Tennis Gear
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="bg-white/10 backdrop-blur-sm border-white/30 text-white hover:bg-white/20 px-8 py-4 text-lg font-semibold rounded-xl min-h-[56px]"
              >
                <Users className="w-5 h-5 mr-2" />
                Explore Mentorship
              </Button>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Hotspot Modal */}
      <AnimatePresence>
        {activeHotspot && (
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setActiveHotspot(null)}
          >
            <motion.div
              className="bg-white rounded-2xl p-6 max-w-md w-full shadow-2xl"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              {(() => {
                const hotspot = currentVideo.hotspots.find(h => String(h.id) === activeHotspot);
                if (!hotspot) return null;

                return (
                  <div className="text-center">
                    <img
                      src={hotspot.image}
                      alt={hotspot.name}
                      className="w-24 h-24 object-cover rounded-lg mx-auto mb-4"
                    />
                    <h3 className="text-xl font-bold text-slate-900 mb-2">{hotspot.name}</h3>
                    <p className="text-2xl font-bold text-green-600 mb-4">R{hotspot.price}</p>

                    <div className="flex gap-3">
                      <Button
                        onClick={() => setActiveHotspot(null)}
                        variant="outline"
                        className="flex-1"
                      >
                        Close
                      </Button>
                      <Button
                        onClick={() => {
                          if (hotspot.type === 'mentorship') {
                            handleEnrollMentorship(hotspot);
                          } else {
                            handleAddToCart(hotspot);
                          }
                        }}
                        className="flex-1 bg-green-600 hover:bg-green-700"
                      >
                        {hotspot.type === 'mentorship' ? (
                          <>
                            <Award className="w-4 h-4 mr-2" />
                            Enroll
                          </>
                        ) : (
                          <>
                            <ShoppingCart className="w-4 h-4 mr-2" />
                            Add to Cart
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                );
              })()}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
}
