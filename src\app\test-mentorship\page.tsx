"use client";

import { useState } from 'react';
import { useMentorshipPrograms, useCreateEnrollment } from '../../hooks/useMentorship';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Calendar, Clock, DollarSign, Users, GraduationCap } from 'lucide-react';
import { useToast } from '../../hooks/use-toast';

export default function TestMentorshipPage() {
  const { toast } = useToast();
  const [selectedProgram, setSelectedProgram] = useState<string>('');
  const [enrollmentData, setEnrollmentData] = useState({
    student_id: '',
    mentor_id: '',
    start_date: '',
    end_date: '',
    payment_type: 'monthly' as 'monthly' | 'upfront'
  });

  // Fetch mentorship programs
  const { data: programs = [], isLoading, error } = useMentorshipPrograms();
  
  // Enrollment mutation
  const createEnrollment = useCreateEnrollment();

  const handleEnrollment = async () => {
    if (!selectedProgram || !enrollmentData.student_id || !enrollmentData.mentor_id) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    try {
      await createEnrollment.mutateAsync({
        ...enrollmentData,
        program_id: selectedProgram
      });
      
      toast({
        title: "Enrollment Successful!",
        description: "Student has been enrolled in the mentorship program",
      });
      
      // Reset form
      setSelectedProgram('');
      setEnrollmentData({
        student_id: '',
        mentor_id: '',
        start_date: '',
        end_date: '',
        payment_type: 'monthly'
      });
    } catch (error: any) {
      toast({
        title: "Enrollment Failed",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Loading Mentorship Programs...</h1>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4 text-red-600">Error Loading Programs</h1>
          <p>{error.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">🎾 Mentorship Program Testing</h1>
        <p className="text-muted-foreground">
          Test the mentorship enrollment flow and program management
        </p>
      </div>

      {/* Available Programs */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {programs.map((program) => (
          <Card 
            key={program.id} 
            className={`cursor-pointer transition-all ${
              selectedProgram === program.id ? 'ring-2 ring-primary' : ''
            }`}
            onClick={() => setSelectedProgram(program.id)}
          >
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GraduationCap className="h-5 w-5" />
                {program.name}
              </CardTitle>
              <CardDescription>{program.description}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  {program.duration_months === 0 ? 'One-time' : `${program.duration_months} months`}
                </span>
              </div>
              
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">R{program.price_monthly}/month</span>
              </div>
              
              {program.price_upfront && (
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">R{program.price_upfront} upfront</span>
                </div>
              )}
              
              {program.features && (
                <div className="space-y-1">
                  <p className="text-xs font-medium">Features:</p>
                  <div className="flex flex-wrap gap-1">
                    {Object.entries(program.features).map(([key, value]) => (
                      <Badge key={key} variant="secondary" className="text-xs">
                        {key}: {String(value)}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Enrollment Form */}
      {selectedProgram && (
        <Card>
          <CardHeader>
            <CardTitle>Test Enrollment</CardTitle>
            <CardDescription>
              Fill in the details to test the enrollment process
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="student_id">Student ID (UUID)</Label>
                <Input
                  id="student_id"
                  placeholder="e.g., 123e4567-e89b-12d3-a456-************"
                  value={enrollmentData.student_id}
                  onChange={(e) => setEnrollmentData(prev => ({ ...prev, student_id: e.target.value }))}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="mentor_id">Mentor ID (UUID)</Label>
                <Input
                  id="mentor_id"
                  placeholder="e.g., 123e4567-e89b-12d3-a456-************"
                  value={enrollmentData.mentor_id}
                  onChange={(e) => setEnrollmentData(prev => ({ ...prev, mentor_id: e.target.value }))}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="start_date">Start Date</Label>
                <Input
                  id="start_date"
                  type="datetime-local"
                  value={enrollmentData.start_date}
                  onChange={(e) => setEnrollmentData(prev => ({ ...prev, start_date: e.target.value }))}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="end_date">End Date</Label>
                <Input
                  id="end_date"
                  type="datetime-local"
                  value={enrollmentData.end_date}
                  onChange={(e) => setEnrollmentData(prev => ({ ...prev, end_date: e.target.value }))}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="payment_type">Payment Type</Label>
                <Select 
                  value={enrollmentData.payment_type} 
                  onValueChange={(value: 'monthly' | 'upfront') => 
                    setEnrollmentData(prev => ({ ...prev, payment_type: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="upfront">Upfront</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <Button 
              onClick={handleEnrollment}
              disabled={createEnrollment.isPending}
              className="w-full"
            >
              {createEnrollment.isPending ? 'Creating Enrollment...' : 'Test Enrollment'}
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
