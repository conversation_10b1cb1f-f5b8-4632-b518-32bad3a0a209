import { NextRequest, NextResponse } from 'next/server';
import { createClient, createServiceRoleClient } from '@/utils/supabase/server';
import { ConsultationFilters } from '@/types/consultations';

/**
 * GET /api/admin/consultations
 * Fetch consultations with filtering, pagination, and search
 */
export async function GET(request: NextRequest) {
  try {
    console.log('GET /api/admin/consultations - Starting request');

    // Verify admin authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      console.log('GET /api/admin/consultations - No session found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const { data: userData } = await supabase
      .from('users')
      .select('role, admin_role')
      .eq('id', session.user.id)
      .single();

    if (!userData || userData.role !== 'admin') {
      console.log('GET /api/admin/consultations - User is not admin');
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    console.log('GET /api/admin/consultations - Admin access confirmed');

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const payment_status = searchParams.get('payment_status');
    const date_from = searchParams.get('date_from');
    const date_to = searchParams.get('date_to');
    const search = searchParams.get('search');

    // Use service role client for database operations
    const serviceSupabase = createServiceRoleClient();

    // Build the query
    let query = serviceSupabase
      .from('consultations')
      .select('*', { count: 'exact' });

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }

    if (payment_status) {
      query = query.eq('payment_status', payment_status);
    }

    if (date_from) {
      query = query.gte('scheduled_date', date_from);
    }

    if (date_to) {
      query = query.lte('scheduled_date', date_to);
    }

    if (search) {
      query = query.or(`first_name.ilike.%${search}%,last_name.ilike.%${search}%,email.ilike.%${search}%,phone_number.ilike.%${search}%`);
    }

    // Apply pagination and ordering
    const offset = (page - 1) * limit;
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: consultations, error, count } = await query;

    if (error) {
      console.error('GET /api/admin/consultations - Database error:', error);
      return NextResponse.json({ error: 'Database error' }, { status: 500 });
    }

    console.log(`GET /api/admin/consultations - Found ${consultations?.length || 0} consultations`);

    return NextResponse.json({
      consultations: consultations || [],
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit),
    });

  } catch (error: any) {
    console.error('GET /api/admin/consultations - Error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/consultations
 * Create a new consultation (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    console.log('POST /api/admin/consultations - Starting request');

    // Verify admin authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const { data: userData } = await supabase
      .from('users')
      .select('role, admin_role')
      .eq('id', session.user.id)
      .single();

    if (!userData || userData.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const {
      first_name,
      last_name,
      email,
      phone_number,
      scheduled_date,
      scheduled_time,
      reason,
      duration = 60,
      payment_amount = 300,
    } = body;

    // Validate required fields
    if (!first_name || !last_name || !email || !phone_number || !scheduled_date || !scheduled_time || !reason) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Use service role client for database operations
    const serviceSupabase = createServiceRoleClient();

    // Create the consultation
    const { data: consultation, error } = await serviceSupabase
      .from('consultations')
      .insert({
        first_name,
        last_name,
        email,
        phone_number,
        scheduled_date,
        scheduled_time,
        reason,
        duration,
        status: 'pending',
        payment_status: 'pending',
        payment_amount,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error('POST /api/admin/consultations - Database error:', error);
      return NextResponse.json({ error: 'Failed to create consultation' }, { status: 500 });
    }

    console.log('POST /api/admin/consultations - Consultation created:', consultation.id);

    return NextResponse.json({
      success: true,
      consultation,
    });

  } catch (error: any) {
    console.error('POST /api/admin/consultations - Error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
