'use client';

import { useState } from 'react';
import { useRecords, useCreateRecord, useUpdateRecord, useDeleteRecord } from '@/lib/tanstack-query';
import { productSchema, productInputSchema, type Product, type ProductInput } from '@/lib/zod-schemas';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, PlusCircle, Pencil, Trash } from 'lucide-react';

/**
 * ProductListWithQuery Component
 * 
 * This component demonstrates how to use TanStack Query with Supabase and Zod validation.
 * It provides a complete CRUD interface for products.
 */
export function ProductListWithQuery() {
  const { toast } = useToast();
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  
  // Initialize form data
  const initialFormData: ProductInput = {
    name: '',
    description: '',
    price: 0,
    category: '',
    stock: 0,
    image: null,
    status: 'In Stock'
  };
  
  const [formData, setFormData] = useState<ProductInput>(initialFormData);
  
  // Fetch products using TanStack Query
  const { data: products, isLoading, isError } = useRecords('products', productSchema, {
    select: '*',
    // Optional filter example
    // filter: (query) => query.eq('category', 'rackets').order('name', { ascending: true }),
  });
  
  // Create product mutation
  const createProduct = useCreateRecord('products', productInputSchema);
  
  // Update product mutation
  const updateProduct = useUpdateRecord('products', productInputSchema);
  
  // Delete product mutation
  const deleteProduct = useDeleteRecord('products');
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    // Handle numeric values
    if (name === 'price' || name === 'stock') {
      setFormData({
        ...formData,
        [name]: parseFloat(value) || 0
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (editingProduct) {
        // Update existing product
        await updateProduct.mutateAsync({
          id: editingProduct.id,
          data: formData
        });
        
        toast({
          title: 'Product updated',
          description: `${formData.name} has been updated successfully.`,
        });
      } else {
        // Create new product
        await createProduct.mutateAsync(formData);
        
        toast({
          title: 'Product created',
          description: `${formData.name} has been added to the catalog.`,
        });
      }
      
      // Reset form
      setFormData(initialFormData);
      setEditingProduct(null);
      setIsFormOpen(false);
    } catch (error) {
      console.error('Error saving product:', error);
      
      toast({
        title: 'Error',
        description: 'There was an error saving the product. Please try again.',
        variant: 'destructive',
      });
    }
  };
  
  // Handle edit product
  const handleEdit = (product: Product) => {
    setEditingProduct(product);
    setFormData({
      name: product.name,
      description: product.description || '',
      price: product.price,
      category: product.category,
      stock: product.stock,
      image: product.image,
      status: product.status
    });
    setIsFormOpen(true);
  };
  
  // Handle delete product
  const handleDelete = async (id: string) => {
    try {
      await deleteProduct.mutateAsync(id);
      
      toast({
        title: 'Product deleted',
        description: 'The product has been removed from the catalog.',
      });
    } catch (error) {
      console.error('Error deleting product:', error);
      
      toast({
        title: 'Error',
        description: 'There was an error deleting the product. Please try again.',
        variant: 'destructive',
      });
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold tracking-tight">Products</h2>
        <Button 
          onClick={() => {
            setEditingProduct(null);
            setFormData(initialFormData);
            setIsFormOpen(!isFormOpen);
          }}
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          Add Product
        </Button>
      </div>
      
      {/* Product Form */}
      {isFormOpen && (
        <Card>
          <CardHeader>
            <CardTitle>{editingProduct ? 'Edit Product' : 'Add New Product'}</CardTitle>
            <CardDescription>
              {editingProduct 
                ? `Update the details for ${editingProduct.name}` 
                : 'Fill in the details for the new product'}
            </CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Product Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Input
                    id="category"
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="price">Price</Label>
                  <Input
                    id="price"
                    name="price"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.price}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="stock">Stock Quantity</Label>
                  <Input
                    id="stock"
                    name="stock"
                    type="number"
                    min="0"
                    value={formData.stock}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description || ''}
                  onChange={handleInputChange}
                  rows={4}
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsFormOpen(false)}
              >
                Cancel
              </Button>
              <Button 
                type="submit"
                disabled={createProduct.isPending || updateProduct.isPending}
              >
                {(createProduct.isPending || updateProduct.isPending) && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {editingProduct ? 'Update Product' : 'Create Product'}
              </Button>
            </CardFooter>
          </form>
        </Card>
      )}
      
      {/* Loading and Error States */}
      {isLoading && (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      )}
      
      {isError && (
        <div className="bg-destructive/15 text-destructive p-4 rounded-md">
          There was an error loading the products. Please refresh the page and try again.
        </div>
      )}
      
      {/* Product List */}
      {!isLoading && !isError && products && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {products.length === 0 ? (
            <div className="col-span-full text-center py-8 text-muted-foreground">
              No products found. Click "Add Product" to create your first product.
            </div>
          ) : (
            products.map((product) => (
              <Card key={product.id}>
                <CardHeader>
                  <CardTitle className="flex justify-between items-start">
                    <span className="truncate">{product.name}</span>
                    <span className="text-lg font-normal">R{product.price.toFixed(2)}</span>
                  </CardTitle>
                  <CardDescription>
                    Category: {product.category}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground line-clamp-3">
                    {product.description || 'No description available.'}
                  </p>
                  <p className="mt-2 text-sm">
                    Stock: <span className={product.stock > 0 ? 'text-green-600' : 'text-red-600'}>
                      {product.stock > 0 ? `${product.stock} in stock` : 'Out of stock'}
                    </span>
                  </p>
                </CardContent>
                <CardFooter className="flex justify-end gap-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handleEdit(product)}
                  >
                    <Pencil className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                  <Button 
                    variant="destructive" 
                    size="sm"
                    onClick={() => handleDelete(product.id)}
                    disabled={deleteProduct.isPending}
                  >
                    {deleteProduct.isPending ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Trash className="h-4 w-4 mr-1" />
                    )}
                    Delete
                  </Button>
                </CardFooter>
              </Card>
            ))
          )}
        </div>
      )}
    </div>
  );
}
